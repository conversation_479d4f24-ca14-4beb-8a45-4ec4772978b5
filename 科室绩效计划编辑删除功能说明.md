# 科室绩效计划编辑删除功能说明

## 功能概述

已为科室绩效计划模块实现了完整的前后端编辑和删除功能，包括：

### 前端功能
1. **表格操作列**：在数据表格中添加了操作列，包含"修改"和"删除"按钮
2. **批量删除**：在工具栏添加了批量删除按钮
3. **权限控制**：所有操作按钮都添加了相应的权限控制

### 后端功能
1. **编辑接口**：PUT `/performance/plan/dept` - 修改科室绩效计划
2. **删除接口**：DELETE `/performance/plan/dept/{ids}` - 删除科室绩效计划（支持批量）
3. **查询接口**：GET `/performance/plan/dept/{id}` - 获取单条记录详情

## 使用说明

### 1. 单条记录编辑
- 点击表格中某行的"修改"按钮
- 系统会弹出编辑对话框，自动填充当前记录的数据
- 修改完成后点击"确定"保存

### 2. 单条记录删除
- 点击表格中某行的"删除"按钮
- 系统会弹出确认对话框，显示要删除的科室名称
- 确认后删除该条记录

### 3. 批量删除
- 勾选表格中要删除的多条记录
- 点击工具栏的"删除"按钮
- 系统会弹出确认对话框，显示要删除的记录数量
- 确认后批量删除选中的记录

### 4. 权限要求
- 编辑功能需要：`performance:plan:dept:edit` 权限
- 删除功能需要：`performance:plan:dept:remove` 权限
- 新增功能需要：`performance:plan:dept:add` 权限
- 导出功能需要：`performance:plan:dept:export` 权限
- 导入功能需要：`performance:plan:dept:import` 权限

## 技术实现

### 前端实现
- 使用Element UI的表格组件和对话框组件
- 通过axios调用后端API接口
- 实现了表单验证和错误处理

### 后端实现
- Spring Boot RESTful API
- MyBatis数据访问层
- 支持事务管理和异常处理
- 完整的CRUD操作

## 数据库表结构

```sql
CREATE TABLE performance_plan_dept (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    dept_name VARCHAR(100) NOT NULL COMMENT '科室名称',
    seq_no INT COMMENT '序号',
    task_type VARCHAR(100) COMMENT '任务类型',
    task_content TEXT COMMENT '绩效任务',
    target_measure TEXT COMMENT '目标及措施',
    eval_standard TEXT COMMENT '评价标准',
    score_weight VARCHAR(50) COMMENT '分值或权重',
    principal VARCHAR(100) COMMENT '责任人',
    deadline VARCHAR(100) COMMENT '完成时限',
    create_by VARCHAR(64) COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间'
);
```

## 注意事项

1. 删除操作不可恢复，请谨慎操作
2. 编辑时必填字段包括：科室名称、任务类型、绩效任务
3. 批量操作时请确保选择了正确的记录
4. 所有操作都会记录操作日志
5. 需要相应的系统权限才能执行操作

## 测试建议

1. 测试单条记录的编辑功能
2. 测试单条记录的删除功能
3. 测试批量删除功能
4. 测试权限控制是否生效
5. 测试表单验证是否正常
6. 测试错误处理机制
