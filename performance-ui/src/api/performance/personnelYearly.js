import request from '@/utils/request'

// 查询个人年度绩效计划列表
export function listPersonnelYearlyPlan(query) {
  return request({
    url: '/performance/personnel/yearly/list',
    method: 'get',
    params: query
  })
}

// 查询个人年度绩效计划详细信息
export function getPersonnelYearlyPlan(id) {
  return request({
    url: '/performance/personnel/yearly/' + id,
    method: 'get'
  })
}

// 新增个人年度绩效计划
export function addPersonnelYearlyPlan(data) {
  return request({
    url: '/performance/personnel/yearly',
    method: 'post',
    data: data
  })
}

// 修改个人年度绩效计划
export function updatePersonnelYearlyPlan(data) {
  return request({
    url: '/performance/personnel/yearly',
    method: 'put',
    data: data
  })
}

// 删除个人年度绩效计划
export function delPersonnelYearlyPlan(ids) {
  return request({
    url: '/performance/personnel/yearly/' + ids,
    method: 'delete'
  })
}

// 获取个性指标列表
export function getPersonalIndicators(planId) {
  return request({
    url: '/performance/personnel/yearly/' + planId + '/personalIndicators',
    method: 'get'
  })
}

// 获取共性指标列表
export function getCommonIndicators(planId) {
  return request({
    url: '/performance/personnel/yearly/' + planId + '/commonIndicators',
    method: 'get'
  })
}

// 导入Excel文档
export function importYearlyExcel(data) {
  return request({
    url: '/performance/personnel/yearly/importExcel',
    method: 'post',
    data: data
  })
}

// 下载Excel模板
export function downloadYearlyExcelTemplate() {
  return request({
    url: '/performance/personnel/yearly/downloadExcelTemplate',
    method: 'get',
    responseType: 'blob'
  })
}
