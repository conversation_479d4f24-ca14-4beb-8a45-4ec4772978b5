<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <!-- 个人季度绩效计划 Tab -->
      <el-tab-pane label="个人季度绩效计划" name="quarterly">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="姓名" prop="name">
            <el-input
              v-model="queryParams.name"
              placeholder="请输入姓名"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="科室" prop="department">
            <el-input
              v-model="queryParams.department"
              placeholder="请输入科室"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="warning" plain icon="el-icon-download" size="mini" :disabled="multiple" @click="handleExportSelected">导出Word</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-upload
              class="upload-demo"
              :action="importUrl"
              :headers="uploadHeaders"
              :on-success="handleImportSuccess"
              :on-error="handleImportError"
              :before-upload="beforeImportUpload"
              :show-file-list="false"
              style="display: inline-block;">
              <el-button type="info" plain icon="el-icon-upload2" size="mini">导入Word</el-button>
            </el-upload>
          </el-col>
          <el-col :span="1.5">
            <el-button type="info" plain icon="el-icon-download" size="mini" @click="handleDownloadTemplate">下载模板</el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-card class="box-card" style="margin-bottom: 20px;">
          <div slot="header" class="clearfix">
            <span style="font-weight: bold;">个人季度绩效计划列表</span>
          </div>
          <el-table
            v-loading="loading"
            :data="planList"
            @selection-change="handleSelectionChange"
            @current-change="handleCurrentChange"
            highlight-current-row
            :height="300">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="姓名" align="center" prop="name" width="100" />
            <el-table-column label="科室" align="center" prop="department" width="150" />
            <el-table-column label="创建时间" align="center" prop="createTime" width="180">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button size="mini" type="text" icon="el-icon-s-data" @click="handleViewPersonalIndicators(scope.row)">个性指标</el-button>
                <el-button size="mini" type="text" icon="el-icon-data-analysis" @click="handleViewCommonIndicators(scope.row)">共性指标</el-button>
                <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </el-card>
      </el-tab-pane>

      <!-- 个人年度绩效计划 Tab -->
      <el-tab-pane label="个人年度绩效计划" name="yearly">
        <el-form :model="yearlyQueryParams" ref="yearlyQueryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="姓名" prop="name">
            <el-input
              v-model="yearlyQueryParams.name"
              placeholder="请输入姓名"
              clearable
              @keyup.enter.native="handleYearlyQuery"
            />
          </el-form-item>
          <el-form-item label="科室" prop="department">
            <el-input
              v-model="yearlyQueryParams.department"
              placeholder="请输入科室"
              clearable
              @keyup.enter.native="handleYearlyQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleYearlyQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetYearlyQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="yearlyMultiple" @click="handleYearlyDelete">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-upload
              class="upload-demo"
              :action="yearlyImportUrl"
              :headers="uploadHeaders"
              :on-success="handleYearlyImportSuccess"
              :on-error="handleYearlyImportError"
              :before-upload="beforeYearlyImportUpload"
              :show-file-list="false"
              style="display: inline-block;">
              <el-button type="info" plain icon="el-icon-upload2" size="mini">导入Excel</el-button>
            </el-upload>
          </el-col>
          <el-col :span="1.5">
            <el-button type="info" plain icon="el-icon-download" size="mini" @click="handleYearlyDownloadTemplate">下载模板</el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getYearlyList"></right-toolbar>
        </el-row>

        <el-card class="box-card" style="margin-bottom: 20px;">
          <div slot="header" class="clearfix">
            <span style="font-weight: bold;">个人年度绩效计划列表</span>
          </div>
          <el-table
            v-loading="yearlyLoading"
            :data="yearlyPlanList"
            @selection-change="handleYearlySelectionChange"
            highlight-current-row
            :height="300">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="姓名" align="center" prop="name" width="100" />
            <el-table-column label="科室" align="center" prop="department" width="150" />
            <el-table-column label="年份" align="center" prop="year" width="100" />
            <el-table-column label="创建时间" align="center" prop="createTime" width="180">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button size="mini" type="text" icon="el-icon-s-data" @click="handleViewYearlyPersonalIndicators(scope.row)">个性指标</el-button>
                <el-button size="mini" type="text" icon="el-icon-data-analysis" @click="handleViewYearlyCommonIndicators(scope.row)">共性指标</el-button>
                <el-button size="mini" type="text" icon="el-icon-delete" @click="handleYearlyDelete(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="yearlyTotal>0"
            :total="yearlyTotal"
            :page.sync="yearlyQueryParams.pageNum"
            :limit.sync="yearlyQueryParams.pageSize"
            @pagination="getYearlyList"
          />
        </el-card>
      </el-tab-pane>
    </el-tabs>



    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="form.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="科室" prop="department">
          <el-input v-model="form.department" placeholder="请输入科室" />
        </el-form-item>
        <!-- <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="DRAFT">草稿</el-radio>
            <el-radio label="SUBMITTED">已提交</el-radio>
            <el-radio label="APPROVED">已审核</el-radio>
          </el-radio-group>
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="taskTitle" :visible.sync="taskOpen" width="800px" append-to-body>
      <el-form ref="taskForm" :model="taskForm" :rules="taskRules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="任务类型" prop="taskType">
              <el-input v-model="taskForm.taskType" placeholder="请输入任务类型" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分值" prop="score">
              <el-input-number v-model="taskForm.score" :min="0" :max="100" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="任务内容" prop="taskContent">
          <el-input v-model="taskForm.taskContent" type="textarea" placeholder="请输入任务内容" />
        </el-form-item>
        <el-form-item label="目标及措施" prop="targetMeasures">
          <el-input v-model="taskForm.targetMeasures" type="textarea" placeholder="请输入目标及措施" />
        </el-form-item>
        <el-form-item label="评价标准" prop="evaluationStandard">
          <el-input v-model="taskForm.evaluationStandard" type="textarea" placeholder="请输入评价标准" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="责任人" prop="responsible">
              <el-input v-model="taskForm.responsible" placeholder="请输入责任人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="完成时限" prop="deadline">
              <el-date-picker
                v-model="taskForm.deadline"
                type="date"
                placeholder="选择完成时限"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitTaskForm">确 定</el-button>
        <el-button @click="cancelTask">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 个性指标弹窗 -->
    <el-dialog title="个性指标" :visible.sync="personalIndicatorOpen" width="80%" append-to-body>
      <el-table :data="personalIndicatorList" style="width: 100%" border>
        <el-table-column label="序号" prop="seqNo" width="60" align="center" />
        <el-table-column label="任务类型" prop="taskCategory" width="120" align="center" />
        <el-table-column label="绩效任务" prop="performanceTask" min-width="200" show-overflow-tooltip />
        <el-table-column label="目标及措施" prop="targetMeasures" min-width="200" show-overflow-tooltip />
        <el-table-column label="评价标准" prop="evaluationCriteria" min-width="200" show-overflow-tooltip />
        <el-table-column label="分值或权重" prop="scoreWeight" width="100" align="center" />
        <el-table-column label="责任分类" prop="responsibilityCategory" width="120" align="center" />
        <el-table-column label="完成时限" prop="completionTime" width="120" align="center" />
      </el-table>
    </el-dialog>

    <!-- 共性指标弹窗 -->
    <el-dialog title="共性指标" :visible.sync="commonIndicatorOpen" width="60%" append-to-body>
      <el-table :data="commonIndicatorList" style="width: 100%" border>
        <el-table-column label="序号" prop="seqNo" width="60" align="center" />
        <el-table-column label="责任分类" prop="responsibilityCategory" min-width="250" show-overflow-tooltip />
        <el-table-column label="评价标准" prop="evaluationStandard" min-width="250" show-overflow-tooltip />
        <el-table-column label="分值或权重" prop="scoreWeight" width="120" align="center" />
        <el-table-column label="完成时限" prop="completionTime" width="120" align="center" />
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import {
  listPersonnelPlan,
  getPersonnelPlan,
  delPersonnelPlan,
  addPersonnelPlan,
  updatePersonnelPlan,
  exportPersonnelPlan,
  batchExportPersonnelPlan,
  importPersonnelPlan,
  downloadTemplate
} from "@/api/performance/personnel";
import {
  listPersonnelTask,
  getPersonnelTask,
  delPersonnelTask,
  addPersonnelTask,
  updatePersonnelTask
} from "@/api/performance/personnelTask";
import {
  listPersonnelYearlyPlan,
  getPersonnelYearlyPlan,
  delPersonnelYearlyPlan,
  addPersonnelYearlyPlan,
  updatePersonnelYearlyPlan,
  getPersonalIndicators,
  getCommonIndicators,
  downloadYearlyExcelTemplate
} from "@/api/performance/personnelYearly";

export default {
  name: "PersonnelPlan",
  dicts: ['performance_status'],
  data() {
    return {
      // Tab相关
      activeTab: 'quarterly',

      // 季度绩效计划相关
      loading: true,
      ids: [],
      single: true,
      multiple: true,
      showSearch: true,
      total: 0,
      planList: [],
      taskList: [],
      personalIndicatorList: [],
      commonIndicatorList: [],
      currentPlan: null,
      title: "",
      taskTitle: "",
      open: false,
      taskOpen: false,
      personalIndicatorOpen: false,
      commonIndicatorOpen: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        department: null,
        status: null
      },
      form: {},
      taskForm: {},
      rules: {
        name: [
          { required: true, message: "姓名不能为空", trigger: "blur" }
        ],
        department: [
          { required: true, message: "科室不能为空", trigger: "blur" }
        ]
      },
      taskRules: {
        taskType: [
          { required: true, message: "任务类型不能为空", trigger: "blur" }
        ],
        taskContent: [
          { required: true, message: "任务内容不能为空", trigger: "blur" }
        ],
        score: [
          { required: true, message: "分值不能为空", trigger: "blur" }
        ]
      },
      importUrl: process.env.VUE_APP_BASE_API + "/performance/personnel/import",
      uploadHeaders: {},

      // 年度绩效计划相关
      yearlyLoading: true,
      yearlyIds: [],
      yearlyMultiple: true,
      yearlyTotal: 0,
      yearlyPlanList: [],
      yearlyQueryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        department: null,
        year: null
      },
      yearlyImportUrl: process.env.VUE_APP_BASE_API + "/performance/personnel/yearly/importExcel"
    };
  },
  created() {
    this.getList();
    // 设置上传认证头
    this.uploadHeaders = {
      Authorization: 'Bearer ' + this.$store.getters.token
    };
  },
  methods: {
    getList() {
      this.loading = true;
      listPersonnelPlan(this.queryParams).then(response => {
        this.planList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getTaskList(planId) {
      if (!planId) return;
      listPersonnelTask({ planId: planId }).then(response => {
        this.taskList = response.rows || [];
      });
    },
    cancel() {
      this.open = false;
      this.reset();
    },
    cancelTask() {
      this.taskOpen = false;
      this.resetTask();
    },
    reset() {
      this.form = {
        id: null,
        name: null,
        department: null,
        status: "DRAFT"
      };
      this.resetForm("form");
    },
    resetTask() {
      this.taskForm = {
        id: null,
        planId: null,
        taskType: null,
        taskContent: null,
        targetMeasures: null,
        evaluationStandard: null,
        score: null,
        responsible: null,
        deadline: null
      };
      this.resetForm("taskForm");
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    handleCurrentChange(currentRow) {
      this.currentPlan = currentRow;
      if (currentRow) {
        this.getTaskList(currentRow.id);
      } else {
        this.taskList = [];
      }
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加绩效计划";
    },
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getPersonnelPlan(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改绩效计划";
      });
    },
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updatePersonnelPlan(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPersonnelPlan(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除绩效计划编号为"' + ids + '"的数据项？').then(function() {
        return delPersonnelPlan(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    handleAddTask() {
      if (!this.currentPlan) {
        this.$modal.msgError("请先选择一个绩效计划");
        return;
      }
      this.resetTask();
      this.taskForm.planId = this.currentPlan.id;
      this.taskOpen = true;
      this.taskTitle = "添加绩效任务";
    },
    handleUpdateTask(row) {
      this.resetTask();
      const id = row.id;
      getPersonnelTask(id).then(response => {
        this.taskForm = response.data;
        this.taskOpen = true;
        this.taskTitle = "修改绩效任务";
      });
    },
    submitTaskForm() {
      this.$refs["taskForm"].validate(valid => {
        if (valid) {
          if (this.taskForm.id != null) {
            updatePersonnelTask(this.taskForm).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.taskOpen = false;
              this.getTaskList(this.currentPlan.id);
            });
          } else {
            addPersonnelTask(this.taskForm).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.taskOpen = false;
              this.getTaskList(this.currentPlan.id);
            });
          }
        }
      });
    },
    handleDeleteTask(row) {
      const id = row.id;
      this.$modal.confirm('是否确认删除该任务数据项？').then(function() {
        return delPersonnelTask(id);
      }).then(() => {
        this.getTaskList(this.currentPlan.id);
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    handleExportSelected() {
      if (this.ids.length === 0) {
        this.$modal.msgWarning("请选择要导出的数据");
        return;
      }
      this.$modal.confirm('是否确认导出选中的' + this.ids.length + '条数据？').then(() => {
        this.$modal.loading("正在导出数据，请稍候...");
        batchExportPersonnelPlan(this.ids).then(response => {
          const blob = new Blob([response], {
            type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
          });
          const link = document.createElement('a');
          link.href = window.URL.createObjectURL(blob);
          link.download = '个人绩效计划_' + new Date().getTime() + '.docx';
          link.click();
          window.URL.revokeObjectURL(link.href);
          this.$modal.closeLoading();
          this.$modal.msgSuccess("导出成功");
        }).catch(() => {
          this.$modal.closeLoading();
          this.$modal.msgError("导出失败");
        });
      }).catch(() => {});
    },
    handleImportSuccess(response) {
      if (response.code === 200) {
        this.$modal.msgSuccess("导入成功");
        this.getList();
      } else {
        this.$modal.msgError(response.msg || "导入失败");
      }
    },
    handleImportError(error) {
      console.error("导入失败:", error);
      if (error.status === 401) {
        this.$modal.msgError("认证失败，请重新登录");
        this.$store.dispatch('LogOut').then(() => {
          location.href = '/login';
        });
      } else {
        this.$modal.msgError("导入失败，请检查文件格式");
      }
    },
    beforeImportUpload(file) {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      if (!isExcel) {
        this.$modal.msgError('只能上传Word文件！');
      }
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        this.$modal.msgError('上传文件大小不能超过 2MB!');
      }
      return isExcel && isLt2M;
    },
    handleDownloadTemplate() {
      this.$modal.loading("正在下载模板，请稍候...");
      downloadTemplate().then(response => {
        const blob = new Blob([response], {
          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        });
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = '个人绩效计划模板.docx';
        link.click();
        window.URL.revokeObjectURL(link.href);
        this.$modal.closeLoading();
      }).catch(() => {
        this.$modal.closeLoading();
        this.$modal.msgError("下载模板失败");
      });
    },
    handleViewPersonalIndicators(row) {
      // 获取个性指标数据 (指标类型 = 1)
      listPersonnelTask({ planId: row.id, indicatorType: 1 }).then(response => {
        this.personalIndicatorList = response.rows || [];
        this.personalIndicatorOpen = true;
      }).catch(() => {
        this.$modal.msgError("获取个性指标数据失败");
      });
    },
    handleViewCommonIndicators(row) {
      // 获取共性指标数据 (指标类型 = 2)
      listPersonnelTask({ planId: row.id, indicatorType: 2 }).then(response => {
        this.commonIndicatorList = response.rows || [];
        this.commonIndicatorOpen = true;
      }).catch(() => {
        this.$modal.msgError("获取共性指标数据失败");
      });
    },

    // 年度绩效计划相关方法
    handleTabClick(tab) {
      if (tab.name === 'yearly') {
        this.getYearlyList();
      } else if (tab.name === 'quarterly') {
        this.getList();
      }
    },

    getYearlyList() {
      this.yearlyLoading = true;
      listPersonnelYearlyPlan(this.yearlyQueryParams).then(response => {
        this.yearlyPlanList = response.rows;
        this.yearlyTotal = response.total;
        this.yearlyLoading = false;
      });
    },

    handleYearlyQuery() {
      this.yearlyQueryParams.pageNum = 1;
      this.getYearlyList();
    },

    resetYearlyQuery() {
      this.resetForm("yearlyQueryForm");
      this.handleYearlyQuery();
    },

    handleYearlySelectionChange(selection) {
      this.yearlyIds = selection.map(item => item.id);
      this.yearlyMultiple = !selection.length;
    },

    handleYearlyDelete(row) {
      const ids = row ? [row.id] : this.yearlyIds;
      const message = row
        ? `是否确认删除"${row.name}"的年度绩效计划数据项？`
        : `是否确认删除选中的${ids.length}条年度绩效计划数据项？`;

      this.$modal.confirm(message).then(function() {
        return delPersonnelYearlyPlan(ids.join(','));
      }).then(() => {
        this.getYearlyList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    beforeYearlyImportUpload(file) {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                      file.type === 'application/vnd.ms-excel';
      if (!isExcel) {
        this.$modal.msgError('只能上传Excel文件格式(.xlsx或.xls)!');
      }
      return isExcel;
    },

    handleYearlyImportSuccess(response) {
      if (response.code === 200) {
        this.$modal.msgSuccess('导入成功');
        this.getYearlyList();
      } else {
        this.$modal.msgError(response.msg || '导入失败');
      }
    },

    handleYearlyImportError(error) {
      console.error("导入失败:", error);
      if (error.status === 401) {
        this.$modal.msgError("认证失败，请重新登录");
        this.$store.dispatch('LogOut').then(() => {
          location.href = '/login';
        });
      } else {
        this.$modal.msgError("导入失败，请检查文件格式");
      }
    },

    handleYearlyDownloadTemplate() {
      downloadYearlyExcelTemplate().then(response => {
        const blob = new Blob([response], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = '个人年度绩效计划模板.xlsx';
        link.click();
        window.URL.revokeObjectURL(link.href);
      });
    },

    handleViewYearlyPersonalIndicators(row) {
      getPersonalIndicators(row.id).then(response => {
        this.personalIndicatorList = response.data || [];
        this.personalIndicatorOpen = true;
      }).catch(() => {
        this.$modal.msgError("获取个性指标数据失败");
      });
    },

    handleViewYearlyCommonIndicators(row) {
      getCommonIndicators(row.id).then(response => {
        this.commonIndicatorList = response.data || [];
        this.commonIndicatorOpen = true;
      }).catch(() => {
        this.$modal.msgError("获取共性指标数据失败");
      });
    }
  }
};
</script>

<style scoped>
.box-card {
  margin-bottom: 20px;
}
</style>
