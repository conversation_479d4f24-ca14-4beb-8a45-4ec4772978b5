<template>
  <div class="app-container">
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="12">
        <el-button type="primary" @click="handleAdd">新增</el-button>
        <el-upload
          class="upload-demo"
          :action="uploadUrl"
          :headers="uploadHeaders"
          :show-file-list="false"
          :on-success="handleImportSuccess"
          :on-error="handleImportError"
          accept=".doc,.docx"
          style="display:inline-block;margin-left:10px;"
        >
          <el-button>导入Word</el-button>
        </el-upload>
        <el-button @click="handleExport" style="margin-left:10px;">导出Word</el-button>
        <el-button @click="handleDownloadTemplate" style="margin-left:10px;">下载模板</el-button>
      </el-col>
    </el-row>
    <el-table :data="tableData" border style="width: 100%" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column prop="id" label="序号" width="60" />
      <el-table-column prop="deptName" label="科室工作任务" />
      <el-table-column prop="category" label="分类" />
      <el-table-column prop="workTarget" label="工作目标" />
      <el-table-column prop="workMeasure" label="工作措施" />
      <el-table-column prop="completeTime" label="计划完成时间" />
      <el-table-column prop="firstWeek" label="第一周" />
      <el-table-column prop="secondWeek" label="第二周" />
      <el-table-column prop="thirdWeek" label="第三周" />
      <el-table-column prop="fourthWeek" label="第四周" />
      <el-table-column prop="responsibleLeader" label="责任领导" />
      <el-table-column prop="responsibleDepartment" label="责任科室" />
      <el-table-column prop="departmentHeader" label="科室负责人" />
      <el-table-column prop="specificResponsiblePerson" label="具体负责人" />

      <el-table-column label="操作" width="180">
        <template slot-scope="scope">
          <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>

    </el-table>
    <el-pagination
      style="margin-top: 20px;"
      background
      layout="prev, pager, next, jumper"
      :total="total"
      :page-size="pageSize"
      :current-page.sync="pageNum"
      @current-change="loadData"
    />
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible">
      <el-form :model="form" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="8"><el-form-item label="科室工作任务"><el-input v-model="form.deptName" /></el-form-item></el-col>
          <!-- <el-col :span="8"><el-form-item label="职务"><el-input v-model="form.position" /></el-form-item></el-col> -->
          <el-col :span="8"><el-form-item label="分类"><el-input v-model="form.category" /></el-form-item></el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8"><el-form-item label="工作目标"><el-input v-model="form.workTarget" /></el-form-item></el-col>
          <el-col :span="8"><el-form-item label="工作措施"><el-input v-model="form.workMeasure"  /></el-form-item></el-col>
          <el-col :span="8"><el-form-item label="计划完成时间"><el-input v-model="form.completeTime"  /></el-form-item></el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8"><el-form-item label="第一周"><el-input v-model="form.firstWeek" /></el-form-item></el-col>
          <el-col :span="8"><el-form-item label="第二周"><el-input v-model="form.secondWeek" /></el-form-item></el-col>

        </el-row>
        <el-row :gutter="20">
          <el-col :span="8"><el-form-item label="第三周"><el-input v-model="form.thirdWeek" /></el-form-item></el-col>
          <el-col :span="8"><el-form-item label="第四周"><el-input v-model="form.fourthWeek" /></el-form-item></el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8"><el-form-item label="责任领导"><el-input v-model="form.responsibleLeader"  /></el-form-item></el-col>
          <el-col :span="8"><el-form-item label="责任科室"><el-input v-model="form.responsibleDepartment"  /></el-form-item></el-col>

        </el-row>
        <el-row :gutter="20">
          <el-col :span="8"><el-form-item label="科室负责人"><el-input v-model="form.departmentHeader" /></el-form-item></el-col>
          <el-col :span="8"><el-form-item label="具体负责人"><el-input v-model="form.specificResponsiblePerson"  /></el-form-item></el-col>

        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listMonthly,
  addMonthly,
  updateMonthly,
  delMonthly,
  exportMonthly,
  downloadTemplateMonthly
} from '@/api/performance/monthly'
import { getToken } from '@/utils/auth'

export default {
  data() {
    return {
      tableData: [],
      total: 0,
      pageNum: 1,
      pageSize: 10,
      dialogVisible: false,
      dialogTitle: '新增',
      form: {},
      selectedIds: [],
      uploadUrl: process.env.VUE_APP_BASE_API + '/performance/monthly/importData',
      uploadHeaders: {
        Authorization: 'Bearer ' + getToken()
      }
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    loadData() {
      listMonthly({ pageNum: this.pageNum, pageSize: this.pageSize }).then(res => {
        if (res.code === 200) {
          this.tableData = res.rows
          this.total = res.total
        }
      })
    },
    handleAdd() {
      this.dialogTitle = '新增'
      this.form = {}
      this.dialogVisible = true
    },
    handleEdit(row) {
      this.dialogTitle = '编辑'
      this.form = Object.assign({}, row)
      this.dialogVisible = true
    },
    handleDelete(row) {
      this.$confirm('确定删除该条记录吗？', '提示', { type: 'warning' }).then(() => {
        delMonthly(row.id).then(() => {
          this.$message.success('删除成功')
          this.loadData()
        })
      })
    },
    submitForm() {
      if (this.form.id) {
        updateMonthly(this.form).then(() => {
          this.$message.success('修改成功')
          this.dialogVisible = false
          this.loadData()
        })
      } else {
        addMonthly(this.form).then(() => {
          this.$message.success('新增成功')
          this.dialogVisible = false
          this.loadData()
        })
      }
    },
    handleSelectionChange(val) {
      this.selectedIds = val.map(item => item.id)
    },
    handleImportSuccess(res) {
      if (res.code === 200) {
        this.$message.success(res.msg || '导入成功')
        this.loadData()
      } else {
        this.$message.error(res.msg || '导入失败')
      }
    },
    handleImportError() {
      this.$message.error('导入失败，请检查文件或网络')
    },
    handleExport() {
      if (this.selectedIds.length === 0) {
        this.$message.warning('请先选择要导出的数据')
        return
      }
      exportMonthly(this.selectedIds).then(data => {
        const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = '月总结导出.docx'
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        window.URL.revokeObjectURL(url)
      })
    },
    handleDownloadTemplate() {
      downloadTemplateMonthly().then(data => {
        const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = '月总结导入模板.docx'
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        window.URL.revokeObjectURL(url)
      })
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
