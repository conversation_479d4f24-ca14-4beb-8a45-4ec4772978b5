{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/iFrame/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/iFrame/index.vue", "mtime": 1753510684528}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBwcm9wczogewogICAgc3JjOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgcmVxdWlyZWQ6IHRydWUKICAgIH0sCiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgaGVpZ2h0OiBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuY2xpZW50SGVpZ2h0IC0gOTQuNSArICJweDsiLAogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICB1cmw6IHRoaXMuc3JjCiAgICB9CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiAoKSB7CiAgICBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UKICAgIH0sIDMwMCkKICAgIGNvbnN0IHRoYXQgPSB0aGlzCiAgICB3aW5kb3cub25yZXNpemUgPSBmdW5jdGlvbiB0ZW1wKCkgewogICAgICB0aGF0LmhlaWdodCA9IGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5jbGllbnRIZWlnaHQgLSA5NC41ICsgInB4OyIKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/iFrame", "sourcesContent": ["<template>\n  <div v-loading=\"loading\" :style=\"'height:' + height\">\n    <iframe\n      :src=\"src\"\n      frameborder=\"no\"\n      style=\"width: 100%; height: 100%\"\n      scrolling=\"auto\"\n    />\n  </div>\n</template>\n<script>\nexport default {\n  props: {\n    src: {\n      type: String,\n      required: true\n    },\n  },\n  data() {\n    return {\n      height: document.documentElement.clientHeight - 94.5 + \"px;\",\n      loading: true,\n      url: this.src\n    }\n  },\n  mounted: function () {\n    setTimeout(() => {\n      this.loading = false\n    }, 300)\n    const that = this\n    window.onresize = function temp() {\n      that.height = document.documentElement.clientHeight - 94.5 + \"px;\"\n    }\n  }\n}\n</script>\n"]}]}