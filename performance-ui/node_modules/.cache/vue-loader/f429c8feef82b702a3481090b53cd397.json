{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/HeaderSearch/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/HeaderSearch/index.vue", "mtime": 1753510684527}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8CA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/HeaderSearch", "sourcesContent": ["<template>\n  <div class=\"header-search\">\n    <svg-icon class-name=\"search-icon\" icon-class=\"search\" @click.stop=\"click\" />\n    <el-dialog\n      :visible.sync=\"show\"\n      width=\"600px\"\n      @close=\"close\"\n      :show-close=\"false\"\n      append-to-body\n    >\n      <el-input\n        v-model=\"search\"\n        ref=\"headerSearchSelectRef\"\n        size=\"large\"\n        @input=\"querySearch\"\n        prefix-icon=\"el-icon-search\"\n        placeholder=\"菜单搜索，支持标题、URL模糊查询\"\n        clearable\n        @keyup.enter.native=\"selectActiveResult\"\n        @keydown.up.native=\"navigateResult('up')\"\n        @keydown.down.native=\"navigateResult('down')\"\n      >\n      </el-input>\n      <el-scrollbar wrap-class=\"right-scrollbar-wrapper\">\n        <div class=\"result-wrap\">\n          <div class=\"search-item\" v-for=\"(item, index) in options\" :key=\"item.path\" :style=\"activeStyle(index)\" @mouseenter=\"activeIndex = index\" @mouseleave=\"activeIndex = -1\">\n            <div class=\"left\">\n              <svg-icon class=\"menu-icon\" :icon-class=\"item.icon\" />\n            </div>\n            <div class=\"search-info\" @click=\"change(item)\">\n              <div class=\"menu-title\">\n                {{ item.title.join(\" / \") }}\n              </div>\n              <div class=\"menu-path\">\n                {{ item.path }}\n              </div>\n            </div>\n            <svg-icon icon-class=\"enter\" v-show=\"index === activeIndex\"/>\n          </div>\n       </div>\n      </el-scrollbar>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport Fuse from 'fuse.js/dist/fuse.min.js'\nimport path from 'path'\nimport { isHttp } from '@/utils/validate'\n\nexport default {\n  name: 'HeaderSearch',\n  data() {\n    return {\n      search: '',\n      options: [],\n      searchPool: [],\n      activeIndex: -1,\n      show: false,\n      fuse: undefined\n    }\n  },\n  computed: {\n    theme() {\n      return this.$store.state.settings.theme\n    },\n    routes() {\n      return this.$store.getters.defaultRoutes\n    }\n  },\n  watch: {\n    routes() {\n      this.searchPool = this.generateRoutes(this.routes)\n    },\n    searchPool(list) {\n      this.initFuse(list)\n    }\n  },\n  mounted() {\n    this.searchPool = this.generateRoutes(this.routes)\n  },\n  methods: {\n    click() {\n      this.show = !this.show\n      if (this.show) {\n        this.$refs.headerSearchSelect && this.$refs.headerSearchSelect.focus()\n        this.options = this.searchPool\n      }\n    },\n    close() {\n      this.$refs.headerSearchSelect && this.$refs.headerSearchSelect.blur()\n      this.search = ''\n      this.options = []\n      this.show = false\n      this.activeIndex = -1\n    },\n    change(val) {\n      const path = val.path\n      const query = val.query\n      if(isHttp(val.path)) {\n        // http(s):// 路径新窗口打开\n        const pindex = path.indexOf(\"http\")\n        window.open(path.substr(pindex, path.length), \"_blank\")\n      } else {\n        if (query) {\n          this.$router.push({ path: path, query: JSON.parse(query) })\n        } else {\n          this.$router.push(path)\n        }\n      }\n      this.search = ''\n      this.options = []\n      this.$nextTick(() => {\n        this.show = false\n      })\n    },\n    initFuse(list) {\n      this.fuse = new Fuse(list, {\n        shouldSort: true,\n        threshold: 0.4,\n        location: 0,\n        distance: 100,\n        minMatchCharLength: 1,\n        keys: [{\n          name: 'title',\n          weight: 0.7\n        }, {\n          name: 'path',\n          weight: 0.3\n        }]\n      })\n    },\n    // Filter out the routes that can be displayed in the sidebar\n    // And generate the internationalized title\n    generateRoutes(routes, basePath = '/', prefixTitle = []) {\n      let res = []\n\n      for (const router of routes) {\n        // skip hidden router\n        if (router.hidden) { continue }\n\n        const data = {\n          path: !isHttp(router.path) ? path.resolve(basePath, router.path) : router.path,\n          title: [...prefixTitle],\n          icon: ''\n        }\n\n        if (router.meta && router.meta.title) {\n          data.title = [...data.title, router.meta.title]\n          data.icon = router.meta.icon\n\n          if (router.redirect !== 'noRedirect') {\n            // only push the routes with title\n            // special case: need to exclude parent router without redirect\n            res.push(data)\n          }\n        }\n\n        if (router.query) {\n          data.query = router.query\n        }\n\n        // recursive child routes\n        if (router.children) {\n          const tempRoutes = this.generateRoutes(router.children, data.path, data.title)\n          if (tempRoutes.length >= 1) {\n            res = [...res, ...tempRoutes]\n          }\n        }\n      }\n      return res\n    },\n    querySearch(query) {\n      this.activeIndex = -1\n      if (query !== '') {\n        this.options = this.fuse.search(query).map((item) => item.item) ?? this.searchPool\n      } else {\n        this.options = this.searchPool\n      }\n    },\n    activeStyle(index) {\n      if (index !== this.activeIndex) return {}\n      return {\n        \"background-color\": this.theme,\n        \"color\": \"#fff\"\n      }\n    },\n    navigateResult(direction) {\n      if (direction === \"up\") {\n        this.activeIndex = this.activeIndex <= 0 ? this.options.length - 1 : this.activeIndex - 1\n      } else if (direction === \"down\") {\n        this.activeIndex = this.activeIndex >= this.options.length - 1 ? 0 : this.activeIndex + 1\n      }\n    },\n    selectActiveResult() {\n      if (this.options.length > 0 && this.activeIndex >= 0) {\n        this.change(this.options[this.activeIndex])\n      }\n    }\n  }\n}\n</script>\n\n<style lang='scss' scoped>\n::v-deep {\n  .el-dialog__header {\n    padding: 0 !important;\n  }\n}\n\n.header-search {\n  .search-icon {\n    cursor: pointer;\n    font-size: 18px;\n    vertical-align: middle;\n  }\n}\n\n.result-wrap {\n  height: 280px;\n  margin: 6px 0;\n\n  .search-item {\n    display: flex;\n    height: 48px;\n    align-items: center;\n    padding-right: 10px;\n\n    .left {\n      width: 60px;\n      text-align: center;\n\n      .menu-icon {\n        width: 18px;\n        height: 18px;\n      }\n    }\n\n    .search-info {\n      padding-left: 5px;\n      margin-top: 10px;\n      width: 100%;\n      display: flex;\n      flex-direction: column;\n      justify-content: flex-start;\n      flex: 1;\n\n      .menu-title,\n      .menu-path {\n        height: 20px;\n      }\n      .menu-path {\n        color: #ccc;\n        font-size: 10px;\n      }\n    }\n  }\n\n  .search-item:hover {\n    cursor: pointer;\n  }\n}\n</style>\n\n"]}]}