{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/evaluation/privateYear/index.vue?vue&type=template&id=671c436a&scoped=true", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/evaluation/privateYear/index.vue", "mtime": 1753510684533}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 1753510684373}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}