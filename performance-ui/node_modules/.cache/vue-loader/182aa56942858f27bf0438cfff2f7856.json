{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/monitor/operlog/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/monitor/operlog/index.vue", "mtime": 1753510684534}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3QsIGRlbE9wZXJsb2csIGNsZWFuT3BlcmxvZyB9IGZyb20gIkAvYXBpL21vbml0b3Ivb3BlcmxvZyIKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiT3BlcmxvZyIsCiAgZGljdHM6IFsnc3lzX29wZXJfdHlwZScsICdzeXNfY29tbW9uX3N0YXR1cyddLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIC8vIOaAu+adoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgLy8g6KGo5qC85pWw5o2uCiAgICAgIGxpc3Q6IFtdLAogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYIKICAgICAgb3BlbjogZmFsc2UsCiAgICAgIC8vIOaXpeacn+iMg+WbtAogICAgICBkYXRlUmFuZ2U6IFtdLAogICAgICAvLyDpu5jorqTmjpLluo8KICAgICAgZGVmYXVsdFNvcnQ6IHsgcHJvcDogIm9wZXJUaW1lIiwgb3JkZXI6ICJkZXNjZW5kaW5nIiB9LAogICAgICAvLyDooajljZXlj4LmlbAKICAgICAgZm9ybToge30sCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIG9wZXJJcDogdW5kZWZpbmVkLAogICAgICAgIHRpdGxlOiB1bmRlZmluZWQsCiAgICAgICAgb3Blck5hbWU6IHVuZGVmaW5lZCwKICAgICAgICBidXNpbmVzc1R5cGU6IHVuZGVmaW5lZCwKICAgICAgICBzdGF0dXM6IHVuZGVmaW5lZAogICAgICB9CiAgICB9CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCkKICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDmn6Xor6LnmbvlvZXml6Xlv5cgKi8KICAgIGdldExpc3QoKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUKICAgICAgbGlzdCh0aGlzLmFkZERhdGVSYW5nZSh0aGlzLnF1ZXJ5UGFyYW1zLCB0aGlzLmRhdGVSYW5nZSkpLnRoZW4oIHJlc3BvbnNlID0+IHsKICAgICAgICAgIHRoaXMubGlzdCA9IHJlc3BvbnNlLnJvd3MKICAgICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbAogICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UKICAgICAgICB9CiAgICAgICkKICAgIH0sCiAgICAvLyDmk43kvZzml6Xlv5fnsbvlnovlrZflhbjnv7vor5EKICAgIHR5cGVGb3JtYXQocm93LCBjb2x1bW4pIHsKICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0RGljdExhYmVsKHRoaXMuZGljdC50eXBlLnN5c19vcGVyX3R5cGUsIHJvdy5idXNpbmVzc1R5cGUpCiAgICB9LAogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDEKICAgICAgdGhpcy5nZXRMaXN0KCkKICAgIH0sCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovCiAgICByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLmRhdGVSYW5nZSA9IFtdCiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxCiAgICAgIHRoaXMuJHJlZnMudGFibGVzLnNvcnQodGhpcy5kZWZhdWx0U29ydC5wcm9wLCB0aGlzLmRlZmF1bHRTb3J0Lm9yZGVyKQogICAgfSwKICAgIC8qKiDlpJrpgInmoYbpgInkuK3mlbDmja4gKi8KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5vcGVySWQpCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aAogICAgfSwKICAgIC8qKiDmjpLluo/op6blj5Hkuovku7YgKi8KICAgIGhhbmRsZVNvcnRDaGFuZ2UoY29sdW1uLCBwcm9wLCBvcmRlcikgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLm9yZGVyQnlDb2x1bW4gPSBjb2x1bW4ucHJvcAogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmlzQXNjID0gY29sdW1uLm9yZGVyCiAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICB9LAogICAgLyoqIOivpue7huaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlVmlldyhyb3cpIHsKICAgICAgdGhpcy5vcGVuID0gdHJ1ZQogICAgICB0aGlzLmZvcm0gPSByb3cKICAgIH0sCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIGNvbnN0IG9wZXJJZHMgPSByb3cub3BlcklkIHx8IHRoaXMuaWRzCiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOaXpeW/l+e8luWPt+S4uiInICsgb3BlcklkcyArICci55qE5pWw5o2u6aG577yfJykudGhlbihmdW5jdGlvbigpIHsKICAgICAgICByZXR1cm4gZGVsT3BlcmxvZyhvcGVySWRzKQogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLmdldExpc3QoKQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KQogICAgfSwKICAgIC8qKiDmuIXnqbrmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZUNsZWFuKCkgewogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTmuIXnqbrmiYDmnInmk43kvZzml6Xlv5fmlbDmja7pobnvvJ8nKS50aGVuKGZ1bmN0aW9uKCkgewogICAgICAgIHJldHVybiBjbGVhbk9wZXJsb2coKQogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLmdldExpc3QoKQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIua4heepuuaIkOWKnyIpCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KQogICAgfSwKICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZUV4cG9ydCgpIHsKICAgICAgdGhpcy5kb3dubG9hZCgnbW9uaXRvci9vcGVybG9nL2V4cG9ydCcsIHsKICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zCiAgICAgIH0sIGBvcGVybG9nXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgKQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiNA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/monitor/operlog", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"操作地址\" prop=\"operIp\">\n        <el-input\n          v-model=\"queryParams.operIp\"\n          placeholder=\"请输入操作地址\"\n          clearable\n          style=\"width: 240px;\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"系统模块\" prop=\"title\">\n        <el-input\n          v-model=\"queryParams.title\"\n          placeholder=\"请输入系统模块\"\n          clearable\n          style=\"width: 240px;\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"操作人员\" prop=\"operName\">\n        <el-input\n          v-model=\"queryParams.operName\"\n          placeholder=\"请输入操作人员\"\n          clearable\n          style=\"width: 240px;\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"类型\" prop=\"businessType\">\n        <el-select\n          v-model=\"queryParams.businessType\"\n          placeholder=\"操作类型\"\n          clearable\n          style=\"width: 240px\"\n        >\n          <el-option\n            v-for=\"dict in dict.type.sys_oper_type\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"状态\" prop=\"status\">\n        <el-select\n          v-model=\"queryParams.status\"\n          placeholder=\"操作状态\"\n          clearable\n          style=\"width: 240px\"\n        >\n          <el-option\n            v-for=\"dict in dict.type.sys_common_status\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"操作时间\">\n        <el-date-picker\n          v-model=\"dateRange\"\n          style=\"width: 240px\"\n          value-format=\"yyyy-MM-dd HH:mm:ss\"\n          type=\"daterange\"\n          range-separator=\"-\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n          :default-time=\"['00:00:00', '23:59:59']\"\n        ></el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['monitor:operlog:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          @click=\"handleClean\"\n          v-hasPermi=\"['monitor:operlog:remove']\"\n        >清空</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['monitor:operlog:export']\"\n        >导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table ref=\"tables\" v-loading=\"loading\" :data=\"list\" @selection-change=\"handleSelectionChange\" :default-sort=\"defaultSort\" @sort-change=\"handleSortChange\">\n      <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n      <el-table-column label=\"日志编号\" align=\"center\" prop=\"operId\" />\n      <el-table-column label=\"系统模块\" align=\"center\" prop=\"title\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"操作类型\" align=\"center\" prop=\"businessType\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\"dict.type.sys_oper_type\" :value=\"scope.row.businessType\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作人员\" align=\"center\" prop=\"operName\" width=\"110\" :show-overflow-tooltip=\"true\" sortable=\"custom\" :sort-orders=\"['descending', 'ascending']\" />\n      <el-table-column label=\"操作地址\" align=\"center\" prop=\"operIp\" width=\"130\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"操作地点\" align=\"center\" prop=\"operLocation\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"操作状态\" align=\"center\" prop=\"status\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\"dict.type.sys_common_status\" :value=\"scope.row.status\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作日期\" align=\"center\" prop=\"operTime\" width=\"160\" sortable=\"custom\" :sort-orders=\"['descending', 'ascending']\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.operTime) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"消耗时间\" align=\"center\" prop=\"costTime\" width=\"110\" :show-overflow-tooltip=\"true\" sortable=\"custom\" :sort-orders=\"['descending', 'ascending']\">\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.costTime }}毫秒</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-view\"\n            @click=\"handleView(scope.row,scope.index)\"\n            v-hasPermi=\"['monitor:operlog:query']\"\n          >详细</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 操作日志详细 -->\n    <el-dialog title=\"操作日志详细\" :visible.sync=\"open\" width=\"800px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" label-width=\"100px\" size=\"mini\">\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"操作模块：\">{{ form.title }} / {{ typeFormat(form) }}</el-form-item>\n            <el-form-item\n              label=\"登录信息：\"\n            >{{ form.operName }} / {{ form.operIp }} / {{ form.operLocation }}</el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"请求地址：\">{{ form.operUrl }}</el-form-item>\n            <el-form-item label=\"请求方式：\">{{ form.requestMethod }}</el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"操作方法：\">{{ form.method }}</el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"请求参数：\">{{ form.operParam }}</el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"返回参数：\">{{ form.jsonResult }}</el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"操作状态：\">\n              <div v-if=\"form.status === 0\">正常</div>\n              <div v-else-if=\"form.status === 1\">失败</div>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"消耗时间：\">{{ form.costTime }}毫秒</el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"操作时间：\">{{ parseTime(form.operTime) }}</el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"异常信息：\" v-if=\"form.status === 1\">{{ form.errorMsg }}</el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"open = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { list, delOperlog, cleanOperlog } from \"@/api/monitor/operlog\"\n\nexport default {\n  name: \"Operlog\",\n  dicts: ['sys_oper_type', 'sys_common_status'],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 表格数据\n      list: [],\n      // 是否显示弹出层\n      open: false,\n      // 日期范围\n      dateRange: [],\n      // 默认排序\n      defaultSort: { prop: \"operTime\", order: \"descending\" },\n      // 表单参数\n      form: {},\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        operIp: undefined,\n        title: undefined,\n        operName: undefined,\n        businessType: undefined,\n        status: undefined\n      }\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    /** 查询登录日志 */\n    getList() {\n      this.loading = true\n      list(this.addDateRange(this.queryParams, this.dateRange)).then( response => {\n          this.list = response.rows\n          this.total = response.total\n          this.loading = false\n        }\n      )\n    },\n    // 操作日志类型字典翻译\n    typeFormat(row, column) {\n      return this.selectDictLabel(this.dict.type.sys_oper_type, row.businessType)\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1\n      this.getList()\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = []\n      this.resetForm(\"queryForm\")\n      this.queryParams.pageNum = 1\n      this.$refs.tables.sort(this.defaultSort.prop, this.defaultSort.order)\n    },\n    /** 多选框选中数据 */\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.operId)\n      this.multiple = !selection.length\n    },\n    /** 排序触发事件 */\n    handleSortChange(column, prop, order) {\n      this.queryParams.orderByColumn = column.prop\n      this.queryParams.isAsc = column.order\n      this.getList()\n    },\n    /** 详细按钮操作 */\n    handleView(row) {\n      this.open = true\n      this.form = row\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const operIds = row.operId || this.ids\n      this.$modal.confirm('是否确认删除日志编号为\"' + operIds + '\"的数据项？').then(function() {\n        return delOperlog(operIds)\n      }).then(() => {\n        this.getList()\n        this.$modal.msgSuccess(\"删除成功\")\n      }).catch(() => {})\n    },\n    /** 清空按钮操作 */\n    handleClean() {\n      this.$modal.confirm('是否确认清空所有操作日志数据项？').then(function() {\n        return cleanOperlog()\n      }).then(() => {\n        this.getList()\n        this.$modal.msgSuccess(\"清空成功\")\n      }).catch(() => {})\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('monitor/operlog/export', {\n        ...this.queryParams\n      }, `operlog_${new Date().getTime()}.xlsx`)\n    }\n  }\n}\n</script>\n\n"]}]}