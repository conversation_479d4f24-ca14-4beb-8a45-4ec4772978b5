{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/HeaderSearch/index.vue?vue&type=template&id=032bd1f0&scoped=true", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/HeaderSearch/index.vue", "mtime": 1753510684527}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 1753510684373}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}