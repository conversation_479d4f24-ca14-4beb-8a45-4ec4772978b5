{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/performance/year/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/performance/year/index.vue", "mtime": 1753510684535}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8EA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/performance/year", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"年度\" prop=\"year\">\n        <el-input v-model=\"queryParams.year\" placeholder=\"请输入年度\" clearable @keyup.enter.native=\"handleQuery\"/>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\" v-hasPermi=\"['performance:yearly:add']\">新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"success\" plain icon=\"el-icon-edit\" size=\"mini\" :disabled=\"single\" @click=\"handleUpdate\" v-hasPermi=\"['performance:yearly:edit']\">修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"danger\" plain icon=\"el-icon-delete\" size=\"mini\" :disabled=\"multiple\" @click=\"handleDelete\" v-hasPermi=\"['performance:yearly:remove']\">删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"info\" plain icon=\"el-icon-upload2\" size=\"mini\" @click=\"handleImport\" v-hasPermi=\"['performance:yearly:import']\">Word导入</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"handleExport\" v-hasPermi=\"['performance:yearly:export']\">Word导出</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"info\" plain icon=\"el-icon-document\" size=\"mini\" @click=\"downloadTemplate\">下载模板</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"yearlyList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"ID\" align=\"center\" prop=\"id\" />\n      <el-table-column label=\"年度\" align=\"center\" prop=\"year\" />\n      <el-table-column label=\"科室\" align=\"center\" prop=\"deptName\" />\n      <el-table-column label=\"职别\" align=\"center\" prop=\"position\" />\n      <el-table-column label=\"类别\" align=\"center\" prop=\"category\" />\n      <el-table-column label=\"姓名\" align=\"center\" prop=\"userName\" />\n      <el-table-column label=\"年度得分\" align=\"center\" prop=\"yearScore\" />\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleUpdate(scope.row)\" v-hasPermi=\"['performance:yearly:edit']\">修改</el-button>\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleDelete(scope.row)\" v-hasPermi=\"['performance:yearly:remove']\">删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination v-show=\"total>0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\" @pagination=\"getList\"/>\n\n    <!-- Word导入对话框 -->\n    <el-dialog :title=\"upload.title\" :visible.sync=\"upload.open\" width=\"400px\" append-to-body>\n        <el-upload\n            ref=\"upload\"\n            :limit=\"1\"\n            accept=\".docx\"\n            :headers=\"upload.headers\"\n            :action=\"upload.url\"\n            :disabled=\"upload.isUploading\"\n            :on-progress=\"handleFileUploadProgress\"\n            :on-success=\"handleFileSuccess\"\n            :auto-upload=\"true\"\n            drag\n        >\n            <i class=\"el-icon-upload\"></i>\n            <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\n            <div class=\"el-upload__tip text-center\" slot=\"tip\">\n                <span>仅允许导入.docx格式文件。</span>\n            </div>\n        </el-upload>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listYearly, getYearly, delYearly, addYearly, updateYearly, exportYearly, downloadTemplateYearly } from \"@/api/performance/yearly\";\nimport { getToken } from \"@/utils/auth\";\n\nexport default {\n  name: \"Yearly\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 年度绩效表格数据\n      yearlyList: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        year: null,\n      },\n      // Word导入参数\n      upload: {\n        open: false,\n        title: \"\",\n        isUploading: false,\n        url: process.env.VUE_APP_BASE_API + \"/performance/yearly/importData\",\n        headers: { Authorization: \"Bearer \" + getToken() }\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    getList() {\n      this.loading = true;\n      listYearly(this.queryParams).then(response => {\n        this.yearlyList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    handleAdd() {\n      // 实际开发中应打开一个表单供用户填写\n      console.log(\"Add new yearly record\");\n    },\n    handleUpdate(row) {\n      const id = row.id || this.ids[0];\n      // 实际开发中应打开一个表单并填充数据供用户修改\n      console.log(`Update yearly record with id: ${id}`);\n    },\n    handleDelete(row) {\n      const ids = row.id ? [row.id] : this.ids;\n      this.$modal.confirm('是否确认删除编号为\"' + ids.join(',') + '\"的数据项？').then(() => {\n        return delYearly(ids);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    handleImport() {\n      this.upload.title = \"年度绩效Word导入\";\n      this.upload.open = true;\n    },\n    handleExport() {\n      const ids = this.ids;\n       this.$modal.confirm('是否确认导出选中的数据项？').then(() => {\n        return exportYearly(ids);\n      }).then(response => {\n        this.$download.name(response.headers['content-disposition'], 'yearly_export.docx');\n      }).catch(() => {});\n    },\n    downloadTemplate() {\n        downloadTemplateYearly().then(response => {\n            this.$download.name(response.headers['content-disposition'], 'yearly_template.docx');\n        });\n    },\n    handleFileUploadProgress(event, file, fileList) {\n        this.upload.isUploading = true;\n    },\n    handleFileSuccess(response, file, fileList) {\n        this.upload.open = false;\n        this.upload.isUploading = false;\n        this.$refs.upload.clearFiles();\n        this.$alert(response.msg, \"导入结果\", { dangerouslyUseHTMLString: true });\n        this.getList();\n    }\n  }\n};\n</script> "]}]}