{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/execute/week/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/execute/week/index.vue", "mtime": 1754489749514}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/execute/week", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"20\" style=\"margin-bottom: 20px;\">\n      <el-col :span=\"12\">\n        <el-button type=\"primary\" @click=\"handleAdd\">新增</el-button>\n        <el-upload\n          :action=\"importUrl\"\n          :show-file-list=\"false\"\n          :on-success=\"handleImportSuccess\"\n          :on-error=\"handleImportError\"\n          :headers=\"uploadHeaders\"\n          accept=\".doc,.docx\"\n          style=\"display:inline-block;margin-left:10px;\"\n        >\n          <el-button>批量导入Word</el-button>\n        </el-upload>\n        <el-button @click=\"handleExport\" style=\"margin-left:10px;\">导出Word</el-button>\n        <el-button @click=\"handleDownloadTemplate\" style=\"margin-left:10px;\">下载模板</el-button>\n      </el-col>\n    </el-row>\n    <el-table :data=\"tableData\" border style=\"width: 100%\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" />\n      <el-table-column prop=\"id\" label=\"序号\" width=\"60\" />\n      <el-table-column prop=\"deptName\" label=\"科室工作任务\" />\n      <el-table-column prop=\"category\" label=\"分类\" />\n      <el-table-column prop=\"workTarget\" label=\"工作目标\" />\n      <el-table-column prop=\"workMeasure\" label=\"工作措施\" />\n      <el-table-column prop=\"completeTime\" label=\"计划完成时间\" />\n      <el-table-column prop=\"firstWeek\" label=\"第一周\" />\n      <el-table-column prop=\"secondWeek\" label=\"第二周\" />\n      <el-table-column prop=\"thirdWeek\" label=\"第三周\" />\n      <el-table-column prop=\"fourthWeek\" label=\"第四周\" />\n      <el-table-column prop=\"responsibleLeader\" label=\"责任领导\" />\n      <el-table-column prop=\"responsibleDepartment\" label=\"责任科室\" />\n      <el-table-column prop=\"departmentHeader\" label=\"科室负责人\" />\n      <el-table-column prop=\"specificResponsiblePerson\" label=\"具体负责人\" />\n\n      <el-table-column label=\"操作\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <el-button size=\"mini\" @click=\"handleEdit(scope.row)\">编辑</el-button>\n          <el-button size=\"mini\" type=\"danger\" @click=\"handleDelete(scope.row)\">删除</el-button>\n        </template>\n      </el-table-column>\n\n    </el-table>\n    <el-pagination\n      style=\"margin-top: 20px;\"\n      background\n      layout=\"prev, pager, next, jumper\"\n      :total=\"total\"\n      :page-size=\"pageSize\"\n      :current-page.sync=\"pageNum\"\n      @current-change=\"loadData\"\n    />\n\n    <!-- 新增/编辑弹窗 -->\n    <el-dialog :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\n      <el-form :model=\"form\" label-width=\"100px\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\"><el-form-item label=\"科室工作任务\"><el-input v-model=\"form.deptName\" /></el-form-item></el-col>\n          <!-- <el-col :span=\"8\"><el-form-item label=\"职务\"><el-input v-model=\"form.position\" /></el-form-item></el-col> -->\n          <el-col :span=\"8\"><el-form-item label=\"分类\"><el-input v-model=\"form.category\" /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\"><el-form-item label=\"工作目标\"><el-input v-model=\"form.workTarget\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"工作措施\"><el-input v-model=\"form.workMeasure\"  /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"计划完成时间\"><el-input v-model=\"form.completeTime\"  /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\"><el-form-item label=\"第一周\"><el-input v-model=\"form.firstWeek\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"第二周\"><el-input v-model=\"form.secondWeek\" /></el-form-item></el-col>\n\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\"><el-form-item label=\"第三周\"><el-input v-model=\"form.thirdWeek\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"第四周\"><el-input v-model=\"form.fourthWeek\" /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\"><el-form-item label=\"责任领导\"><el-input v-model=\"form.responsibleLeader\"  /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"责任科室\"><el-input v-model=\"form.responsibleDepartment\"  /></el-form-item></el-col>\n\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\"><el-form-item label=\"科室负责人\"><el-input v-model=\"form.departmentHeader\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"具体负责人\"><el-input v-model=\"form.specificResponsiblePerson\"  /></el-form-item></el-col>\n\n        </el-row>\n\n\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"submitForm\">确定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  listWeekly,\n  addWeekly,\n  updateWeekly,\n  delWeekly,\n  exportWeekly,\n  downloadTemplateWeekly\n} from '@/api/performance/weekly'\n\nexport default {\n  data() {\n    return {\n      tableData: [],\n      total: 0,\n      pageNum: 1,\n      pageSize: 10,\n      dialogVisible: false,\n      dialogTitle: '新增',\n      form: {},\n      importUrl: process.env.VUE_APP_BASE_API + '/performance/weekly/import',\n      uploadHeaders: {},\n      selectedIds: []\n    }\n  },\n  created() {\n    this.loadData()\n    this.uploadHeaders = {\n      Authorization: 'Bearer ' + this.$store.getters.token\n    }\n  },\n  methods: {\n    loadData() {\n      listWeekly({ pageNum: this.pageNum, pageSize: this.pageSize }).then(res => {\n        if (res.code === 200) {\n          this.tableData = res.rows\n          this.total = res.total\n        }\n      })\n    },\n    handleAdd() {\n      this.dialogTitle = '新增'\n      this.form = {}\n      this.dialogVisible = true\n    },\n    handleEdit(row) {\n      this.dialogTitle = '编辑'\n      this.form = Object.assign({}, row)\n      this.dialogVisible = true\n    },\n    handleDelete(row) {\n      this.$confirm('确定删除该条记录吗？', '提示', { type: 'warning' }).then(() => {\n        delWeekly(row.id).then(() => {\n          this.$message.success('删除成功')\n          this.loadData()\n        })\n      })\n    },\n    submitForm() {\n      if (this.form.id) {\n        updateWeekly(this.form).then(() => {\n          this.$message.success('修改成功')\n          this.dialogVisible = false\n          this.loadData()\n        })\n      } else {\n        addWeekly(this.form).then(() => {\n          this.$message.success('新增成功')\n          this.dialogVisible = false\n          this.loadData()\n        })\n      }\n    },\n    handleExport() {\n      if (this.selectedIds.length === 0) {\n        this.$message.warning('请先选择要导出的数据')\n        return\n      }\n      exportWeekly(this.selectedIds).then(res => {\n        const blob = new Blob([res], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })\n        const url = window.URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.href = url\n        a.download = '周记实导出.docx'\n        a.click()\n        window.URL.revokeObjectURL(url)\n      })\n    },\n    handleSelectionChange(val) {\n      this.selectedIds = val.map(item => item.id)\n    },\n    handleImportSuccess(response, file, fileList) {\n      if (response.code === 200) {\n        this.$message.success(response.msg || '导入成功')\n        this.loadData()\n      } else {\n        this.$message.error(response.msg || '导入失败')\n      }\n    },\n    handleImportError(err) {\n      this.$message.error('导入失败，请检查文件或联系管理员')\n      console.error(err)\n    },\n    handleDownloadTemplate() {\n      downloadTemplateWeekly().then(res => {\n        const blob = new Blob([res], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })\n        const url = window.URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.href = url\n        a.download = '周记实导入模板.docx'\n        a.click()\n        window.URL.revokeObjectURL(url)\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.app-container {\n  padding: 20px;\n}\n</style>\n"]}]}