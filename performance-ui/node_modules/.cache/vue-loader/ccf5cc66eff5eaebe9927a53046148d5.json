{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/register.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/register.vue", "mtime": 1753510684535}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["register.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "register.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"register\">\n    <el-form ref=\"registerForm\" :model=\"registerForm\" :rules=\"registerRules\" class=\"register-form\">\n      <h3 class=\"title\">{{title}}</h3>\n      <el-form-item prop=\"username\">\n        <el-input v-model=\"registerForm.username\" type=\"text\" auto-complete=\"off\" placeholder=\"账号\">\n          <svg-icon slot=\"prefix\" icon-class=\"user\" class=\"el-input__icon input-icon\" />\n        </el-input>\n      </el-form-item>\n      <el-form-item prop=\"password\">\n        <el-input\n          v-model=\"registerForm.password\"\n          type=\"password\"\n          auto-complete=\"off\"\n          placeholder=\"密码\"\n          @keyup.enter.native=\"handleRegister\"\n        >\n          <svg-icon slot=\"prefix\" icon-class=\"password\" class=\"el-input__icon input-icon\" />\n        </el-input>\n      </el-form-item>\n      <el-form-item prop=\"confirmPassword\">\n        <el-input\n          v-model=\"registerForm.confirmPassword\"\n          type=\"password\"\n          auto-complete=\"off\"\n          placeholder=\"确认密码\"\n          @keyup.enter.native=\"handleRegister\"\n        >\n          <svg-icon slot=\"prefix\" icon-class=\"password\" class=\"el-input__icon input-icon\" />\n        </el-input>\n      </el-form-item>\n      <el-form-item prop=\"code\" v-if=\"captchaEnabled\">\n        <el-input\n          v-model=\"registerForm.code\"\n          auto-complete=\"off\"\n          placeholder=\"验证码\"\n          style=\"width: 63%\"\n          @keyup.enter.native=\"handleRegister\"\n        >\n          <svg-icon slot=\"prefix\" icon-class=\"validCode\" class=\"el-input__icon input-icon\" />\n        </el-input>\n        <div class=\"register-code\">\n          <img :src=\"codeUrl\" @click=\"getCode\" class=\"register-code-img\"/>\n        </div>\n      </el-form-item>\n      <el-form-item style=\"width:100%;\">\n        <el-button\n          :loading=\"loading\"\n          size=\"medium\"\n          type=\"primary\"\n          style=\"width:100%;\"\n          @click.native.prevent=\"handleRegister\"\n        >\n          <span v-if=\"!loading\">注 册</span>\n          <span v-else>注 册 中...</span>\n        </el-button>\n        <div style=\"float: right;\">\n          <router-link class=\"link-type\" :to=\"'/login'\">使用已有账户登录</router-link>\n        </div>\n      </el-form-item>\n    </el-form>\n    <!--  底部  -->\n    <div class=\"el-register-footer\">\n      <span>Copyright © 2018-2025 ruoyi.vip All Rights Reserved.</span>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getCodeImg, register } from \"@/api/login\"\n\nexport default {\n  name: \"Register\",\n  data() {\n    const equalToPassword = (rule, value, callback) => {\n      if (this.registerForm.password !== value) {\n        callback(new Error(\"两次输入的密码不一致\"))\n      } else {\n        callback()\n      }\n    }\n    return {\n      title: process.env.VUE_APP_TITLE,\n      codeUrl: \"\",\n      registerForm: {\n        username: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        code: \"\",\n        uuid: \"\"\n      },\n      registerRules: {\n        username: [\n          { required: true, trigger: \"blur\", message: \"请输入您的账号\" },\n          { min: 2, max: 20, message: '用户账号长度必须介于 2 和 20 之间', trigger: 'blur' }\n        ],\n        password: [\n          { required: true, trigger: \"blur\", message: \"请输入您的密码\" },\n          { min: 5, max: 20, message: \"用户密码长度必须介于 5 和 20 之间\", trigger: \"blur\" },\n          { pattern: /^[^<>\"'|\\\\]+$/, message: \"不能包含非法字符：< > \\\" ' \\\\\\ |\", trigger: \"blur\" }\n        ],\n        confirmPassword: [\n          { required: true, trigger: \"blur\", message: \"请再次输入您的密码\" },\n          { required: true, validator: equalToPassword, trigger: \"blur\" }\n        ],\n        code: [{ required: true, trigger: \"change\", message: \"请输入验证码\" }]\n      },\n      loading: false,\n      captchaEnabled: true\n    }\n  },\n  created() {\n    this.getCode()\n  },\n  methods: {\n    getCode() {\n      getCodeImg().then(res => {\n        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled\n        if (this.captchaEnabled) {\n          this.codeUrl = \"data:image/jpeg;base64,\" + res.img\n          this.registerForm.uuid = res.uuid\n        }\n      })\n    },\n    handleRegister() {\n      this.$refs.registerForm.validate(valid => {\n        if (valid) {\n          this.loading = true\n          register(this.registerForm).then(res => {\n            const username = this.registerForm.username\n            this.$alert(\"<font color='red'>恭喜你，您的账号 \" + username + \" 注册成功！</font>\", '系统提示', {\n              dangerouslyUseHTMLString: true,\n              type: 'success'\n            }).then(() => {\n              this.$router.push(\"/login\")\n            }).catch(() => {})\n          }).catch(() => {\n            this.loading = false\n            if (this.captchaEnabled) {\n              this.getCode()\n            }\n          })\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style rel=\"stylesheet/scss\" lang=\"scss\">\n.register {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n  background-image: url(\"../assets/images/login-background.jpg\");\n  background-size: cover;\n}\n.title {\n  margin: 0px auto 30px auto;\n  text-align: center;\n  color: #707070;\n}\n\n.register-form {\n  border-radius: 6px;\n  background: #ffffff;\n  width: 400px;\n  padding: 25px 25px 5px 25px;\n  .el-input {\n    height: 38px;\n    input {\n      height: 38px;\n    }\n  }\n  .input-icon {\n    height: 39px;\n    width: 14px;\n    margin-left: 2px;\n  }\n}\n.register-tip {\n  font-size: 13px;\n  text-align: center;\n  color: #bfbfbf;\n}\n.register-code {\n  width: 33%;\n  height: 38px;\n  float: right;\n  img {\n    cursor: pointer;\n    vertical-align: middle;\n  }\n}\n.el-register-footer {\n  height: 40px;\n  line-height: 40px;\n  position: fixed;\n  bottom: 0;\n  width: 100%;\n  text-align: center;\n  color: #fff;\n  font-family: Arial;\n  font-size: 12px;\n  letter-spacing: 1px;\n}\n.register-code-img {\n  height: 38px;\n}\n</style>\n"]}]}