{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/system/user/profile/userAvatar.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/system/user/profile/userAvatar.vue", "mtime": 1753510684536}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["userAvatar.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwDA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "userAvatar.vue", "sourceRoot": "src/views/system/user/profile", "sourcesContent": ["<template>\n  <div>\n    <div class=\"user-info-head\" @click=\"editCropper()\"><img v-bind:src=\"options.img\" title=\"点击上传头像\" class=\"img-circle img-lg\" /></div>\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body @opened=\"modalOpened\"  @close=\"closeDialog\">\n      <el-row>\n        <el-col :xs=\"24\" :md=\"12\" :style=\"{height: '350px'}\">\n          <vue-cropper\n            ref=\"cropper\"\n            :img=\"options.img\"\n            :info=\"true\"\n            :autoCrop=\"options.autoCrop\"\n            :autoCropWidth=\"options.autoCropWidth\"\n            :autoCropHeight=\"options.autoCropHeight\"\n            :fixedBox=\"options.fixedBox\"\n            :outputType=\"options.outputType\"\n            @realTime=\"realTime\"\n            v-if=\"visible\"\n          />\n        </el-col>\n        <el-col :xs=\"24\" :md=\"12\" :style=\"{height: '350px'}\">\n          <div class=\"avatar-upload-preview\">\n            <img :src=\"previews.url\" :style=\"previews.img\" />\n          </div>\n        </el-col>\n      </el-row>\n      <br />\n      <el-row>\n        <el-col :lg=\"2\" :sm=\"3\" :xs=\"3\">\n          <el-upload action=\"#\" :http-request=\"requestUpload\" :show-file-list=\"false\" :before-upload=\"beforeUpload\">\n            <el-button size=\"small\">\n              选择\n              <i class=\"el-icon-upload el-icon--right\"></i>\n            </el-button>\n          </el-upload>\n        </el-col>\n        <el-col :lg=\"{span: 1, offset: 2}\" :sm=\"2\" :xs=\"2\">\n          <el-button icon=\"el-icon-plus\" size=\"small\" @click=\"changeScale(1)\"></el-button>\n        </el-col>\n        <el-col :lg=\"{span: 1, offset: 1}\" :sm=\"2\" :xs=\"2\">\n          <el-button icon=\"el-icon-minus\" size=\"small\" @click=\"changeScale(-1)\"></el-button>\n        </el-col>\n        <el-col :lg=\"{span: 1, offset: 1}\" :sm=\"2\" :xs=\"2\">\n          <el-button icon=\"el-icon-refresh-left\" size=\"small\" @click=\"rotateLeft()\"></el-button>\n        </el-col>\n        <el-col :lg=\"{span: 1, offset: 1}\" :sm=\"2\" :xs=\"2\">\n          <el-button icon=\"el-icon-refresh-right\" size=\"small\" @click=\"rotateRight()\"></el-button>\n        </el-col>\n        <el-col :lg=\"{span: 2, offset: 6}\" :sm=\"2\" :xs=\"2\">\n          <el-button type=\"primary\" size=\"small\" @click=\"uploadImg()\">提 交</el-button>\n        </el-col>\n      </el-row>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport store from \"@/store\"\nimport { VueCropper } from \"vue-cropper\"\nimport { uploadAvatar } from \"@/api/system/user\"\nimport { debounce } from '@/utils'\n\nexport default {\n  components: { VueCropper },\n  data() {\n    return {\n      // 是否显示弹出层\n      open: false,\n      // 是否显示cropper\n      visible: false,\n      // 弹出层标题\n      title: \"修改头像\",\n      options: {\n        img: store.getters.avatar,  //裁剪图片的地址\n        autoCrop: true,             // 是否默认生成截图框\n        autoCropWidth: 200,         // 默认生成截图框宽度\n        autoCropHeight: 200,        // 默认生成截图框高度\n        fixedBox: true,             // 固定截图框大小 不允许改变\n        outputType:\"png\",           // 默认生成截图为PNG格式\n        filename: 'avatar'          // 文件名称\n      },\n      previews: {},\n      resizeHandler: null\n    }\n  },\n  methods: {\n    // 编辑头像\n    editCropper() {\n      this.open = true\n    },\n    // 打开弹出层结束时的回调\n    modalOpened() {\n      this.visible = true\n      if (!this.resizeHandler) {\n        this.resizeHandler = debounce(() => {\n          this.refresh()\n        }, 100)\n      }\n      window.addEventListener(\"resize\", this.resizeHandler)\n    },\n    // 刷新组件\n    refresh() {\n      this.$refs.cropper.refresh()\n    },\n    // 覆盖默认的上传行为\n    requestUpload() {\n    },\n    // 向左旋转\n    rotateLeft() {\n      this.$refs.cropper.rotateLeft()\n    },\n    // 向右旋转\n    rotateRight() {\n      this.$refs.cropper.rotateRight()\n    },\n    // 图片缩放\n    changeScale(num) {\n      num = num || 1\n      this.$refs.cropper.changeScale(num)\n    },\n    // 上传预处理\n    beforeUpload(file) {\n      if (file.type.indexOf(\"image/\") == -1) {\n        this.$modal.msgError(\"文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。\")\n      } else {\n        const reader = new FileReader()\n        reader.readAsDataURL(file)\n        reader.onload = () => {\n          this.options.img = reader.result\n          this.options.filename = file.name\n        }\n      }\n    },\n    // 上传图片\n    uploadImg() {\n      this.$refs.cropper.getCropBlob(data => {\n        let formData = new FormData()\n        formData.append(\"avatarfile\", data, this.options.filename)\n        uploadAvatar(formData).then(response => {\n          this.open = false\n          this.options.img = process.env.VUE_APP_BASE_API + response.imgUrl\n          store.commit('SET_AVATAR', this.options.img)\n          this.$modal.msgSuccess(\"修改成功\")\n          this.visible = false\n        })\n      })\n    },\n    // 实时预览\n    realTime(data) {\n      this.previews = data\n    },\n    // 关闭窗口\n    closeDialog() {\n      this.options.img = store.getters.avatar\n      this.visible = false\n      window.removeEventListener(\"resize\", this.resizeHandler)\n    }\n  }\n}\n</script>\n<style scoped lang=\"scss\">\n.user-info-head {\n  position: relative;\n  display: inline-block;\n  height: 120px;\n}\n\n.user-info-head:hover:after {\n  content: '+';\n  position: absolute;\n  left: 0;\n  right: 0;\n  top: 0;\n  bottom: 0;\n  color: #eee;\n  background: rgba(0, 0, 0, 0.5);\n  font-size: 24px;\n  font-style: normal;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  cursor: pointer;\n  line-height: 110px;\n  border-radius: 50%;\n}\n</style>\n"]}]}