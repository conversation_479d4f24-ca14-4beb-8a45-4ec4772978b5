{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/ImagePreview/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/ImagePreview/index.vue", "mtime": 1753510684527}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGlzRXh0ZXJuYWwgfSBmcm9tICJAL3V0aWxzL3ZhbGlkYXRlIgoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJJbWFnZVByZXZpZXciLAogIHByb3BzOiB7CiAgICBzcmM6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAiIgogICAgfSwKICAgIHdpZHRoOiB7CiAgICAgIHR5cGU6IFtOdW1iZXIsIFN0cmluZ10sCiAgICAgIGRlZmF1bHQ6ICIiCiAgICB9LAogICAgaGVpZ2h0OiB7CiAgICAgIHR5cGU6IFtOdW1iZXIsIFN0cmluZ10sCiAgICAgIGRlZmF1bHQ6ICIiCiAgICB9CiAgfSwKICBjb21wdXRlZDogewogICAgcmVhbFNyYygpIHsKICAgICAgaWYgKCF0aGlzLnNyYykgewogICAgICAgIHJldHVybgogICAgICB9CiAgICAgIGxldCByZWFsX3NyYyA9IHRoaXMuc3JjLnNwbGl0KCIsIilbMF0KICAgICAgaWYgKGlzRXh0ZXJuYWwocmVhbF9zcmMpKSB7CiAgICAgICAgcmV0dXJuIHJlYWxfc3JjCiAgICAgIH0KICAgICAgcmV0dXJuIHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyByZWFsX3NyYwogICAgfSwKICAgIHJlYWxTcmNMaXN0KCkgewogICAgICBpZiAoIXRoaXMuc3JjKSB7CiAgICAgICAgcmV0dXJuCiAgICAgIH0KICAgICAgbGV0IHJlYWxfc3JjX2xpc3QgPSB0aGlzLnNyYy5zcGxpdCgiLCIpCiAgICAgIGxldCBzcmNMaXN0ID0gW10KICAgICAgcmVhbF9zcmNfbGlzdC5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgIGlmIChpc0V4dGVybmFsKGl0ZW0pKSB7CiAgICAgICAgICByZXR1cm4gc3JjTGlzdC5wdXNoKGl0ZW0pCiAgICAgICAgfQogICAgICAgIHJldHVybiBzcmNMaXN0LnB1c2gocHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArIGl0ZW0pCiAgICAgIH0pCiAgICAgIHJldHVybiBzcmNMaXN0CiAgICB9LAogICAgcmVhbFdpZHRoKCkgewogICAgICByZXR1cm4gdHlwZW9mIHRoaXMud2lkdGggPT0gInN0cmluZyIgPyB0aGlzLndpZHRoIDogYCR7dGhpcy53aWR0aH1weGAKICAgIH0sCiAgICByZWFsSGVpZ2h0KCkgewogICAgICByZXR1cm4gdHlwZW9mIHRoaXMuaGVpZ2h0ID09ICJzdHJpbmciID8gdGhpcy5oZWlnaHQgOiBgJHt0aGlzLmhlaWdodH1weGAKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;AAcA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/ImagePreview", "sourcesContent": ["<template>\n  <el-image\n    :src=\"`${realSrc}`\"\n    fit=\"cover\"\n    :style=\"`width:${realWidth};height:${realHeight};`\"\n    :preview-src-list=\"realSrcList\"\n  >\n    <div slot=\"error\" class=\"image-slot\">\n      <i class=\"el-icon-picture-outline\"></i>\n    </div>\n  </el-image>\n</template>\n\n<script>\nimport { isExternal } from \"@/utils/validate\"\n\nexport default {\n  name: \"ImagePreview\",\n  props: {\n    src: {\n      type: String,\n      default: \"\"\n    },\n    width: {\n      type: [Number, String],\n      default: \"\"\n    },\n    height: {\n      type: [Number, String],\n      default: \"\"\n    }\n  },\n  computed: {\n    realSrc() {\n      if (!this.src) {\n        return\n      }\n      let real_src = this.src.split(\",\")[0]\n      if (isExternal(real_src)) {\n        return real_src\n      }\n      return process.env.VUE_APP_BASE_API + real_src\n    },\n    realSrcList() {\n      if (!this.src) {\n        return\n      }\n      let real_src_list = this.src.split(\",\")\n      let srcList = []\n      real_src_list.forEach(item => {\n        if (isExternal(item)) {\n          return srcList.push(item)\n        }\n        return srcList.push(process.env.VUE_APP_BASE_API + item)\n      })\n      return srcList\n    },\n    realWidth() {\n      return typeof this.width == \"string\" ? this.width : `${this.width}px`\n    },\n    realHeight() {\n      return typeof this.height == \"string\" ? this.height : `${this.height}px`\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.el-image {\n  border-radius: 5px;\n  background-color: #ebeef5;\n  box-shadow: 0 0 5px 1px #ccc;\n  ::v-deep .el-image__inner {\n    transition: all 0.3s;\n    cursor: pointer;\n    &:hover {\n      transform: scale(1.2);\n    }\n  }\n  ::v-deep .image-slot {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    width: 100%;\n    height: 100%;\n    color: #909399;\n    font-size: 30px;\n  }\n}\n</style>\n"]}]}