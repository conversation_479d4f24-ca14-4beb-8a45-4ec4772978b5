{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/monitor/cache/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/monitor/cache/index.vue", "mtime": 1753510684534}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldENhY2hlIH0gZnJvbSAiQC9hcGkvbW9uaXRvci9jYWNoZSIKaW1wb3J0ICogYXMgZWNoYXJ0cyBmcm9tICJlY2hhcnRzIgoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJDYWNoZSIsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOe7n+iuoeWRveS7pOS/oeaBrwogICAgICBjb21tYW5kc3RhdHM6IG51bGwsCiAgICAgIC8vIOS9v+eUqOWGheWtmAogICAgICB1c2VkbWVtb3J5OiBudWxsLAogICAgICAvLyBjYWNoZeS/oeaBrwogICAgICBjYWNoZTogW10KICAgIH0KICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKQogICAgdGhpcy5vcGVuTG9hZGluZygpCiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l57yT5a2Y6K+i5L+h5oGvICovCiAgICBnZXRMaXN0KCkgewogICAgICBnZXRDYWNoZSgpLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAgICAgdGhpcy5jYWNoZSA9IHJlc3BvbnNlLmRhdGEKICAgICAgICB0aGlzLiRtb2RhbC5jbG9zZUxvYWRpbmcoKQoKICAgICAgICB0aGlzLmNvbW1hbmRzdGF0cyA9IGVjaGFydHMuaW5pdCh0aGlzLiRyZWZzLmNvbW1hbmRzdGF0cywgIm1hY2Fyb25zIikKICAgICAgICB0aGlzLmNvbW1hbmRzdGF0cy5zZXRPcHRpb24oewogICAgICAgICAgdG9vbHRpcDogewogICAgICAgICAgICB0cmlnZ2VyOiAiaXRlbSIsCiAgICAgICAgICAgIGZvcm1hdHRlcjogInthfSA8YnIvPntifSA6IHtjfSAoe2R9JSkiLAogICAgICAgICAgfSwKICAgICAgICAgIHNlcmllczogWwogICAgICAgICAgICB7CiAgICAgICAgICAgICAgbmFtZTogIuWRveS7pCIsCiAgICAgICAgICAgICAgdHlwZTogInBpZSIsCiAgICAgICAgICAgICAgcm9zZVR5cGU6ICJyYWRpdXMiLAogICAgICAgICAgICAgIHJhZGl1czogWzE1LCA5NV0sCiAgICAgICAgICAgICAgY2VudGVyOiBbIjUwJSIsICIzOCUiXSwKICAgICAgICAgICAgICBkYXRhOiByZXNwb25zZS5kYXRhLmNvbW1hbmRTdGF0cywKICAgICAgICAgICAgICBhbmltYXRpb25FYXNpbmc6ICJjdWJpY0luT3V0IiwKICAgICAgICAgICAgICBhbmltYXRpb25EdXJhdGlvbjogMTAwMCwKICAgICAgICAgICAgfQogICAgICAgICAgXQogICAgICAgIH0pCiAgICAgICAgdGhpcy51c2VkbWVtb3J5ID0gZWNoYXJ0cy5pbml0KHRoaXMuJHJlZnMudXNlZG1lbW9yeSwgIm1hY2Fyb25zIikKICAgICAgICB0aGlzLnVzZWRtZW1vcnkuc2V0T3B0aW9uKHsKICAgICAgICAgIHRvb2x0aXA6IHsKICAgICAgICAgICAgZm9ybWF0dGVyOiAie2J9IDxici8+e2F9IDogIiArIHRoaXMuY2FjaGUuaW5mby51c2VkX21lbW9yeV9odW1hbiwKICAgICAgICAgIH0sCiAgICAgICAgICBzZXJpZXM6IFsKICAgICAgICAgICAgewogICAgICAgICAgICAgIG5hbWU6ICLls7DlgLwiLAogICAgICAgICAgICAgIHR5cGU6ICJnYXVnZSIsCiAgICAgICAgICAgICAgbWluOiAwLAogICAgICAgICAgICAgIG1heDogMTAwMCwKICAgICAgICAgICAgICBkZXRhaWw6IHsKICAgICAgICAgICAgICAgIGZvcm1hdHRlcjogdGhpcy5jYWNoZS5pbmZvLnVzZWRfbWVtb3J5X2h1bWFuLAogICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgZGF0YTogWwogICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICB2YWx1ZTogcGFyc2VGbG9hdCh0aGlzLmNhY2hlLmluZm8udXNlZF9tZW1vcnlfaHVtYW4pLAogICAgICAgICAgICAgICAgICBuYW1lOiAi5YaF5a2Y5raI6ICXIiwKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBdCiAgICAgICAgICAgIH0KICAgICAgICAgIF0KICAgICAgICB9KQogICAgICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCJyZXNpemUiLCAoKSA9PiB7CiAgICAgICAgICB0aGlzLmNvbW1hbmRzdGF0cy5yZXNpemUoKQogICAgICAgICAgdGhpcy51c2VkbWVtb3J5LnJlc2l6ZSgpCiAgICAgICAgfSkKICAgICAgfSkKICAgIH0sCiAgICAvLyDmiZPlvIDliqDovb3lsYIKICAgIG9wZW5Mb2FkaW5nKCkgewogICAgICB0aGlzLiRtb2RhbC5sb2FkaW5nKCLmraPlnKjliqDovb3nvJPlrZjnm5HmjqfmlbDmja7vvIzor7fnqI3lgJnvvIEiKQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/monitor/cache", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"10\">\n      <el-col :span=\"24\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\"><span><i class=\"el-icon-monitor\"></i> 基本信息</span></div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <table cellspacing=\"0\" style=\"width: 100%\">\n              <tbody>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">Redis版本</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.redis_version }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">运行模式</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.redis_mode == \"standalone\" ? \"单机\" : \"集群\" }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">端口</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.tcp_port }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">客户端数</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.connected_clients }}</div></td>\n                </tr>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">运行时间(天)</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.uptime_in_days }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">使用内存</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.used_memory_human }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">使用CPU</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ parseFloat(cache.info.used_cpu_user_children).toFixed(2) }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">内存配置</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.maxmemory_human }}</div></td>\n                </tr>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">AOF是否开启</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.aof_enabled == \"0\" ? \"否\" : \"是\" }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">RDB是否成功</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.rdb_last_bgsave_status }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">Key数量</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.dbSize\">{{ cache.dbSize }} </div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">网络入口/出口</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.instantaneous_input_kbps }}kps/{{cache.info.instantaneous_output_kbps}}kps</div></td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"12\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\"><span><i class=\"el-icon-pie-chart\"></i> 命令统计</span></div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <div ref=\"commandstats\" style=\"height: 420px\" />\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"12\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\"><span><i class=\"el-icon-odometer\"></i> 内存信息</span></div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <div ref=\"usedmemory\" style=\"height: 420px\" />\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport { getCache } from \"@/api/monitor/cache\"\nimport * as echarts from \"echarts\"\n\nexport default {\n  name: \"Cache\",\n  data() {\n    return {\n      // 统计命令信息\n      commandstats: null,\n      // 使用内存\n      usedmemory: null,\n      // cache信息\n      cache: []\n    }\n  },\n  created() {\n    this.getList()\n    this.openLoading()\n  },\n  methods: {\n    /** 查缓存询信息 */\n    getList() {\n      getCache().then((response) => {\n        this.cache = response.data\n        this.$modal.closeLoading()\n\n        this.commandstats = echarts.init(this.$refs.commandstats, \"macarons\")\n        this.commandstats.setOption({\n          tooltip: {\n            trigger: \"item\",\n            formatter: \"{a} <br/>{b} : {c} ({d}%)\",\n          },\n          series: [\n            {\n              name: \"命令\",\n              type: \"pie\",\n              roseType: \"radius\",\n              radius: [15, 95],\n              center: [\"50%\", \"38%\"],\n              data: response.data.commandStats,\n              animationEasing: \"cubicInOut\",\n              animationDuration: 1000,\n            }\n          ]\n        })\n        this.usedmemory = echarts.init(this.$refs.usedmemory, \"macarons\")\n        this.usedmemory.setOption({\n          tooltip: {\n            formatter: \"{b} <br/>{a} : \" + this.cache.info.used_memory_human,\n          },\n          series: [\n            {\n              name: \"峰值\",\n              type: \"gauge\",\n              min: 0,\n              max: 1000,\n              detail: {\n                formatter: this.cache.info.used_memory_human,\n              },\n              data: [\n                {\n                  value: parseFloat(this.cache.info.used_memory_human),\n                  name: \"内存消耗\",\n                }\n              ]\n            }\n          ]\n        })\n        window.addEventListener(\"resize\", () => {\n          this.commandstats.resize()\n          this.usedmemory.resize()\n        })\n      })\n    },\n    // 打开加载层\n    openLoading() {\n      this.$modal.loading(\"正在加载缓存监控数据，请稍候！\")\n    }\n  }\n}\n</script>\n"]}]}