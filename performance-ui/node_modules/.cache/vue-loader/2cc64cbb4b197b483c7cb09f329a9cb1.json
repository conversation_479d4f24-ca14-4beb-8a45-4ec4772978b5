{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/result/yearResult/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/result/yearResult/index.vue", "mtime": 1753510684535}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/result/yearResult", "sourcesContent": ["<script>\nimport { listYearlyResult, listYearlyGoodGrade, listDepartmentPerformance, listMemberOrganization, listOrganizationPerformance } from '@/api/performance/yearlyResult'\n\nexport default {\n  name: 'YearResult',\n  data() {\n    return {\n      // 当前激活的tab\n      activeTab: 'tab1',\n      // 加载状态\n      loading: true,\n      // 分类列表\n      categoryList: [],\n      // 年度结果列表（按分类排序）\n      resultList: [],\n      // \"好\"等次人员列表\n      goodGradeList: [],\n      // 科室绩效评价结果列表\n      departmentList: [],\n      // 班子成员绩效评价结果列表\n      memberList: [],\n      // 组织绩效评价结果列表\n      organizationList: [],\n      // 查询参数\n      queryParams: {\n        year: new Date().getFullYear()\n      }\n    }\n  },\n  created() {\n    this.getResultList()\n    this.getGoodGradeList()\n  },\n  methods: {\n    // 获取年度结果列表（按分类排序）\n    getResultList() {\n      this.loading = true\n      listYearlyResult(this.queryParams).then(res => {\n        if (res.code === 200) {\n          this.resultList = res.data || []\n          // 提取所有分类\n          this.categoryList = [...new Set(this.resultList.map(item => item.category))]\n        }\n        this.loading = false\n      })\n    },\n    // 获取\"好\"等次人员列表\n    getGoodGradeList() {\n      listYearlyGoodGrade(this.queryParams).then(res => {\n        if (res.code === 200) {\n          this.goodGradeList = res.data || []\n        }\n      })\n    },\n    // 按年度查询\n    handleQuery() {\n      if (this.activeTab === 'tab1') {\n        this.getResultList()\n        this.getGoodGradeList()\n      } else if (this.activeTab === 'tab2') {\n        this.getDepartmentList()\n      } else if (this.activeTab === 'tab3') {\n        this.getMemberList()\n      } else if (this.activeTab === 'tab4') {\n        this.getOrganizationList()\n      }\n    },\n    // 重置查询\n    resetQuery() {\n      this.queryParams = {\n        year: new Date().getFullYear()\n      }\n      this.handleQuery()\n    },\n    // 获取科室绩效评价结果列表\n    getDepartmentList() {\n      this.loading = true\n      listDepartmentPerformance(this.queryParams).then(res => {\n        if (res.code === 200) {\n          this.departmentList = res.data || []\n        }\n        this.loading = false\n      })\n    },\n    // 获取班子成员绩效评价结果列表\n    getMemberList() {\n      this.loading = true\n      listMemberOrganization(this.queryParams).then(res => {\n        if (res.code === 200) {\n          this.memberList = res.data || []\n        }\n        this.loading = false\n      })\n    },\n    // 获取组织绩效评价结果列表\n    getOrganizationList() {\n      this.loading = true\n      listOrganizationPerformance(this.queryParams).then(res => {\n        if (res.code === 200) {\n          this.organizationList = res.data || []\n        }\n        this.loading = false\n      })\n    },\n    // 切换Tab\n    handleTabClick(tab) {\n      if (tab.name === 'tab1') {\n        this.getResultList()\n        this.getGoodGradeList()\n      } else if (tab.name === 'tab2') {\n        this.getDepartmentList()\n      } else if (tab.name === 'tab3') {\n        this.getMemberList()\n      } else if (tab.name === 'tab4') {\n        this.getOrganizationList()\n      }\n    }\n  }\n}\n</script>\n\n<template>\n  <div class=\"app-container\">\n    <!-- Tab栏 -->\n    <el-tabs v-model=\"activeTab\" @tab-click=\"handleTabClick\">\n      <el-tab-pane label=\"个人年度分类排序\" name=\"tab1\">\n        <!-- 查询区域 -->\n        <el-form :model=\"queryParams\" ref=\"que  ryForm\" :inline=\"true\" class=\"search-form\">\n          <el-form-item label=\"年度\">\n            <el-input v-model=\"queryParams.year\" placeholder=\"请输入年度\" clearable type=\"number\" />\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" @click=\"handleQuery\">查询</el-button>\n            <el-button @click=\"resetQuery\">重置</el-button>\n          </el-form-item>\n        </el-form>\n\n        <!-- 分类排序展示区域 -->\n        <el-card class=\"box-card\" v-loading=\"loading\">\n          <div slot=\"header\" class=\"card-header\">\n            <span>{{ queryParams.year }}年度绩效结果分类排序</span>\n          </div>\n          <div v-for=\"category in categoryList\" :key=\"category\" class=\"category-section\">\n            <h3>{{ category }}</h3>\n            <el-table :data=\"resultList.filter(item => item.category === category)\" border style=\"width: 100%\">\n              <el-table-column prop=\"deptName\" label=\"科室\" />\n              <el-table-column prop=\"position\" label=\"职务\" />\n              <el-table-column prop=\"userName\" label=\"姓名\" />\n              <el-table-column prop=\"yearScore\" label=\"年度得分\" />\n              <el-table-column prop=\"sameLevelOrder\" label=\"同层级排序\" />\n              <el-table-column prop=\"grade\" label=\"等次\" />\n            </el-table>\n          </div>\n        </el-card>\n\n        <!-- \"好\"等次人员名单 -->\n        <el-card class=\"box-card\" style=\"margin-top: 20px;\">\n          <div slot=\"header\" class=\"card-header\">\n            <span>{{ queryParams.year }}年度\"好\"等次人员名单</span>\n          </div>\n          <el-table :data=\"goodGradeList\" border style=\"width: 100%\">\n            <el-table-column prop=\"deptName\" label=\"科室\" />\n            <el-table-column prop=\"position\" label=\"职务\" />\n            <el-table-column prop=\"category\" label=\"分类\" />\n            <el-table-column prop=\"userName\" label=\"姓名\" />\n            <el-table-column prop=\"yearScore\" label=\"年度得分\" />\n            <el-table-column prop=\"sameLevelOrder\" label=\"同层级排序\" />\n          </el-table>\n        </el-card>\n      </el-tab-pane>\n\n      <el-tab-pane label=\"科室绩效评价\" name=\"tab2\">\n        <!-- 查询区域 -->\n        <el-form :model=\"queryParams\" ref=\"queryForm2\" :inline=\"true\" class=\"search-form\">\n          <el-form-item label=\"年度\">\n            <el-input v-model=\"queryParams.year\" placeholder=\"请输入年度\" clearable type=\"number\" />\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" @click=\"handleQuery\">查询</el-button>\n            <el-button @click=\"resetQuery\">重置</el-button>\n          </el-form-item>\n        </el-form>\n\n        <!-- 科室绩效评价结果表格 -->\n        <el-card class=\"box-card\" v-loading=\"loading\">\n          <div slot=\"header\" class=\"card-header\">\n            <span>{{ queryParams.year }}年度科室绩效评价结果（按总分排序）</span>\n          </div>\n          <el-table :data=\"departmentList\" border style=\"width: 100%\">\n            <el-table-column prop=\"deptName\" label=\"科室名称\" />\n            <el-table-column prop=\"testTime\" label=\"测评时间\" />\n            <el-table-column prop=\"year\" label=\"年份\" />\n            <el-table-column prop=\"totalScore\" label=\"总分\" sortable />\n            <el-table-column prop=\"remark\" label=\"备注\" />\n          </el-table>\n        </el-card>\n      </el-tab-pane>\n\n      <el-tab-pane label=\"班子成员绩效评价\" name=\"tab3\">\n        <!-- 查询区域 -->\n        <el-form :model=\"queryParams\" ref=\"queryForm3\" :inline=\"true\" class=\"search-form\">\n          <el-form-item label=\"年度\">\n            <el-input v-model=\"queryParams.year\" placeholder=\"请输入年度\" clearable type=\"number\" />\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" @click=\"handleQuery\">查询</el-button>\n            <el-button @click=\"resetQuery\">重置</el-button>\n          </el-form-item>\n        </el-form>\n\n        <!-- 班子成员绩效评价结果表格 -->\n        <el-card class=\"box-card\" v-loading=\"loading\">\n          <div slot=\"header\" class=\"card-header\">\n            <span>{{ queryParams.year }}年度班子成员绩效评价结果（按评价分值排序）</span>\n          </div>\n          <el-table :data=\"memberList\" border style=\"width: 100%\">\n            <el-table-column prop=\"memberName\" label=\"姓名\" />\n            <el-table-column prop=\"testTime\" label=\"测评时间\" />\n            <el-table-column prop=\"taskType\" label=\"任务类型\" />\n            <el-table-column prop=\"performanceTask\" label=\"绩效任务\" />\n            <el-table-column prop=\"responsibleLeader\" label=\"责任领导\" />\n            <el-table-column prop=\"evaluationScore\" label=\"评价分值\" sortable />\n            <el-table-column prop=\"remark\" label=\"备注\" />\n          </el-table>\n        </el-card>\n      </el-tab-pane>\n\n      <el-tab-pane label=\"组织绩效评价\" name=\"tab4\">\n        <!-- 查询区域 -->\n        <el-form :model=\"queryParams\" ref=\"queryForm4\" :inline=\"true\" class=\"search-form\">\n          <el-form-item label=\"年度\">\n            <el-input v-model=\"queryParams.year\" placeholder=\"请输入年度\" clearable type=\"number\" />\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" @click=\"handleQuery\">查询</el-button>\n            <el-button @click=\"resetQuery\">重置</el-button>\n          </el-form-item>\n        </el-form>\n\n        <!-- 组织绩效评价结果表格 -->\n        <el-card class=\"box-card\" v-loading=\"loading\">\n          <div slot=\"header\" class=\"card-header\">\n            <span>{{ queryParams.year }}年度组织绩效评价结果（按评价分值排序）</span>\n          </div>\n          <el-table :data=\"organizationList\" border style=\"width: 100%\">\n            <el-table-column prop=\"taskType\" label=\"任务类型\" />\n            <el-table-column prop=\"taskSource\" label=\"任务来源\" />\n            <el-table-column prop=\"performanceTask\" label=\"绩效任务\" />\n            <el-table-column prop=\"responsibleDept\" label=\"责任科室\" />\n            <el-table-column prop=\"responsibleLeader\" label=\"责任领导\" />\n            <el-table-column prop=\"evaluationScore\" label=\"评价分值\" sortable />\n            <el-table-column prop=\"remark\" label=\"备注\" />\n          </el-table>\n        </el-card>\n      </el-tab-pane>\n    </el-tabs>\n  </div>\n</template>\n\n<style scoped lang=\"scss\">\n.search-form {\n  margin-bottom: 20px;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.category-section {\n  margin-bottom: 20px;\n\n  h3 {\n    margin-bottom: 10px;\n    padding-left: 5px;\n    border-left: 4px solid #409EFF;\n  }\n}\n\n.empty-tab {\n  padding: 40px;\n  text-align: center;\n  color: #909399;\n  font-size: 16px;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n}\n</style>\n"]}]}