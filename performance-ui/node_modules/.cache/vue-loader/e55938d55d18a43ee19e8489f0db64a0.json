{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/execute/year/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/execute/year/index.vue", "mtime": 1753510684533}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgGA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/execute/year", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"20\" style=\"margin-bottom: 20px;\">\n      <el-col :span=\"12\">\n        <el-button type=\"primary\" @click=\"handleAdd\">新增</el-button>\n        <el-upload\n          class=\"upload-demo\"\n          :action=\"uploadExcelUrl\"\n          :headers=\"upload.headers\"\n          :show-file-list=\"false\"\n          :on-success=\"handleFileSuccess\"\n          :on-error=\"handleImportError\"\n          accept=\".xls,.xlsx\"\n          style=\"display:inline-block;margin-left:10px;\"\n        >\n          <el-button type=\"success\">导入Excel</el-button>\n        </el-upload>\n        <el-button @click=\"handleExport\" style=\"margin-left:10px;\">导出Excel</el-button>\n        <el-button @click=\"downloadExcelTemplate\" style=\"margin-left:10px;\">下载Excel模板</el-button>\n      </el-col>\n    </el-row>\n    <el-table :data=\"tableData\" border style=\"width: 100%\" @selection-change=\"handleSelectionChange\" v-loading=\"loading\">\n      <el-table-column type=\"selection\" width=\"55\" />\n\n      <el-table-column prop=\"name\" label=\"姓名\" width=\"50\" />\n      <el-table-column prop=\"gender\" label=\"性别\" width=\"50\"/>\n      <el-table-column prop=\"birthday\" label=\"出生年月\" width=\"100\" />\n      <el-table-column prop=\"politicalStatus\" label=\"政治面貌\" width=\"80\" />\n      <el-table-column prop=\"currentPosition\" label=\"任现职时间\" width=\"100\" />\n      <el-table-column prop=\"unitandPosition\" label=\"单位及职务职级\" width=\"150\" />\n      <el-table-column prop=\"workContent\" label=\"从事或分管工作\" width=\"150\" />\n      <el-table-column prop=\"selfSummary\" label=\"个人总结\" width=\"300\" />\n      <el-table-column prop=\"training\" label=\"参加脱产培训情况\" width=\"300\" />\n      <el-table-column prop=\"advice\" label=\"主管领导评语和考核等次建议\" width=\"300\" />\n      <el-table-column prop=\"attitude\" label=\"机关负责人或考核委员会意见\" width=\"300\" />\n      <el-table-column prop=\"remark\" label=\"需要说明的情况\" width=\"300\" />\n\n      <el-table-column label=\"操作\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <el-button size=\"mini\" @click=\"handleUpdate(scope.row)\">编辑</el-button>\n          <el-button size=\"mini\" type=\"danger\" @click=\"handleDelete(scope.row)\">删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <el-pagination\n      style=\"margin-top: 20px;\"\n      background\n      layout=\"prev, pager, next, jumper\"\n      :total=\"total\"\n      :page-size=\"queryParams.pageSize\"\n      :current-page.sync=\"queryParams.pageNum\"\n      @current-change=\"getList\"\n    />\n    <el-dialog :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\n      <el-form :model=\"form\" label-width=\"100px\">\n        <el-row :gutter=\"20\">\n          <!-- <el-col :span=\"8\"><el-form-item label=\"序号\"><el-input v-model=\"form.id\" /></el-form-item></el-col> -->\n          <el-col :span=\"8\"><el-form-item label=\"姓名\"><el-input v-model=\"form.name\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"性别\"><el-input v-model=\"form.gender\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"出生年月\"><el-input v-model=\"form.birthday\" /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\"><el-form-item label=\"政治面貌\"><el-input v-model=\"form.politicalStatus\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"任现职时间\"><el-input v-model=\"form.currentPosition\" /></el-form-item></el-col>\n\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"10\"><el-form-item label=\"单位及职务职级\"><el-input v-model=\"form.unitandPosition\" /></el-form-item></el-col>\n          <el-col :span=\"10\"><el-form-item label=\"从事或分管工作\"><el-input v-model=\"form.workContent\" /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"20\"><el-form-item label=\"个人总结\"><el-input v-model=\"form.selfSummary\" /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"20\"><el-form-item label=\"参加脱产培训情况\"><el-input v-model=\"form.training\" /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"20\"><el-form-item label=\"主管领导评语和考核等次建议\"><el-input v-model=\"form.advice\" /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"20\"><el-form-item label=\"机关负责人或考核委员会意见\"><el-input v-model=\"form.attitude\" /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"20\"><el-form-item label=\"需要说明的情况\"><el-input v-model=\"form.remark\" /></el-form-item></el-col>\n        </el-row>\n\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"submitForm\">确定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listYearly, addYearly, updateYearly, delYearly, exportYearly, downloadTemplateYearly, downloadExcelTemplateYearly } from \"@/api/performance/yearly\";\nimport { getToken } from \"@/utils/auth\";\n\nexport default {\n  name: \"Yearly\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 表格数据\n      tableData: [],\n      // 弹出层标题\n      dialogTitle: \"\",\n      // 是否显示弹出层\n      dialogVisible: false,\n      // 表单参数\n      form: {},\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        year: null,\n      },\n      // Word导入参数\n      upload: {\n        open: false,\n        title: \"\",\n        isUploading: false,\n        url: process.env.VUE_APP_BASE_API + \"/performance/yearly/importData\",\n        headers: { Authorization: \"Bearer \" + getToken() }\n      },\n      // Excel导入URL\n      uploadExcelUrl: process.env.VUE_APP_BASE_API + \"/performance/yearly/importExcel\"\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询列表 */\n    getList() {\n      this.loading = true;\n      listYearly(this.queryParams).then(response => {\n        this.tableData = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    /** 多选框选中数据 */\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.form = {};\n      this.dialogVisible = true;\n      this.dialogTitle = \"新增年度总结\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      const id = row.id || this.ids[0];\n      // 在实际应用中，推荐通过ID查询最新数据\n      // getYearly(id).then(response => { ... });\n      this.form = Object.assign({}, row);\n      this.dialogVisible = true;\n      this.dialogTitle = \"修改年度总结\";\n    },\n    /** 提交按钮 */\n    submitForm() {\n      if (this.form.id != null) {\n        updateYearly(this.form).then(response => {\n          this.$modal.msgSuccess(\"修改成功\");\n          this.dialogVisible = false;\n          this.getList();\n        });\n      } else {\n        addYearly(this.form).then(response => {\n          this.$modal.msgSuccess(\"新增成功\");\n          this.dialogVisible = false;\n          this.getList();\n        });\n      }\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row.id ? [row.id] : this.ids;\n      this.$modal.confirm('是否确认删除编号为\"' + ids.join(',') + '\"的数据项？').then(() => {\n        return delYearly(ids);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 导入按钮操作 */\n    handleImport() {\n      this.upload.title = \"年度绩效Word导入\";\n      this.upload.open = true;\n    },\n    /** 导入错误处理 */\n    handleImportError() {\n      this.$message.error('导入失败，请检查文件或网络');\n    },\n\n    /** 导出按钮操作 */\n    handleExport() {\n      if (this.ids.length === 0) {\n        this.$modal.msgWarning(\"请选择要导出的数据\");\n        return;\n      }\n      this.$modal.confirm('是否确认导出选中的数据项？').then(() => {\n        return exportYearly(this.ids);\n      }).then(data => {\n        const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })\n        const url = window.URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.href = url\n        a.download = '年度总结导出.docx'\n        document.body.appendChild(a)\n        a.click()\n        document.body.removeChild(a)\n        window.URL.revokeObjectURL(url)\n      }).catch(() => {});\n    },\n\n    // /** 下载Word模板操作 */\n    // downloadTemplate() {\n    //   downloadTemplateYearly().then(data => {\n    //     const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })\n    //     const url = window.URL.createObjectURL(blob)\n    //     const a = document.createElement('a')\n    //     a.href = url\n    //     a.download = '年度总结导入模板.docx'\n    //     document.body.appendChild(a)\n    //     a.click()\n    //     document.body.removeChild(a)\n    //     window.URL.revokeObjectURL(url)\n    //   });\n    // },\n\n    /** 下载Excel模板操作 */\n    downloadExcelTemplate() {\n      downloadExcelTemplateYearly().then(data => {\n        const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })\n        const url = window.URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.href = url\n        a.download = '年度总结Excel导入模板.xlsx'\n        document.body.appendChild(a)\n        a.click()\n        document.body.removeChild(a)\n        window.URL.revokeObjectURL(url)\n      });\n    },\n    /** 文件上传中处理 */\n    handleFileUploadProgress(event, file, fileList) {\n      this.upload.isUploading = true;\n    },\n    /** 文件上传成功处理 */\n    handleFileSuccess(response, file, fileList) {\n      if (response.code === 200) {\n        this.$message.success(response.msg || '导入成功');\n        this.getList();\n      } else {\n        this.$message.error(response.msg || '导入失败');\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.app-container {\n  padding: 20px;\n}\n</style>\n"]}]}