{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/execute/quarter/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/execute/quarter/index.vue", "mtime": 1753510684533}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/execute/quarter", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"20\" style=\"margin-bottom: 20px;\">\n      <el-col :span=\"12\">\n        <el-button type=\"primary\" @click=\"handleAdd\">新增</el-button>\n        <el-upload\n          class=\"upload-demo\"\n          :action=\"uploadExcelUrl\"\n          :headers=\"uploadHeaders\"\n          :show-file-list=\"false\"\n          :on-success=\"handleImportSuccess\"\n          :on-error=\"handleImportError\"\n          accept=\".xls,.xlsx\"\n          style=\"display:inline-block;margin-left:10px;\"\n        >\n          <el-button type=\"success\">导入Excel</el-button>\n        </el-upload>\n        <el-button @click=\"handleExport\" style=\"margin-left:10px;\">导出Word</el-button>\n        <el-button @click=\"handleDownloadExcelTemplate\" style=\"margin-left:10px;\">下载Excel模板</el-button>\n      </el-col>\n    </el-row>\n    <el-table :data=\"tableData\" border style=\"width: 100%\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" />\n      <el-table-column prop=\"id\" label=\"序号\" width=\"60\" />\n      <el-table-column prop=\"deptName\" label=\"科室\" />\n      <el-table-column prop=\"position\" label=\"职务\" />\n      <el-table-column prop=\"category\" label=\"分类\" />\n      <el-table-column prop=\"userName\" label=\"姓名\" />\n      <el-table-column prop=\"planScore\" label=\"计划分值\" />\n      <el-table-column prop=\"selfScore\" label=\"自评分值\" />\n      <el-table-column prop=\"sectionLeaderComment\" label=\"科长点评\" />\n      <el-table-column prop=\"divisionLeaderComment\" label=\"分管领导点评\" />\n      <el-table-column prop=\"groupLeaderScore\" label=\"领导小组评鉴\" />\n      <el-table-column prop=\"taskSubtotalScore\" label=\"任务小计得分\" />\n      <el-table-column prop=\"incentiveIndex\" label=\"激励指标\" />\n      <el-table-column prop=\"constraintIndex\" label=\"约束指标\" />\n      <el-table-column prop=\"quarterScore\" label=\"季度得分\" />\n      <el-table-column prop=\"sameLevelOrder\" label=\"同层级排序\" />\n      <el-table-column prop=\"grade\" label=\"等次\" />\n      <el-table-column prop=\"remark\" label=\"备注\" />\n      <el-table-column prop=\"year\" label=\"年\" width=\"80\" />\n      <el-table-column prop=\"quarter\" label=\"季度\" width=\"60\" />\n      <el-table-column label=\"操作\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <el-button size=\"mini\" @click=\"handleEdit(scope.row)\">编辑</el-button>\n          <el-button size=\"mini\" type=\"danger\" @click=\"handleDelete(scope.row)\">删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <el-pagination\n      style=\"margin-top: 20px;\"\n      background\n      layout=\"prev, pager, next, jumper\"\n      :total=\"total\"\n      :page-size=\"pageSize\"\n      :current-page.sync=\"pageNum\"\n      @current-change=\"loadData\"\n    />\n    <el-dialog :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\n      <el-form :model=\"form\" label-width=\"100px\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\"><el-form-item label=\"科室\"><el-input v-model=\"form.deptName\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"职务\"><el-input v-model=\"form.position\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"分类\"><el-input v-model=\"form.category\" /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\"><el-form-item label=\"姓名\"><el-input v-model=\"form.userName\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"计划分值\"><el-input v-model=\"form.planScore\" type=\"number\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"自评分值\"><el-input v-model=\"form.selfScore\" type=\"number\" /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\"><el-form-item label=\"科长点评\"><el-input v-model=\"form.sectionLeaderComment\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"分管领导点评\"><el-input v-model=\"form.divisionLeaderComment\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"领导小组评鉴\"><el-input v-model=\"form.groupLeaderScore\" type=\"number\" /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\"><el-form-item label=\"任务小计得分\"><el-input v-model=\"form.taskSubtotalScore\" type=\"number\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"激励指标\"><el-input v-model=\"form.incentiveIndex\" type=\"number\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"约束指标\"><el-input v-model=\"form.constraintIndex\" type=\"number\" /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\"><el-form-item label=\"季度得分\"><el-input v-model=\"form.quarterScore\" type=\"number\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"同层级排序\"><el-input v-model=\"form.sameLevelOrder\" type=\"number\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"等次\"><el-input v-model=\"form.grade\" /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\"><el-form-item label=\"备注\"><el-input v-model=\"form.remark\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"年\"><el-input v-model=\"form.year\" type=\"number\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"季度\"><el-input v-model=\"form.quarter\" type=\"number\" /></el-form-item></el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"submitForm\">确定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  listQuarterly,\n  addQuarterly,\n  updateQuarterly,\n  delQuarterly,\n  exportQuarterly,\n  downloadTemplateQuarterly,\n  downloadExcelTemplateQuarterly\n} from '@/api/performance/quarterly'\nimport { getToken } from '@/utils/auth'\n\nexport default {\n  data() {\n    return {\n      tableData: [],\n      total: 0,\n      pageNum: 1,\n      pageSize: 10,\n      dialogVisible: false,\n      dialogTitle: '新增',\n      form: {},\n      selectedIds: [],\n      uploadUrl: process.env.VUE_APP_BASE_API + '/performance/quarterly/importData',\n      uploadExcelUrl: process.env.VUE_APP_BASE_API + '/performance/quarterly/importExcel',\n      uploadHeaders: {\n        Authorization: 'Bearer ' + getToken()\n      }\n    }\n  },\n  created() {\n    this.loadData()\n  },\n  methods: {\n    loadData() {\n      listQuarterly({ pageNum: this.pageNum, pageSize: this.pageSize }).then(res => {\n        if (res.code === 200) {\n          this.tableData = res.rows\n          this.total = res.total\n        }\n      })\n    },\n    handleAdd() {\n      this.dialogTitle = '新增'\n      this.form = {}\n      this.dialogVisible = true\n    },\n    handleEdit(row) {\n      this.dialogTitle = '编辑'\n      this.form = Object.assign({}, row)\n      this.dialogVisible = true\n    },\n    handleDelete(row) {\n      this.$confirm('确定删除该条记录吗？', '提示', { type: 'warning' }).then(() => {\n        delQuarterly(row.id).then(() => {\n          this.$message.success('删除成功')\n          this.loadData()\n        })\n      })\n    },\n    submitForm() {\n      if (this.form.id) {\n        updateQuarterly(this.form).then(() => {\n          this.$message.success('修改成功')\n          this.dialogVisible = false\n          this.loadData()\n        })\n      } else {\n        addQuarterly(this.form).then(() => {\n          this.$message.success('新增成功')\n          this.dialogVisible = false\n          this.loadData()\n        })\n      }\n    },\n    handleSelectionChange(val) {\n      this.selectedIds = val.map(item => item.id)\n    },\n    handleImportSuccess(res) {\n      if (res.code === 200) {\n        this.$message.success(res.msg || '导入成功')\n        this.loadData()\n      } else {\n        this.$message.error(res.msg || '导入失败')\n      }\n    },\n    handleImportError() {\n      this.$message.error('导入失败，请检查文件或网络')\n    },\n    handleExport() {\n      if (this.selectedIds.length === 0) {\n        this.$message.warning('请先选择要导出的数据')\n        return\n      }\n      exportQuarterly(this.selectedIds).then(data => {\n        const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })\n        const url = window.URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.href = url\n        a.download = '季度总结导出.docx'\n        document.body.appendChild(a)\n        a.click()\n        document.body.removeChild(a)\n        window.URL.revokeObjectURL(url)\n      })\n    },\n    handleDownloadTemplate() {\n      downloadTemplateQuarterly().then(data => {\n        const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })\n        const url = window.URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.href = url\n        a.download = '季度总结导入模板.docx'\n        document.body.appendChild(a)\n        a.click()\n        document.body.removeChild(a)\n        window.URL.revokeObjectURL(url)\n      })\n    },\n    handleDownloadExcelTemplate() {\n      downloadExcelTemplateQuarterly().then(data => {\n        const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })\n        const url = window.URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.href = url\n        a.download = '季度总结Excel导入模板.xlsx'\n        document.body.appendChild(a)\n        a.click()\n        document.body.removeChild(a)\n        window.URL.revokeObjectURL(url)\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.app-container {\n  padding: 20px;\n}\n</style>\n"]}]}