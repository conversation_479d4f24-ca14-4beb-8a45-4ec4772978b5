{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/performance/personnel/index.vue?vue&type=style&index=0&id=6037761a&scoped=true&lang=css", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/performance/personnel/index.vue", "mtime": 1754489749513}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/css-loader/dist/cjs.js", "mtime": 1753510683027}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1753510684373}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/postcss-loader/src/index.js", "mtime": 1753510684004}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmJveC1jYXJkIHsKICBtYXJnaW4tYm90dG9tOiAyMHB4Owp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwuBA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/performance/personnel", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-tabs v-model=\"activeTab\" @tab-click=\"handleTabClick\">\n      <!-- 个人季度绩效计划 Tab -->\n      <el-tab-pane label=\"个人季度绩效计划\" name=\"quarterly\">\n        <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n          <el-form-item label=\"姓名\" prop=\"name\">\n            <el-input\n              v-model=\"queryParams.name\"\n              placeholder=\"请输入姓名\"\n              clearable\n              @keyup.enter.native=\"handleQuery\"\n            />\n          </el-form-item>\n          <el-form-item label=\"科室\" prop=\"department\">\n            <el-input\n              v-model=\"queryParams.department\"\n              placeholder=\"请输入科室\"\n              clearable\n              @keyup.enter.native=\"handleQuery\"\n            />\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n          </el-form-item>\n        </el-form>\n\n        <el-row :gutter=\"10\" class=\"mb8\">\n          <el-col :span=\"1.5\">\n            <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" :disabled=\"multiple\" @click=\"handleExportSelected\">导出Word</el-button>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button type=\"danger\" plain icon=\"el-icon-delete\" size=\"mini\" :disabled=\"multiple\" @click=\"handleDelete\">删除</el-button>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-upload\n              class=\"upload-demo\"\n              :action=\"importUrl\"\n              :headers=\"uploadHeaders\"\n              :on-success=\"handleImportSuccess\"\n              :on-error=\"handleImportError\"\n              :before-upload=\"beforeImportUpload\"\n              :show-file-list=\"false\"\n              style=\"display: inline-block;\">\n              <el-button type=\"info\" plain icon=\"el-icon-upload2\" size=\"mini\">导入Word</el-button>\n            </el-upload>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button type=\"info\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"handleDownloadTemplate\">下载模板</el-button>\n          </el-col>\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n        </el-row>\n\n        <el-card class=\"box-card\" style=\"margin-bottom: 20px;\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span style=\"font-weight: bold;\">个人季度绩效计划列表</span>\n          </div>\n          <el-table\n            v-loading=\"loading\"\n            :data=\"planList\"\n            @selection-change=\"handleSelectionChange\"\n            @current-change=\"handleCurrentChange\"\n            highlight-current-row\n            :height=\"300\">\n            <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n            <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" width=\"100\" />\n            <el-table-column label=\"科室\" align=\"center\" prop=\"department\" width=\"150\" />\n            <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\n              <template slot-scope=\"scope\">\n                <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"操作\" align=\"center\" width=\"200\" class-name=\"small-padding fixed-width\">\n              <template slot-scope=\"scope\">\n                <el-button size=\"mini\" type=\"text\" icon=\"el-icon-s-data\" @click=\"handleViewPersonalIndicators(scope.row)\">个性指标</el-button>\n                <el-button size=\"mini\" type=\"text\" icon=\"el-icon-data-analysis\" @click=\"handleViewCommonIndicators(scope.row)\">共性指标</el-button>\n                <el-button size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleDelete(scope.row)\">删除</el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n          <pagination\n            v-show=\"total>0\"\n            :total=\"total\"\n            :page.sync=\"queryParams.pageNum\"\n            :limit.sync=\"queryParams.pageSize\"\n            @pagination=\"getList\"\n          />\n        </el-card>\n      </el-tab-pane>\n\n      <!-- 个人年度绩效计划 Tab -->\n      <el-tab-pane label=\"个人年度绩效计划\" name=\"yearly\">\n        <el-form :model=\"yearlyQueryParams\" ref=\"yearlyQueryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n          <el-form-item label=\"姓名\" prop=\"name\">\n            <el-input\n              v-model=\"yearlyQueryParams.name\"\n              placeholder=\"请输入姓名\"\n              clearable\n              @keyup.enter.native=\"handleYearlyQuery\"\n            />\n          </el-form-item>\n          <el-form-item label=\"科室\" prop=\"department\">\n            <el-input\n              v-model=\"yearlyQueryParams.department\"\n              placeholder=\"请输入科室\"\n              clearable\n              @keyup.enter.native=\"handleYearlyQuery\"\n            />\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleYearlyQuery\">搜索</el-button>\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetYearlyQuery\">重置</el-button>\n          </el-form-item>\n        </el-form>\n\n        <el-row :gutter=\"10\" class=\"mb8\">\n          <el-col :span=\"1.5\">\n            <el-button type=\"danger\" plain icon=\"el-icon-delete\" size=\"mini\" :disabled=\"yearlyMultiple\" @click=\"handleYearlyDelete\">删除</el-button>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-upload\n              class=\"upload-demo\"\n              :action=\"yearlyImportUrl\"\n              :headers=\"uploadHeaders\"\n              :on-success=\"handleYearlyImportSuccess\"\n              :on-error=\"handleYearlyImportError\"\n              :before-upload=\"beforeYearlyImportUpload\"\n              :show-file-list=\"false\"\n              style=\"display: inline-block;\">\n              <el-button type=\"info\" plain icon=\"el-icon-upload2\" size=\"mini\">导入Excel</el-button>\n            </el-upload>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button type=\"info\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"handleYearlyDownloadTemplate\">下载模板</el-button>\n          </el-col>\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getYearlyList\"></right-toolbar>\n        </el-row>\n\n        <el-card class=\"box-card\" style=\"margin-bottom: 20px;\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span style=\"font-weight: bold;\">个人年度绩效计划列表</span>\n          </div>\n          <el-table\n            v-loading=\"yearlyLoading\"\n            :data=\"yearlyPlanList\"\n            @selection-change=\"handleYearlySelectionChange\"\n            highlight-current-row\n            :height=\"300\">\n            <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n            <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" width=\"100\" />\n            <el-table-column label=\"科室\" align=\"center\" prop=\"department\" width=\"150\" />\n            <el-table-column label=\"年份\" align=\"center\" prop=\"year\" width=\"100\" />\n            <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\n              <template slot-scope=\"scope\">\n                <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"操作\" align=\"center\" width=\"200\" class-name=\"small-padding fixed-width\">\n              <template slot-scope=\"scope\">\n                <el-button size=\"mini\" type=\"text\" icon=\"el-icon-s-data\" @click=\"handleViewYearlyPersonalIndicators(scope.row)\">个性指标</el-button>\n                <el-button size=\"mini\" type=\"text\" icon=\"el-icon-data-analysis\" @click=\"handleViewYearlyCommonIndicators(scope.row)\">共性指标</el-button>\n                <el-button size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleYearlyDelete(scope.row)\">删除</el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n          <pagination\n            v-show=\"yearlyTotal>0\"\n            :total=\"yearlyTotal\"\n            :page.sync=\"yearlyQueryParams.pageNum\"\n            :limit.sync=\"yearlyQueryParams.pageSize\"\n            @pagination=\"getYearlyList\"\n          />\n        </el-card>\n      </el-tab-pane>\n    </el-tabs>\n\n\n\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n        <el-form-item label=\"姓名\" prop=\"name\">\n          <el-input v-model=\"form.name\" placeholder=\"请输入姓名\" />\n        </el-form-item>\n        <el-form-item label=\"科室\" prop=\"department\">\n          <el-input v-model=\"form.department\" placeholder=\"请输入科室\" />\n        </el-form-item>\n        <!-- <el-form-item label=\"状态\" prop=\"status\">\n          <el-radio-group v-model=\"form.status\">\n            <el-radio label=\"DRAFT\">草稿</el-radio>\n            <el-radio label=\"SUBMITTED\">已提交</el-radio>\n            <el-radio label=\"APPROVED\">已审核</el-radio>\n          </el-radio-group>\n        </el-form-item> -->\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <el-dialog :title=\"taskTitle\" :visible.sync=\"taskOpen\" width=\"800px\" append-to-body>\n      <el-form ref=\"taskForm\" :model=\"taskForm\" :rules=\"taskRules\" label-width=\"100px\">\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"任务类型\" prop=\"taskType\">\n              <el-input v-model=\"taskForm.taskType\" placeholder=\"请输入任务类型\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"分值\" prop=\"score\">\n              <el-input-number v-model=\"taskForm.score\" :min=\"0\" :max=\"100\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-form-item label=\"任务内容\" prop=\"taskContent\">\n          <el-input v-model=\"taskForm.taskContent\" type=\"textarea\" placeholder=\"请输入任务内容\" />\n        </el-form-item>\n        <el-form-item label=\"目标及措施\" prop=\"targetMeasures\">\n          <el-input v-model=\"taskForm.targetMeasures\" type=\"textarea\" placeholder=\"请输入目标及措施\" />\n        </el-form-item>\n        <el-form-item label=\"评价标准\" prop=\"evaluationStandard\">\n          <el-input v-model=\"taskForm.evaluationStandard\" type=\"textarea\" placeholder=\"请输入评价标准\" />\n        </el-form-item>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"责任人\" prop=\"responsible\">\n              <el-input v-model=\"taskForm.responsible\" placeholder=\"请输入责任人\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"完成时限\" prop=\"deadline\">\n              <el-date-picker\n                v-model=\"taskForm.deadline\"\n                type=\"date\"\n                placeholder=\"选择完成时限\"\n                format=\"yyyy-MM-dd\"\n                value-format=\"yyyy-MM-dd\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitTaskForm\">确 定</el-button>\n        <el-button @click=\"cancelTask\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 个性指标弹窗 -->\n    <el-dialog title=\"个性指标\" :visible.sync=\"personalIndicatorOpen\" width=\"80%\" append-to-body>\n      <el-table :data=\"personalIndicatorList\" style=\"width: 100%\" border>\n        <el-table-column label=\"序号\" prop=\"seqNo\" width=\"60\" align=\"center\" />\n        <el-table-column label=\"任务类型\" prop=\"taskCategory\" width=\"120\" align=\"center\" />\n        <el-table-column label=\"绩效任务\" prop=\"performanceTask\" min-width=\"200\" show-overflow-tooltip />\n        <el-table-column label=\"目标及措施\" prop=\"targetMeasures\" min-width=\"200\" show-overflow-tooltip />\n        <el-table-column label=\"评价标准\" prop=\"evaluationCriteria\" min-width=\"200\" show-overflow-tooltip />\n        <el-table-column label=\"分值或权重\" prop=\"scoreWeight\" width=\"100\" align=\"center\" />\n        <el-table-column label=\"责任分类\" prop=\"responsibilityCategory\" width=\"120\" align=\"center\" />\n        <el-table-column label=\"完成时限\" prop=\"completionTime\" width=\"120\" align=\"center\" />\n      </el-table>\n    </el-dialog>\n\n    <!-- 共性指标弹窗 -->\n    <el-dialog title=\"共性指标\" :visible.sync=\"commonIndicatorOpen\" width=\"60%\" append-to-body>\n      <el-table :data=\"commonIndicatorList\" style=\"width: 100%\" border>\n        <el-table-column label=\"序号\" prop=\"seqNo\" width=\"60\" align=\"center\" />\n        <el-table-column label=\"责任分类\" prop=\"responsibilityCategory\" min-width=\"250\" show-overflow-tooltip />\n        <el-table-column label=\"评价标准\" prop=\"evaluationStandard\" min-width=\"250\" show-overflow-tooltip />\n        <el-table-column label=\"分值或权重\" prop=\"scoreWeight\" width=\"120\" align=\"center\" />\n        <el-table-column label=\"完成时限\" prop=\"completionTime\" width=\"120\" align=\"center\" />\n      </el-table>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  listPersonnelPlan,\n  getPersonnelPlan,\n  delPersonnelPlan,\n  addPersonnelPlan,\n  updatePersonnelPlan,\n  exportPersonnelPlan,\n  batchExportPersonnelPlan,\n  importPersonnelPlan,\n  downloadTemplate\n} from \"@/api/performance/personnel\";\nimport {\n  listPersonnelTask,\n  getPersonnelTask,\n  delPersonnelTask,\n  addPersonnelTask,\n  updatePersonnelTask\n} from \"@/api/performance/personnelTask\";\nimport {\n  listPersonnelYearlyPlan,\n  getPersonnelYearlyPlan,\n  delPersonnelYearlyPlan,\n  addPersonnelYearlyPlan,\n  updatePersonnelYearlyPlan,\n  getPersonalIndicators,\n  getCommonIndicators,\n  downloadYearlyExcelTemplate\n} from \"@/api/performance/personnelYearly\";\n\nexport default {\n  name: \"PersonnelPlan\",\n  dicts: ['performance_status'],\n  data() {\n    return {\n      // Tab相关\n      activeTab: 'quarterly',\n\n      // 季度绩效计划相关\n      loading: true,\n      ids: [],\n      single: true,\n      multiple: true,\n      showSearch: true,\n      total: 0,\n      planList: [],\n      taskList: [],\n      personalIndicatorList: [],\n      commonIndicatorList: [],\n      currentPlan: null,\n      title: \"\",\n      taskTitle: \"\",\n      open: false,\n      taskOpen: false,\n      personalIndicatorOpen: false,\n      commonIndicatorOpen: false,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        name: null,\n        department: null,\n        status: null\n      },\n      form: {},\n      taskForm: {},\n      rules: {\n        name: [\n          { required: true, message: \"姓名不能为空\", trigger: \"blur\" }\n        ],\n        department: [\n          { required: true, message: \"科室不能为空\", trigger: \"blur\" }\n        ]\n      },\n      taskRules: {\n        taskType: [\n          { required: true, message: \"任务类型不能为空\", trigger: \"blur\" }\n        ],\n        taskContent: [\n          { required: true, message: \"任务内容不能为空\", trigger: \"blur\" }\n        ],\n        score: [\n          { required: true, message: \"分值不能为空\", trigger: \"blur\" }\n        ]\n      },\n      importUrl: process.env.VUE_APP_BASE_API + \"/performance/personnel/import\",\n      uploadHeaders: {},\n\n      // 年度绩效计划相关\n      yearlyLoading: true,\n      yearlyIds: [],\n      yearlyMultiple: true,\n      yearlyTotal: 0,\n      yearlyPlanList: [],\n      yearlyQueryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        name: null,\n        department: null,\n        year: null\n      },\n      yearlyImportUrl: process.env.VUE_APP_BASE_API + \"/performance/personnel/yearly/importExcel\"\n    };\n  },\n  created() {\n    this.getList();\n    // 设置上传认证头\n    this.uploadHeaders = {\n      Authorization: 'Bearer ' + this.$store.getters.token\n    };\n  },\n  methods: {\n    getList() {\n      this.loading = true;\n      listPersonnelPlan(this.queryParams).then(response => {\n        this.planList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    getTaskList(planId) {\n      if (!planId) return;\n      listPersonnelTask({ planId: planId }).then(response => {\n        this.taskList = response.rows || [];\n      });\n    },\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    cancelTask() {\n      this.taskOpen = false;\n      this.resetTask();\n    },\n    reset() {\n      this.form = {\n        id: null,\n        name: null,\n        department: null,\n        status: \"DRAFT\"\n      };\n      this.resetForm(\"form\");\n    },\n    resetTask() {\n      this.taskForm = {\n        id: null,\n        planId: null,\n        taskType: null,\n        taskContent: null,\n        targetMeasures: null,\n        evaluationStandard: null,\n        score: null,\n        responsible: null,\n        deadline: null\n      };\n      this.resetForm(\"taskForm\");\n    },\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    handleCurrentChange(currentRow) {\n      this.currentPlan = currentRow;\n      if (currentRow) {\n        this.getTaskList(currentRow.id);\n      } else {\n        this.taskList = [];\n      }\n    },\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加绩效计划\";\n    },\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids\n      getPersonnelPlan(id).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改绩效计划\";\n      });\n    },\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id != null) {\n            updatePersonnelPlan(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addPersonnelPlan(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    handleDelete(row) {\n      const ids = row.id || this.ids;\n      this.$modal.confirm('是否确认删除绩效计划编号为\"' + ids + '\"的数据项？').then(function() {\n        return delPersonnelPlan(ids);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    handleAddTask() {\n      if (!this.currentPlan) {\n        this.$modal.msgError(\"请先选择一个绩效计划\");\n        return;\n      }\n      this.resetTask();\n      this.taskForm.planId = this.currentPlan.id;\n      this.taskOpen = true;\n      this.taskTitle = \"添加绩效任务\";\n    },\n    handleUpdateTask(row) {\n      this.resetTask();\n      const id = row.id;\n      getPersonnelTask(id).then(response => {\n        this.taskForm = response.data;\n        this.taskOpen = true;\n        this.taskTitle = \"修改绩效任务\";\n      });\n    },\n    submitTaskForm() {\n      this.$refs[\"taskForm\"].validate(valid => {\n        if (valid) {\n          if (this.taskForm.id != null) {\n            updatePersonnelTask(this.taskForm).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.taskOpen = false;\n              this.getTaskList(this.currentPlan.id);\n            });\n          } else {\n            addPersonnelTask(this.taskForm).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.taskOpen = false;\n              this.getTaskList(this.currentPlan.id);\n            });\n          }\n        }\n      });\n    },\n    handleDeleteTask(row) {\n      const id = row.id;\n      this.$modal.confirm('是否确认删除该任务数据项？').then(function() {\n        return delPersonnelTask(id);\n      }).then(() => {\n        this.getTaskList(this.currentPlan.id);\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    handleExportSelected() {\n      if (this.ids.length === 0) {\n        this.$modal.msgWarning(\"请选择要导出的数据\");\n        return;\n      }\n      this.$modal.confirm('是否确认导出选中的' + this.ids.length + '条数据？').then(() => {\n        this.$modal.loading(\"正在导出数据，请稍候...\");\n        batchExportPersonnelPlan(this.ids).then(response => {\n          const blob = new Blob([response], {\n            type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'\n          });\n          const link = document.createElement('a');\n          link.href = window.URL.createObjectURL(blob);\n          link.download = '个人绩效计划_' + new Date().getTime() + '.docx';\n          link.click();\n          window.URL.revokeObjectURL(link.href);\n          this.$modal.closeLoading();\n          this.$modal.msgSuccess(\"导出成功\");\n        }).catch(() => {\n          this.$modal.closeLoading();\n          this.$modal.msgError(\"导出失败\");\n        });\n      }).catch(() => {});\n    },\n    handleImportSuccess(response) {\n      if (response.code === 200) {\n        this.$modal.msgSuccess(\"导入成功\");\n        this.getList();\n      } else {\n        this.$modal.msgError(response.msg || \"导入失败\");\n      }\n    },\n    handleImportError(error) {\n      console.error(\"导入失败:\", error);\n      if (error.status === 401) {\n        this.$modal.msgError(\"认证失败，请重新登录\");\n        this.$store.dispatch('LogOut').then(() => {\n          location.href = '/login';\n        });\n      } else {\n        this.$modal.msgError(\"导入失败，请检查文件格式\");\n      }\n    },\n    beforeImportUpload(file) {\n      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';\n      if (!isExcel) {\n        this.$modal.msgError('只能上传Word文件！');\n      }\n      const isLt2M = file.size / 1024 / 1024 < 2;\n      if (!isLt2M) {\n        this.$modal.msgError('上传文件大小不能超过 2MB!');\n      }\n      return isExcel && isLt2M;\n    },\n    handleDownloadTemplate() {\n      this.$modal.loading(\"正在下载模板，请稍候...\");\n      downloadTemplate().then(response => {\n        const blob = new Blob([response], {\n          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'\n        });\n        const link = document.createElement('a');\n        link.href = window.URL.createObjectURL(blob);\n        link.download = '个人绩效计划模板.docx';\n        link.click();\n        window.URL.revokeObjectURL(link.href);\n        this.$modal.closeLoading();\n      }).catch(() => {\n        this.$modal.closeLoading();\n        this.$modal.msgError(\"下载模板失败\");\n      });\n    },\n    handleViewPersonalIndicators(row) {\n      // 获取个性指标数据 (指标类型 = 1)\n      listPersonnelTask({ planId: row.id, indicatorType: 1 }).then(response => {\n        this.personalIndicatorList = response.rows || [];\n        this.personalIndicatorOpen = true;\n      }).catch(() => {\n        this.$modal.msgError(\"获取个性指标数据失败\");\n      });\n    },\n    handleViewCommonIndicators(row) {\n      // 获取共性指标数据 (指标类型 = 2)\n      listPersonnelTask({ planId: row.id, indicatorType: 2 }).then(response => {\n        this.commonIndicatorList = response.rows || [];\n        this.commonIndicatorOpen = true;\n      }).catch(() => {\n        this.$modal.msgError(\"获取共性指标数据失败\");\n      });\n    },\n\n    // 年度绩效计划相关方法\n    handleTabClick(tab) {\n      if (tab.name === 'yearly') {\n        this.getYearlyList();\n      } else if (tab.name === 'quarterly') {\n        this.getList();\n      }\n    },\n\n    getYearlyList() {\n      this.yearlyLoading = true;\n      listPersonnelYearlyPlan(this.yearlyQueryParams).then(response => {\n        this.yearlyPlanList = response.rows;\n        this.yearlyTotal = response.total;\n        this.yearlyLoading = false;\n      });\n    },\n\n    handleYearlyQuery() {\n      this.yearlyQueryParams.pageNum = 1;\n      this.getYearlyList();\n    },\n\n    resetYearlyQuery() {\n      this.resetForm(\"yearlyQueryForm\");\n      this.handleYearlyQuery();\n    },\n\n    handleYearlySelectionChange(selection) {\n      this.yearlyIds = selection.map(item => item.id);\n      this.yearlyMultiple = !selection.length;\n    },\n\n    handleYearlyDelete(row) {\n      const ids = row ? [row.id] : this.yearlyIds;\n      const message = row\n        ? `是否确认删除\"${row.name}\"的年度绩效计划数据项？`\n        : `是否确认删除选中的${ids.length}条年度绩效计划数据项？`;\n\n      this.$modal.confirm(message).then(function() {\n        return delPersonnelYearlyPlan(ids.join(','));\n      }).then(() => {\n        this.getYearlyList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n\n    beforeYearlyImportUpload(file) {\n      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\n                      file.type === 'application/vnd.ms-excel';\n      if (!isExcel) {\n        this.$modal.msgError('只能上传Excel文件格式(.xlsx或.xls)!');\n      }\n      return isExcel;\n    },\n\n    handleYearlyImportSuccess(response) {\n      if (response.code === 200) {\n        this.$modal.msgSuccess('导入成功');\n        this.getYearlyList();\n      } else {\n        this.$modal.msgError(response.msg || '导入失败');\n      }\n    },\n\n    handleYearlyImportError(error) {\n      console.error(\"导入失败:\", error);\n      if (error.status === 401) {\n        this.$modal.msgError(\"认证失败，请重新登录\");\n        this.$store.dispatch('LogOut').then(() => {\n          location.href = '/login';\n        });\n      } else {\n        this.$modal.msgError(\"导入失败，请检查文件格式\");\n      }\n    },\n\n    handleYearlyDownloadTemplate() {\n      downloadYearlyExcelTemplate().then(response => {\n        const blob = new Blob([response], {\n          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\n        });\n        const link = document.createElement('a');\n        link.href = window.URL.createObjectURL(blob);\n        link.download = '个人年度绩效计划模板.xlsx';\n        link.click();\n        window.URL.revokeObjectURL(link.href);\n      });\n    },\n\n    handleViewYearlyPersonalIndicators(row) {\n      getPersonalIndicators(row.id).then(response => {\n        this.personalIndicatorList = response.data || [];\n        this.personalIndicatorOpen = true;\n      }).catch(() => {\n        this.$modal.msgError(\"获取个性指标数据失败\");\n      });\n    },\n\n    handleViewYearlyCommonIndicators(row) {\n      getCommonIndicators(row.id).then(response => {\n        this.commonIndicatorList = response.data || [];\n        this.commonIndicatorOpen = true;\n      }).catch(() => {\n        this.$modal.msgError(\"获取共性指标数据失败\");\n      });\n    }\n  }\n};\n</script>\n\n<style scoped>\n.box-card {\n  margin-bottom: 20px;\n}\n</style>\n"]}]}