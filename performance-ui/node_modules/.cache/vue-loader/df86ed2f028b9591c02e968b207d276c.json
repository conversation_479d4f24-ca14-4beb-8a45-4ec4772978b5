{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/system/user/profile/userAvatar.vue?vue&type=style&index=0&id=e81d90b0&scoped=true&lang=scss", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/system/user/profile/userAvatar.vue", "mtime": 1753510684536}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/css-loader/dist/cjs.js", "mtime": 1753510683027}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1753510684373}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/postcss-loader/src/index.js", "mtime": 1753510684004}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/sass-loader/dist/cjs.js", "mtime": 1753510684159}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi51c2VyLWluZm8taGVhZCB7CiAgcG9zaXRpb246IHJlbGF0aXZlOwogIGRpc3BsYXk6IGlubGluZS1ibG9jazsKICBoZWlnaHQ6IDEyMHB4Owp9CgoudXNlci1pbmZvLWhlYWQ6aG92ZXI6YWZ0ZXIgewogIGNvbnRlbnQ6ICcrJzsKICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgbGVmdDogMDsKICByaWdodDogMDsKICB0b3A6IDA7CiAgYm90dG9tOiAwOwogIGNvbG9yOiAjZWVlOwogIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC41KTsKICBmb250LXNpemU6IDI0cHg7CiAgZm9udC1zdHlsZTogbm9ybWFsOwogIC13ZWJraXQtZm9udC1zbW9vdGhpbmc6IGFudGlhbGlhc2VkOwogIC1tb3otb3N4LWZvbnQtc21vb3RoaW5nOiBncmF5c2NhbGU7CiAgY3Vyc29yOiBwb2ludGVyOwogIGxpbmUtaGVpZ2h0OiAxMTBweDsKICBib3JkZXItcmFkaXVzOiA1MCU7Cn0K"}, {"version": 3, "sources": ["userAvatar.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgKA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "userAvatar.vue", "sourceRoot": "src/views/system/user/profile", "sourcesContent": ["<template>\n  <div>\n    <div class=\"user-info-head\" @click=\"editCropper()\"><img v-bind:src=\"options.img\" title=\"点击上传头像\" class=\"img-circle img-lg\" /></div>\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body @opened=\"modalOpened\"  @close=\"closeDialog\">\n      <el-row>\n        <el-col :xs=\"24\" :md=\"12\" :style=\"{height: '350px'}\">\n          <vue-cropper\n            ref=\"cropper\"\n            :img=\"options.img\"\n            :info=\"true\"\n            :autoCrop=\"options.autoCrop\"\n            :autoCropWidth=\"options.autoCropWidth\"\n            :autoCropHeight=\"options.autoCropHeight\"\n            :fixedBox=\"options.fixedBox\"\n            :outputType=\"options.outputType\"\n            @realTime=\"realTime\"\n            v-if=\"visible\"\n          />\n        </el-col>\n        <el-col :xs=\"24\" :md=\"12\" :style=\"{height: '350px'}\">\n          <div class=\"avatar-upload-preview\">\n            <img :src=\"previews.url\" :style=\"previews.img\" />\n          </div>\n        </el-col>\n      </el-row>\n      <br />\n      <el-row>\n        <el-col :lg=\"2\" :sm=\"3\" :xs=\"3\">\n          <el-upload action=\"#\" :http-request=\"requestUpload\" :show-file-list=\"false\" :before-upload=\"beforeUpload\">\n            <el-button size=\"small\">\n              选择\n              <i class=\"el-icon-upload el-icon--right\"></i>\n            </el-button>\n          </el-upload>\n        </el-col>\n        <el-col :lg=\"{span: 1, offset: 2}\" :sm=\"2\" :xs=\"2\">\n          <el-button icon=\"el-icon-plus\" size=\"small\" @click=\"changeScale(1)\"></el-button>\n        </el-col>\n        <el-col :lg=\"{span: 1, offset: 1}\" :sm=\"2\" :xs=\"2\">\n          <el-button icon=\"el-icon-minus\" size=\"small\" @click=\"changeScale(-1)\"></el-button>\n        </el-col>\n        <el-col :lg=\"{span: 1, offset: 1}\" :sm=\"2\" :xs=\"2\">\n          <el-button icon=\"el-icon-refresh-left\" size=\"small\" @click=\"rotateLeft()\"></el-button>\n        </el-col>\n        <el-col :lg=\"{span: 1, offset: 1}\" :sm=\"2\" :xs=\"2\">\n          <el-button icon=\"el-icon-refresh-right\" size=\"small\" @click=\"rotateRight()\"></el-button>\n        </el-col>\n        <el-col :lg=\"{span: 2, offset: 6}\" :sm=\"2\" :xs=\"2\">\n          <el-button type=\"primary\" size=\"small\" @click=\"uploadImg()\">提 交</el-button>\n        </el-col>\n      </el-row>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport store from \"@/store\"\nimport { VueCropper } from \"vue-cropper\"\nimport { uploadAvatar } from \"@/api/system/user\"\nimport { debounce } from '@/utils'\n\nexport default {\n  components: { VueCropper },\n  data() {\n    return {\n      // 是否显示弹出层\n      open: false,\n      // 是否显示cropper\n      visible: false,\n      // 弹出层标题\n      title: \"修改头像\",\n      options: {\n        img: store.getters.avatar,  //裁剪图片的地址\n        autoCrop: true,             // 是否默认生成截图框\n        autoCropWidth: 200,         // 默认生成截图框宽度\n        autoCropHeight: 200,        // 默认生成截图框高度\n        fixedBox: true,             // 固定截图框大小 不允许改变\n        outputType:\"png\",           // 默认生成截图为PNG格式\n        filename: 'avatar'          // 文件名称\n      },\n      previews: {},\n      resizeHandler: null\n    }\n  },\n  methods: {\n    // 编辑头像\n    editCropper() {\n      this.open = true\n    },\n    // 打开弹出层结束时的回调\n    modalOpened() {\n      this.visible = true\n      if (!this.resizeHandler) {\n        this.resizeHandler = debounce(() => {\n          this.refresh()\n        }, 100)\n      }\n      window.addEventListener(\"resize\", this.resizeHandler)\n    },\n    // 刷新组件\n    refresh() {\n      this.$refs.cropper.refresh()\n    },\n    // 覆盖默认的上传行为\n    requestUpload() {\n    },\n    // 向左旋转\n    rotateLeft() {\n      this.$refs.cropper.rotateLeft()\n    },\n    // 向右旋转\n    rotateRight() {\n      this.$refs.cropper.rotateRight()\n    },\n    // 图片缩放\n    changeScale(num) {\n      num = num || 1\n      this.$refs.cropper.changeScale(num)\n    },\n    // 上传预处理\n    beforeUpload(file) {\n      if (file.type.indexOf(\"image/\") == -1) {\n        this.$modal.msgError(\"文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。\")\n      } else {\n        const reader = new FileReader()\n        reader.readAsDataURL(file)\n        reader.onload = () => {\n          this.options.img = reader.result\n          this.options.filename = file.name\n        }\n      }\n    },\n    // 上传图片\n    uploadImg() {\n      this.$refs.cropper.getCropBlob(data => {\n        let formData = new FormData()\n        formData.append(\"avatarfile\", data, this.options.filename)\n        uploadAvatar(formData).then(response => {\n          this.open = false\n          this.options.img = process.env.VUE_APP_BASE_API + response.imgUrl\n          store.commit('SET_AVATAR', this.options.img)\n          this.$modal.msgSuccess(\"修改成功\")\n          this.visible = false\n        })\n      })\n    },\n    // 实时预览\n    realTime(data) {\n      this.previews = data\n    },\n    // 关闭窗口\n    closeDialog() {\n      this.options.img = store.getters.avatar\n      this.visible = false\n      window.removeEventListener(\"resize\", this.resizeHandler)\n    }\n  }\n}\n</script>\n<style scoped lang=\"scss\">\n.user-info-head {\n  position: relative;\n  display: inline-block;\n  height: 120px;\n}\n\n.user-info-head:hover:after {\n  content: '+';\n  position: absolute;\n  left: 0;\n  right: 0;\n  top: 0;\n  bottom: 0;\n  color: #eee;\n  background: rgba(0, 0, 0, 0.5);\n  font-size: 24px;\n  font-style: normal;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  cursor: pointer;\n  line-height: 110px;\n  border-radius: 50%;\n}\n</style>\n"]}]}