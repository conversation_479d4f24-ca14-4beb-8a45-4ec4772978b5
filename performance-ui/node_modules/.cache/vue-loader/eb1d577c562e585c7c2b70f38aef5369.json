{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/tool/build/index.vue?vue&type=style&index=0&id=39cfdb14&lang=scss", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/tool/build/index.vue", "mtime": 1753510684536}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/css-loader/dist/cjs.js", "mtime": 1753510683027}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1753510684373}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/postcss-loader/src/index.js", "mtime": 1753510684004}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/sass-loader/dist/cjs.js", "mtime": 1753510684159}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmVkaXRvci10YWJzewogIGJhY2tncm91bmQ6ICMxMjEzMTU7CiAgLmVsLXRhYnNfX2hlYWRlcnsKICAgIG1hcmdpbjogMDsKICAgIGJvcmRlci1ib3R0b20tY29sb3I6ICMxMjEzMTU7CiAgICAuZWwtdGFic19fbmF2ewogICAgICBib3JkZXItY29sb3I6ICMxMjEzMTU7CiAgICB9CiAgfQogIC5lbC10YWJzX19pdGVtewogICAgaGVpZ2h0OiAzMnB4OwogICAgbGluZS1oZWlnaHQ6IDMycHg7CiAgICBjb2xvcjogIzg4OGE4ZTsKICAgIGJvcmRlci1sZWZ0OiAxcHggc29saWQgIzEyMTMxNSAhaW1wb3J0YW50OwogICAgYmFja2dyb3VuZDogIzM2MzYzNjsKICAgIG1hcmdpbi1yaWdodDogNXB4OwogICAgdXNlci1zZWxlY3Q6IG5vbmU7CiAgfQogIC5lbC10YWJzX19pdGVtLmlzLWFjdGl2ZXsKICAgIGJhY2tncm91bmQ6ICMxZTFlMWU7CiAgICBib3JkZXItYm90dG9tLWNvbG9yOiAjMWUxZTFlIWltcG9ydGFudDsKICAgIGNvbG9yOiAjZmZmOwogIH0KICAuZWwtaWNvbi1lZGl0ewogICAgY29sb3I6ICNmMWZhOGM7CiAgfQogIC5lbC1pY29uLWRvY3VtZW50ewogICAgY29sb3I6ICNhOTU4MTI7CiAgfQp9CgovLyBob21lCi5yaWdodC1zY3JvbGxiYXIgewogIC5lbC1zY3JvbGxiYXJfX3ZpZXcgewogICAgcGFkZGluZzogMTJweCAxOHB4IDE1cHggMTVweDsKICB9Cn0KLmxlZnQtc2Nyb2xsYmFyIC5lbC1zY3JvbGxiYXJfX3dyYXAgewogIGJveC1zaXppbmc6IGJvcmRlci1ib3g7CiAgb3ZlcmZsb3cteDogaGlkZGVuICFpbXBvcnRhbnQ7CiAgbWFyZ2luLWJvdHRvbTogMCAhaW1wb3J0YW50Owp9Ci5jZW50ZXItdGFic3sKICAuZWwtdGFic19faGVhZGVyewogICAgbWFyZ2luLWJvdHRvbTogMCFpbXBvcnRhbnQ7CiAgfQogIC5lbC10YWJzX19pdGVtewogICAgd2lkdGg6IDUwJTsKICAgIHRleHQtYWxpZ246IGNlbnRlcjsKICB9CiAgLmVsLXRhYnNfX25hdnsKICAgIHdpZHRoOiAxMDAlOwogIH0KfQoucmVnLWl0ZW17CiAgcGFkZGluZzogMTJweCA2cHg7CiAgYmFja2dyb3VuZDogI2Y4ZjhmODsKICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgYm9yZGVyLXJhZGl1czogNHB4OwogIC5jbG9zZS1idG57CiAgICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgICByaWdodDogLTZweDsKICAgIHRvcDogLTZweDsKICAgIGRpc3BsYXk6IGJsb2NrOwogICAgd2lkdGg6IDE2cHg7CiAgICBoZWlnaHQ6IDE2cHg7CiAgICBsaW5lLWhlaWdodDogMTZweDsKICAgIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC4yKTsKICAgIGJvcmRlci1yYWRpdXM6IDUwJTsKICAgIGNvbG9yOiAjZmZmOwogICAgdGV4dC1hbGlnbjogY2VudGVyOwogICAgei1pbmRleDogMTsKICAgIGN1cnNvcjogcG9pbnRlcjsKICAgIGZvbnQtc2l6ZTogMTJweDsKICAgICY6aG92ZXJ7CiAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjEwLCAyMywgMjMsIDAuNSkKICAgIH0KICB9CiAgJiArIC5yZWctaXRlbXsKICAgIG1hcmdpbi10b3A6IDE4cHg7CiAgfQp9Ci5hY3Rpb24tYmFyewogICYgLmVsLWJ1dHRvbisuZWwtYnV0dG9uIHsKICAgIG1hcmdpbi1sZWZ0OiAxNXB4OwogIH0KICAmIGkgewogICAgZm9udC1zaXplOiAyMHB4OwogICAgdmVydGljYWwtYWxpZ246IG1pZGRsZTsKICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsKICAgIHRvcDogLTFweDsKICB9Cn0KCi5jdXN0b20tdHJlZS1ub2RlewogIHdpZHRoOiAxMDAlOwogIGZvbnQtc2l6ZTogMTRweDsKICAubm9kZS1vcGVyYXRpb257CiAgICBmbG9hdDogcmlnaHQ7CiAgfQogIGlbY2xhc3MqPSJlbC1pY29uIl0gKyBpW2NsYXNzKj0iZWwtaWNvbiJdewogICAgbWFyZ2luLWxlZnQ6IDZweDsKICB9CiAgLmVsLWljb24tcGx1c3sKICAgIGNvbG9yOiAjNDA5RUZGOwogIH0KICAuZWwtaWNvbi1kZWxldGV7CiAgICBjb2xvcjogIzE1N2EwYzsKICB9Cn0KCi5sZWZ0LXNjcm9sbGJhciAuZWwtc2Nyb2xsYmFyX192aWV3ewogIG92ZXJmbG93LXg6IGhpZGRlbjsKfQoKLmVsLXJhdGV7CiAgZGlzcGxheTogaW5saW5lLWJsb2NrOwogIHZlcnRpY2FsLWFsaWduOiB0ZXh0LXRvcDsKfQouZWwtdXBsb2FkX190aXB7CiAgbGluZS1oZWlnaHQ6IDEuMjsKfQoKJHNlbGVjdGVkQ29sb3I6ICNmNmY3ZmY7CiRsaWdodGVyQmx1ZTogIzQwOUVGRjsKCi5jb250YWluZXIgewogIHBvc2l0aW9uOiByZWxhdGl2ZTsKICB3aWR0aDogMTAwJTsKICBoZWlnaHQ6IDEwMCU7Cn0KCi5jb21wb25lbnRzLWxpc3QgewogIHBhZGRpbmc6IDhweDsKICBib3gtc2l6aW5nOiBib3JkZXItYm94OwogIGhlaWdodDogMTAwJTsKICAuY29tcG9uZW50cy1pdGVtIHsKICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsKICAgIHdpZHRoOiA0OCU7CiAgICBtYXJnaW46IDElOwogICAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDBtcyAhaW1wb3J0YW50OwogIH0KfQouY29tcG9uZW50cy1kcmFnZ2FibGV7CiAgcGFkZGluZy1ib3R0b206IDIwcHg7Cn0KLmNvbXBvbmVudHMtdGl0bGV7CiAgZm9udC1zaXplOiAxNHB4OwogIGNvbG9yOiAjMjIyOwogIG1hcmdpbjogNnB4IDJweDsKICAuc3ZnLWljb257CiAgICBjb2xvcjogIzY2NjsKICAgIGZvbnQtc2l6ZTogMThweDsKICB9Cn0KCi5jb21wb25lbnRzLWJvZHkgewogIHBhZGRpbmc6IDhweCAxMHB4OwogIGJhY2tncm91bmQ6ICRzZWxlY3RlZENvbG9yOwogIGZvbnQtc2l6ZTogMTJweDsKICBjdXJzb3I6IG1vdmU7CiAgYm9yZGVyOiAxcHggZGFzaGVkICRzZWxlY3RlZENvbG9yOwogIGJvcmRlci1yYWRpdXM6IDNweDsKICAuc3ZnLWljb257CiAgICBjb2xvcjogIzc3NzsKICAgIGZvbnQtc2l6ZTogMTVweDsKICB9CiAgJjpob3ZlciB7CiAgICBib3JkZXI6IDFweCBkYXNoZWQgIzc4N2JlODsKICAgIGNvbG9yOiAjNzg3YmU4OwogICAgLnN2Zy1pY29uIHsKICAgICAgY29sb3I6ICM3ODdiZTg7CiAgICB9CiAgfQp9CgoubGVmdC1ib2FyZCB7CiAgd2lkdGg6IDI2MHB4OwogIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICBsZWZ0OiAwOwogIHRvcDogMDsKICBoZWlnaHQ6IDEwMHZoOwp9Ci5sZWZ0LXNjcm9sbGJhcnsKICBoZWlnaHQ6IGNhbGMoMTAwdmggLSA0MnB4KTsKICBvdmVyZmxvdzogaGlkZGVuOwp9Ci5jZW50ZXItc2Nyb2xsYmFyIHsKICBoZWlnaHQ6IGNhbGMoMTAwdmggLSA0MnB4KTsKICBvdmVyZmxvdzogaGlkZGVuOwogIGJvcmRlci1sZWZ0OiAxcHggc29saWQgI2YxZThlODsKICBib3JkZXItcmlnaHQ6IDFweCBzb2xpZCAjZjFlOGU4OwogIGJveC1zaXppbmc6IGJvcmRlci1ib3g7Cn0KLmNlbnRlci1ib2FyZCB7CiAgaGVpZ2h0OiAxMDB2aDsKICB3aWR0aDogYXV0bzsKICBtYXJnaW46IDAgMzUwcHggMCAyNjBweDsKICBib3gtc2l6aW5nOiBib3JkZXItYm94Owp9Ci5lbXB0eS1pbmZvewogIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICB0b3A6IDQ2JTsKICBsZWZ0OiAwOwogIHJpZ2h0OiAwOwogIHRleHQtYWxpZ246IGNlbnRlcjsKICBmb250LXNpemU6IDE4cHg7CiAgY29sb3I6ICNjY2IxZWE7CiAgbGV0dGVyLXNwYWNpbmc6IDRweDsKfQouYWN0aW9uLWJhcnsKICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgaGVpZ2h0OiA0MnB4OwogIHRleHQtYWxpZ246IHJpZ2h0OwogIHBhZGRpbmc6IDAgMTVweDsKICBib3gtc2l6aW5nOiBib3JkZXItYm94OzsKICBib3JkZXI6IDFweCBzb2xpZCAjZjFlOGU4OwogIGJvcmRlci10b3A6IG5vbmU7CiAgYm9yZGVyLWxlZnQ6IG5vbmU7CiAgLmRlbGV0ZS1idG57CiAgICBjb2xvcjogI0Y1NkM2QzsKICB9Cn0KLmxvZ28td3JhcHBlcnsKICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgaGVpZ2h0OiA0MnB4OwogIGJhY2tncm91bmQ6ICNmZmY7CiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmMWU4ZTg7CiAgYm94LXNpemluZzogYm9yZGVyLWJveDsKfQoubG9nb3sKICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgbGVmdDogMTJweDsKICB0b3A6IDZweDsKICBsaW5lLWhlaWdodDogMzBweDsKICBjb2xvcjogIzAwYWZmZjsKICBmb250LXdlaWdodDogNjAwOwogIGZvbnQtc2l6ZTogMTdweDsKICB3aGl0ZS1zcGFjZTogbm93cmFwOwogID4gaW1newogICAgd2lkdGg6IDMwcHg7CiAgICBoZWlnaHQ6IDMwcHg7CiAgICB2ZXJ0aWNhbC1hbGlnbjogdG9wOwogIH0KICAuZ2l0aHViewogICAgZGlzcGxheTogaW5saW5lLWJsb2NrOwogICAgdmVydGljYWwtYWxpZ246IHN1YjsKICAgIG1hcmdpbi1sZWZ0OiAxNXB4OwogICAgPiBpbWd7CiAgICAgIGhlaWdodDogMjJweDsKICAgIH0KICB9Cn0KCi5jZW50ZXItYm9hcmQtcm93IHsKICBwYWRkaW5nOiAxMnB4IDEycHggMTVweCAxMnB4OwogIGJveC1zaXppbmc6IGJvcmRlci1ib3g7CiAgJiA+IC5lbC1mb3JtIHsKICAgIC8vIDY5ID0gMTIrMTUrNDIKICAgIGhlaWdodDogY2FsYygxMDB2aCAtIDY5cHgpOwogIH0KfQouZHJhd2luZy1ib2FyZCB7CiAgaGVpZ2h0OiAxMDAlOwogIHBvc2l0aW9uOiByZWxhdGl2ZTsKICAuY29tcG9uZW50cy1ib2R5IHsKICAgIHBhZGRpbmc6IDA7CiAgICBtYXJnaW46IDA7CiAgICBmb250LXNpemU6IDA7CiAgfQogIC5zb3J0YWJsZS1naG9zdCB7CiAgICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgICBkaXNwbGF5OiBibG9jazsKICAgIG92ZXJmbG93OiBoaWRkZW47CiAgICAmOjpiZWZvcmUgewogICAgICBjb250ZW50OiAiICI7CiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICAgICAgbGVmdDogMDsKICAgICAgcmlnaHQ6IDA7CiAgICAgIHRvcDogMDsKICAgICAgaGVpZ2h0OiAzcHg7CiAgICAgIGJhY2tncm91bmQ6IHJnYig4OSwgODksIDIyMyk7CiAgICAgIHotaW5kZXg6IDI7CiAgICB9CiAgfQogIC5jb21wb25lbnRzLWl0ZW0uc29ydGFibGUtZ2hvc3QgewogICAgd2lkdGg6IDEwMCU7CiAgICBoZWlnaHQ6IDYwcHg7CiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAkc2VsZWN0ZWRDb2xvcjsKICB9CiAgLmFjdGl2ZS1mcm9tLWl0ZW0gewogICAgJiA+IC5lbC1mb3JtLWl0ZW17CiAgICAgIGJhY2tncm91bmQ6ICRzZWxlY3RlZENvbG9yOwogICAgICBib3JkZXItcmFkaXVzOiA2cHg7CiAgICB9CiAgICAmID4gLmRyYXdpbmctaXRlbS1jb3B5LCAmID4gLmRyYXdpbmctaXRlbS1kZWxldGV7CiAgICAgIGRpc3BsYXk6IGluaXRpYWw7CiAgICB9CiAgICAmID4gLmNvbXBvbmVudC1uYW1lewogICAgICBjb2xvcjogJGxpZ2h0ZXJCbHVlOwogICAgfQogIH0KICAuZWwtZm9ybS1pdGVtewogICAgbWFyZ2luLWJvdHRvbTogMTVweDsKICB9Cn0KLmRyYXdpbmctaXRlbXsKICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgY3Vyc29yOiBtb3ZlOwogICYudW5mb2N1cy1ib3JkZXJlZDpub3QoLmFjdGl2ZUZyb21JdGVtKSA+IGRpdjpmaXJzdC1jaGlsZCAgewogICAgYm9yZGVyOiAxcHggZGFzaGVkICNjY2M7CiAgfQogIC5lbC1mb3JtLWl0ZW17CiAgICBwYWRkaW5nOiAxMnB4IDEwcHg7CiAgfQp9Ci5kcmF3aW5nLXJvdy1pdGVtewogIHBvc2l0aW9uOiByZWxhdGl2ZTsKICBjdXJzb3I6IG1vdmU7CiAgYm94LXNpemluZzogYm9yZGVyLWJveDsKICBib3JkZXI6IDFweCBkYXNoZWQgI2NjYzsKICBib3JkZXItcmFkaXVzOiAzcHg7CiAgcGFkZGluZzogMCAycHg7CiAgbWFyZ2luLWJvdHRvbTogMTVweDsKICAuZHJhd2luZy1yb3ctaXRlbSB7CiAgICBtYXJnaW4tYm90dG9tOiAycHg7CiAgfQogIC5lbC1jb2x7CiAgICBtYXJnaW4tdG9wOiAyMnB4OwogIH0KICAuZWwtZm9ybS1pdGVtewogICAgbWFyZ2luLWJvdHRvbTogMDsKICB9CiAgLmRyYWctd3JhcHBlcnsKICAgIG1pbi1oZWlnaHQ6IDgwcHg7CiAgfQogICYuYWN0aXZlLWZyb20taXRlbXsKICAgIGJvcmRlcjogMXB4IGRhc2hlZCAkbGlnaHRlckJsdWU7CiAgfQogIC5jb21wb25lbnQtbmFtZXsKICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICAgIHRvcDogMDsKICAgIGxlZnQ6IDA7CiAgICBmb250LXNpemU6IDEycHg7CiAgICBjb2xvcjogI2JiYjsKICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsKICAgIHBhZGRpbmc6IDAgNnB4OwogIH0KfQouZHJhd2luZy1pdGVtLCAuZHJhd2luZy1yb3ctaXRlbXsKICAmOmhvdmVyIHsKICAgICYgPiAuZWwtZm9ybS1pdGVtewogICAgICBiYWNrZ3JvdW5kOiAkc2VsZWN0ZWRDb2xvcjsKICAgICAgYm9yZGVyLXJhZGl1czogNnB4OwogICAgfQogICAgJiA+IC5kcmF3aW5nLWl0ZW0tY29weSwgJiA+IC5kcmF3aW5nLWl0ZW0tZGVsZXRlewogICAgICBkaXNwbGF5OiBpbml0aWFsOwogICAgfQogIH0KICAmID4gLmRyYXdpbmctaXRlbS1jb3B5LCAmID4gLmRyYXdpbmctaXRlbS1kZWxldGV7CiAgICBkaXNwbGF5OiBub25lOwogICAgcG9zaXRpb246IGFic29sdXRlOwogICAgdG9wOiAtMTBweDsKICAgIHdpZHRoOiAyMnB4OwogICAgaGVpZ2h0OiAyMnB4OwogICAgbGluZS1oZWlnaHQ6IDIycHg7CiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgICBib3JkZXItcmFkaXVzOiA1MCU7CiAgICBmb250LXNpemU6IDEycHg7CiAgICBib3JkZXI6IDFweCBzb2xpZDsKICAgIGN1cnNvcjogcG9pbnRlcjsKICAgIHotaW5kZXg6IDE7CiAgfQogICYgPiAuZHJhd2luZy1pdGVtLWNvcHl7CiAgICByaWdodDogNTZweDsKICAgIGJvcmRlci1jb2xvcjogJGxpZ2h0ZXJCbHVlOwogICAgY29sb3I6ICRsaWdodGVyQmx1ZTsKICAgIGJhY2tncm91bmQ6ICNmZmY7CiAgICAmOmhvdmVyewogICAgICBiYWNrZ3JvdW5kOiAkbGlnaHRlckJsdWU7CiAgICAgIGNvbG9yOiAjZmZmOwogICAgfQogIH0KICAmID4gLmRyYXdpbmctaXRlbS1kZWxldGV7CiAgICByaWdodDogMjRweDsKICAgIGJvcmRlci1jb2xvcjogI0Y1NkM2QzsKICAgIGNvbG9yOiAjRjU2QzZDOwogICAgYmFja2dyb3VuZDogI2ZmZjsKICAgICY6aG92ZXJ7CiAgICAgIGJhY2tncm91bmQ6ICNGNTZDNkM7CiAgICAgIGNvbG9yOiAjZmZmOwogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/tool/build", "sourcesContent": ["<template>\n  <div class=\"container\">\n    <div class=\"left-board\">\n      <div class=\"logo-wrapper\">\n        <div class=\"logo\">\n          <img :src=\"logo\" alt=\"logo\"> Form Generator\n        </div>\n      </div>\n      <el-scrollbar class=\"left-scrollbar\">\n        <div class=\"components-list\">\n          <div class=\"components-title\">\n            <svg-icon icon-class=\"component\" />输入型组件\n          </div>\n          <draggable\n            class=\"components-draggable\"\n            :list=\"inputComponents\"\n            :group=\"{ name: 'componentsGroup', pull: 'clone', put: false }\"\n            :clone=\"cloneComponent\"\n            draggable=\".components-item\"\n            :sort=\"false\"\n            @end=\"onEnd\"\n          >\n            <div\n              v-for=\"(element, index) in inputComponents\" :key=\"index\" class=\"components-item\"\n              @click=\"addComponent(element)\"\n            >\n              <div class=\"components-body\">\n                <svg-icon :icon-class=\"element.tagIcon\" />\n                {{ element.label }}\n              </div>\n            </div>\n          </draggable>\n          <div class=\"components-title\">\n            <svg-icon icon-class=\"component\" />选择型组件\n          </div>\n          <draggable\n            class=\"components-draggable\"\n            :list=\"selectComponents\"\n            :group=\"{ name: 'componentsGroup', pull: 'clone', put: false }\"\n            :clone=\"cloneComponent\"\n            draggable=\".components-item\"\n            :sort=\"false\"\n            @end=\"onEnd\"\n          >\n            <div\n              v-for=\"(element, index) in selectComponents\"\n              :key=\"index\"\n              class=\"components-item\"\n              @click=\"addComponent(element)\"\n            >\n              <div class=\"components-body\">\n                <svg-icon :icon-class=\"element.tagIcon\" />\n                {{ element.label }}\n              </div>\n            </div>\n          </draggable>\n          <div class=\"components-title\">\n            <svg-icon icon-class=\"component\" /> 布局型组件\n          </div>\n          <draggable\n            class=\"components-draggable\" :list=\"layoutComponents\"\n            :group=\"{ name: 'componentsGroup', pull: 'clone', put: false }\" :clone=\"cloneComponent\"\n            draggable=\".components-item\" :sort=\"false\" @end=\"onEnd\"\n          >\n            <div\n              v-for=\"(element, index) in layoutComponents\" :key=\"index\" class=\"components-item\"\n              @click=\"addComponent(element)\"\n            >\n              <div class=\"components-body\">\n                <svg-icon :icon-class=\"element.tagIcon\" />\n                {{ element.label }}\n              </div>\n            </div>\n          </draggable>\n        </div>\n      </el-scrollbar>\n    </div>\n\n    <div class=\"center-board\">\n      <div class=\"action-bar\">\n        <el-button icon=\"el-icon-download\" type=\"text\" @click=\"download\">\n          导出vue文件\n        </el-button>\n        <el-button class=\"copy-btn-main\" icon=\"el-icon-document-copy\" type=\"text\" @click=\"copy\">\n          复制代码\n        </el-button>\n        <el-button class=\"delete-btn\" icon=\"el-icon-delete\" type=\"text\" @click=\"empty\">\n          清空\n        </el-button>\n      </div>\n      <el-scrollbar class=\"center-scrollbar\">\n        <el-row class=\"center-board-row\" :gutter=\"formConf.gutter\">\n          <el-form\n            :size=\"formConf.size\"\n            :label-position=\"formConf.labelPosition\"\n            :disabled=\"formConf.disabled\"\n            :label-width=\"formConf.labelWidth + 'px'\"\n          >\n            <draggable class=\"drawing-board\" :list=\"drawingList\" :animation=\"340\" group=\"componentsGroup\">\n              <draggable-item\n                v-for=\"(element, index) in drawingList\"\n                :key=\"element.renderKey\"\n                :drawing-list=\"drawingList\"\n                :element=\"element\"\n                :index=\"index\"\n                :active-id=\"activeId\"\n                :form-conf=\"formConf\"\n                @activeItem=\"activeFormItem\"\n                @copyItem=\"drawingItemCopy\"\n                @deleteItem=\"drawingItemDelete\"\n              />\n            </draggable>\n            <div v-show=\"!drawingList.length\" class=\"empty-info\">\n              从左侧拖入或点选组件进行表单设计\n            </div>\n          </el-form>\n        </el-row>\n      </el-scrollbar>\n    </div>\n\n    <right-panel\n      :active-data=\"activeData\"\n      :form-conf=\"formConf\"\n      :show-field=\"!!drawingList.length\"\n      @tag-change=\"tagChange\"\n    />\n\n    <code-type-dialog\n      :visible.sync=\"dialogVisible\"\n      title=\"选择生成类型\"\n      :show-file-name=\"showFileName\"\n      @confirm=\"generate\"\n    />\n    <input id=\"copyNode\" type=\"hidden\">\n  </div>\n</template>\n\n<script>\nimport draggable from 'vuedraggable'\nimport beautifier from 'js-beautify'\nimport ClipboardJS from 'clipboard'\nimport render from '@/utils/generator/render'\nimport RightPanel from './RightPanel'\nimport { inputComponents, selectComponents, layoutComponents, formConf } from '@/utils/generator/config'\nimport { beautifierConf, titleCase } from '@/utils/index'\nimport { makeUpHtml, vueTemplate, vueScript, cssStyle } from '@/utils/generator/html'\nimport { makeUpJs } from '@/utils/generator/js'\nimport { makeUpCss } from '@/utils/generator/css'\nimport drawingDefault from '@/utils/generator/drawingDefault'\nimport logo from '@/assets/logo/logo.png'\nimport CodeTypeDialog from './CodeTypeDialog'\nimport DraggableItem from './DraggableItem'\n\nlet oldActiveId\nlet tempActiveData\n\nexport default {\n  components: {\n    draggable,\n    render,\n    RightPanel,\n    CodeTypeDialog,\n    DraggableItem\n  },\n  data() {\n    return {\n      logo,\n      idGlobal: 100,\n      formConf,\n      inputComponents,\n      selectComponents,\n      layoutComponents,\n      labelWidth: 100,\n      drawingList: drawingDefault,\n      drawingData: {},\n      activeId: drawingDefault[0].formId,\n      drawerVisible: false,\n      formData: {},\n      dialogVisible: false,\n      generateConf: null,\n      showFileName: false,\n      activeData: drawingDefault[0]\n    }\n  },\n  created() {\n    // 防止 firefox 下 拖拽 会新打卡一个选项卡\n    document.body.ondrop = event => {\n      event.preventDefault()\n      event.stopPropagation()\n    }\n  },\n  watch: {\n    'activeData.label': function (val, oldVal) {\n      if (\n        this.activeData.placeholder === undefined\n        || !this.activeData.tag\n        || oldActiveId !== this.activeId\n      ) {\n        return\n      }\n      this.activeData.placeholder = this.activeData.placeholder.replace(oldVal, '') + val\n    },\n    activeId: {\n      handler(val) {\n        oldActiveId = val\n      },\n      immediate: true\n    }\n  },\n  mounted() {\n    const clipboard = new ClipboardJS('#copyNode', {\n      text: trigger => {\n        const codeStr = this.generateCode()\n        this.$notify({\n          title: '成功',\n          message: '代码已复制到剪切板，可粘贴。',\n          type: 'success'\n        })\n        return codeStr\n      }\n    })\n    clipboard.on('error', e => {\n      this.$message.error('代码复制失败')\n    })\n  },\n  methods: {\n    activeFormItem(element) {\n      this.activeData = element\n      this.activeId = element.formId\n    },\n    onEnd(obj, a) {\n      if (obj.from !== obj.to) {\n        this.activeData = tempActiveData\n        this.activeId = this.idGlobal\n      }\n    },\n    addComponent(item) {\n      const clone = this.cloneComponent(item)\n      this.drawingList.push(clone)\n      this.activeFormItem(clone)\n    },\n    cloneComponent(origin) {\n      const clone = JSON.parse(JSON.stringify(origin))\n      clone.formId = ++this.idGlobal\n      clone.span = formConf.span\n      clone.renderKey = +new Date() // 改变renderKey后可以实现强制更新组件\n      if (!clone.layout) clone.layout = 'colFormItem'\n      if (clone.layout === 'colFormItem') {\n        clone.vModel = `field${this.idGlobal}`\n        clone.placeholder !== undefined && (clone.placeholder += clone.label)\n        tempActiveData = clone\n      } else if (clone.layout === 'rowFormItem') {\n        delete clone.label\n        clone.componentName = `row${this.idGlobal}`\n        clone.gutter = this.formConf.gutter\n        tempActiveData = clone\n      }\n      return tempActiveData\n    },\n    AssembleFormData() {\n      this.formData = {\n        fields: JSON.parse(JSON.stringify(this.drawingList)),\n        ...this.formConf\n      }\n    },\n    generate(data) {\n      const func = this[`exec${titleCase(this.operationType)}`]\n      this.generateConf = data\n      func && func(data)\n    },\n    execRun(data) {\n      this.AssembleFormData()\n      this.drawerVisible = true\n    },\n    execDownload(data) {\n      const codeStr = this.generateCode()\n      const blob = new Blob([codeStr], { type: 'text/plain;charset=utf-8' })\n      this.$download.saveAs(blob, data.fileName)\n    },\n    execCopy(data) {\n      document.getElementById('copyNode').click()\n    },\n    empty() {\n      this.$confirm('确定要清空所有组件吗？', '提示', { type: 'warning' }).then(\n        () => {\n          this.drawingList = []\n        }\n      )\n    },\n    drawingItemCopy(item, parent) {\n      let clone = JSON.parse(JSON.stringify(item))\n      clone = this.createIdAndKey(clone)\n      parent.push(clone)\n      this.activeFormItem(clone)\n    },\n    createIdAndKey(item) {\n      item.formId = ++this.idGlobal\n      item.renderKey = +new Date()\n      if (item.layout === 'colFormItem') {\n        item.vModel = `field${this.idGlobal}`\n      } else if (item.layout === 'rowFormItem') {\n        item.componentName = `row${this.idGlobal}`\n      }\n      if (Array.isArray(item.children)) {\n        item.children = item.children.map(childItem => this.createIdAndKey(childItem))\n      }\n      return item\n    },\n    drawingItemDelete(index, parent) {\n      parent.splice(index, 1)\n      this.$nextTick(() => {\n        const len = this.drawingList.length\n        if (len) {\n          this.activeFormItem(this.drawingList[len - 1])\n        }\n      })\n    },\n    generateCode() {\n      const { type } = this.generateConf\n      this.AssembleFormData()\n      const script = vueScript(makeUpJs(this.formData, type))\n      const html = vueTemplate(makeUpHtml(this.formData, type))\n      const css = cssStyle(makeUpCss(this.formData))\n      return beautifier.html(html + script + css, beautifierConf.html)\n    },\n    download() {\n      this.dialogVisible = true\n      this.showFileName = true\n      this.operationType = 'download'\n    },\n    run() {\n      this.dialogVisible = true\n      this.showFileName = false\n      this.operationType = 'run'\n    },\n    copy() {\n      this.dialogVisible = true\n      this.showFileName = false\n      this.operationType = 'copy'\n    },\n    tagChange(newTag) {\n      newTag = this.cloneComponent(newTag)\n      newTag.vModel = this.activeData.vModel\n      newTag.formId = this.activeId\n      newTag.span = this.activeData.span\n      delete this.activeData.tag\n      delete this.activeData.tagIcon\n      delete this.activeData.document\n      Object.keys(newTag).forEach(key => {\n        if (this.activeData[key] !== undefined\n          && typeof this.activeData[key] === typeof newTag[key]) {\n          newTag[key] = this.activeData[key]\n        }\n      })\n      this.activeData = newTag\n      this.updateDrawingList(newTag, this.drawingList)\n    },\n    updateDrawingList(newTag, list) {\n      const index = list.findIndex(item => item.formId === this.activeId)\n      if (index > -1) {\n        list.splice(index, 1, newTag)\n      } else {\n        list.forEach(item => {\n          if (Array.isArray(item.children)) this.updateDrawingList(newTag, item.children)\n        })\n      }\n    }\n  }\n}\n</script>\n\n<style lang='scss'>\n.editor-tabs{\n  background: #121315;\n  .el-tabs__header{\n    margin: 0;\n    border-bottom-color: #121315;\n    .el-tabs__nav{\n      border-color: #121315;\n    }\n  }\n  .el-tabs__item{\n    height: 32px;\n    line-height: 32px;\n    color: #888a8e;\n    border-left: 1px solid #121315 !important;\n    background: #363636;\n    margin-right: 5px;\n    user-select: none;\n  }\n  .el-tabs__item.is-active{\n    background: #1e1e1e;\n    border-bottom-color: #1e1e1e!important;\n    color: #fff;\n  }\n  .el-icon-edit{\n    color: #f1fa8c;\n  }\n  .el-icon-document{\n    color: #a95812;\n  }\n}\n\n// home\n.right-scrollbar {\n  .el-scrollbar__view {\n    padding: 12px 18px 15px 15px;\n  }\n}\n.left-scrollbar .el-scrollbar__wrap {\n  box-sizing: border-box;\n  overflow-x: hidden !important;\n  margin-bottom: 0 !important;\n}\n.center-tabs{\n  .el-tabs__header{\n    margin-bottom: 0!important;\n  }\n  .el-tabs__item{\n    width: 50%;\n    text-align: center;\n  }\n  .el-tabs__nav{\n    width: 100%;\n  }\n}\n.reg-item{\n  padding: 12px 6px;\n  background: #f8f8f8;\n  position: relative;\n  border-radius: 4px;\n  .close-btn{\n    position: absolute;\n    right: -6px;\n    top: -6px;\n    display: block;\n    width: 16px;\n    height: 16px;\n    line-height: 16px;\n    background: rgba(0, 0, 0, 0.2);\n    border-radius: 50%;\n    color: #fff;\n    text-align: center;\n    z-index: 1;\n    cursor: pointer;\n    font-size: 12px;\n    &:hover{\n      background: rgba(210, 23, 23, 0.5)\n    }\n  }\n  & + .reg-item{\n    margin-top: 18px;\n  }\n}\n.action-bar{\n  & .el-button+.el-button {\n    margin-left: 15px;\n  }\n  & i {\n    font-size: 20px;\n    vertical-align: middle;\n    position: relative;\n    top: -1px;\n  }\n}\n\n.custom-tree-node{\n  width: 100%;\n  font-size: 14px;\n  .node-operation{\n    float: right;\n  }\n  i[class*=\"el-icon\"] + i[class*=\"el-icon\"]{\n    margin-left: 6px;\n  }\n  .el-icon-plus{\n    color: #409EFF;\n  }\n  .el-icon-delete{\n    color: #157a0c;\n  }\n}\n\n.left-scrollbar .el-scrollbar__view{\n  overflow-x: hidden;\n}\n\n.el-rate{\n  display: inline-block;\n  vertical-align: text-top;\n}\n.el-upload__tip{\n  line-height: 1.2;\n}\n\n$selectedColor: #f6f7ff;\n$lighterBlue: #409EFF;\n\n.container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n\n.components-list {\n  padding: 8px;\n  box-sizing: border-box;\n  height: 100%;\n  .components-item {\n    display: inline-block;\n    width: 48%;\n    margin: 1%;\n    transition: transform 0ms !important;\n  }\n}\n.components-draggable{\n  padding-bottom: 20px;\n}\n.components-title{\n  font-size: 14px;\n  color: #222;\n  margin: 6px 2px;\n  .svg-icon{\n    color: #666;\n    font-size: 18px;\n  }\n}\n\n.components-body {\n  padding: 8px 10px;\n  background: $selectedColor;\n  font-size: 12px;\n  cursor: move;\n  border: 1px dashed $selectedColor;\n  border-radius: 3px;\n  .svg-icon{\n    color: #777;\n    font-size: 15px;\n  }\n  &:hover {\n    border: 1px dashed #787be8;\n    color: #787be8;\n    .svg-icon {\n      color: #787be8;\n    }\n  }\n}\n\n.left-board {\n  width: 260px;\n  position: absolute;\n  left: 0;\n  top: 0;\n  height: 100vh;\n}\n.left-scrollbar{\n  height: calc(100vh - 42px);\n  overflow: hidden;\n}\n.center-scrollbar {\n  height: calc(100vh - 42px);\n  overflow: hidden;\n  border-left: 1px solid #f1e8e8;\n  border-right: 1px solid #f1e8e8;\n  box-sizing: border-box;\n}\n.center-board {\n  height: 100vh;\n  width: auto;\n  margin: 0 350px 0 260px;\n  box-sizing: border-box;\n}\n.empty-info{\n  position: absolute;\n  top: 46%;\n  left: 0;\n  right: 0;\n  text-align: center;\n  font-size: 18px;\n  color: #ccb1ea;\n  letter-spacing: 4px;\n}\n.action-bar{\n  position: relative;\n  height: 42px;\n  text-align: right;\n  padding: 0 15px;\n  box-sizing: border-box;;\n  border: 1px solid #f1e8e8;\n  border-top: none;\n  border-left: none;\n  .delete-btn{\n    color: #F56C6C;\n  }\n}\n.logo-wrapper{\n  position: relative;\n  height: 42px;\n  background: #fff;\n  border-bottom: 1px solid #f1e8e8;\n  box-sizing: border-box;\n}\n.logo{\n  position: absolute;\n  left: 12px;\n  top: 6px;\n  line-height: 30px;\n  color: #00afff;\n  font-weight: 600;\n  font-size: 17px;\n  white-space: nowrap;\n  > img{\n    width: 30px;\n    height: 30px;\n    vertical-align: top;\n  }\n  .github{\n    display: inline-block;\n    vertical-align: sub;\n    margin-left: 15px;\n    > img{\n      height: 22px;\n    }\n  }\n}\n\n.center-board-row {\n  padding: 12px 12px 15px 12px;\n  box-sizing: border-box;\n  & > .el-form {\n    // 69 = 12+15+42\n    height: calc(100vh - 69px);\n  }\n}\n.drawing-board {\n  height: 100%;\n  position: relative;\n  .components-body {\n    padding: 0;\n    margin: 0;\n    font-size: 0;\n  }\n  .sortable-ghost {\n    position: relative;\n    display: block;\n    overflow: hidden;\n    &::before {\n      content: \" \";\n      position: absolute;\n      left: 0;\n      right: 0;\n      top: 0;\n      height: 3px;\n      background: rgb(89, 89, 223);\n      z-index: 2;\n    }\n  }\n  .components-item.sortable-ghost {\n    width: 100%;\n    height: 60px;\n    background-color: $selectedColor;\n  }\n  .active-from-item {\n    & > .el-form-item{\n      background: $selectedColor;\n      border-radius: 6px;\n    }\n    & > .drawing-item-copy, & > .drawing-item-delete{\n      display: initial;\n    }\n    & > .component-name{\n      color: $lighterBlue;\n    }\n  }\n  .el-form-item{\n    margin-bottom: 15px;\n  }\n}\n.drawing-item{\n  position: relative;\n  cursor: move;\n  &.unfocus-bordered:not(.activeFromItem) > div:first-child  {\n    border: 1px dashed #ccc;\n  }\n  .el-form-item{\n    padding: 12px 10px;\n  }\n}\n.drawing-row-item{\n  position: relative;\n  cursor: move;\n  box-sizing: border-box;\n  border: 1px dashed #ccc;\n  border-radius: 3px;\n  padding: 0 2px;\n  margin-bottom: 15px;\n  .drawing-row-item {\n    margin-bottom: 2px;\n  }\n  .el-col{\n    margin-top: 22px;\n  }\n  .el-form-item{\n    margin-bottom: 0;\n  }\n  .drag-wrapper{\n    min-height: 80px;\n  }\n  &.active-from-item{\n    border: 1px dashed $lighterBlue;\n  }\n  .component-name{\n    position: absolute;\n    top: 0;\n    left: 0;\n    font-size: 12px;\n    color: #bbb;\n    display: inline-block;\n    padding: 0 6px;\n  }\n}\n.drawing-item, .drawing-row-item{\n  &:hover {\n    & > .el-form-item{\n      background: $selectedColor;\n      border-radius: 6px;\n    }\n    & > .drawing-item-copy, & > .drawing-item-delete{\n      display: initial;\n    }\n  }\n  & > .drawing-item-copy, & > .drawing-item-delete{\n    display: none;\n    position: absolute;\n    top: -10px;\n    width: 22px;\n    height: 22px;\n    line-height: 22px;\n    text-align: center;\n    border-radius: 50%;\n    font-size: 12px;\n    border: 1px solid;\n    cursor: pointer;\n    z-index: 1;\n  }\n  & > .drawing-item-copy{\n    right: 56px;\n    border-color: $lighterBlue;\n    color: $lighterBlue;\n    background: #fff;\n    &:hover{\n      background: $lighterBlue;\n      color: #fff;\n    }\n  }\n  & > .drawing-item-delete{\n    right: 24px;\n    border-color: #F56C6C;\n    color: #F56C6C;\n    background: #fff;\n    &:hover{\n      background: #F56C6C;\n      color: #fff;\n    }\n  }\n}\n</style>\n"]}]}