{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/evaluation/privateQuarter/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/evaluation/privateQuarter/index.vue", "mtime": 1753510684533}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/evaluation/privateQuarter", "sourcesContent": ["<script>\nimport { listPrivateQuarterly, getPrivateQuarterly, delPrivateQuarterly, addPrivateQuarterly, updatePrivateQuarterly, exportPrivateQuarterly, exportWordPrivateQuarterly, downloadTemplate, importWordPrivateQuarterly } from \"@/api/performance/privateQuarterly\";\nimport { getToken } from \"@/utils/auth\";\n\nexport default {\n  name: \"PrivateQuarterly\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 个人季度评价表格数据\n      privateQuarterlyList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 查询参数\n      queryParams: {\n        name: undefined,\n        year: undefined,\n        quarter: undefined\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        name: [\n          { required: true, message: \"姓名不能为空\", trigger: \"blur\" }\n        ],\n        year: [\n          { required: true, message: \"年份不能为空\", trigger: \"blur\" }\n        ],\n        quarter: [\n          { required: true, message: \"季度不能为空\", trigger: \"blur\" }\n        ],\n        testTime: [\n          { required: true, message: \"测评时间不能为空\", trigger: \"blur\" }\n        ]\n      },\n      // 上传参数\n      upload: {\n        // 是否显示弹出层（导入）\n        open: false,\n        // 弹出层标题（导入）\n        title: \"\",\n        // 是否禁用上传\n        isUploading: false,\n        // 设置上传的请求头部\n        headers: { Authorization: \"Bearer \" + getToken() },\n        // 上传的地址\n        url: process.env.VUE_APP_BASE_API + \"/system/privateQuarterly/importData\",\n        // 是否更新已经存在的数据\n        updateSupport: false,\n        // 当前激活的标签页\n        activeTab: \"excel\"\n      },\n      // 季度选项\n      quarterOptions: [\n        { value: 1, label: \"第一季度\" },\n        { value: 2, label: \"第二季度\" },\n        { value: 3, label: \"第三季度\" },\n        { value: 4, label: \"第四季度\" }\n      ],\n      // 共性指标类型选项\n      commonTypeOptions: [\n        { value: \"政治表现\", label: \"政治表现\" },\n        { value: \"能力素质\", label: \"能力素质\" },\n        { value: \"精神状态、工作作风\", label: \"精神状态、工作作风\" },\n        { value: \"廉洁自律\", label: \"廉洁自律\" }\n      ],\n      // 激励约束指标类型选项\n      incentiveTypeOptions: [\n        { value: \"激励指标\", label: \"激励指标\" },\n        { value: \"约束指标\", label: \"约束指标\" }\n      ]\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询个人季度评价列表 */\n    getList() {\n      this.loading = true;\n      listPrivateQuarterly(this.queryParams).then(response => {\n        this.privateQuarterlyList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: undefined,\n        name: undefined,\n        position: undefined,\n        year: new Date().getFullYear(),\n        quarter: Math.floor(new Date().getMonth() / 3) + 1,\n        testTime: undefined,\n        totalScore: undefined,\n        selfEvaluation: undefined,\n        leaderEvaluation: undefined,\n        status: \"0\",\n        commonIndicators: [\n          { indicatorType: \"政治表现\", indicatorContent: \"\", evaluationStandard: \"\", score: \"\", selfScore: \"\", leaderScore: \"\", departmentScore: \"\", groupScore: \"\", subtotalScore: \"\", quarterlyScore: \"\" },\n          { indicatorType: \"能力素质\", indicatorContent: \"\", evaluationStandard: \"\", score: \"\", selfScore: \"\", leaderScore: \"\", departmentScore: \"\", groupScore: \"\", subtotalScore: \"\", quarterlyScore: \"\" },\n          { indicatorType: \"精神状态、工作作风\", indicatorContent: \"\", evaluationStandard: \"\", score: \"\", selfScore: \"\", leaderScore: \"\", departmentScore: \"\", groupScore: \"\", subtotalScore: \"\", quarterlyScore: \"\" },\n          { indicatorType: \"廉洁自律\", indicatorContent: \"\", evaluationStandard: \"\", score: \"\", selfScore: \"\", leaderScore: \"\", departmentScore: \"\", groupScore: \"\", subtotalScore: \"\", quarterlyScore: \"\" }\n        ],\n        individualIndicators: [],\n        incentiveIndicators: [\n          { indicatorType: \"激励指标\", indicatorContent: \"\", evaluationStandard: \"\", score: \"\", selfScore: \"\", leaderScore: \"\", departmentScore: \"\", groupScore: \"\", subtotalScore: \"\" },\n          { indicatorType: \"约束指标\", indicatorContent: \"\", evaluationStandard: \"\", score: \"\", selfScore: \"\", leaderScore: \"\", departmentScore: \"\", groupScore: \"\", subtotalScore: \"\" }\n        ]\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加个人季度评价\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids[0];\n      getPrivateQuarterly(id).then(response => {\n        this.form = response.data;\n        // 确保共性指标和激励约束指标存在\n        if (!this.form.commonIndicators || this.form.commonIndicators.length === 0) {\n          this.form.commonIndicators = [\n            { indicatorType: \"政治表现\", indicatorContent: \"\", evaluationStandard: \"\", score: \"\", selfScore: \"\", leaderScore: \"\", departmentScore: \"\", groupScore: \"\", subtotalScore: \"\", quarterlyScore: \"\" },\n            { indicatorType: \"能力素质\", indicatorContent: \"\", evaluationStandard: \"\", score: \"\", selfScore: \"\", leaderScore: \"\", departmentScore: \"\", groupScore: \"\", subtotalScore: \"\", quarterlyScore: \"\" },\n            { indicatorType: \"精神状态、工作作风\", indicatorContent: \"\", evaluationStandard: \"\", score: \"\", selfScore: \"\", leaderScore: \"\", departmentScore: \"\", groupScore: \"\", subtotalScore: \"\", quarterlyScore: \"\" },\n            { indicatorType: \"廉洁自律\", indicatorContent: \"\", evaluationStandard: \"\", score: \"\", selfScore: \"\", leaderScore: \"\", departmentScore: \"\", groupScore: \"\", subtotalScore: \"\", quarterlyScore: \"\" }\n          ];\n        }\n        if (!this.form.incentiveIndicators || this.form.incentiveIndicators.length === 0) {\n          this.form.incentiveIndicators = [\n            { indicatorType: \"激励指标\", indicatorContent: \"\", evaluationStandard: \"\", score: \"\", selfScore: \"\", leaderScore: \"\", departmentScore: \"\", groupScore: \"\", subtotalScore: \"\" },\n            { indicatorType: \"约束指标\", indicatorContent: \"\", evaluationStandard: \"\", score: \"\", selfScore: \"\", leaderScore: \"\", departmentScore: \"\", groupScore: \"\", subtotalScore: \"\" }\n          ];\n        }\n        // 确保个性指标存在\n        if (!this.form.individualIndicators) {\n          this.form.individualIndicators = [];\n        }\n        this.open = true;\n        this.title = \"修改个人季度评价\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id != null) {\n            updatePrivateQuarterly(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addPrivateQuarterly(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row.id || this.ids;\n      this.$modal.confirm('是否确认删除个人季度评价编号为\"' + ids + '\"的数据项？').then(function() {\n        return delPrivateQuarterly(ids);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.$modal.confirm('是否确认导出所有个人季度评价数据项？').then(() => {\n        this.$modal.loading(\"正在导出数据，请稍后...\");\n        exportPrivateQuarterly(this.queryParams).then(response => {\n          this.$download.excel(response, '个人季度评价数据.xlsx');\n          this.$modal.closeLoading();\n        });\n      });\n    },\n    /** 导入按钮操作 */\n    handleImport() {\n      this.upload.title = \"个人季度评价数据导入\";\n      this.upload.open = true;\n    },\n    /** 下载模板操作 */\n    importTemplate() {\n      this.$modal.loading(\"正在下载模板，请稍后...\");\n      downloadTemplate().then(response => {\n        this.$download.saveAs(response, '个人季度评价模板.docx');\n        this.$modal.closeLoading();\n      });\n    },\n    // 文件上传中处理\n    handleFileUploadProgress(event, file, fileList) {\n      this.upload.isUploading = true;\n    },\n    // 文件上传成功处理\n    handleFileSuccess(response, file, fileList) {\n      this.upload.open = false;\n      this.upload.isUploading = false;\n      this.$refs.upload.clearFiles();\n      this.$alert(response.msg, \"导入结果\", { dangerouslyUseHTMLString: true });\n      this.getList();\n    },\n    // 提交上传文件\n    submitFileForm() {\n      this.$refs.upload.submit();\n    },\n    // Word文件上传前处理\n    beforeWordUpload(file) {\n      const isDocx = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';\n      const isDoc = file.type === 'application/msword';\n      if (!isDocx && !isDoc) {\n        this.$message.error('上传文件只能是 Word 格式!');\n        return false;\n      }\n      return true;\n    },\n    // Word文件上传处理\n    uploadWordRequest(options) {\n      this.$modal.loading(\"正在导入数据，请稍后...\");\n      importWordPrivateQuarterly(options.file).then(response => {\n        this.$modal.closeLoading();\n        if (response.code === 200) {\n          this.$modal.msgSuccess(response.msg);\n          this.upload.open = false;\n          this.getList();\n        } else {\n          this.$modal.msgError(response.msg);\n        }\n      }).catch(error => {\n        this.$modal.closeLoading();\n        this.$modal.msgError(\"导入失败：\" + error);\n      });\n    },\n    // Word文件上传成功处理\n    handleWordSuccess(response, file, fileList) {\n      if (response.code === 200) {\n        this.$modal.msgSuccess(response.msg);\n        this.upload.open = false;\n        this.getList();\n      } else {\n        this.$modal.msgError(response.msg);\n      }\n    },\n    // Word文件上传失败处理\n    handleWordError(err) {\n      this.$modal.msgError(\"导入失败，请检查文件格式或网络连接\");\n    },\n    /** 导出Word按钮操作 */\n    handleExportWord() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要导出的数据\");\n        return;\n      }\n      this.$modal.confirm('是否确认导出选中的个人季度评价数据项？').then(() => {\n        this.$modal.loading(\"正在导出数据，请稍后...\");\n        exportWordPrivateQuarterly(this.ids).then(response => {\n          this.$download.saveAs(response, '个人季度评价.docx');\n          this.$modal.closeLoading();\n        });\n      });\n    },\n    /** 添加个性指标行 */\n    handleAddIndividualRow() {\n      if (!this.form.individualIndicators) {\n        this.form.individualIndicators = [];\n      }\n      this.form.individualIndicators.push({\n        serialNumber: this.form.individualIndicators.length + 1,\n        indicatorType: \"个性指标\",\n        indicatorContent: \"\",\n        evaluationStandard: \"\",\n        score: \"\",\n        selfScore: \"\",\n        leaderScore: \"\",\n        departmentScore: \"\",\n        groupScore: \"\",\n        subtotalScore: \"\",\n        quarterlyScore: \"\"\n      });\n    },\n    /** 删除个性指标行 */\n    handleDeleteIndividualRow(index) {\n      this.form.individualIndicators.splice(index, 1);\n      // 重新排序\n      this.form.individualIndicators.forEach((item, i) => {\n        item.serialNumber = i + 1;\n      });\n    }\n  }\n};\n</script>\n\n<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"姓名\" prop=\"name\">\n        <el-input\n          v-model=\"queryParams.name\"\n          placeholder=\"请输入姓名\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"年份\" prop=\"year\">\n        <el-input\n          v-model=\"queryParams.year\"\n          placeholder=\"请输入年份\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"季度\" prop=\"quarter\">\n        <el-select v-model=\"queryParams.quarter\" placeholder=\"请选择季度\" clearable>\n          <el-option\n            v-for=\"dict in quarterOptions\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['system:privateQuarterly:add']\"\n        >新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n          v-hasPermi=\"['system:privateQuarterly:edit']\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['system:privateQuarterly:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['system:privateQuarterly:export']\"\n        >导出Excel</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleExportWord\"\n          v-hasPermi=\"['system:privateQuarterly:export']\"\n        >导出Word</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"info\"\n          plain\n          icon=\"el-icon-upload2\"\n          size=\"mini\"\n          @click=\"handleImport\"\n          v-hasPermi=\"['system:privateQuarterly:import']\"\n        >导入</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"info\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"importTemplate\"\n          v-hasPermi=\"['system:privateQuarterly:export']\"\n        >下载模板</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"privateQuarterlyList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"ID\" align=\"center\" prop=\"id\" width=\"80\" />\n      <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" />\n      <el-table-column label=\"职务\" align=\"center\" prop=\"position\" />\n      <el-table-column label=\"年份\" align=\"center\" prop=\"year\" />\n      <el-table-column label=\"季度\" align=\"center\" prop=\"quarter\">\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.quarter === 1 ? '第一季度' : scope.row.quarter === 2 ? '第二季度' : scope.row.quarter === 3 ? '第三季度' : '第四季度' }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"测评时间\" align=\"center\" prop=\"testTime\" width=\"180\" />\n<!--      <el-table-column label=\"总分\" align=\"center\" prop=\"totalScore\" />-->\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['system:privateQuarterly:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['system:privateQuarterly:remove']\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改个人季度评价对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"80vw\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n        <el-row>\n          <el-col :span=\"8\">\n            <el-form-item label=\"姓名\" prop=\"name\">\n              <el-input v-model=\"form.name\" placeholder=\"请输入姓名\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"职务\" prop=\"position\">\n              <el-input v-model=\"form.position\" placeholder=\"请输入职务\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"测评时间\" prop=\"testTime\">\n              <el-date-picker clearable\n                v-model=\"form.testTime\"\n                type=\"date\"\n                value-format=\"yyyy-MM-dd\"\n                placeholder=\"请选择测评时间\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"8\">\n            <el-form-item label=\"年份\" prop=\"year\">\n              <el-input v-model=\"form.year\" placeholder=\"请输入年份\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"季度\" prop=\"quarter\">\n              <el-select v-model=\"form.quarter\" placeholder=\"请选择季度\">\n                <el-option\n                  v-for=\"dict in quarterOptions\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n<!--          <el-col :span=\"8\">-->\n<!--            <el-form-item label=\"总分\" prop=\"totalScore\">-->\n<!--              <el-input v-model=\"form.totalScore\" placeholder=\"请输入总分\" />-->\n<!--            </el-form-item>-->\n<!--          </el-col>-->\n        </el-row>\n\n        <el-tabs type=\"border-card\">\n          <el-tab-pane label=\"共性指标\">\n            <el-table :data=\"form.commonIndicators\" border>\n              <el-table-column label=\"指标类型\" align=\"center\" width=\"120\">\n                <template slot-scope=\"scope\">\n                  <el-select v-model=\"scope.row.indicatorType\" placeholder=\"请选择指标类型\">\n                    <el-option\n                      v-for=\"dict in commonTypeOptions\"\n                      :key=\"dict.value\"\n                      :label=\"dict.label\"\n                      :value=\"dict.value\"\n                    ></el-option>\n                  </el-select>\n                </template>\n              </el-table-column>\n<!--              <el-table-column label=\"指标内容\" align=\"center\">-->\n<!--                <template slot-scope=\"scope\">-->\n<!--                  <el-input v-model=\"scope.row.indicatorContent\" type=\"textarea\" placeholder=\"请输入指标内容\" />-->\n<!--                </template>-->\n<!--              </el-table-column>-->\n              <el-table-column label=\"评价标准\" align=\"center\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.evaluationStandard\" type=\"textarea\" placeholder=\"请输入评价标准\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"分值\" align=\"center\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.score\" placeholder=\"分值\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"自评分值\" align=\"center\" width=\"80\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.selfScore\" placeholder=\"自评\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"科长点评\" align=\"center\" width=\"80\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.leaderScore\" placeholder=\"科长点评\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"分管领导点评\" align=\"center\" width=\"90\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.departmentScore\" placeholder=\"分管领导点评\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"领导小组评鉴\" align=\"center\" width=\"90\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.groupScore\" placeholder=\"领导小组评鉴\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"小计得分\" align=\"center\" width=\"80\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.subtotalScore\" placeholder=\"小计得分\" />\n                </template>\n              </el-table-column>\n<!--              <el-table-column label=\"季度得分\" align=\"center\" width=\"80\">-->\n<!--                <template slot-scope=\"scope\">-->\n<!--                  <el-input v-model=\"scope.row.quarterlyScore\" placeholder=\"季度得分\" />-->\n<!--                </template>-->\n<!--              </el-table-column>-->\n            </el-table>\n          </el-tab-pane>\n\n          <el-tab-pane label=\"个性指标\">\n            <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAddIndividualRow\">添加个性指标</el-button>\n            <el-table :data=\"form.individualIndicators\" border>\n              <el-table-column label=\"序号\" align=\"center\" width=\"60\">\n                <template slot-scope=\"scope\">\n                  <span>{{ scope.row.serialNumber }}</span>\n                </template>\n              </el-table-column>\n<!--              <el-table-column label=\"指标内容\" align=\"center\">-->\n<!--                <template slot-scope=\"scope\">-->\n<!--                  <el-input v-model=\"scope.row.indicatorContent\" type=\"textarea\" placeholder=\"请输入指标内容\" />-->\n<!--                </template>-->\n<!--              </el-table-column>-->\n              <el-table-column label=\"评价标准\" align=\"center\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.evaluationStandard\" type=\"textarea\" placeholder=\"请输入评价标准\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"分值\" align=\"center\" width=\"80\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.score\" placeholder=\"分值\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"自评分值\" align=\"center\" width=\"80\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.selfScore\" placeholder=\"自评\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"科长点评\" align=\"center\" width=\"80\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.leaderScore\" placeholder=\"科长点评\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"分管领导点评\" align=\"center\" width=\"90\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.departmentScore\" placeholder=\"分管领导点评\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"领导小组评鉴\" align=\"center\" width=\"90\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.groupScore\" placeholder=\"领导小组评鉴\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"小计得分\" align=\"center\" width=\"80\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.subtotalScore\" placeholder=\"小计得分\" />\n                </template>\n              </el-table-column>\n<!--              <el-table-column label=\"季度得分\" align=\"center\" width=\"80\">-->\n<!--                <template slot-scope=\"scope\">-->\n<!--                  <el-input v-model=\"scope.row.quarterlyScore\" placeholder=\"季度得分\" />-->\n<!--                </template>-->\n<!--              </el-table-column>-->\n              <el-table-column label=\"操作\" align=\"center\" width=\"60\">\n                <template slot-scope=\"scope\">\n                  <el-button type=\"danger\" icon=\"el-icon-delete\" size=\"mini\" @click=\"handleDeleteIndividualRow(scope.$index)\"></el-button>\n                </template>\n              </el-table-column>\n            </el-table>\n          </el-tab-pane>\n\n          <el-tab-pane label=\"激励约束指标\">\n            <el-table :data=\"form.incentiveIndicators\" border>\n              <el-table-column label=\"指标类型\" align=\"center\" width=\"120\">\n                <template slot-scope=\"scope\">\n                  <el-select v-model=\"scope.row.indicatorType\" placeholder=\"请选择指标类型\">\n                    <el-option\n                      v-for=\"dict in incentiveTypeOptions\"\n                      :key=\"dict.value\"\n                      :label=\"dict.label\"\n                      :value=\"dict.value\"\n                    ></el-option>\n                  </el-select>\n                </template>\n              </el-table-column>\n              <el-table-column label=\"具体事项描述\" align=\"center\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.indicatorContent\" type=\"textarea\" placeholder=\"请输入具体事项描述\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"评分细则对应标准\" align=\"center\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.evaluationStandard\" type=\"textarea\" placeholder=\"请输入评分细则对应标准\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"分值\" align=\"center\" width=\"80\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.score\" placeholder=\"分值\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"自评\" align=\"center\" width=\"80\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.selfScore\" placeholder=\"自评\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"科长点评\" align=\"center\" width=\"80\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.leaderScore\" placeholder=\"科长点评\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"分管领导点评\" align=\"center\" width=\"90\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.departmentScore\" placeholder=\"分管领导点评\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"领导小组评鉴\" align=\"center\" width=\"90\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.groupScore\" placeholder=\"领导小组评鉴\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"小计得分\" align=\"center\" width=\"80\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.subtotalScore\" placeholder=\"小计得分\" />\n                </template>\n              </el-table-column>\n            </el-table>\n          </el-tab-pane>\n        </el-tabs>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 导入对话框 -->\n    <el-dialog :title=\"upload.title\" :visible.sync=\"upload.open\" width=\"400px\" append-to-body>\n      <el-tabs v-model=\"upload.activeTab\">\n        <!-- <el-tab-pane label=\"Excel导入\" name=\"excel\">\n          <el-upload\n            ref=\"upload\"\n            :limit=\"1\"\n            accept=\".xlsx, .xls\"\n            :headers=\"upload.headers\"\n            :action=\"upload.url\"\n            :disabled=\"upload.isUploading\"\n            :on-progress=\"handleFileUploadProgress\"\n            :on-success=\"handleFileSuccess\"\n            :auto-upload=\"false\"\n            drag\n          >\n            <i class=\"el-icon-upload\"></i>\n            <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\n            <div class=\"el-upload__tip text-center\" slot=\"tip\">\n              <span>仅允许导入xls、xlsx格式文件。</span>\n              <el-checkbox v-model=\"upload.updateSupport\" />是否更新已经存在的数据\n            </div>\n          </el-upload>\n          <div slot=\"footer\" class=\"dialog-footer\">\n            <el-button type=\"primary\" @click=\"submitFileForm\">确 定</el-button>\n            <el-button @click=\"upload.open = false\">取 消</el-button>\n          </div>\n        </el-tab-pane> -->\n\n        <el-tab-pane label=\"Word导入\" name=\"word\">\n          <el-upload\n            ref=\"uploadWord\"\n            :limit=\"1\"\n            accept=\".doc, .docx\"\n            action=\"#\"\n            :on-success=\"handleWordSuccess\"\n            :on-error=\"handleWordError\"\n            :before-upload=\"beforeWordUpload\"\n            :http-request=\"uploadWordRequest\"\n            :auto-upload=\"true\"\n            drag\n          >\n            <i class=\"el-icon-upload\"></i>\n            <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\n            <div class=\"el-upload__tip text-center\" slot=\"tip\">\n              <span>仅允许导入doc、docx格式文件。</span>\n            </div>\n          </el-upload>\n        </el-tab-pane>\n      </el-tabs>\n    </el-dialog>\n  </div>\n</template>\n\n<style scoped lang=\"scss\">\n.el-tag + .el-tag {\n  margin-left: 10px;\n}\n</style>\n"]}]}