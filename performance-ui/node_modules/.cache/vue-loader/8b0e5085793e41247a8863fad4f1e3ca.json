{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/evaluation/organization/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/evaluation/organization/index.vue", "mtime": 1753510684533}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/evaluation/organization", "sourcesContent": ["<script>\nimport { listOrganization, getOrganization, addOrganization, updateOrganization, delOrganization, importWord, exportWord, downloadTemplate } from '@/api/performance/organization'\nimport { getToken } from '@/utils/auth'\n\nexport default {\n  name: 'OrganizationEvaluation',\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 组织绩效考核测评表表格数据\n      tableData: [],\n      // 弹出层标题\n      title: '',\n      // 是否显示弹出层\n      open: false,\n      // 查询参数\n      queryParams: {\n        taskType: null,\n        responsibleDept: null,\n        status: '0'\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        taskType: [\n          { required: true, message: '任务类型不能为空', trigger: 'blur' }\n        ],\n        performanceTask: [\n          { required: true, message: '绩效任务不能为空', trigger: 'blur' }\n        ],\n        responsibleDept: [\n          { required: true, message: '责任科室不能为空', trigger: 'blur' }\n        ]\n      },\n      // 上传参数\n      upload: {\n        // 是否显示弹出层\n        open: false,\n        // 弹出层标题\n        title: '',\n        // 是否禁用上传\n        isUploading: false,\n        // 设置上传的请求头部\n        headers: { Authorization: 'Bearer ' + getToken() },\n        // 上传的地址\n        url: process.env.VUE_APP_BASE_API + '/performance/organization/importWord'\n      }\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    /** 查询组织绩效考核测评表列表 */\n    getList() {\n      this.loading = true\n      listOrganization(this.queryParams).then(response => {\n        this.tableData = response.rows\n        this.total = response.total\n        this.loading = false\n      })\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false\n      this.reset()\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: null,\n        serialNumber: null,\n        taskType: null,\n        taskSource: null,\n        performanceTask: null,\n        targetMeasures: null,\n        responsibleDept: null,\n        scoreWeight: null,\n        responsibleLeader: null,\n        completionDeadline: null,\n        evaluationScore: null,\n        status: '0',\n        remark: null\n      }\n      this.resetForm('form')\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.getList()\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm('queryForm')\n      this.handleQuery()\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length !== 1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset()\n      this.open = true\n      this.title = '添加组织绩效考核测评表'\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset()\n      const id = row.id || this.ids[0]\n      getOrganization(id).then(response => {\n        this.form = response.data\n        this.open = true\n        this.title = '修改组织绩效考核测评表'\n      })\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs['form'].validate(valid => {\n        if (valid) {\n          if (this.form.id != null) {\n            updateOrganization(this.form).then(response => {\n              this.$modal.msgSuccess('修改成功')\n              this.open = false\n              this.getList()\n            })\n          } else {\n            addOrganization(this.form).then(response => {\n              this.$modal.msgSuccess('新增成功')\n              this.open = false\n              this.getList()\n            })\n          }\n        }\n      })\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row.id || this.ids\n      this.$modal.confirm('是否确认删除组织绩效考核测评表编号为\"' + ids + '\"的数据项？').then(() => {\n        return delOrganization(ids)\n      }).then(() => {\n        this.getList()\n        this.$modal.msgSuccess('删除成功')\n      }).catch(() => {})\n    },\n    /** 导入按钮操作 */\n    handleImport() {\n      this.upload.open = true\n      this.upload.title = '导入组织绩效考核测评表数据'\n    },\n    /** 下载模板操作 */\n    handleDownloadTemplate() {\n      downloadTemplate().then(response => {\n        const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })\n        const link = document.createElement('a')\n        link.href = window.URL.createObjectURL(blob)\n        link.download = '组织绩效考核测评表模板.docx'\n        link.click()\n        window.URL.revokeObjectURL(link.href)\n      })\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      const ids = this.ids.length ? this.ids : []\n      this.$modal.confirm('是否确认导出所选组织绩效考核测评表数据？').then(() => {\n        exportWord(ids).then(response => {\n          const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })\n          const link = document.createElement('a')\n          link.href = window.URL.createObjectURL(blob)\n          link.download = '组织绩效考核测评表.docx'\n          link.click()\n          window.URL.revokeObjectURL(link.href)\n        })\n      }).catch(() => {})\n    },\n    // 文件上传中处理\n    handleFileUploadProgress(event, file, fileList) {\n      this.upload.isUploading = true\n    },\n    // 文件上传成功处理\n    handleFileSuccess(response, file, fileList) {\n      this.upload.open = false\n      this.upload.isUploading = false\n      this.$refs.upload.clearFiles()\n      this.$alert(response.msg, '导入结果', { dangerouslyUseHTMLString: true })\n      this.getList()\n    },\n    // 提交上传文件\n    submitFileForm() {\n      this.$refs.upload.submit()\n    }\n  }\n}\n</script>\n\n<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" class=\"search-form\">\n      <el-form-item label=\"任务类型\" prop=\"taskType\">\n        <el-input v-model=\"queryParams.taskType\" placeholder=\"请输入任务类型\" clearable />\n      </el-form-item>\n      <el-form-item label=\"责任科室\" prop=\"responsibleDept\">\n        <el-input v-model=\"queryParams.responsibleDept\" placeholder=\"请输入责任科室\" clearable />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" @click=\"handleAdd\">新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"success\" plain icon=\"el-icon-edit\" :disabled=\"single\" @click=\"handleUpdate\">修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"danger\" plain icon=\"el-icon-delete\" :disabled=\"multiple\" @click=\"handleDelete\">删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"warning\" plain icon=\"el-icon-upload\" @click=\"handleImport\">导入</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"info\" plain icon=\"el-icon-download\" @click=\"handleDownloadTemplate\">下载模板</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"info\" plain icon=\"el-icon-download\" :disabled=\"multiple\" @click=\"handleExport\">导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"tableData\" @selection-change=\"handleSelectionChange\" border>\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"序号\" align=\"center\" prop=\"serialNumber\" width=\"80\" />\n      <el-table-column label=\"任务类型\" align=\"center\" prop=\"taskType\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"任务来源\" align=\"center\" prop=\"taskSource\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"绩效任务\" align=\"center\" prop=\"performanceTask\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"目标及措施\" align=\"center\" prop=\"targetMeasures\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"责任科室\" align=\"center\" prop=\"responsibleDept\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"分值及权重\" align=\"center\" prop=\"scoreWeight\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"责任领导\" align=\"center\" prop=\"responsibleLeader\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"完成时限\" align=\"center\" prop=\"completionDeadline\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"评价分值\" align=\"center\" prop=\"evaluationScore\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleUpdate(scope.row)\">修改</el-button>\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleDelete(scope.row)\">删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改组织绩效考核测评表对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"780px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"序号\" prop=\"serialNumber\">\n              <el-input-number v-model=\"form.serialNumber\" :min=\"1\" :max=\"999\" controls-position=\"right\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"任务类型\" prop=\"taskType\">\n              <el-input v-model=\"form.taskType\" placeholder=\"请输入任务类型\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"任务来源\" prop=\"taskSource\">\n              <el-input v-model=\"form.taskSource\" placeholder=\"请输入任务来源\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-form-item label=\"绩效任务\" prop=\"performanceTask\">\n          <el-input v-model=\"form.performanceTask\" type=\"textarea\" placeholder=\"请输入绩效任务\" />\n        </el-form-item>\n        <el-form-item label=\"目标及措施\" prop=\"targetMeasures\">\n          <el-input v-model=\"form.targetMeasures\" type=\"textarea\" placeholder=\"请输入目标及措施\" />\n        </el-form-item>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"责任科室\" prop=\"responsibleDept\">\n              <el-input v-model=\"form.responsibleDept\" placeholder=\"请输入责任科室\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"分值及权重\" prop=\"scoreWeight\">\n              <el-input v-model=\"form.scoreWeight\" placeholder=\"请输入分值及权重\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"责任领导\" prop=\"responsibleLeader\">\n              <el-input v-model=\"form.responsibleLeader\" placeholder=\"请输入责任领导\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"完成时限\" prop=\"completionDeadline\">\n              <el-input v-model=\"form.completionDeadline\" placeholder=\"请输入完成时限\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"评价分值\" prop=\"evaluationScore\">\n              <el-input-number v-model=\"form.evaluationScore\" :precision=\"2\" :step=\"0.1\" :min=\"0\" :max=\"100\" controls-position=\"right\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入备注\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 导入对话框 -->\n    <el-dialog :title=\"upload.title\" :visible.sync=\"upload.open\" width=\"400px\" append-to-body>\n      <el-upload\n        ref=\"upload\"\n        :limit=\"1\"\n        accept=\".docx\"\n        :headers=\"upload.headers\"\n        :action=\"upload.url\"\n        :disabled=\"upload.isUploading\"\n        :on-progress=\"handleFileUploadProgress\"\n        :on-success=\"handleFileSuccess\"\n        :auto-upload=\"false\"\n        drag\n      >\n        <i class=\"el-icon-upload\"></i>\n        <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\n        <div class=\"el-upload__tip\" slot=\"tip\">只能上传docx文件，且不超过10MB</div>\n      </el-upload>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitFileForm\">确 定</el-button>\n        <el-button @click=\"upload.open = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<style scoped lang=\"scss\">\n.search-form {\n  margin-bottom: 20px;\n}\n.mb8 {\n  margin-bottom: 8px;\n}\n</style>\n"]}]}