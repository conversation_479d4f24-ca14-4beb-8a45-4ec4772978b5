{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/system/user/profile/userInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/system/user/profile/userInfo.vue", "mtime": 1753510684536}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["userInfo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAyBA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "userInfo.vue", "sourceRoot": "src/views/system/user/profile", "sourcesContent": ["<template>\n  <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n    <el-form-item label=\"用户昵称\" prop=\"nickName\">\n      <el-input v-model=\"form.nickName\" maxlength=\"30\" />\n    </el-form-item> \n    <el-form-item label=\"手机号码\" prop=\"phonenumber\">\n      <el-input v-model=\"form.phonenumber\" maxlength=\"11\" />\n    </el-form-item>\n    <el-form-item label=\"邮箱\" prop=\"email\">\n      <el-input v-model=\"form.email\" maxlength=\"50\" />\n    </el-form-item>\n    <el-form-item label=\"性别\">\n      <el-radio-group v-model=\"form.sex\">\n        <el-radio label=\"0\">男</el-radio>\n        <el-radio label=\"1\">女</el-radio>\n      </el-radio-group>\n    </el-form-item>\n    <el-form-item>\n      <el-button type=\"primary\" size=\"mini\" @click=\"submit\">保存</el-button>\n      <el-button type=\"danger\" size=\"mini\" @click=\"close\">关闭</el-button>\n    </el-form-item>\n  </el-form>\n</template>\n\n<script>\nimport { updateUserProfile } from \"@/api/system/user\"\n\nexport default {\n  props: {\n    user: {\n      type: Object\n    }\n  },\n  data() {\n    return {\n      form: {},\n      // 表单校验\n      rules: {\n        nickName: [\n          { required: true, message: \"用户昵称不能为空\", trigger: \"blur\" }\n        ],\n        email: [\n          { required: true, message: \"邮箱地址不能为空\", trigger: \"blur\" },\n          {\n            type: \"email\",\n            message: \"请输入正确的邮箱地址\",\n            trigger: [\"blur\", \"change\"]\n          }\n        ],\n        phonenumber: [\n          { required: true, message: \"手机号码不能为空\", trigger: \"blur\" },\n          {\n            pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/,\n            message: \"请输入正确的手机号码\",\n            trigger: \"blur\"\n          }\n        ]\n      }\n    }\n  },\n  watch: {\n    user: {\n      handler(user) {\n        if (user) {\n          this.form = { nickName: user.nickName, phonenumber: user.phonenumber, email: user.email, sex: user.sex }\n        }\n      },\n      immediate: true\n    }\n  },\n  methods: {\n    submit() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          updateUserProfile(this.form).then(response => {\n            this.$modal.msgSuccess(\"修改成功\")\n            this.user.phonenumber = this.form.phonenumber\n            this.user.email = this.form.email\n          })\n        }\n      })\n    },\n    close() {\n      this.$tab.closePage()\n    }\n  }\n}\n</script>\n"]}]}