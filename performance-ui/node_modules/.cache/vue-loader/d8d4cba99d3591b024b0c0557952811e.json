{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/execute/year/index.vue?vue&type=style&index=0&id=120ca9f4&scoped=true&lang=css", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/execute/year/index.vue", "mtime": 1753510684533}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/css-loader/dist/cjs.js", "mtime": 1753510683027}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1753510684373}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/postcss-loader/src/index.js", "mtime": 1753510684004}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5hcHAtY29udGFpbmVyIHsKICBwYWRkaW5nOiAyMHB4Owp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiSA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/execute/year", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"20\" style=\"margin-bottom: 20px;\">\n      <el-col :span=\"12\">\n        <el-button type=\"primary\" @click=\"handleAdd\">新增</el-button>\n        <el-upload\n          class=\"upload-demo\"\n          :action=\"uploadExcelUrl\"\n          :headers=\"upload.headers\"\n          :show-file-list=\"false\"\n          :on-success=\"handleFileSuccess\"\n          :on-error=\"handleImportError\"\n          accept=\".xls,.xlsx\"\n          style=\"display:inline-block;margin-left:10px;\"\n        >\n          <el-button type=\"success\">导入Excel</el-button>\n        </el-upload>\n        <el-button @click=\"handleExport\" style=\"margin-left:10px;\">导出Excel</el-button>\n        <el-button @click=\"downloadExcelTemplate\" style=\"margin-left:10px;\">下载Excel模板</el-button>\n      </el-col>\n    </el-row>\n    <el-table :data=\"tableData\" border style=\"width: 100%\" @selection-change=\"handleSelectionChange\" v-loading=\"loading\">\n      <el-table-column type=\"selection\" width=\"55\" />\n\n      <el-table-column prop=\"name\" label=\"姓名\" width=\"50\" />\n      <el-table-column prop=\"gender\" label=\"性别\" width=\"50\"/>\n      <el-table-column prop=\"birthday\" label=\"出生年月\" width=\"100\" />\n      <el-table-column prop=\"politicalStatus\" label=\"政治面貌\" width=\"80\" />\n      <el-table-column prop=\"currentPosition\" label=\"任现职时间\" width=\"100\" />\n      <el-table-column prop=\"unitandPosition\" label=\"单位及职务职级\" width=\"150\" />\n      <el-table-column prop=\"workContent\" label=\"从事或分管工作\" width=\"150\" />\n      <el-table-column prop=\"selfSummary\" label=\"个人总结\" width=\"300\" />\n      <el-table-column prop=\"training\" label=\"参加脱产培训情况\" width=\"300\" />\n      <el-table-column prop=\"advice\" label=\"主管领导评语和考核等次建议\" width=\"300\" />\n      <el-table-column prop=\"attitude\" label=\"机关负责人或考核委员会意见\" width=\"300\" />\n      <el-table-column prop=\"remark\" label=\"需要说明的情况\" width=\"300\" />\n\n      <el-table-column label=\"操作\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <el-button size=\"mini\" @click=\"handleUpdate(scope.row)\">编辑</el-button>\n          <el-button size=\"mini\" type=\"danger\" @click=\"handleDelete(scope.row)\">删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <el-pagination\n      style=\"margin-top: 20px;\"\n      background\n      layout=\"prev, pager, next, jumper\"\n      :total=\"total\"\n      :page-size=\"queryParams.pageSize\"\n      :current-page.sync=\"queryParams.pageNum\"\n      @current-change=\"getList\"\n    />\n    <el-dialog :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\n      <el-form :model=\"form\" label-width=\"100px\">\n        <el-row :gutter=\"20\">\n          <!-- <el-col :span=\"8\"><el-form-item label=\"序号\"><el-input v-model=\"form.id\" /></el-form-item></el-col> -->\n          <el-col :span=\"8\"><el-form-item label=\"姓名\"><el-input v-model=\"form.name\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"性别\"><el-input v-model=\"form.gender\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"出生年月\"><el-input v-model=\"form.birthday\" /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\"><el-form-item label=\"政治面貌\"><el-input v-model=\"form.politicalStatus\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"任现职时间\"><el-input v-model=\"form.currentPosition\" /></el-form-item></el-col>\n\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"10\"><el-form-item label=\"单位及职务职级\"><el-input v-model=\"form.unitandPosition\" /></el-form-item></el-col>\n          <el-col :span=\"10\"><el-form-item label=\"从事或分管工作\"><el-input v-model=\"form.workContent\" /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"20\"><el-form-item label=\"个人总结\"><el-input v-model=\"form.selfSummary\" /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"20\"><el-form-item label=\"参加脱产培训情况\"><el-input v-model=\"form.training\" /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"20\"><el-form-item label=\"主管领导评语和考核等次建议\"><el-input v-model=\"form.advice\" /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"20\"><el-form-item label=\"机关负责人或考核委员会意见\"><el-input v-model=\"form.attitude\" /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"20\"><el-form-item label=\"需要说明的情况\"><el-input v-model=\"form.remark\" /></el-form-item></el-col>\n        </el-row>\n\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"submitForm\">确定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listYearly, addYearly, updateYearly, delYearly, exportYearly, downloadTemplateYearly, downloadExcelTemplateYearly } from \"@/api/performance/yearly\";\nimport { getToken } from \"@/utils/auth\";\n\nexport default {\n  name: \"Yearly\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 表格数据\n      tableData: [],\n      // 弹出层标题\n      dialogTitle: \"\",\n      // 是否显示弹出层\n      dialogVisible: false,\n      // 表单参数\n      form: {},\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        year: null,\n      },\n      // Word导入参数\n      upload: {\n        open: false,\n        title: \"\",\n        isUploading: false,\n        url: process.env.VUE_APP_BASE_API + \"/performance/yearly/importData\",\n        headers: { Authorization: \"Bearer \" + getToken() }\n      },\n      // Excel导入URL\n      uploadExcelUrl: process.env.VUE_APP_BASE_API + \"/performance/yearly/importExcel\"\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询列表 */\n    getList() {\n      this.loading = true;\n      listYearly(this.queryParams).then(response => {\n        this.tableData = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    /** 多选框选中数据 */\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.form = {};\n      this.dialogVisible = true;\n      this.dialogTitle = \"新增年度总结\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      const id = row.id || this.ids[0];\n      // 在实际应用中，推荐通过ID查询最新数据\n      // getYearly(id).then(response => { ... });\n      this.form = Object.assign({}, row);\n      this.dialogVisible = true;\n      this.dialogTitle = \"修改年度总结\";\n    },\n    /** 提交按钮 */\n    submitForm() {\n      if (this.form.id != null) {\n        updateYearly(this.form).then(response => {\n          this.$modal.msgSuccess(\"修改成功\");\n          this.dialogVisible = false;\n          this.getList();\n        });\n      } else {\n        addYearly(this.form).then(response => {\n          this.$modal.msgSuccess(\"新增成功\");\n          this.dialogVisible = false;\n          this.getList();\n        });\n      }\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row.id ? [row.id] : this.ids;\n      this.$modal.confirm('是否确认删除编号为\"' + ids.join(',') + '\"的数据项？').then(() => {\n        return delYearly(ids);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 导入按钮操作 */\n    handleImport() {\n      this.upload.title = \"年度绩效Word导入\";\n      this.upload.open = true;\n    },\n    /** 导入错误处理 */\n    handleImportError() {\n      this.$message.error('导入失败，请检查文件或网络');\n    },\n\n    /** 导出按钮操作 */\n    handleExport() {\n      if (this.ids.length === 0) {\n        this.$modal.msgWarning(\"请选择要导出的数据\");\n        return;\n      }\n      this.$modal.confirm('是否确认导出选中的数据项？').then(() => {\n        return exportYearly(this.ids);\n      }).then(data => {\n        const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })\n        const url = window.URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.href = url\n        a.download = '年度总结导出.docx'\n        document.body.appendChild(a)\n        a.click()\n        document.body.removeChild(a)\n        window.URL.revokeObjectURL(url)\n      }).catch(() => {});\n    },\n\n    // /** 下载Word模板操作 */\n    // downloadTemplate() {\n    //   downloadTemplateYearly().then(data => {\n    //     const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })\n    //     const url = window.URL.createObjectURL(blob)\n    //     const a = document.createElement('a')\n    //     a.href = url\n    //     a.download = '年度总结导入模板.docx'\n    //     document.body.appendChild(a)\n    //     a.click()\n    //     document.body.removeChild(a)\n    //     window.URL.revokeObjectURL(url)\n    //   });\n    // },\n\n    /** 下载Excel模板操作 */\n    downloadExcelTemplate() {\n      downloadExcelTemplateYearly().then(data => {\n        const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })\n        const url = window.URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.href = url\n        a.download = '年度总结Excel导入模板.xlsx'\n        document.body.appendChild(a)\n        a.click()\n        document.body.removeChild(a)\n        window.URL.revokeObjectURL(url)\n      });\n    },\n    /** 文件上传中处理 */\n    handleFileUploadProgress(event, file, fileList) {\n      this.upload.isUploading = true;\n    },\n    /** 文件上传成功处理 */\n    handleFileSuccess(response, file, fileList) {\n      if (response.code === 200) {\n        this.$message.success(response.msg || '导入成功');\n        this.getList();\n      } else {\n        this.$message.error(response.msg || '导入失败');\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.app-container {\n  padding: 20px;\n}\n</style>\n"]}]}