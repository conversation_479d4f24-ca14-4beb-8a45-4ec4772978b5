{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/ThemePicker/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/ThemePicker/index.vue", "mtime": 1753510684528}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;AAUA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/ThemePicker", "sourcesContent": ["<template>\n  <el-color-picker\n    v-model=\"theme\"\n    :predefine=\"['#409EFF', '#1890ff', '#304156','#212121','#11a983', '#13c2c2', '#6959CD', '#f5222d', ]\"\n    class=\"theme-picker\"\n    popper-class=\"theme-picker-dropdown\"\n  />\n</template>\n\n<script>\nconst ORIGINAL_THEME = '#409EFF' // default color\n\nexport default {\n  data() {\n    return {\n      chalk: '', // content of theme-chalk css\n      theme: ''\n    }\n  },\n  computed: {\n    defaultTheme() {\n      return this.$store.state.settings.theme\n    }\n  },\n  watch: {\n    defaultTheme: {\n      handler: function(val, oldVal) {\n        this.theme = val\n      },\n      immediate: true\n    },\n    async theme(val) {\n      await this.setTheme(val)\n    }\n  },\n  created() {\n    if(this.defaultTheme !== ORIGINAL_THEME) {\n      this.setTheme(this.defaultTheme)\n    }\n  },\n  methods: {\n    async setTheme(val) {\n      const oldVal = this.chalk ? this.theme : ORIGINAL_THEME\n      if (typeof val !== 'string') return\n      const themeCluster = this.getThemeCluster(val.replace('#', ''))\n      const originalCluster = this.getThemeCluster(oldVal.replace('#', ''))\n\n      const getHandler = (variable, id) => {\n        return () => {\n          const originalCluster = this.getThemeCluster(ORIGINAL_THEME.replace('#', ''))\n          const newStyle = this.updateStyle(this[variable], originalCluster, themeCluster)\n\n          let styleTag = document.getElementById(id)\n          if (!styleTag) {\n            styleTag = document.createElement('style')\n            styleTag.setAttribute('id', id)\n            document.head.appendChild(styleTag)\n          }\n          styleTag.innerText = newStyle\n        }\n      }\n\n      if (!this.chalk) {\n        const url = `/styles/theme-chalk/index.css`\n        await this.getCSSString(url, 'chalk')\n      }\n\n      const chalkHandler = getHandler('chalk', 'chalk-style')\n      chalkHandler()\n\n      const styles = [].slice.call(document.querySelectorAll('style'))\n        .filter(style => {\n          const text = style.innerText\n          return new RegExp(oldVal, 'i').test(text) && !/Chalk Variables/.test(text)\n        })\n      styles.forEach(style => {\n        const { innerText } = style\n        if (typeof innerText !== 'string') return\n        style.innerText = this.updateStyle(innerText, originalCluster, themeCluster)\n      })\n\n      this.$emit('change', val)\n    },\n\n    updateStyle(style, oldCluster, newCluster) {\n      let newStyle = style\n      oldCluster.forEach((color, index) => {\n        newStyle = newStyle.replace(new RegExp(color, 'ig'), newCluster[index])\n      })\n      return newStyle\n    },\n\n    getCSSString(url, variable) {\n      return new Promise(resolve => {\n        const xhr = new XMLHttpRequest()\n        xhr.onreadystatechange = () => {\n          if (xhr.readyState === 4 && xhr.status === 200) {\n            this[variable] = xhr.responseText.replace(/@font-face{[^}]+}/, '')\n            resolve()\n          }\n        }\n        xhr.open('GET', url)\n        xhr.send()\n      })\n    },\n\n    getThemeCluster(theme) {\n      const tintColor = (color, tint) => {\n        let red = parseInt(color.slice(0, 2), 16)\n        let green = parseInt(color.slice(2, 4), 16)\n        let blue = parseInt(color.slice(4, 6), 16)\n\n        if (tint === 0) { // when primary color is in its rgb space\n          return [red, green, blue].join(',')\n        } else {\n          red += Math.round(tint * (255 - red))\n          green += Math.round(tint * (255 - green))\n          blue += Math.round(tint * (255 - blue))\n\n          red = red.toString(16)\n          green = green.toString(16)\n          blue = blue.toString(16)\n\n          return `#${red}${green}${blue}`\n        }\n      }\n\n      const shadeColor = (color, shade) => {\n        let red = parseInt(color.slice(0, 2), 16)\n        let green = parseInt(color.slice(2, 4), 16)\n        let blue = parseInt(color.slice(4, 6), 16)\n\n        red = Math.round((1 - shade) * red)\n        green = Math.round((1 - shade) * green)\n        blue = Math.round((1 - shade) * blue)\n\n        red = red.toString(16)\n        green = green.toString(16)\n        blue = blue.toString(16)\n\n        return `#${red}${green}${blue}`\n      }\n\n      const clusters = [theme]\n      for (let i = 0; i <= 9; i++) {\n        clusters.push(tintColor(theme, Number((i / 10).toFixed(2))))\n      }\n      clusters.push(shadeColor(theme, 0.1))\n      return clusters\n    }\n  }\n}\n</script>\n\n<style>\n.theme-message,\n.theme-picker-dropdown {\n  z-index: 99999 !important;\n}\n\n.theme-picker .el-color-picker__trigger {\n  height: 26px !important;\n  width: 26px !important;\n  padding: 2px;\n}\n\n.theme-picker-dropdown .el-color-dropdown__link-btn {\n  display: none;\n}\n</style>\n"]}]}