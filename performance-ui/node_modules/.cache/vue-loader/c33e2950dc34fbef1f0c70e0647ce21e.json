{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/execute/month/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/execute/month/index.vue", "mtime": 1754401892477}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/execute/month", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"20\" style=\"margin-bottom: 20px;\">\n      <el-col :span=\"12\">\n        <el-button type=\"primary\" @click=\"handleAdd\">新增</el-button>\n        <el-upload\n          class=\"upload-demo\"\n          :action=\"uploadUrl\"\n          :headers=\"uploadHeaders\"\n          :show-file-list=\"false\"\n          :on-success=\"handleImportSuccess\"\n          :on-error=\"handleImportError\"\n          accept=\".doc,.docx\"\n          style=\"display:inline-block;margin-left:10px;\"\n        >\n          <el-button>导入Word</el-button>\n        </el-upload>\n        <el-button @click=\"handleExport\" style=\"margin-left:10px;\">导出Word</el-button>\n        <el-button @click=\"handleDownloadTemplate\" style=\"margin-left:10px;\">下载模板</el-button>\n      </el-col>\n    </el-row>\n    <el-table :data=\"tableData\" border style=\"width: 100%\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" />\n      <el-table-column prop=\"id\" label=\"序号\" width=\"60\" />\n      <el-table-column prop=\"deptName\" label=\"科室工作任务\" />\n      <el-table-column prop=\"category\" label=\"分类\" />\n      <el-table-column prop=\"workTarget\" label=\"工作目标\" />\n      <el-table-column prop=\"workMeasure\" label=\"工作措施\" />\n      <el-table-column prop=\"completeTime\" label=\"计划完成时间\" />\n      <el-table-column prop=\"firstWeek\" label=\"第一周\" />\n      <el-table-column prop=\"secondWeek\" label=\"第二周\" />\n      <el-table-column prop=\"thirdWeek\" label=\"第三周\" />\n      <el-table-column prop=\"fourthWeek\" label=\"第四周\" />\n      <el-table-column prop=\"responsibleLeader\" label=\"责任领导\" />\n      <el-table-column prop=\"responsibleDepartment\" label=\"责任科室\" />\n      <el-table-column prop=\"departmentHeader\" label=\"科室负责人\" />\n      <el-table-column prop=\"specificResponsiblePerson\" label=\"具体负责人\" />\n\n      <el-table-column label=\"操作\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <el-button size=\"mini\" @click=\"handleEdit(scope.row)\">编辑</el-button>\n          <el-button size=\"mini\" type=\"danger\" @click=\"handleDelete(scope.row)\">删除</el-button>\n        </template>\n      </el-table-column>\n\n    </el-table>\n    <el-pagination\n      style=\"margin-top: 20px;\"\n      background\n      layout=\"prev, pager, next, jumper\"\n      :total=\"total\"\n      :page-size=\"pageSize\"\n      :current-page.sync=\"pageNum\"\n      @current-change=\"loadData\"\n    />\n    <el-dialog :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\n      <el-form :model=\"form\" label-width=\"100px\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\"><el-form-item label=\"科室工作任务\"><el-input v-model=\"form.deptName\" /></el-form-item></el-col>\n          <!-- <el-col :span=\"8\"><el-form-item label=\"职务\"><el-input v-model=\"form.position\" /></el-form-item></el-col> -->\n          <el-col :span=\"8\"><el-form-item label=\"分类\"><el-input v-model=\"form.category\" /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\"><el-form-item label=\"工作目标\"><el-input v-model=\"form.workTarget\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"工作措施\"><el-input v-model=\"form.workMeasure\"  /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"计划完成时间\"><el-input v-model=\"form.completeTime\"  /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\"><el-form-item label=\"第一周\"><el-input v-model=\"form.firstWeek\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"第二周\"><el-input v-model=\"form.secondWeek\" /></el-form-item></el-col>\n\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\"><el-form-item label=\"第三周\"><el-input v-model=\"form.thirdWeek\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"第四周\"><el-input v-model=\"form.fourthWeek\" /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\"><el-form-item label=\"责任领导\"><el-input v-model=\"form.responsibleLeader\"  /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"责任科室\"><el-input v-model=\"form.responsibleDepartment\"  /></el-form-item></el-col>\n\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\"><el-form-item label=\"科室负责人\"><el-input v-model=\"form.departmentHeader\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"具体负责人\"><el-input v-model=\"form.specificResponsiblePerson\"  /></el-form-item></el-col>\n\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"submitForm\">确定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  listMonthly,\n  addMonthly,\n  updateMonthly,\n  delMonthly,\n  exportMonthly,\n  downloadTemplateMonthly\n} from '@/api/performance/monthly'\nimport { getToken } from '@/utils/auth'\n\nexport default {\n  data() {\n    return {\n      tableData: [],\n      total: 0,\n      pageNum: 1,\n      pageSize: 10,\n      dialogVisible: false,\n      dialogTitle: '新增',\n      form: {},\n      selectedIds: [],\n      uploadUrl: process.env.VUE_APP_BASE_API + '/performance/monthly/importData',\n      uploadHeaders: {\n        Authorization: 'Bearer ' + getToken()\n      }\n    }\n  },\n  created() {\n    this.loadData()\n  },\n  methods: {\n    loadData() {\n      listMonthly({ pageNum: this.pageNum, pageSize: this.pageSize }).then(res => {\n        if (res.code === 200) {\n          this.tableData = res.rows\n          this.total = res.total\n        }\n      })\n    },\n    handleAdd() {\n      this.dialogTitle = '新增'\n      this.form = {}\n      this.dialogVisible = true\n    },\n    handleEdit(row) {\n      this.dialogTitle = '编辑'\n      this.form = Object.assign({}, row)\n      this.dialogVisible = true\n    },\n    handleDelete(row) {\n      this.$confirm('确定删除该条记录吗？', '提示', { type: 'warning' }).then(() => {\n        delMonthly(row.id).then(() => {\n          this.$message.success('删除成功')\n          this.loadData()\n        })\n      })\n    },\n    submitForm() {\n      if (this.form.id) {\n        updateMonthly(this.form).then(() => {\n          this.$message.success('修改成功')\n          this.dialogVisible = false\n          this.loadData()\n        })\n      } else {\n        addMonthly(this.form).then(() => {\n          this.$message.success('新增成功')\n          this.dialogVisible = false\n          this.loadData()\n        })\n      }\n    },\n    handleSelectionChange(val) {\n      this.selectedIds = val.map(item => item.id)\n    },\n    handleImportSuccess(res) {\n      if (res.code === 200) {\n        this.$message.success(res.msg || '导入成功')\n        this.loadData()\n      } else {\n        this.$message.error(res.msg || '导入失败')\n      }\n    },\n    handleImportError() {\n      this.$message.error('导入失败，请检查文件或网络')\n    },\n    handleExport() {\n      if (this.selectedIds.length === 0) {\n        this.$message.warning('请先选择要导出的数据')\n        return\n      }\n      exportMonthly(this.selectedIds).then(data => {\n        const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })\n        const url = window.URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.href = url\n        a.download = '月总结导出.docx'\n        document.body.appendChild(a)\n        a.click()\n        document.body.removeChild(a)\n        window.URL.revokeObjectURL(url)\n      })\n    },\n    handleDownloadTemplate() {\n      downloadTemplateMonthly().then(data => {\n        const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })\n        const url = window.URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.href = url\n        a.download = '月总结导入模板.docx'\n        document.body.appendChild(a)\n        a.click()\n        document.body.removeChild(a)\n        window.URL.revokeObjectURL(url)\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.app-container {\n  padding: 20px;\n}\n</style>\n"]}]}