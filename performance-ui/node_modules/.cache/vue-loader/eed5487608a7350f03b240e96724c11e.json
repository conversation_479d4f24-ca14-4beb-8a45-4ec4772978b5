{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/SvgIcon/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/SvgIcon/index.vue", "mtime": 1753510684528}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGlzRXh0ZXJuYWwgfSBmcm9tICdAL3V0aWxzL3ZhbGlkYXRlJwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdTdmdJY29uJywKICBwcm9wczogewogICAgaWNvbkNsYXNzOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgcmVxdWlyZWQ6IHRydWUKICAgIH0sCiAgICBjbGFzc05hbWU6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAnJwogICAgfQogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIGlzRXh0ZXJuYWwoKSB7CiAgICAgIHJldHVybiBpc0V4dGVybmFsKHRoaXMuaWNvbkNsYXNzKQogICAgfSwKICAgIGljb25OYW1lKCkgewogICAgICByZXR1cm4gYCNpY29uLSR7dGhpcy5pY29uQ2xhc3N9YAogICAgfSwKICAgIHN2Z0NsYXNzKCkgewogICAgICBpZiAodGhpcy5jbGFzc05hbWUpIHsKICAgICAgICByZXR1cm4gJ3N2Zy1pY29uICcgKyB0aGlzLmNsYXNzTmFtZQogICAgICB9IGVsc2UgewogICAgICAgIHJldHVybiAnc3ZnLWljb24nCiAgICAgIH0KICAgIH0sCiAgICBzdHlsZUV4dGVybmFsSWNvbigpIHsKICAgICAgcmV0dXJuIHsKICAgICAgICBtYXNrOiBgdXJsKCR7dGhpcy5pY29uQ2xhc3N9KSBuby1yZXBlYXQgNTAlIDUwJWAsCiAgICAgICAgJy13ZWJraXQtbWFzayc6IGB1cmwoJHt0aGlzLmljb25DbGFzc30pIG5vLXJlcGVhdCA1MCUgNTAlYAogICAgICB9CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;AAQA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/SvgIcon", "sourcesContent": ["<template>\n  <div v-if=\"isExternal\" :style=\"styleExternalIcon\" class=\"svg-external-icon svg-icon\" v-on=\"$listeners\" />\n  <svg v-else :class=\"svgClass\" aria-hidden=\"true\" v-on=\"$listeners\">\n    <use :xlink:href=\"iconName\" />\n  </svg>\n</template>\n\n<script>\nimport { isExternal } from '@/utils/validate'\n\nexport default {\n  name: 'SvgIcon',\n  props: {\n    iconClass: {\n      type: String,\n      required: true\n    },\n    className: {\n      type: String,\n      default: ''\n    }\n  },\n  computed: {\n    isExternal() {\n      return isExternal(this.iconClass)\n    },\n    iconName() {\n      return `#icon-${this.iconClass}`\n    },\n    svgClass() {\n      if (this.className) {\n        return 'svg-icon ' + this.className\n      } else {\n        return 'svg-icon'\n      }\n    },\n    styleExternalIcon() {\n      return {\n        mask: `url(${this.iconClass}) no-repeat 50% 50%`,\n        '-webkit-mask': `url(${this.iconClass}) no-repeat 50% 50%`\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.svg-icon {\n  width: 1em;\n  height: 1em;\n  vertical-align: -0.15em;\n  fill: currentColor;\n  overflow: hidden;\n}\n\n.svg-external-icon {\n  background-color: currentColor;\n  mask-size: cover!important;\n  display: inline-block;\n}\n</style>\n"]}]}