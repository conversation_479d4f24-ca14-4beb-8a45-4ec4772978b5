{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/tool/gen/basicInfoForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/tool/gen/basicInfoForm.vue", "mtime": 1753510684536}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBwcm9wczogewogICAgaW5mbzogewogICAgICB0eXBlOiBPYmplY3QsCiAgICAgIGRlZmF1bHQ6IG51bGwKICAgIH0KICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBydWxlczogewogICAgICAgIHRhYmxlTmFtZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+i+k+WFpeihqOWQjeensCIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICB0YWJsZUNvbW1lbnQ6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fovpPlhaXooajmj4/ov7AiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgY2xhc3NOYW1lOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36L6T5YWl5a6e5L2T57G75ZCN56ewIiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIGZ1bmN0aW9uQXV0aG9yOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36L6T5YWl5L2c6ICFIiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdCiAgICAgIH0KICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["basicInfoForm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "basicInfoForm.vue", "sourceRoot": "src/views/tool/gen", "sourcesContent": ["<template>\n  <el-form ref=\"basicInfoForm\" :model=\"info\" :rules=\"rules\" label-width=\"150px\">\n    <el-row>\n      <el-col :span=\"12\">\n        <el-form-item label=\"表名称\" prop=\"tableName\">\n          <el-input placeholder=\"请输入仓库名称\" v-model=\"info.tableName\" />\n        </el-form-item>\n      </el-col>\n      <el-col :span=\"12\">\n        <el-form-item label=\"表描述\" prop=\"tableComment\">\n          <el-input placeholder=\"请输入\" v-model=\"info.tableComment\" />\n        </el-form-item>\n      </el-col>\n      <el-col :span=\"12\">\n        <el-form-item label=\"实体类名称\" prop=\"className\">\n          <el-input placeholder=\"请输入\" v-model=\"info.className\" />\n        </el-form-item>\n      </el-col>\n      <el-col :span=\"12\">\n        <el-form-item label=\"作者\" prop=\"functionAuthor\">\n          <el-input placeholder=\"请输入\" v-model=\"info.functionAuthor\" />\n        </el-form-item>\n      </el-col>\n      <el-col :span=\"24\">\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input type=\"textarea\" :rows=\"3\" v-model=\"info.remark\"></el-input>\n        </el-form-item>\n      </el-col>\n    </el-row>\n  </el-form>\n</template>\n\n<script>\nexport default {\n  props: {\n    info: {\n      type: Object,\n      default: null\n    }\n  },\n  data() {\n    return {\n      rules: {\n        tableName: [\n          { required: true, message: \"请输入表名称\", trigger: \"blur\" }\n        ],\n        tableComment: [\n          { required: true, message: \"请输入表描述\", trigger: \"blur\" }\n        ],\n        className: [\n          { required: true, message: \"请输入实体类名称\", trigger: \"blur\" }\n        ],\n        functionAuthor: [\n          { required: true, message: \"请输入作者\", trigger: \"blur\" }\n        ]\n      }\n    }\n  }\n}\n</script>\n"]}]}