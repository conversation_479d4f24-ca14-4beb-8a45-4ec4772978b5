{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/result/quarterResult/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/result/quarterResult/index.vue", "mtime": 1753510684535}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/result/quarterResult", "sourcesContent": ["<script>\nimport { listQuarterlyResult, listQuarterlyGoodGrade } from '@/api/performance/quarterlyResult'\n\nexport default {\n  name: 'QuarterResult',\n  data() {\n    return {\n      // 加载状态\n      loading: true,\n      // 分类列表\n      categoryList: [],\n      // 季度结果列表（按分类排序）\n      resultList: [],\n      // \"好\"等次人员列表\n      goodGradeList: [],\n      // 查询参数\n      queryParams: {\n        year: new Date().getFullYear(),\n        quarter: Math.floor((new Date().getMonth() + 3) / 3)\n      }\n    }\n  },\n  created() {\n    this.getResultList()\n    this.getGoodGradeList()\n  },\n  methods: {\n    // 获取季度结果列表（按分类排序）\n    getResultList() {\n      this.loading = true\n      listQuarterlyResult(this.queryParams).then(res => {\n        if (res.code === 200) {\n          this.resultList = res.data || []\n          // 提取所有分类\n          this.categoryList = [...new Set(this.resultList.map(item => item.category))]\n        }\n        this.loading = false\n      })\n    },\n    // 获取\"好\"等次人员列表\n    getGoodGradeList() {\n      listQuarterlyGoodGrade(this.queryParams).then(res => {\n        if (res.code === 200) {\n          this.goodGradeList = res.data || []\n        }\n      })\n    },\n    // 按年度查询\n    handleQuery() {\n      this.getResultList()\n      this.getGoodGradeList()\n    },\n    // 重置查询\n    resetQuery() {\n      this.queryParams = {\n        year: new Date().getFullYear(),\n        quarter: Math.floor((new Date().getMonth() + 3) / 3)\n      }\n      this.handleQuery()\n    }\n  }\n}\n</script>\n\n<template>\n  <div class=\"app-container\">\n    <!-- 查询区域 -->\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" class=\"search-form\">\n      <el-form-item label=\"年度\">\n        <el-input v-model=\"queryParams.year\" placeholder=\"请输入年度\" clearable type=\"number\" />\n      </el-form-item>\n      <el-form-item label=\"季度\">\n        <el-select v-model=\"queryParams.quarter\" placeholder=\"请选择季度\" clearable>\n          <el-option label=\"第一季度\" :value=\"1\" />\n          <el-option label=\"第二季度\" :value=\"2\" />\n          <el-option label=\"第三季度\" :value=\"3\" />\n          <el-option label=\"第四季度\" :value=\"4\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" @click=\"handleQuery\">查询</el-button>\n        <el-button @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <!-- 分类排序展示区域 -->\n    <el-card class=\"box-card\" v-loading=\"loading\">\n      <div slot=\"header\" class=\"card-header\">\n        <span>{{ queryParams.year }}年第{{ queryParams.quarter }}季度绩效结果分类排序</span>\n      </div>\n      <div v-for=\"category in categoryList\" :key=\"category\" class=\"category-section\">\n        <h3>{{ category }}</h3>\n        <el-table :data=\"resultList.filter(item => item.category === category)\" border style=\"width: 100%\">\n          <el-table-column prop=\"deptName\" label=\"科室\" />\n          <el-table-column prop=\"position\" label=\"职务\" />\n          <el-table-column prop=\"userName\" label=\"姓名\" />\n          <el-table-column prop=\"quarterScore\" label=\"季度得分\" />\n          <el-table-column prop=\"sameLevelOrder\" label=\"同层级排序\" />\n          <el-table-column prop=\"grade\" label=\"等次\" />\n        </el-table>\n      </div>\n    </el-card>\n\n    <!-- \"好\"等次人员名单 -->\n    <el-card class=\"box-card\" style=\"margin-top: 20px;\">\n      <div slot=\"header\" class=\"card-header\">\n        <span>{{ queryParams.year }}年第{{ queryParams.quarter }}季度\"好\"等次人员名单</span>\n      </div>\n      <el-table :data=\"goodGradeList\" border style=\"width: 100%\">\n        <el-table-column prop=\"deptName\" label=\"科室\" />\n        <el-table-column prop=\"position\" label=\"职务\" />\n        <el-table-column prop=\"category\" label=\"分类\" />\n        <el-table-column prop=\"userName\" label=\"姓名\" />\n        <el-table-column prop=\"quarterScore\" label=\"季度得分\" />\n        <el-table-column prop=\"sameLevelOrder\" label=\"同层级排序\" />\n      </el-table>\n    </el-card>\n  </div>\n</template>\n\n<style scoped lang=\"scss\">\n.search-form {\n  margin-bottom: 20px;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.category-section {\n  margin-bottom: 20px;\n\n  h3 {\n    margin-bottom: 10px;\n    padding-left: 5px;\n    border-left: 4px solid #409EFF;\n  }\n}\n</style>\n"]}]}