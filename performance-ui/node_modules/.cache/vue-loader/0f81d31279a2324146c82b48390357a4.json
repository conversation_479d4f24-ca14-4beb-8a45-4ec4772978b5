{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/performance/leader/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/performance/leader/index.vue", "mtime": 1754318751675}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgbGlzdExlYWRlciwgZ2V0TGVhZGVyLCBhZGRMZWFkZXIsIHVwZGF0ZUxlYWRlciwgZGVsTGVhZGVyLAogIGV4cG9ydExlYWRlciwgYmF0Y2hFeHBvcnRMZWFkZXIsIGRvd25sb2FkVGVtcGxhdGUsIGRvd25sb2FkRXhjZWxUZW1wbGF0ZQp9IGZyb20gJ0AvYXBpL3BlcmZvcm1hbmNlL2xlYWRlcicKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiTGVhZGVyUGxhbiIsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIGlkczogW10sCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgbXVsdGlwbGU6IHRydWUsCiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIHRvdGFsOiAwLAogICAgICBsaXN0OiBbXSwKICAgICAgdGl0bGU6ICIiLAogICAgICBvcGVuOiBmYWxzZSwKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBuYW1lOiBudWxsLAogICAgICAgIHRhc2tUeXBlOiBudWxsLAogICAgICAgIG9yZ05hbWU6IG51bGwsCiAgICAgICAgcGxhblllYXI6IG51bGwKICAgICAgfSwKICAgICAgZm9ybToge30sCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgbmFtZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWnk+WQjeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LAogICAgICAgICAgeyBtYXg6IDEwMCwgbWVzc2FnZTogIuWnk+WQjemVv+W6puS4jeiDvei2hei/hzEwMOS4quWtl+espiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICB0YXNrVHlwZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuS7u+WKoeexu+Wei+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LAogICAgICAgICAgeyBtYXg6IDEwMCwgbWVzc2FnZTogIuS7u+WKoeexu+Wei+mVv+W6puS4jeiDvei2hei/hzEwMOS4quWtl+espiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBwZXJmb3JtYW5jZVRhc2s6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLnu6nmlYjku7vliqHkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwKICAgICAgICAgIHsgbWF4OiAxMDAwLCBtZXNzYWdlOiAi57up5pWI5Lu75Yqh6ZW/5bqm5LiN6IO96LaF6L+HMTAwMOS4quWtl+espiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICB0YXJnZXRNZWFzdXJlczogWwogICAgICAgICAgeyBtYXg6IDEwMDAsIG1lc3NhZ2U6ICLnm67moIflj4rmjqrmlr3plb/luqbkuI3og73otoXov4cxMDAw5Liq5a2X56ymIiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIGV2YWx1YXRpb25Dcml0ZXJpYTogWwogICAgICAgICAgeyBtYXg6IDEwMDAsIG1lc3NhZ2U6ICLor4Tku7fmoIflh4bplb/luqbkuI3og73otoXov4cxMDAw5Liq5a2X56ymIiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIHJlc3BvbnNpYmlsaXR5OiBbCiAgICAgICAgICB7IG1heDogMTAwLCBtZXNzYWdlOiAi6LSj5Lu76ZW/5bqm5LiN6IO96LaF6L+HMTAw5Liq5a2X56ymIiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIHRhc2tDYXRlZ29yeTogWwogICAgICAgICAgeyBtYXg6IDEwMCwgbWVzc2FnZTogIui0o+S7u+WIhuexu+mVv+W6puS4jeiDvei2hei/hzEwMOS4quWtl+espiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICB3ZWlnaHRTY29yZTogWwogICAgICAgICAgeyBtYXg6IDUwLCBtZXNzYWdlOiAi5p2D6YeN5YiG5YC86ZW/5bqm5LiN6IO96LaF6L+HNTDkuKrlrZfnrKYiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgZGVhZGxpbmU6IFsKICAgICAgICAgIHsgbWF4OiAxMDAsIG1lc3NhZ2U6ICLlrozmiJDml7bpmZDplb/luqbkuI3og73otoXov4cxMDDkuKrlrZfnrKYiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgb3JnTmFtZTogWwogICAgICAgICAgeyBtYXg6IDEwMCwgbWVzc2FnZTogIuaJgOWxnue7hOe7h+mVv+W6puS4jeiDvei2hei/hzEwMOS4quWtl+espiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBwbGFuWWVhcjogWwogICAgICAgICAgeyBtYXg6IDEwLCBtZXNzYWdlOiAi6K6h5YiS5bm05Lu96ZW/5bqm5LiN6IO96LaF6L+HMTDkuKrlrZfnrKYiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0KICAgICAgfSwKICAgICAgaW1wb3J0VXJsOiBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgJy9wZXJmb3JtYW5jZS9sZWFkZXIvaW1wb3J0RXhjZWwnLAogICAgICBpbXBvcnRQYXJhbXM6IHsgcGxhblllYXI6ICcnLCBvcmdOYW1lOiAnJyB9LAogICAgICB1cGxvYWRIZWFkZXJzOiB7fQogICAgfQogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgLy8g6K6+572u5LiK5Lyg6K6k6K+B5aS0CiAgICB0aGlzLnVwbG9hZEhlYWRlcnMgPSB7CiAgICAgIEF1dGhvcml6YXRpb246ICdCZWFyZXIgJyArIHRoaXMuJHN0b3JlLmdldHRlcnMudG9rZW4KICAgIH07CiAgfSwKICBtZXRob2RzOiB7CiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBsaXN0TGVhZGVyKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMubGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgY2FuY2VsKCkgewogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgdGhpcy5yZXNldCgpOwogICAgfSwKICAgIHJlc2V0KCkgewogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgaWQ6IG51bGwsCiAgICAgICAgbmFtZTogbnVsbCwKICAgICAgICB0YXNrVHlwZTogbnVsbCwKICAgICAgICBwZXJmb3JtYW5jZVRhc2s6IG51bGwsCiAgICAgICAgdGFyZ2V0TWVhc3VyZXM6IG51bGwsCiAgICAgICAgZXZhbHVhdGlvbkNyaXRlcmlhOiBudWxsLAogICAgICAgIHJlc3BvbnNpYmlsaXR5OiBudWxsLAogICAgICAgIHRhc2tDYXRlZ29yeTogbnVsbCwKICAgICAgICB3ZWlnaHRTY29yZTogbnVsbCwKICAgICAgICBkZWFkbGluZTogbnVsbCwKICAgICAgICBvcmdOYW1lOiBudWxsLAogICAgICAgIHBsYW5ZZWFyOiBudWxsCiAgICAgIH07CiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7CiAgICB9LAogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLmlkKQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGghPT0xCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aAogICAgfSwKICAgIGhhbmRsZUFkZCgpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICB0aGlzLm9wZW4gPSB0cnVlOwogICAgICB0aGlzLnRpdGxlID0gIua3u+WKoOePreWtkOaIkOWRmOe7qeaViOiuoeWIkiI7CiAgICB9LAogICAgaGFuZGxlRWRpdChyb3cpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICBjb25zdCBpZCA9IHJvdy5pZDsKICAgICAgZ2V0TGVhZGVyKGlkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOwogICAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnnj63lrZDmiJDlkZjnu6nmlYjorqHliJIiOwogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuiOt+WPluaVsOaNruWksei0pSIpOwogICAgICB9KTsKICAgIH0sCiAgICBzdWJtaXRGb3JtKCkgewogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgaWYgKHRoaXMuZm9ybS5pZCAhPSBudWxsKSB7CiAgICAgICAgICAgIHVwZGF0ZUxlYWRlcih0aGlzLmZvcm0pLnRoZW4oKCkgPT4gewogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOwogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuS/ruaUueWksei0pSIpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIGFkZExlYWRlcih0aGlzLmZvcm0pLnRoZW4oKCkgPT4gewogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOwogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuaWsOWinuWksei0pSIpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgY29uc3QgaWRzID0gcm93ID8gW3Jvdy5pZF0gOiB0aGlzLmlkczsKICAgICAgY29uc3QgbWVzc2FnZSA9IHJvdwogICAgICAgID8gYOaYr+WQpuehruiupOWIoOmZpCIke3Jvdy5uYW1lfSLnmoTnu6nmlYjorqHliJLmlbDmja7pobnvvJ9gCiAgICAgICAgOiBg5piv5ZCm56Gu6K6k5Yig6Zmk6YCJ5Lit55qEJHtpZHMubGVuZ3RofeadoeePreWtkOaIkOWRmOe7qeaViOiuoeWIkuaVsOaNrumhue+8n2A7CgogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKG1lc3NhZ2UpLnRoZW4oZnVuY3Rpb24oKSB7CiAgICAgICAgcmV0dXJuIGRlbExlYWRlcihpZHMuam9pbignLCcpKTsKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7CiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsKICAgIH0sCiAgICBoYW5kbGVFeHBvcnRTZWxlY3RlZCgpIHsKICAgICAgaWYgKHRoaXMuaWRzLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLor7fpgInmi6nopoHlr7zlh7rnmoTmlbDmja4iKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgLy8g5aaC5p6c5Y+q6YCJ5oup5LqG5LiA5p2h5pWw5o2u77yM5L2/55So5Y2V5Liq5a+85Ye6CiAgICAgIGlmICh0aGlzLmlkcy5sZW5ndGggPT09IDEpIHsKICAgICAgICBjb25zdCBzZWxlY3RlZFJvdyA9IHRoaXMubGlzdC5maW5kKGl0ZW0gPT4gaXRlbS5pZCA9PT0gdGhpcy5pZHNbMF0pOwogICAgICAgIHRoaXMuaGFuZGxlRXhwb3J0U2luZ2xlKHNlbGVjdGVkUm93KTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgLy8g5aSa5p2h5pWw5o2u5L2/55So5om56YeP5a+85Ye6CiAgICAgIHRoaXMuJG1vZGFsLmxvYWRpbmcoIuato+WcqOWvvOWHuuaVsOaNru+8jOivt+eojeWAmS4uLiIpOwogICAgICBiYXRjaEV4cG9ydExlYWRlcih0aGlzLmlkcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgY29uc3QgYmxvYiA9IG5ldyBCbG9iKFtyZXNwb25zZV0sIHsgCiAgICAgICAgICB0eXBlOiAnYXBwbGljYXRpb24vdm5kLm9wZW54bWxmb3JtYXRzLW9mZmljZWRvY3VtZW50LndvcmRwcm9jZXNzaW5nbWwuZG9jdW1lbnQnIAogICAgICAgIH0pOwogICAgICAgIGNvbnN0IGxpbmsgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdhJyk7CiAgICAgICAgbGluay5ocmVmID0gd2luZG93LlVSTC5jcmVhdGVPYmplY3RVUkwoYmxvYik7CiAgICAgICAgbGluay5kb3dubG9hZCA9ICfnj63lrZDmiJDlkZjnu6nmlYjorqHliJIuZG9jeCc7CiAgICAgICAgbGluay5jbGljaygpOwogICAgICAgIHdpbmRvdy5VUkwucmV2b2tlT2JqZWN0VVJMKGxpbmsuaHJlZik7CiAgICAgICAgdGhpcy4kbW9kYWwuY2xvc2VMb2FkaW5nKCk7CiAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICB0aGlzLiRtb2RhbC5jbG9zZUxvYWRpbmcoKTsKICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlRXhwb3J0U2luZ2xlKHJvdykgewogICAgICB0aGlzLiRtb2RhbC5sb2FkaW5nKCLmraPlnKjlr7zlh7rmlbDmja7vvIzor7fnqI3lgJkuLi4iKTsKICAgICAgZXhwb3J0TGVhZGVyKHJvdy5pZCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgY29uc3QgYmxvYiA9IG5ldyBCbG9iKFtyZXNwb25zZV0sIHsgCiAgICAgICAgICB0eXBlOiAnYXBwbGljYXRpb24vdm5kLm9wZW54bWxmb3JtYXRzLW9mZmljZWRvY3VtZW50LndvcmRwcm9jZXNzaW5nbWwuZG9jdW1lbnQnIAogICAgICAgIH0pOwogICAgICAgIGNvbnN0IGxpbmsgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdhJyk7CiAgICAgICAgbGluay5ocmVmID0gd2luZG93LlVSTC5jcmVhdGVPYmplY3RVUkwoYmxvYik7CiAgICAgICAgbGluay5kb3dubG9hZCA9IGAke3Jvdy5uYW1lIHx8ICfnj63lrZDmiJDlkZgnfV/nu6nmlYjorqHliJIuZG9jeGA7CiAgICAgICAgbGluay5jbGljaygpOwogICAgICAgIHdpbmRvdy5VUkwucmV2b2tlT2JqZWN0VVJMKGxpbmsuaHJlZik7CiAgICAgICAgdGhpcy4kbW9kYWwuY2xvc2VMb2FkaW5nKCk7CiAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICB0aGlzLiRtb2RhbC5jbG9zZUxvYWRpbmcoKTsKICAgICAgfSk7CiAgICB9LAogICAgYmVmb3JlSW1wb3J0VXBsb2FkKGZpbGUpIHsKICAgICAgY29uc3QgaXNFeGNlbCA9IGZpbGUudHlwZSA9PT0gJ2FwcGxpY2F0aW9uL3ZuZC5vcGVueG1sZm9ybWF0cy1vZmZpY2Vkb2N1bWVudC5zcHJlYWRzaGVldG1sLnNoZWV0JyB8fAogICAgICAgICAgICAgICAgICAgICAgZmlsZS50eXBlID09PSAnYXBwbGljYXRpb24vdm5kLm1zLWV4Y2VsJzsKICAgICAgaWYgKCFpc0V4Y2VsKSB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoJ+WPquiDveS4iuS8oEV4Y2Vs5paH5Lu25qC85byPKC54bHN45oiWLnhscykhJyk7CiAgICAgIH0KICAgICAgcmV0dXJuIGlzRXhjZWw7CiAgICB9LAogICAgaGFuZGxlSW1wb3J0U3VjY2VzcyhyZXNwb25zZSkgewogICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygn5a+85YWl5oiQ5YqfJyk7CiAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IocmVzcG9uc2UubXNnIHx8ICflr7zlhaXlpLHotKUnKTsKICAgICAgfQogICAgfSwKICAgIGhhbmRsZUltcG9ydEVycm9yKGVycm9yKSB7CiAgICAgIGNvbnNvbGUuZXJyb3IoIuWvvOWFpeWksei0pToiLCBlcnJvcik7CiAgICAgIGlmIChlcnJvci5zdGF0dXMgPT09IDQwMSkgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLorqTor4HlpLHotKXvvIzor7fph43mlrDnmbvlvZUiKTsKICAgICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnTG9nT3V0JykudGhlbigoKSA9PiB7CiAgICAgICAgICBsb2NhdGlvbi5ocmVmID0gJy9sb2dpbic7CiAgICAgICAgfSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuWvvOWFpeWksei0pe+8jOivt+ajgOafpeaWh+S7tuagvOW8jyIpOwogICAgICB9CiAgICB9LAogICAgZG93bmxvYWRUZW1wbGF0ZSgpIHsKICAgICAgZG93bmxvYWRFeGNlbFRlbXBsYXRlKCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgY29uc3QgYmxvYiA9IG5ldyBCbG9iKFtyZXNwb25zZV0sIHsKICAgICAgICAgIHR5cGU6ICdhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQuc3ByZWFkc2hlZXRtbC5zaGVldCcKICAgICAgICB9KTsKICAgICAgICBjb25zdCBsaW5rID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnYScpOwogICAgICAgIGxpbmsuaHJlZiA9IHdpbmRvdy5VUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpOwogICAgICAgIGxpbmsuZG93bmxvYWQgPSAn54+t5a2Q5oiQ5ZGY57up5pWI6K6h5YiS5qih5p2/Lnhsc3gnOwogICAgICAgIGxpbmsuY2xpY2soKTsKICAgICAgICB3aW5kb3cuVVJMLnJldm9rZU9iamVjdFVSTChsaW5rLmhyZWYpOwogICAgICB9KTsKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqKA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/performance/leader", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"姓名\" prop=\"name\">\n        <el-input\n          v-model=\"queryParams.name\"\n          placeholder=\"请输入姓名\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"任务类型\" prop=\"taskType\">\n        <el-input\n          v-model=\"queryParams.taskType\"\n          placeholder=\"请输入任务类型\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\" v-hasPermi=\"['performance:leader:add']\">新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"danger\" plain icon=\"el-icon-delete\" size=\"mini\" :disabled=\"multiple\" @click=\"handleDelete\" v-hasPermi=\"['performance:leader:remove']\">删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" :disabled=\"multiple\" @click=\"handleExportSelected\" v-hasPermi=\"['performance:leader:export']\">导出Word</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-upload\n          class=\"upload-demo\"\n          :action=\"importUrl\"\n          :headers=\"uploadHeaders\"\n          :on-success=\"handleImportSuccess\"\n          :on-error=\"handleImportError\"\n          :before-upload=\"beforeImportUpload\"\n          :show-file-list=\"false\"\n          :data=\"importParams\"\n          style=\"display: inline-block;\"\n          v-hasPermi=\"['performance:leader:import']\">\n          <el-button type=\"info\" plain icon=\"el-icon-upload2\" size=\"mini\">导入Excel</el-button>\n        </el-upload>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"info\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"downloadTemplate\" v-hasPermi=\"['performance:leader:list']\">下载模板</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n    \n    <el-table \n      v-loading=\"loading\"\n      :data=\"list\" \n      @selection-change=\"handleSelectionChange\"\n      style=\"width: 100%\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column prop=\"name\" label=\"姓名\" width=\"100\"/>\n      <el-table-column prop=\"taskType\" label=\"任务类型\" width=\"120\"/>\n      <el-table-column prop=\"performanceTask\" label=\"绩效任务\" min-width=\"200\" show-overflow-tooltip/>\n      <el-table-column prop=\"targetMeasures\" label=\"目标及措施\" min-width=\"200\" show-overflow-tooltip/>\n      <el-table-column prop=\"evaluationCriteria\" label=\"评价标准\" min-width=\"200\" show-overflow-tooltip/>\n      <el-table-column prop=\"taskCategory\" label=\"责任分类\" width=\"100\"/>\n      <el-table-column prop=\"weightScore\" label=\"权重分值\" width=\"100\"/>\n      <el-table-column prop=\"deadline\" label=\"完成时限\" width=\"120\"/>\n      <el-table-column label=\"操作\" align=\"center\" width=\"180\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleEdit(scope.row)\"\n            v-hasPermi=\"['performance:leader:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['performance:leader:remove']\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"900px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"姓名\" prop=\"name\">\n              <el-input v-model=\"form.name\" placeholder=\"请输入姓名\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"任务类型\" prop=\"taskType\">\n              <el-input v-model=\"form.taskType\" placeholder=\"请输入任务类型/类别\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-form-item label=\"绩效任务\" prop=\"performanceTask\">\n          <el-input v-model=\"form.performanceTask\" type=\"textarea\" placeholder=\"请输入绩效任务\" />\n        </el-form-item>\n        <el-form-item label=\"目标及措施\" prop=\"targetMeasures\">\n          <el-input v-model=\"form.targetMeasures\" type=\"textarea\" placeholder=\"请输入目标及措施\" />\n        </el-form-item>\n        <el-form-item label=\"评价标准\" prop=\"evaluationCriteria\">\n          <el-input v-model=\"form.evaluationCriteria\" type=\"textarea\" placeholder=\"请输入评价标准\" />\n        </el-form-item>\n        <el-row>\n          <el-col :span=\"8\">\n            <el-form-item label=\"责任\" prop=\"responsibility\">\n              <el-input v-model=\"form.responsibility\" placeholder=\"请输入责任\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"责任分类\" prop=\"taskCategory\">\n              <el-input v-model=\"form.taskCategory\" placeholder=\"请输入责任分类\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"权重分值\" prop=\"weightScore\">\n              <el-input v-model=\"form.weightScore\" placeholder=\"请输入权重分值\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"8\">\n            <el-form-item label=\"完成时限\" prop=\"deadline\">\n              <el-input v-model=\"form.deadline\" placeholder=\"请输入完成时限\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属组织\" prop=\"orgName\">\n              <el-input v-model=\"form.orgName\" placeholder=\"请输入所属组织\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"计划年份\" prop=\"planYear\">\n              <el-input v-model=\"form.planYear\" placeholder=\"请输入计划年份\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  listLeader, getLeader, addLeader, updateLeader, delLeader,\n  exportLeader, batchExportLeader, downloadTemplate, downloadExcelTemplate\n} from '@/api/performance/leader'\n\nexport default {\n  name: \"LeaderPlan\",\n  data() {\n    return {\n      loading: true,\n      ids: [],\n      single: true,\n      multiple: true,\n      showSearch: true,\n      total: 0,\n      list: [],\n      title: \"\",\n      open: false,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        name: null,\n        taskType: null,\n        orgName: null,\n        planYear: null\n      },\n      form: {},\n      rules: {\n        name: [\n          { required: true, message: \"姓名不能为空\", trigger: \"blur\" },\n          { max: 100, message: \"姓名长度不能超过100个字符\", trigger: \"blur\" }\n        ],\n        taskType: [\n          { required: true, message: \"任务类型不能为空\", trigger: \"blur\" },\n          { max: 100, message: \"任务类型长度不能超过100个字符\", trigger: \"blur\" }\n        ],\n        performanceTask: [\n          { required: true, message: \"绩效任务不能为空\", trigger: \"blur\" },\n          { max: 1000, message: \"绩效任务长度不能超过1000个字符\", trigger: \"blur\" }\n        ],\n        targetMeasures: [\n          { max: 1000, message: \"目标及措施长度不能超过1000个字符\", trigger: \"blur\" }\n        ],\n        evaluationCriteria: [\n          { max: 1000, message: \"评价标准长度不能超过1000个字符\", trigger: \"blur\" }\n        ],\n        responsibility: [\n          { max: 100, message: \"责任长度不能超过100个字符\", trigger: \"blur\" }\n        ],\n        taskCategory: [\n          { max: 100, message: \"责任分类长度不能超过100个字符\", trigger: \"blur\" }\n        ],\n        weightScore: [\n          { max: 50, message: \"权重分值长度不能超过50个字符\", trigger: \"blur\" }\n        ],\n        deadline: [\n          { max: 100, message: \"完成时限长度不能超过100个字符\", trigger: \"blur\" }\n        ],\n        orgName: [\n          { max: 100, message: \"所属组织长度不能超过100个字符\", trigger: \"blur\" }\n        ],\n        planYear: [\n          { max: 10, message: \"计划年份长度不能超过10个字符\", trigger: \"blur\" }\n        ]\n      },\n      importUrl: process.env.VUE_APP_BASE_API + '/performance/leader/importExcel',\n      importParams: { planYear: '', orgName: '' },\n      uploadHeaders: {}\n    }\n  },\n  created() {\n    this.getList();\n    // 设置上传认证头\n    this.uploadHeaders = {\n      Authorization: 'Bearer ' + this.$store.getters.token\n    };\n  },\n  methods: {\n    getList() {\n      this.loading = true;\n      listLeader(this.queryParams).then(response => {\n        this.list = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    reset() {\n      this.form = {\n        id: null,\n        name: null,\n        taskType: null,\n        performanceTask: null,\n        targetMeasures: null,\n        evaluationCriteria: null,\n        responsibility: null,\n        taskCategory: null,\n        weightScore: null,\n        deadline: null,\n        orgName: null,\n        planYear: null\n      };\n      this.resetForm(\"form\");\n    },\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加班子成员绩效计划\";\n    },\n    handleEdit(row) {\n      this.reset();\n      const id = row.id;\n      getLeader(id).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改班子成员绩效计划\";\n      }).catch(() => {\n        this.$modal.msgError(\"获取数据失败\");\n      });\n    },\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id != null) {\n            updateLeader(this.form).then(() => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            }).catch(() => {\n              this.$modal.msgError(\"修改失败\");\n            });\n          } else {\n            addLeader(this.form).then(() => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            }).catch(() => {\n              this.$modal.msgError(\"新增失败\");\n            });\n          }\n        }\n      });\n    },\n    handleDelete(row) {\n      const ids = row ? [row.id] : this.ids;\n      const message = row\n        ? `是否确认删除\"${row.name}\"的绩效计划数据项？`\n        : `是否确认删除选中的${ids.length}条班子成员绩效计划数据项？`;\n\n      this.$modal.confirm(message).then(function() {\n        return delLeader(ids.join(','));\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    handleExportSelected() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要导出的数据\");\n        return;\n      }\n      // 如果只选择了一条数据，使用单个导出\n      if (this.ids.length === 1) {\n        const selectedRow = this.list.find(item => item.id === this.ids[0]);\n        this.handleExportSingle(selectedRow);\n        return;\n      }\n      // 多条数据使用批量导出\n      this.$modal.loading(\"正在导出数据，请稍候...\");\n      batchExportLeader(this.ids).then(response => {\n        const blob = new Blob([response], { \n          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' \n        });\n        const link = document.createElement('a');\n        link.href = window.URL.createObjectURL(blob);\n        link.download = '班子成员绩效计划.docx';\n        link.click();\n        window.URL.revokeObjectURL(link.href);\n        this.$modal.closeLoading();\n      }).catch(() => {\n        this.$modal.closeLoading();\n      });\n    },\n    handleExportSingle(row) {\n      this.$modal.loading(\"正在导出数据，请稍候...\");\n      exportLeader(row.id).then(response => {\n        const blob = new Blob([response], { \n          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' \n        });\n        const link = document.createElement('a');\n        link.href = window.URL.createObjectURL(blob);\n        link.download = `${row.name || '班子成员'}_绩效计划.docx`;\n        link.click();\n        window.URL.revokeObjectURL(link.href);\n        this.$modal.closeLoading();\n      }).catch(() => {\n        this.$modal.closeLoading();\n      });\n    },\n    beforeImportUpload(file) {\n      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\n                      file.type === 'application/vnd.ms-excel';\n      if (!isExcel) {\n        this.$modal.msgError('只能上传Excel文件格式(.xlsx或.xls)!');\n      }\n      return isExcel;\n    },\n    handleImportSuccess(response) {\n      if (response.code === 200) {\n        this.$modal.msgSuccess('导入成功');\n        this.getList();\n      } else {\n        this.$modal.msgError(response.msg || '导入失败');\n      }\n    },\n    handleImportError(error) {\n      console.error(\"导入失败:\", error);\n      if (error.status === 401) {\n        this.$modal.msgError(\"认证失败，请重新登录\");\n        this.$store.dispatch('LogOut').then(() => {\n          location.href = '/login';\n        });\n      } else {\n        this.$modal.msgError(\"导入失败，请检查文件格式\");\n      }\n    },\n    downloadTemplate() {\n      downloadExcelTemplate().then(response => {\n        const blob = new Blob([response], {\n          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\n        });\n        const link = document.createElement('a');\n        link.href = window.URL.createObjectURL(blob);\n        link.download = '班子成员绩效计划模板.xlsx';\n        link.click();\n        window.URL.revokeObjectURL(link.href);\n      });\n    }\n  }\n}\n</script> \n\n<style scoped>\n.upload-demo {\n  display: inline-block;\n  }\n  </style> "]}]}