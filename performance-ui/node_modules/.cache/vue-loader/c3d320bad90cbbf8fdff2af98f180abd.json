{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/RuoYi/Git/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/RuoYi/Git/index.vue", "mtime": 1753510684528}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnUnVvWWlHaXQnLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB1cmw6ICdodHRwczovL2dpdGVlLmNvbS95X3Byb2plY3QvUnVvWWktVnVlJwogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgZ290bygpIHsKICAgICAgd2luZG93Lm9wZW4odGhpcy51cmwpCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/RuoYi/Git", "sourcesContent": ["<template>\n  <div>\n    <svg-icon icon-class=\"github\" @click=\"goto\" />\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'RuoYiGit',\n  data() {\n    return {\n      url: 'https://gitee.com/y_project/RuoYi-Vue'\n    }\n  },\n  methods: {\n    goto() {\n      window.open(this.url)\n    }\n  }\n}\n</script>"]}]}