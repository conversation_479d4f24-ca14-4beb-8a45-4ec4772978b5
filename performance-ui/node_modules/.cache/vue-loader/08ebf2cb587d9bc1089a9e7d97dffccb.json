{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/index.vue?vue&type=style&index=0&id=a83bd3b0&scoped=true&lang=css", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/index.vue", "mtime": 1753510684533}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/css-loader/dist/cjs.js", "mtime": 1753510683027}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1753510684373}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/postcss-loader/src/index.js", "mtime": 1753510684004}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLndlbGNvbWUtY29udGVudCB7CiAgcGFkZGluZzogMjBweDsKICB0ZXh0LWFsaWduOiBjZW50ZXI7Cn0KCi53ZWxjb21lLXRleHQgaDIgewogIGNvbG9yOiAjMzAzMTMzOwogIG1hcmdpbi1ib3R0b206IDE1cHg7Cn0KCi53ZWxjb21lLXRleHQgcCB7CiAgY29sb3I6ICM2MDYyNjY7CiAgZm9udC1zaXplOiAxNnB4OwogIGxpbmUtaGVpZ2h0OiAxLjY7Cn0KCi5mZWF0dXJlLWNhcmQgewogIHRleHQtYWxpZ246IGNlbnRlcjsKICBwYWRkaW5nOiAyMHB4OwogIGhlaWdodDogMTgwcHg7CiAgY3Vyc29yOiBwb2ludGVyOwogIHRyYW5zaXRpb246IGFsbCAwLjNzOwp9CgouZmVhdHVyZS1jYXJkOmhvdmVyIHsKICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTVweCk7Cn0KCi5mZWF0dXJlLWljb24gewogIGZvbnQtc2l6ZTogNDhweDsKICBtYXJnaW4tYm90dG9tOiAxNXB4Owp9CgouZmVhdHVyZS1jYXJkIGgzIHsKICBjb2xvcjogIzMwMzEzMzsKICBtYXJnaW4tYm90dG9tOiAxMHB4Owp9CgouZmVhdHVyZS1jYXJkIHAgewogIGNvbG9yOiAjOTA5Mzk5OwogIGZvbnQtc2l6ZTogMTRweDsKfQoKCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2EA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"20\">\n      <el-col :span=\"24\">\n        <el-card class=\"box-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span style=\"font-size: 18px; font-weight: bold;\">欢迎使用绩效管理系统</span>\n          </div>\n          <div class=\"welcome-content\">\n            <div class=\"welcome-text\">\n              <h2>科级及以下人员绩效评估系统</h2>\n              <p>本系统用于绩效管理，其中绩效计划包括组织绩效、科室绩效、班子成员绩效和个人绩效的全流程管理。</p>\n            </div>\n            \n            <el-row :gutter=\"20\" style=\"margin-top: 30px;\">\n              <el-col :span=\"6\">\n                <el-card class=\"feature-card\" shadow=\"hover\">\n                  <div class=\"feature-icon\">\n                    <i class=\"el-icon-s-grid\" style=\"color: #409EFF;\"></i>\n                  </div>\n                  <h3>组织绩效</h3>\n                </el-card>\n              </el-col>\n              \n              <el-col :span=\"6\">\n                <el-card class=\"feature-card\" shadow=\"hover\">\n                  <div class=\"feature-icon\">\n                    <i class=\"el-icon-office-building\" style=\"color: #67C23A;\"></i>\n                  </div>\n                  <h3>科室绩效</h3>\n                </el-card>\n              </el-col>\n              \n              <el-col :span=\"6\">\n                <el-card class=\"feature-card\" shadow=\"hover\">\n                  <div class=\"feature-icon\">\n                    <i class=\"el-icon-user-solid\" style=\"color: #E6A23C;\"></i>\n                  </div>\n                  <h3>班子成员绩效</h3>\n                </el-card>\n              </el-col>\n              \n              <el-col :span=\"6\">\n                <el-card class=\"feature-card\" shadow=\"hover\">\n                  <div class=\"feature-icon\">\n                    <i class=\"el-icon-user\" style=\"color: #F56C6C;\"></i>\n                  </div>\n                  <h3>个人绩效</h3>\n                </el-card>\n              </el-col>\n            </el-row>\n            \n\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"Index\",\n  data() {\n    return {\n      \n    }\n  },\n  methods: {\n    \n  }\n}\n</script>\n\n<style scoped>\n.welcome-content {\n  padding: 20px;\n  text-align: center;\n}\n\n.welcome-text h2 {\n  color: #303133;\n  margin-bottom: 15px;\n}\n\n.welcome-text p {\n  color: #606266;\n  font-size: 16px;\n  line-height: 1.6;\n}\n\n.feature-card {\n  text-align: center;\n  padding: 20px;\n  height: 180px;\n  cursor: pointer;\n  transition: all 0.3s;\n}\n\n.feature-card:hover {\n  transform: translateY(-5px);\n}\n\n.feature-icon {\n  font-size: 48px;\n  margin-bottom: 15px;\n}\n\n.feature-card h3 {\n  color: #303133;\n  margin-bottom: 10px;\n}\n\n.feature-card p {\n  color: #909399;\n  font-size: 14px;\n}\n\n\n</style> "]}]}