{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/RightToolbar/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/RightToolbar/index.vue", "mtime": 1753510684528}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/RightToolbar", "sourcesContent": ["<template>\n  <div class=\"top-right-btn\" :style=\"style\">\n    <el-row>\n      <el-tooltip class=\"item\" effect=\"dark\" :content=\"showSearch ? '隐藏搜索' : '显示搜索'\" placement=\"top\" v-if=\"search\">\n        <el-button size=\"mini\" circle icon=\"el-icon-search\" @click=\"toggleSearch()\" />\n      </el-tooltip>\n      <el-tooltip class=\"item\" effect=\"dark\" content=\"刷新\" placement=\"top\">\n        <el-button size=\"mini\" circle icon=\"el-icon-refresh\" @click=\"refresh()\" />\n      </el-tooltip>\n      <el-tooltip class=\"item\" effect=\"dark\" content=\"显隐列\" placement=\"top\" v-if=\"columns\">\n        <el-button size=\"mini\" circle icon=\"el-icon-menu\" @click=\"showColumn()\" v-if=\"showColumnsType == 'transfer'\"/>\n        <el-dropdown trigger=\"click\" :hide-on-click=\"false\" style=\"padding-left: 12px\" v-if=\"showColumnsType == 'checkbox'\">\n          <el-button size=\"mini\" circle icon=\"el-icon-menu\" />\n          <el-dropdown-menu slot=\"dropdown\">\n            <!-- 全选/反选 按钮 -->\n            <el-dropdown-item>\n              <el-checkbox :indeterminate=\"isIndeterminate\" v-model=\"isChecked\" @change=\"toggleCheckAll\"> 列展示 </el-checkbox>\n            </el-dropdown-item>\n            <div class=\"check-line\"></div>\n            <template v-for=\"item in columns\">\n              <el-dropdown-item :key=\"item.key\">\n                <el-checkbox v-model=\"item.visible\" @change=\"checkboxChange($event, item.label)\" :label=\"item.label\" />\n              </el-dropdown-item>\n            </template>\n          </el-dropdown-menu>\n        </el-dropdown>\n      </el-tooltip>\n    </el-row>\n    <el-dialog :title=\"title\" :visible.sync=\"open\" append-to-body>\n      <el-transfer\n        :titles=\"['显示', '隐藏']\"\n        v-model=\"value\"\n        :data=\"columns\"\n        @change=\"dataChange\"\n      ></el-transfer>\n    </el-dialog>\n  </div>\n</template>\n<script>\nexport default {\n  name: \"RightToolbar\",\n  data() {\n    return {\n      // 显隐数据\n      value: [],\n      // 弹出层标题\n      title: \"显示/隐藏\",\n      // 是否显示弹出层\n      open: false\n    }\n  },\n  props: {\n    /* 是否显示检索条件 */\n    showSearch: {\n      type: Boolean,\n      default: true\n    },\n    /* 显隐列信息 */\n    columns: {\n      type: Array\n    },\n    /* 是否显示检索图标 */\n    search: {\n      type: Boolean,\n      default: true\n    },\n    /* 显隐列类型（transfer穿梭框、checkbox复选框） */\n    showColumnsType: {\n      type: String,\n      default: \"checkbox\"\n    },\n    /* 右外边距 */\n    gutter: {\n      type: Number,\n      default: 10\n    },\n  },\n  computed: {\n    style() {\n      const ret = {}\n      if (this.gutter) {\n        ret.marginRight = `${this.gutter / 2}px`\n      }\n      return ret\n    },\n    isChecked: {\n      get() {\n        return this.columns.every((col) => col.visible)\n      },\n      set() {}\n    },\n    isIndeterminate() {\n      return this.columns.some((col) => col.visible) && !this.isChecked\n    }\n  },\n  created() {\n    if (this.showColumnsType == 'transfer') {\n      // 显隐列初始默认隐藏列\n      for (let item in this.columns) {\n        if (this.columns[item].visible === false) {\n          this.value.push(parseInt(item))\n        }\n      }\n    }\n  },\n  methods: {\n    // 搜索\n    toggleSearch() {\n      this.$emit(\"update:showSearch\", !this.showSearch)\n    },\n    // 刷新\n    refresh() {\n      this.$emit(\"queryTable\")\n    },\n    // 右侧列表元素变化\n    dataChange(data) {\n      for (let item in this.columns) {\n        const key = this.columns[item].key\n        this.columns[item].visible = !data.includes(key)\n      }\n    },\n    // 打开显隐列dialog\n    showColumn() {\n      this.open = true\n    },\n    // 单勾选\n    checkboxChange(event, label) {\n      this.columns.filter(item => item.label == label)[0].visible = event\n    },\n    // 切换全选/反选\n    toggleCheckAll() {\n      const newValue = !this.isChecked\n      this.columns.forEach((col) => (col.visible = newValue))\n    }\n  },\n}\n</script>\n<style lang=\"scss\" scoped>\n::v-deep .el-transfer__button {\n  border-radius: 50%;\n  padding: 12px;\n  display: block;\n  margin-left: 0px;\n}\n::v-deep .el-transfer__button:first-child {\n  margin-bottom: 10px;\n}\n.check-line {\n  width: 90%;\n  height: 1px;\n  background-color: #ccc;\n  margin: 3px auto;\n}\n</style>\n"]}]}