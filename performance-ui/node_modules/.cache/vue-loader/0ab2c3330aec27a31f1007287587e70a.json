{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/evaluation/member/index.vue?vue&type=style&index=0&id=809e5834&scoped=true&lang=scss", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/evaluation/member/index.vue", "mtime": 1753510684533}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/css-loader/dist/cjs.js", "mtime": 1753510683027}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1753510684373}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/postcss-loader/src/index.js", "mtime": 1753510684004}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/sass-loader/dist/cjs.js", "mtime": 1753510684159}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLnNlYXJjaC1mb3JtIHsKICBtYXJnaW4tYm90dG9tOiAyMHB4Owp9Ci5tYjggewogIG1hcmdpbi1ib3R0b206IDhweDsKfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+SA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/evaluation/member", "sourcesContent": ["<script>\nimport { listMemberOrganization, addMemberOrganization, updateMemberOrganization, delMemberOrganization, exportMemberWord, downloadMemberTemplate, importMemberWord } from '@/api/performance/memberOrganization'\n\nexport default {\n  name: 'MemberOrganizationEvaluation',\n  data() {\n    return {\n      loading: false,\n      ids: [],\n      single: true,\n      multiple: true,\n      total: 0,\n      tableData: [],\n      showSearch: true,\n      queryParams: {\n        memberName: '',\n        testTime: ''\n      },\n      form: {},\n      open: false,\n      title: '',\n      rules: {\n        memberName: [\n          { required: true, message: '姓名不能为空', trigger: 'blur' }\n        ],\n        testTime: [\n          { required: true, message: '测评时间不能为空', trigger: 'blur' }\n        ]\n      },\n      upload: {\n        open: false,\n        title: '',\n        isUploading: false,\n        file: null\n      }\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    getList() {\n      this.loading = true\n      listMemberOrganization(this.queryParams).then(res => {\n        this.tableData = res.rows\n        this.total = res.total\n        this.loading = false\n      })\n    },\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length !== 1\n      this.multiple = !selection.length\n    },\n    handleAdd() {\n      this.reset()\n      this.open = true\n      this.title = '新增绩效任务'\n    },\n    handleUpdate(row) {\n      this.reset()\n      this.form = Object.assign({}, row)\n      this.open = true\n      this.title = '修改绩效任务'\n    },\n    submitForm() {\n      this.$refs['form'].validate(valid => {\n        if (valid) {\n          if (this.form.id) {\n            updateMemberOrganization(this.form).then(() => {\n              this.$modal.msgSuccess('修改成功')\n              this.open = false\n              this.getList()\n            })\n          } else {\n            addMemberOrganization(this.form).then(() => {\n              this.$modal.msgSuccess('新增成功')\n              this.open = false\n              this.getList()\n            })\n          }\n        }\n      })\n    },\n    handleDelete(row) {\n      const ids = row.id || this.ids\n      this.$modal.confirm('是否确认删除选中数据项？').then(() => {\n        return delMemberOrganization(ids)\n      }).then(() => {\n        this.getList()\n        this.$modal.msgSuccess('删除成功')\n      }).catch(() => {})\n    },\n    handleExport() {\n      // 校验姓名和测评时间是否有值\n      if (!this.queryParams.memberName || !this.queryParams.memberName.trim()) {\n        this.$message.error('请先输入姓名再导出')\n        return\n      }\n      if (!this.queryParams.testTime || !this.queryParams.testTime.trim()) {\n        this.$message.error('请先输入测评时间再导出')\n        return\n      }\n\n      // 校验是否选择了数据\n      if (!this.ids || this.ids.length === 0) {\n        this.$message.error('请先选择要导出的数据（勾选表格左侧的复选框）')\n        return\n      }\n\n      exportMemberWord(this.queryParams, this.ids).then(res => {\n        const blob = new Blob([res], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })\n        const link = document.createElement('a')\n        link.href = window.URL.createObjectURL(blob)\n        link.download = '班子成员组织绩效考核测评表.docx'\n        link.click()\n        window.URL.revokeObjectURL(link.href)\n      })\n    },\n    handleDownloadTemplate() {\n      downloadMemberTemplate().then(res => {\n        const blob = new Blob([res], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })\n        const link = document.createElement('a')\n        link.href = window.URL.createObjectURL(blob)\n        link.download = '班子成员组织绩效考核测评表模板.docx'\n        link.click()\n        window.URL.revokeObjectURL(link.href)\n      })\n    },\n    handleImport() {\n      this.upload.open = true\n      this.upload.title = '导入班子成员组织绩效考核测评表数据'\n      this.upload.file = null\n    },\n    handleFileChange(file) {\n      this.upload.file = file.raw\n    },\n    submitFileForm() {\n      if (!this.upload.file) {\n        this.$message.error('请先选择文件')\n        return\n      }\n      this.upload.isUploading = true\n      importMemberWord(this.upload.file).then(res => {\n        this.upload.open = false\n        this.upload.isUploading = false\n        this.$alert(res.msg, '导入结果', { dangerouslyUseHTMLString: true })\n        this.getList()\n      }).catch(() => {\n        this.upload.isUploading = false\n      })\n    },\n    reset() {\n      this.form = {\n        id: null,\n        memberName: '',\n        testTime: '',\n        serialNumber: null,\n        taskType: '',\n        performanceTask: '',\n        targetMeasures: '',\n        evaluationStandard: '',\n        responsibilityCategory: '',\n        responsibleLeader: '',\n        completionDeadline: '',\n        evaluationScore: null,\n      }\n      this.$nextTick(() => {\n        if (this.$refs['form']) this.$refs['form'].resetFields()\n      })\n    }\n  },\n  computed: {\n    // 自动计算合计分值\n    totalScore() {\n      return this.tableData.reduce((sum, row) => sum + (parseFloat(row.evaluationScore) || 0), 0).toFixed(2)\n    }\n  }\n}\n</script>\n\n<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" class=\"search-form\">\n      <el-form-item label=\"姓名\" prop=\"memberName\">\n        <el-input v-model=\"queryParams.memberName\" placeholder=\"请输入姓名\" clearable />\n      </el-form-item>\n      <el-form-item label=\"测评时间\" prop=\"testTime\">\n        <el-input v-model=\"queryParams.testTime\" placeholder=\"请输入测评时间\" clearable />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"getList\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" @click=\"reset\">重置</el-button>\n      </el-form-item>\n    </el-form>\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" @click=\"handleAdd\">新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"success\" plain icon=\"el-icon-edit\" :disabled=\"single\" @click=\"handleUpdate\">修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"danger\" plain icon=\"el-icon-delete\" :disabled=\"multiple\" @click=\"handleDelete\">删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"info\" plain icon=\"el-icon-download\" @click=\"handleExport\">导出Word</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"info\" plain icon=\"el-icon-download\" @click=\"handleDownloadTemplate\">下载模板</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"warning\" plain icon=\"el-icon-upload\" @click=\"handleImport\">导入</el-button>\n      </el-col>\n    </el-row>\n    <el-table v-loading=\"loading\" :data=\"tableData\" @selection-change=\"handleSelectionChange\" border>\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"序号\" align=\"center\" prop=\"serialNumber\" width=\"60\" />\n      <el-table-column label=\"任务类型\" align=\"center\" prop=\"taskType\" />\n      <el-table-column label=\"绩效任务\" align=\"center\" prop=\"performanceTask\" />\n      <el-table-column label=\"目标及措施\" align=\"center\" prop=\"targetMeasures\" />\n      <el-table-column label=\"评价标准\" align=\"center\" prop=\"evaluationStandard\" />\n      <el-table-column label=\"责任分类\" align=\"center\" prop=\"responsibilityCategory\" />\n      <el-table-column label=\"责任领导\" align=\"center\" prop=\"responsibleLeader\" />\n      <el-table-column label=\"完成时限\" align=\"center\" prop=\"completionDeadline\" />\n      <el-table-column label=\"评价分值\" align=\"center\" prop=\"evaluationScore\" />\n    </el-table>\n    <pagination v-show=\"total>0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\" @pagination=\"getList\" />\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"姓名\" prop=\"memberName\">\n              <el-input v-model=\"form.memberName\" placeholder=\"请输入姓名\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"测评时间\" prop=\"testTime\">\n              <el-input v-model=\"form.testTime\" placeholder=\"请输入测评时间\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"序号\" prop=\"serialNumber\">\n              <el-input-number v-model=\"form.serialNumber\" :min=\"1\" :max=\"999\" controls-position=\"right\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"任务类型\" prop=\"taskType\">\n              <el-input v-model=\"form.taskType\" placeholder=\"请输入任务类型\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-form-item label=\"绩效任务\" prop=\"performanceTask\">\n          <el-input v-model=\"form.performanceTask\" placeholder=\"请输入绩效任务\" />\n        </el-form-item>\n        <el-form-item label=\"目标及措施\" prop=\"targetMeasures\">\n          <el-input v-model=\"form.targetMeasures\" placeholder=\"请输入目标及措施\" />\n        </el-form-item>\n        <el-form-item label=\"评价标准\" prop=\"evaluationStandard\">\n          <el-input v-model=\"form.evaluationStandard\" placeholder=\"请输入评价标准\" />\n        </el-form-item>\n        <el-form-item label=\"责任分类\" prop=\"responsibilityCategory\">\n          <el-input v-model=\"form.responsibilityCategory\" placeholder=\"请输入责任分类\" />\n        </el-form-item>\n        <el-form-item label=\"责任领导\" prop=\"responsibleLeader\">\n          <el-input v-model=\"form.responsibleLeader\" placeholder=\"请输入责任领导\" />\n        </el-form-item>\n        <el-form-item label=\"完成时限\" prop=\"completionDeadline\">\n          <el-input v-model=\"form.completionDeadline\" placeholder=\"请输入完成时限\" />\n        </el-form-item>\n        <el-form-item label=\"评价分值\" prop=\"evaluationScore\">\n          <el-input-number v-model=\"form.evaluationScore\" :precision=\"2\" :step=\"0.1\" :min=\"0\" :max=\"100\" controls-position=\"right\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"open = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n    <el-dialog :title=\"upload.title\" :visible.sync=\"upload.open\" width=\"400px\" append-to-body>\n      <el-upload\n        :limit=\"1\"\n        accept=\".docx\"\n        :auto-upload=\"false\"\n        :disabled=\"upload.isUploading\"\n        :on-change=\"handleFileChange\"\n        drag\n      >\n        <i class=\"el-icon-upload\"></i>\n        <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\n        <div class=\"el-upload__tip\" slot=\"tip\">只能上传docx文件，且不超过10MB</div>\n      </el-upload>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" :loading=\"upload.isUploading\" @click=\"submitFileForm\">确 定</el-button>\n        <el-button @click=\"upload.open = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<style scoped lang=\"scss\">\n.search-form {\n  margin-bottom: 20px;\n}\n.mb8 {\n  margin-bottom: 8px;\n}\n</style>\n"]}]}