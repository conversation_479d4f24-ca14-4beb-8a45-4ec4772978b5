{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/layout/components/AppMain.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/layout/components/AppMain.vue", "mtime": 1753510684529}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBjb3B5cmlnaHQgZnJvbSAiLi9Db3B5cmlnaHQvaW5kZXgiCmltcG9ydCBpZnJhbWVUb2dnbGUgZnJvbSAiLi9JZnJhbWVUb2dnbGUvaW5kZXgiCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0FwcE1haW4nLAogIGNvbXBvbmVudHM6IHsgaWZyYW1lVG9nZ2xlLCBjb3B5cmlnaHQgfSwKICBjb21wdXRlZDogewogICAgY2FjaGVkVmlld3MoKSB7CiAgICAgIHJldHVybiB0aGlzLiRzdG9yZS5zdGF0ZS50YWdzVmlldy5jYWNoZWRWaWV3cwogICAgfSwKICAgIGtleSgpIHsKICAgICAgcmV0dXJuIHRoaXMuJHJvdXRlLnBhdGgKICAgIH0KICB9LAogIHdhdGNoOiB7CiAgICAkcm91dGUoKSB7CiAgICAgIHRoaXMuYWRkSWZyYW1lKCkKICAgIH0KICB9LAogIG1vdW50ZWQoKSB7CiAgICB0aGlzLmFkZElmcmFtZSgpCiAgfSwKICBtZXRob2RzOiB7CiAgICBhZGRJZnJhbWUoKSB7CiAgICAgIGNvbnN0IHsgbmFtZSB9ID0gdGhpcy4kcm91dGUKICAgICAgaWYgKG5hbWUgJiYgdGhpcy4kcm91dGUubWV0YS5saW5rKSB7CiAgICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ3RhZ3NWaWV3L2FkZElmcmFtZVZpZXcnLCB0aGlzLiRyb3V0ZSkKICAgICAgfQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["AppMain.vue"], "names": [], "mappings": ";;;;;;;;;;;;;AAaA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "AppMain.vue", "sourceRoot": "src/layout/components", "sourcesContent": ["<template>\n  <section class=\"app-main\">\n    <transition name=\"fade-transform\" mode=\"out-in\">\n      <keep-alive :include=\"cachedViews\">\n        <router-view v-if=\"!$route.meta.link\" :key=\"key\" />\n      </keep-alive>\n    </transition>\n    <iframe-toggle />\n    <copyright />\n  </section>\n</template>\n\n<script>\nimport copyright from \"./Copyright/index\"\nimport iframeToggle from \"./IframeToggle/index\"\n\nexport default {\n  name: 'AppMain',\n  components: { iframeToggle, copyright },\n  computed: {\n    cachedViews() {\n      return this.$store.state.tagsView.cachedViews\n    },\n    key() {\n      return this.$route.path\n    }\n  },\n  watch: {\n    $route() {\n      this.addIframe()\n    }\n  },\n  mounted() {\n    this.addIframe()\n  },\n  methods: {\n    addIframe() {\n      const { name } = this.$route\n      if (name && this.$route.meta.link) {\n        this.$store.dispatch('tagsView/addIframeView', this.$route)\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-main {\n  /* 50= navbar  50  */\n  min-height: calc(100vh - 50px);\n  width: 100%;\n  position: relative;\n  overflow: hidden;\n}\n\n.app-main:has(.copyright) {\n  padding-bottom: 36px;\n}\n\n.fixed-header + .app-main {\n  padding-top: 50px;\n}\n\n.hasTagsView {\n  .app-main {\n    /* 84 = navbar + tags-view = 50 + 34 */\n    min-height: calc(100vh - 84px);\n  }\n\n  .fixed-header + .app-main {\n    padding-top: 84px;\n  }\n}\n</style>\n\n<style lang=\"scss\">\n// fix css style bug in open el-dialog\n.el-popup-parent--hidden {\n  .fixed-header {\n    padding-right: 6px;\n  }\n}\n\n::-webkit-scrollbar {\n  width: 6px;\n  height: 6px;\n}\n\n::-webkit-scrollbar-track {\n  background-color: #f1f1f1;\n}\n\n::-webkit-scrollbar-thumb {\n  background-color: #c0c0c0;\n  border-radius: 3px;\n}\n</style>\n"]}]}