{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/layout/components/Sidebar/Logo.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/layout/components/Sidebar/Logo.vue", "mtime": 1753510684529}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBsb2dvSW1nIGZyb20gJ0AvYXNzZXRzL2xvZ28vbG9nby5wbmcnCmltcG9ydCB2YXJpYWJsZXMgZnJvbSAnQC9hc3NldHMvc3R5bGVzL3ZhcmlhYmxlcy5zY3NzJwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdTaWRlYmFyTG9nbycsCiAgcHJvcHM6IHsKICAgIGNvbGxhcHNlOiB7CiAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgIHJlcXVpcmVkOiB0cnVlCiAgICB9CiAgfSwKICBjb21wdXRlZDogewogICAgdmFyaWFibGVzKCkgewogICAgICByZXR1cm4gdmFyaWFibGVzCiAgICB9LAogICAgc2lkZVRoZW1lKCkgewogICAgICByZXR1cm4gdGhpcy4kc3RvcmUuc3RhdGUuc2V0dGluZ3Muc2lkZVRoZW1lCiAgICB9CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgdGl0bGU6ICfnu6nmlYjnrqHnkIbns7vnu58nLAogICAgICBsb2dvOiBsb2dvSW1nCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["Logo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAgBA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Logo.vue", "sourceRoot": "src/layout/components/Sidebar", "sourcesContent": ["<template>\n  <div class=\"sidebar-logo-container\" :class=\"{'collapse':collapse}\" :style=\"{ backgroundColor: sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground }\">\n    <transition name=\"sidebarLogoFade\">\n      <router-link v-if=\"collapse\" key=\"collapse\" class=\"sidebar-logo-link\" to=\"/\">\n        <img v-if=\"logo\" :src=\"logo\" class=\"sidebar-logo\" />\n        <h1 v-else class=\"sidebar-title\" :style=\"{ color: sideTheme === 'theme-dark' ? variables.logoTitleColor : variables.logoLightTitleColor }\">{{ title }} </h1>\n      </router-link>\n      <router-link v-else key=\"expand\" class=\"sidebar-logo-link\" to=\"/\">\n        <img v-if=\"logo\" :src=\"logo\" class=\"sidebar-logo\" />\n        <h1 class=\"sidebar-title\" :style=\"{ color: sideTheme === 'theme-dark' ? variables.logoTitleColor : variables.logoLightTitleColor }\">{{ title }} </h1>\n      </router-link>\n    </transition>\n  </div>\n</template>\n\n<script>\nimport logoImg from '@/assets/logo/logo.png'\nimport variables from '@/assets/styles/variables.scss'\n\nexport default {\n  name: 'SidebarLogo',\n  props: {\n    collapse: {\n      type: Boolean,\n      required: true\n    }\n  },\n  computed: {\n    variables() {\n      return variables\n    },\n    sideTheme() {\n      return this.$store.state.settings.sideTheme\n    }\n  },\n  data() {\n    return {\n      title: '绩效管理系统',\n      logo: logoImg\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.sidebarLogoFade-enter-active {\n  transition: opacity 1.5s;\n}\n\n.sidebarLogoFade-enter,\n.sidebarLogoFade-leave-to {\n  opacity: 0;\n}\n\n.sidebar-logo-container {\n  position: relative;\n  width: 100%;\n  height: 50px;\n  line-height: 50px;\n  background: #2b2f3a;\n  text-align: center;\n  overflow: hidden;\n\n  & .sidebar-logo-link {\n    height: 100%;\n    width: 100%;\n\n    & .sidebar-logo {\n      width: 32px;\n      height: 32px;\n      vertical-align: middle;\n      margin-right: 12px;\n    }\n\n    & .sidebar-title {\n      display: inline-block;\n      margin: 0;\n      color: #fff;\n      font-weight: 600;\n      line-height: 50px;\n      font-size: 14px;\n      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;\n      vertical-align: middle;\n    }\n  }\n\n  &.collapse {\n    .sidebar-logo {\n      margin-right: 0px;\n    }\n  }\n}\n</style>\n"]}]}