{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/layout/index.vue?vue&type=style&index=0&id=13877386&lang=scss&scoped=true", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/layout/index.vue", "mtime": 1753510684530}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/css-loader/dist/cjs.js", "mtime": 1753510683027}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1753510684373}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/postcss-loader/src/index.js", "mtime": 1753510684004}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/sass-loader/dist/cjs.js", "mtime": 1753510684159}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCkBpbXBvcnQgIn5AL2Fzc2V0cy9zdHlsZXMvbWl4aW4uc2NzcyI7CkBpbXBvcnQgIn5AL2Fzc2V0cy9zdHlsZXMvdmFyaWFibGVzLnNjc3MiOwoKLmFwcC13cmFwcGVyIHsKICBAaW5jbHVkZSBjbGVhcmZpeDsKICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgaGVpZ2h0OiAxMDAlOwogIHdpZHRoOiAxMDAlOwoKICAmLm1vYmlsZS5vcGVuU2lkZWJhciB7CiAgICBwb3NpdGlvbjogZml4ZWQ7CiAgICB0b3A6IDA7CiAgfQp9CgouZHJhd2VyLWJnIHsKICBiYWNrZ3JvdW5kOiAjMDAwOwogIG9wYWNpdHk6IDAuMzsKICB3aWR0aDogMTAwJTsKICB0b3A6IDA7CiAgaGVpZ2h0OiAxMDAlOwogIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICB6LWluZGV4OiA5OTk7Cn0KCi5maXhlZC1oZWFkZXIgewogIHBvc2l0aW9uOiBmaXhlZDsKICB0b3A6IDA7CiAgcmlnaHQ6IDA7CiAgei1pbmRleDogOTsKICB3aWR0aDogY2FsYygxMDAlIC0gI3skYmFzZS1zaWRlYmFyLXdpZHRofSk7CiAgdHJhbnNpdGlvbjogd2lkdGggMC4yOHM7Cn0KCi5oaWRlU2lkZWJhciAuZml4ZWQtaGVhZGVyIHsKICB3aWR0aDogY2FsYygxMDAlIC0gNTRweCk7Cn0KCi5zaWRlYmFySGlkZSAuZml4ZWQtaGVhZGVyIHsKICB3aWR0aDogMTAwJTsKfQoKLm1vYmlsZSAuZml4ZWQtaGVhZGVyIHsKICB3aWR0aDogMTAwJTsKfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout", "sourcesContent": ["<template>\n  <div :class=\"classObj\" class=\"app-wrapper\" :style=\"{'--current-color': theme}\">\n    <div v-if=\"device==='mobile'&&sidebar.opened\" class=\"drawer-bg\" @click=\"handleClickOutside\"/>\n    <sidebar v-if=\"!sidebar.hide\" class=\"sidebar-container\"/>\n    <div :class=\"{hasTagsView:needTagsView,sidebarHide:sidebar.hide}\" class=\"main-container\">\n      <div :class=\"{'fixed-header':fixedHeader}\">\n        <navbar @setLayout=\"setLayout\"/>\n        <tags-view v-if=\"needTagsView\"/>\n      </div>\n      <app-main/>\n      <settings ref=\"settingRef\"/>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { AppMain, Navbar, Settings, Sidebar, TagsView } from './components'\nimport ResizeMixin from './mixin/ResizeHandler'\nimport { mapState } from 'vuex'\nimport variables from '@/assets/styles/variables.scss'\n\nexport default {\n  name: 'Layout',\n  components: {\n    AppMain,\n    Navbar,\n    Settings,\n    Sidebar,\n    TagsView\n  },\n  mixins: [ResizeMixin],\n  computed: {\n    ...mapState({\n      theme: state => state.settings.theme,\n      sideTheme: state => state.settings.sideTheme,\n      sidebar: state => state.app.sidebar,\n      device: state => state.app.device,\n      needTagsView: state => state.settings.tagsView,\n      fixedHeader: state => state.settings.fixedHeader\n    }),\n    classObj() {\n      return {\n        hideSidebar: !this.sidebar.opened,\n        openSidebar: this.sidebar.opened,\n        withoutAnimation: this.sidebar.withoutAnimation,\n        mobile: this.device === 'mobile'\n      }\n    },\n    variables() {\n      return variables\n    }\n  },\n  methods: {\n    handleClickOutside() {\n      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })\n    },\n    setLayout() {\n      this.$refs.settingRef.openSetting()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n  @import \"~@/assets/styles/mixin.scss\";\n  @import \"~@/assets/styles/variables.scss\";\n\n  .app-wrapper {\n    @include clearfix;\n    position: relative;\n    height: 100%;\n    width: 100%;\n\n    &.mobile.openSidebar {\n      position: fixed;\n      top: 0;\n    }\n  }\n\n  .drawer-bg {\n    background: #000;\n    opacity: 0.3;\n    width: 100%;\n    top: 0;\n    height: 100%;\n    position: absolute;\n    z-index: 999;\n  }\n\n  .fixed-header {\n    position: fixed;\n    top: 0;\n    right: 0;\n    z-index: 9;\n    width: calc(100% - #{$base-sidebar-width});\n    transition: width 0.28s;\n  }\n\n  .hideSidebar .fixed-header {\n    width: calc(100% - 54px);\n  }\n\n  .sidebarHide .fixed-header {\n    width: 100%;\n  }\n\n  .mobile .fixed-header {\n    width: 100%;\n  }\n</style>\n"]}]}