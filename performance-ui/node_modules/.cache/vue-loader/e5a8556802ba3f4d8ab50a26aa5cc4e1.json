{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/FileUpload/index.vue?vue&type=style&index=0&id=211f81e0&scoped=true&lang=scss", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/FileUpload/index.vue", "mtime": 1753510684527}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/css-loader/dist/cjs.js", "mtime": 1753510683027}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1753510684373}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/postcss-loader/src/index.js", "mtime": 1753510684004}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/sass-loader/dist/cjs.js", "mtime": 1753510684159}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouZmlsZS11cGxvYWQtZGFyZyB7CiAgb3BhY2l0eTogMC41OwogIGJhY2tncm91bmQ6ICNjOGViZmI7Cn0KLnVwbG9hZC1maWxlLXVwbG9hZGVyIHsKICBtYXJnaW4tYm90dG9tOiA1cHg7Cn0KLnVwbG9hZC1maWxlLWxpc3QgLmVsLXVwbG9hZC1saXN0X19pdGVtIHsKICBib3JkZXI6IDFweCBzb2xpZCAjZTRlN2VkOwogIGxpbmUtaGVpZ2h0OiAyOwogIG1hcmdpbi1ib3R0b206IDEwcHg7CiAgcG9zaXRpb246IHJlbGF0aXZlOwp9Ci51cGxvYWQtZmlsZS1saXN0IC5lbGUtdXBsb2FkLWxpc3RfX2l0ZW0tY29udGVudCB7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBjb2xvcjogaW5oZXJpdDsKfQouZWxlLXVwbG9hZC1saXN0X19pdGVtLWNvbnRlbnQtYWN0aW9uIC5lbC1saW5rIHsKICBtYXJnaW4tcmlnaHQ6IDEwcHg7Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+OA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/FileUpload", "sourcesContent": ["<template>\n  <div class=\"upload-file\">\n    <el-upload\n      multiple\n      :action=\"uploadFileUrl\"\n      :before-upload=\"handleBeforeUpload\"\n      :file-list=\"fileList\"\n      :data=\"data\"\n      :limit=\"limit\"\n      :on-error=\"handleUploadError\"\n      :on-exceed=\"handleExceed\"\n      :on-success=\"handleUploadSuccess\"\n      :show-file-list=\"false\"\n      :headers=\"headers\"\n      class=\"upload-file-uploader\"\n      ref=\"fileUpload\"\n      v-if=\"!disabled\"\n    >\n      <!-- 上传按钮 -->\n      <el-button size=\"mini\" type=\"primary\">选取文件</el-button>\n      <!-- 上传提示 -->\n      <div class=\"el-upload__tip\" slot=\"tip\" v-if=\"showTip\">\n        请上传\n        <template v-if=\"fileSize\"> 大小不超过 <b style=\"color: #f56c6c\">{{ fileSize }}MB</b> </template>\n        <template v-if=\"fileType\"> 格式为 <b style=\"color: #f56c6c\">{{ fileType.join(\"/\") }}</b> </template>\n        的文件\n      </div>\n    </el-upload>\n\n    <!-- 文件列表 -->\n    <transition-group ref=\"uploadFileList\" class=\"upload-file-list el-upload-list el-upload-list--text\" name=\"el-fade-in-linear\" tag=\"ul\">\n      <li :key=\"file.url\" class=\"el-upload-list__item ele-upload-list__item-content\" v-for=\"(file, index) in fileList\">\n        <el-link :href=\"`${baseUrl}${file.url}`\" :underline=\"false\" target=\"_blank\">\n          <span class=\"el-icon-document\"> {{ getFileName(file.name) }} </span>\n        </el-link>\n        <div class=\"ele-upload-list__item-content-action\">\n          <el-link :underline=\"false\" @click=\"handleDelete(index)\" type=\"danger\" v-if=\"!disabled\">删除</el-link>\n        </div>\n      </li>\n    </transition-group>\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\"\nimport Sortable from 'sortablejs'\n\nexport default {\n  name: \"FileUpload\",\n  props: {\n    // 值\n    value: [String, Object, Array],\n    // 上传接口地址\n    action: {\n      type: String,\n      default: \"/common/upload\"\n    },\n    // 上传携带的参数\n    data: {\n      type: Object\n    },\n    // 数量限制\n    limit: {\n      type: Number,\n      default: 5\n    },\n    // 大小限制(MB)\n    fileSize: {\n      type: Number,\n      default: 5\n    },\n    // 文件类型, 例如['png', 'jpg', 'jpeg']\n    fileType: {\n      type: Array,\n      default: () => [\"doc\", \"docx\", \"xls\", \"xlsx\", \"ppt\", \"pptx\", \"txt\", \"pdf\"]\n    },\n    // 是否显示提示\n    isShowTip: {\n      type: Boolean,\n      default: true\n    },\n    // 禁用组件（仅查看文件）\n    disabled: {\n      type: Boolean,\n      default: false\n    },\n    // 拖动排序\n    drag: {\n      type: Boolean,\n      default: true\n    }\n  },\n  data() {\n    return {\n      number: 0,\n      uploadList: [],\n      baseUrl: process.env.VUE_APP_BASE_API,\n      uploadFileUrl: process.env.VUE_APP_BASE_API + this.action, // 上传文件服务器地址\n      headers: {\n        Authorization: \"Bearer \" + getToken(),\n      },\n      fileList: []\n    }\n  },\n  mounted() {\n    if (this.drag && !this.disabled) {\n      this.$nextTick(() => {\n        const element = this.$refs.uploadFileList?.$el || this.$refs.uploadFileList\n        Sortable.create(element, {\n          ghostClass: 'file-upload-darg',\n          onEnd: (evt) => {\n            const movedItem = this.fileList.splice(evt.oldIndex, 1)[0]\n            this.fileList.splice(evt.newIndex, 0, movedItem)\n            this.$emit(\"input\", this.listToString(this.fileList))\n          }\n        })\n      })\n    }\n  },\n  watch: {\n    value: {\n      handler(val) {\n        if (val) {\n          let temp = 1\n          // 首先将值转为数组\n          const list = Array.isArray(val) ? val : this.value.split(',')\n          // 然后将数组转为对象数组\n          this.fileList = list.map(item => {\n            if (typeof item === \"string\") {\n              item = { name: item, url: item }\n            }\n            item.uid = item.uid || new Date().getTime() + temp++\n            return item\n          })\n        } else {\n          this.fileList = []\n          return []\n        }\n      },\n      deep: true,\n      immediate: true\n    }\n  },\n  computed: {\n    // 是否显示提示\n    showTip() {\n      return this.isShowTip && (this.fileType || this.fileSize)\n    },\n  },\n  methods: {\n    // 上传前校检格式和大小\n    handleBeforeUpload(file) {\n      // 校检文件类型\n      if (this.fileType) {\n        const fileName = file.name.split('.')\n        const fileExt = fileName[fileName.length - 1]\n        const isTypeOk = this.fileType.indexOf(fileExt) >= 0\n        if (!isTypeOk) {\n          this.$modal.msgError(`文件格式不正确，请上传${this.fileType.join(\"/\")}格式文件!`)\n          return false\n        }\n      }\n      // 校检文件名是否包含特殊字符\n      if (file.name.includes(',')) {\n        this.$modal.msgError('文件名不正确，不能包含英文逗号!')\n        return false\n      }\n      // 校检文件大小\n      if (this.fileSize) {\n        const isLt = file.size / 1024 / 1024 < this.fileSize\n        if (!isLt) {\n          this.$modal.msgError(`上传文件大小不能超过 ${this.fileSize} MB!`)\n          return false\n        }\n      }\n      this.$modal.loading(\"正在上传文件，请稍候...\")\n      this.number++\n      return true\n    },\n    // 文件个数超出\n    handleExceed() {\n      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`)\n    },\n    // 上传失败\n    handleUploadError(err) {\n      this.$modal.msgError(\"上传文件失败，请重试\")\n      this.$modal.closeLoading()\n    },\n    // 上传成功回调\n    handleUploadSuccess(res, file) {\n      if (res.code === 200) {\n        this.uploadList.push({ name: res.fileName, url: res.fileName })\n        this.uploadedSuccessfully()\n      } else {\n        this.number--\n        this.$modal.closeLoading()\n        this.$modal.msgError(res.msg)\n        this.$refs.fileUpload.handleRemove(file)\n        this.uploadedSuccessfully()\n      }\n    },\n    // 删除文件\n    handleDelete(index) {\n      this.fileList.splice(index, 1)\n      this.$emit(\"input\", this.listToString(this.fileList))\n    },\n    // 上传结束处理\n    uploadedSuccessfully() {\n      if (this.number > 0 && this.uploadList.length === this.number) {\n        this.fileList = this.fileList.concat(this.uploadList)\n        this.uploadList = []\n        this.number = 0\n        this.$emit(\"input\", this.listToString(this.fileList))\n        this.$modal.closeLoading()\n      }\n    },\n    // 获取文件名称\n    getFileName(name) {\n      // 如果是url那么取最后的名字 如果不是直接返回\n      if (name.lastIndexOf(\"/\") > -1) {\n        return name.slice(name.lastIndexOf(\"/\") + 1)\n      } else {\n        return name\n      }\n    },\n    // 对象转成指定字符串分隔\n    listToString(list, separator) {\n      let strs = \"\"\n      separator = separator || \",\"\n      for (let i in list) {\n        strs += list[i].url + separator\n      }\n      return strs != '' ? strs.substr(0, strs.length - 1) : ''\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.file-upload-darg {\n  opacity: 0.5;\n  background: #c8ebfb;\n}\n.upload-file-uploader {\n  margin-bottom: 5px;\n}\n.upload-file-list .el-upload-list__item {\n  border: 1px solid #e4e7ed;\n  line-height: 2;\n  margin-bottom: 10px;\n  position: relative;\n}\n.upload-file-list .ele-upload-list__item-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  color: inherit;\n}\n.ele-upload-list__item-content-action .el-link {\n  margin-right: 10px;\n}\n</style>\n"]}]}