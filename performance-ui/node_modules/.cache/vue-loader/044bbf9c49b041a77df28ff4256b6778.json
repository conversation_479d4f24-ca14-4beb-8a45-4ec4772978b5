{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/Breadcrumb/index.vue?vue&type=template&id=b50ef614&scoped=true", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/Breadcrumb/index.vue", "mtime": 1753510684527}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 1753510684373}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxlbC1icmVhZGNydW1iIGNsYXNzPSJhcHAtYnJlYWRjcnVtYiIgc2VwYXJhdG9yPSIvIj4KICA8dHJhbnNpdGlvbi1ncm91cCBuYW1lPSJicmVhZGNydW1iIj4KICAgIDxlbC1icmVhZGNydW1iLWl0ZW0gdi1mb3I9IihpdGVtLCBpbmRleCkgaW4gbGV2ZWxMaXN0IiA6a2V5PSJpdGVtLnBhdGgiPgogICAgICA8c3BhbiB2LWlmPSJpdGVtLnJlZGlyZWN0ID09PSAnbm9SZWRpcmVjdCcgfHwgaW5kZXggPT0gbGV2ZWxMaXN0Lmxlbmd0aCAtIDEiIGNsYXNzPSJuby1yZWRpcmVjdCI+e3sgaXRlbS5tZXRhLnRpdGxlIH19PC9zcGFuPgogICAgICA8YSB2LWVsc2UgQGNsaWNrLnByZXZlbnQ9ImhhbmRsZUxpbmsoaXRlbSkiPnt7IGl0ZW0ubWV0YS50aXRsZSB9fTwvYT4KICAgIDwvZWwtYnJlYWRjcnVtYi1pdGVtPgogIDwvdHJhbnNpdGlvbi1ncm91cD4KPC9lbC1icmVhZGNydW1iPgo="}, null]}