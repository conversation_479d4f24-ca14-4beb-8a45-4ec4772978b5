{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/monitor/online/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/monitor/online/index.vue", "mtime": 1753510684534}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3QsIGZvcmNlTG9nb3V0IH0gZnJvbSAiQC9hcGkvbW9uaXRvci9vbmxpbmUiCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIk9ubGluZSIsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDmgLvmnaHmlbAKICAgICAgdG90YWw6IDAsCiAgICAgIC8vIOihqOagvOaVsOaNrgogICAgICBsaXN0OiBbXSwKICAgICAgcGFnZU51bTogMSwKICAgICAgcGFnZVNpemU6IDEwLAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBpcGFkZHI6IHVuZGVmaW5lZCwKICAgICAgICB1c2VyTmFtZTogdW5kZWZpbmVkCiAgICAgIH0KICAgIH0KICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKQogIH0sCiAgbWV0aG9kczogewogICAgLyoqIOafpeivoueZu+W9leaXpeW/l+WIl+ihqCAqLwogICAgZ2V0TGlzdCgpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQogICAgICBsaXN0KHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMubGlzdCA9IHJlc3BvbnNlLnJvd3MKICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWwKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQogICAgICB9KQogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnBhZ2VOdW0gPSAxCiAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICB9LAogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLwogICAgcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKQogICAgfSwKICAgIC8qKiDlvLrpgIDmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZUZvcmNlTG9nb3V0KHJvdykgewogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTlvLrpgIDlkI3np7DkuLoiJyArIHJvdy51c2VyTmFtZSArICci55qE55So5oi377yfJykudGhlbihmdW5jdGlvbigpIHsKICAgICAgICByZXR1cm4gZm9yY2VMb2dvdXQocm93LnRva2VuSWQpCiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5by66YCA5oiQ5YqfIikKICAgICAgfSkuY2F0Y2goKCkgPT4ge30pCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/monitor/online", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"68px\">\n      <el-form-item label=\"登录地址\" prop=\"ipaddr\">\n        <el-input\n          v-model=\"queryParams.ipaddr\"\n          placeholder=\"请输入登录地址\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"用户名称\" prop=\"userName\">\n        <el-input\n          v-model=\"queryParams.userName\"\n          placeholder=\"请输入用户名称\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n\n    </el-form>\n    <el-table\n      v-loading=\"loading\"\n      :data=\"list.slice((pageNum-1)*pageSize,pageNum*pageSize)\"\n      style=\"width: 100%;\"\n    >\n      <el-table-column label=\"序号\" type=\"index\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <span>{{(pageNum - 1) * pageSize + scope.$index + 1}}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"会话编号\" align=\"center\" prop=\"tokenId\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"登录名称\" align=\"center\" prop=\"userName\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"部门名称\" align=\"center\" prop=\"deptName\" />\n      <el-table-column label=\"主机\" align=\"center\" prop=\"ipaddr\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"登录地点\" align=\"center\" prop=\"loginLocation\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"浏览器\" align=\"center\" prop=\"browser\" />\n      <el-table-column label=\"操作系统\" align=\"center\" prop=\"os\" />\n      <el-table-column label=\"登录时间\" align=\"center\" prop=\"loginTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.loginTime) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleForceLogout(scope.row)\"\n            v-hasPermi=\"['monitor:online:forceLogout']\"\n          >强退</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination v-show=\"total>0\" :total=\"total\" :page.sync=\"pageNum\" :limit.sync=\"pageSize\" />\n  </div>\n</template>\n\n<script>\nimport { list, forceLogout } from \"@/api/monitor/online\"\n\nexport default {\n  name: \"Online\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 总条数\n      total: 0,\n      // 表格数据\n      list: [],\n      pageNum: 1,\n      pageSize: 10,\n      // 查询参数\n      queryParams: {\n        ipaddr: undefined,\n        userName: undefined\n      }\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    /** 查询登录日志列表 */\n    getList() {\n      this.loading = true\n      list(this.queryParams).then(response => {\n        this.list = response.rows\n        this.total = response.total\n        this.loading = false\n      })\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.pageNum = 1\n      this.getList()\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\")\n      this.handleQuery()\n    },\n    /** 强退按钮操作 */\n    handleForceLogout(row) {\n      this.$modal.confirm('是否确认强退名称为\"' + row.userName + '\"的用户？').then(function() {\n        return forceLogout(row.tokenId)\n      }).then(() => {\n        this.getList()\n        this.$modal.msgSuccess(\"强退成功\")\n      }).catch(() => {})\n    }\n  }\n}\n</script>\n\n"]}]}