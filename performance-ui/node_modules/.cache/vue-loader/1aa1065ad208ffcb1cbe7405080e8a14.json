{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/system/user/authRole.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/system/user/authRole.vue", "mtime": 1753510684536}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldEF1dGhSb2xlLCB1cGRhdGVBdXRoUm9sZSB9IGZyb20gIkAvYXBpL3N5c3RlbS91c2VyIgoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJBdXRoUm9sZSIsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDliIbpobXkv6Hmga8KICAgICAgdG90YWw6IDAsCiAgICAgIHBhZ2VOdW06IDEsCiAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgLy8g6YCJ5Lit6KeS6Imy57yW5Y+3CiAgICAgIHJvbGVJZHM6IFtdLAogICAgICAvLyDop5LoibLkv6Hmga8KICAgICAgcm9sZXM6IFtdLAogICAgICAvLyDnlKjmiLfkv6Hmga8KICAgICAgZm9ybToge30KICAgIH0KICB9LAogIGNyZWF0ZWQoKSB7CiAgICBjb25zdCB1c2VySWQgPSB0aGlzLiRyb3V0ZS5wYXJhbXMgJiYgdGhpcy4kcm91dGUucGFyYW1zLnVzZXJJZAogICAgaWYgKHVzZXJJZCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlCiAgICAgIGdldEF1dGhSb2xlKHVzZXJJZCkudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS51c2VyCiAgICAgICAgdGhpcy5yb2xlcyA9IHJlc3BvbnNlLnJvbGVzCiAgICAgICAgdGhpcy50b3RhbCA9IHRoaXMucm9sZXMubGVuZ3RoCiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgICAgdGhpcy5yb2xlcy5mb3JFYWNoKChyb3cpID0+IHsKICAgICAgICAgICAgaWYgKHJvdy5mbGFnKSB7CiAgICAgICAgICAgICAgdGhpcy4kcmVmcy50YWJsZS50b2dnbGVSb3dTZWxlY3Rpb24ocm93KQogICAgICAgICAgICB9CiAgICAgICAgICB9KQogICAgICAgIH0pCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UKICAgICAgfSkKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDljZXlh7vpgInkuK3ooYzmlbDmja4gKi8KICAgIGNsaWNrUm93KHJvdykgewogICAgICBpZiAodGhpcy5jaGVja1NlbGVjdGFibGUocm93KSkgewogICAgICAgIHRoaXMuJHJlZnMudGFibGUudG9nZ2xlUm93U2VsZWN0aW9uKHJvdykKICAgICAgfQogICAgfSwKICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrgogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLnJvbGVJZHMgPSBzZWxlY3Rpb24ubWFwKChpdGVtKSA9PiBpdGVtLnJvbGVJZCkKICAgIH0sCiAgICAvLyDkv53lrZjpgInkuK3nmoTmlbDmja7nvJblj7cKICAgIGdldFJvd0tleShyb3cpIHsKICAgICAgcmV0dXJuIHJvdy5yb2xlSWQKICAgIH0sCiAgICAvLyDmo4Dmn6Xop5LoibLnirbmgIEKICAgIGNoZWNrU2VsZWN0YWJsZShyb3cpIHsKICAgICAgcmV0dXJuIHJvdy5zdGF0dXMgPT09ICIwIiA/IHRydWUgOiBmYWxzZQogICAgfSwKICAgIC8qKiDmj5DkuqTmjInpkq4gKi8KICAgIHN1Ym1pdEZvcm0oKSB7CiAgICAgIGNvbnN0IHVzZXJJZCA9IHRoaXMuZm9ybS51c2VySWQKICAgICAgY29uc3Qgcm9sZUlkcyA9IHRoaXMucm9sZUlkcy5qb2luKCIsIikKICAgICAgdXBkYXRlQXV0aFJvbGUoeyB1c2VySWQ6IHVzZXJJZCwgcm9sZUlkczogcm9sZUlkcyB9KS50aGVuKChyZXNwb25zZSkgPT4gewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaOiOadg+aIkOWKnyIpCiAgICAgICAgdGhpcy5jbG9zZSgpCiAgICAgIH0pCiAgICB9LAogICAgLyoqIOWFs+mXreaMiemSriAqLwogICAgY2xvc2UoKSB7CiAgICAgIGNvbnN0IG9iaiA9IHsgcGF0aDogIi9zeXN0ZW0vdXNlciIgfQogICAgICB0aGlzLiR0YWIuY2xvc2VPcGVuUGFnZShvYmopCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["authRole.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgDA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "authRole.vue", "sourceRoot": "src/views/system/user", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <h4 class=\"form-header h4\">基本信息</h4>\n    <el-form ref=\"form\" :model=\"form\" label-width=\"80px\">\n      <el-row>\n        <el-col :span=\"8\" :offset=\"2\">\n          <el-form-item label=\"用户昵称\" prop=\"nickName\">\n            <el-input v-model=\"form.nickName\" disabled />\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"8\" :offset=\"2\">\n          <el-form-item label=\"登录账号\" prop=\"userName\">\n            <el-input v-model=\"form.userName\" disabled />\n          </el-form-item>\n        </el-col>\n      </el-row>\n    </el-form>\n\n    <h4 class=\"form-header h4\">角色信息</h4>\n    <el-table v-loading=\"loading\" :row-key=\"getRowKey\" @row-click=\"clickRow\" ref=\"table\" @selection-change=\"handleSelectionChange\" :data=\"roles.slice((pageNum-1)*pageSize,pageNum*pageSize)\">\n      <el-table-column label=\"序号\" type=\"index\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <span>{{ (pageNum - 1) * pageSize + scope.$index + 1 }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column type=\"selection\" :reserve-selection=\"true\" :selectable=\"checkSelectable\" width=\"55\" />\n      <el-table-column label=\"角色编号\" align=\"center\" prop=\"roleId\" />\n      <el-table-column label=\"角色名称\" align=\"center\" prop=\"roleName\" />\n      <el-table-column label=\"权限字符\" align=\"center\" prop=\"roleKey\" />\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime) }}</span>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination v-show=\"total>0\" :total=\"total\" :page.sync=\"pageNum\" :limit.sync=\"pageSize\" />\n\n    <el-form label-width=\"100px\">\n      <el-form-item style=\"text-align: center;margin-left:-120px;margin-top:30px;\">\n        <el-button type=\"primary\" @click=\"submitForm()\">提交</el-button>\n        <el-button @click=\"close()\">返回</el-button>\n      </el-form-item>\n    </el-form>\n  </div>\n</template>\n\n<script>\nimport { getAuthRole, updateAuthRole } from \"@/api/system/user\"\n\nexport default {\n  name: \"AuthRole\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 分页信息\n      total: 0,\n      pageNum: 1,\n      pageSize: 10,\n      // 选中角色编号\n      roleIds: [],\n      // 角色信息\n      roles: [],\n      // 用户信息\n      form: {}\n    }\n  },\n  created() {\n    const userId = this.$route.params && this.$route.params.userId\n    if (userId) {\n      this.loading = true\n      getAuthRole(userId).then((response) => {\n        this.form = response.user\n        this.roles = response.roles\n        this.total = this.roles.length\n        this.$nextTick(() => {\n          this.roles.forEach((row) => {\n            if (row.flag) {\n              this.$refs.table.toggleRowSelection(row)\n            }\n          })\n        })\n        this.loading = false\n      })\n    }\n  },\n  methods: {\n    /** 单击选中行数据 */\n    clickRow(row) {\n      if (this.checkSelectable(row)) {\n        this.$refs.table.toggleRowSelection(row)\n      }\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.roleIds = selection.map((item) => item.roleId)\n    },\n    // 保存选中的数据编号\n    getRowKey(row) {\n      return row.roleId\n    },\n    // 检查角色状态\n    checkSelectable(row) {\n      return row.status === \"0\" ? true : false\n    },\n    /** 提交按钮 */\n    submitForm() {\n      const userId = this.form.userId\n      const roleIds = this.roleIds.join(\",\")\n      updateAuthRole({ userId: userId, roleIds: roleIds }).then((response) => {\n        this.$modal.msgSuccess(\"授权成功\")\n        this.close()\n      })\n    },\n    /** 关闭按钮 */\n    close() {\n      const obj = { path: \"/system/user\" }\n      this.$tab.closeOpenPage(obj)\n    }\n  }\n}\n</script>"]}]}