{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/DictTag/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/DictTag/index.vue", "mtime": 1753510684527}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/DictTag", "sourcesContent": ["<template>\n  <div>\n    <template v-for=\"(item, index) in options\">\n      <template v-if=\"values.includes(item.value)\">\n        <span\n          v-if=\"(item.raw.listClass == 'default' || item.raw.listClass == '') && (item.raw.cssClass == '' || item.raw.cssClass == null)\"\n          :key=\"item.value\"\n          :index=\"index\"\n          :class=\"item.raw.cssClass\"\n          >{{ item.label + ' ' }}</span\n        >\n        <el-tag\n          v-else\n          :disable-transitions=\"true\"\n          :key=\"item.value\"\n          :index=\"index\"\n          :type=\"item.raw.listClass == 'primary' ? '' : item.raw.listClass\"\n          :class=\"item.raw.cssClass\"\n        >\n          {{ item.label + ' ' }}\n        </el-tag>\n      </template>\n    </template>\n    <template v-if=\"unmatch && showValue\">\n      {{ unmatchArray | handleArray }}\n    </template>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"DictTag\",\n  props: {\n    options: {\n      type: Array,\n      default: null,\n    },\n    value: [Number, String, Array],\n    // 当未找到匹配的数据时，显示value\n    showValue: {\n      type: Boolean,\n      default: true,\n    },\n    separator: {\n      type: String,\n      default: \",\"\n    }\n  },\n  data() {\n    return {\n      unmatchArray: [], // 记录未匹配的项\n    }\n  },\n  computed: {\n    values() {\n      if (this.value === null || typeof this.value === 'undefined' || this.value === '') return []\n      return Array.isArray(this.value) ? this.value.map(item => '' + item) : String(this.value).split(this.separator)\n    },\n    unmatch() {\n      this.unmatchArray = []\n      // 没有value不显示\n      if (this.value === null || typeof this.value === 'undefined' || this.value === '' || this.options.length === 0) return false\n      // 传入值为数组\n      let unmatch = false // 添加一个标志来判断是否有未匹配项\n      this.values.forEach(item => {\n        if (!this.options.some(v => v.value === item)) {\n          this.unmatchArray.push(item)\n          unmatch = true // 如果有未匹配项，将标志设置为true\n        }\n      })\n      return unmatch // 返回标志的值\n    },\n\n  },\n  filters: {\n    handleArray(array) {\n      if (array.length === 0) return ''\n      return array.reduce((pre, cur) => {\n        return pre + ' ' + cur\n      })\n    },\n  }\n}\n</script>\n<style scoped>\n.el-tag + .el-tag {\n  margin-left: 10px;\n}\n</style>\n"]}]}