{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/layout/components/Copyright/index.vue?vue&type=style&index=0&id=32cef426&scoped=true&lang=css", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/layout/components/Copyright/index.vue", "mtime": 1753510684529}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/css-loader/dist/cjs.js", "mtime": 1753510683027}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1753510684373}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/postcss-loader/src/index.js", "mtime": 1753510684004}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgouY29weXJpZ2h0IHsKICBwb3NpdGlvbjogZml4ZWQ7CiAgYm90dG9tOiAwOwogIGxlZnQ6IDA7CiAgcmlnaHQ6IDA7CiAgaGVpZ2h0OiAzNnB4OwogIHBhZGRpbmc6IDEwcHggMjBweDsKICB0ZXh0LWFsaWduOiByaWdodDsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOGY4OwogIGNvbG9yOiAjNjY2OwogIGZvbnQtc2l6ZTogMTRweDsKICBib3JkZXItdG9wOiAxcHggc29saWQgI2U3ZTdlNzsKICB6LWluZGV4OiA5OTk7Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAoBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout/components/Copyright", "sourcesContent": ["<template>\n  <footer v-if=\"visible\" class=\"copyright\">\n    <span>{{ content }}</span>\n  </footer>\n</template>\n\n<script>\nexport default {\n  computed: {\n    visible() {\n      return this.$store.state.settings.footerVisible\n    },\n    content() {\n      return this.$store.state.settings.footerContent\n    }\n  }\n}\n</script>\n\n<style scoped>\n.copyright {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 36px;\n  padding: 10px 20px;\n  text-align: right;\n  background-color: #f8f8f8;\n  color: #666;\n  font-size: 14px;\n  border-top: 1px solid #e7e7e7;\n  z-index: 999;\n}\n</style>"]}]}