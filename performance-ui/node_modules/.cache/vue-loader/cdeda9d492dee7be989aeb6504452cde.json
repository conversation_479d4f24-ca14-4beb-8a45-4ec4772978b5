{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/Editor/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/Editor/index.vue", "mtime": 1753510684527}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAoBA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Editor", "sourcesContent": ["<template>\n  <div>\n    <el-upload\n      :action=\"uploadUrl\"\n      :before-upload=\"handleBeforeUpload\"\n      :on-success=\"handleUploadSuccess\"\n      :on-error=\"handleUploadError\"\n      name=\"file\"\n      :show-file-list=\"false\"\n      :headers=\"headers\"\n      style=\"display: none\"\n      ref=\"upload\"\n      v-if=\"this.type == 'url'\"\n    >\n    </el-upload>\n    <div class=\"editor\" ref=\"editor\" :style=\"styles\"></div>\n  </div>\n</template>\n\n<script>\nimport axios from \"axios\"\nimport Quill from \"quill\"\nimport \"quill/dist/quill.core.css\"\nimport \"quill/dist/quill.snow.css\"\nimport \"quill/dist/quill.bubble.css\"\nimport { getToken } from \"@/utils/auth\"\n\nexport default {\n  name: \"Editor\",\n  props: {\n    /* 编辑器的内容 */\n    value: {\n      type: String,\n      default: \"\",\n    },\n    /* 高度 */\n    height: {\n      type: Number,\n      default: null,\n    },\n    /* 最小高度 */\n    minHeight: {\n      type: Number,\n      default: null,\n    },\n    /* 只读 */\n    readOnly: {\n      type: Boolean,\n      default: false,\n    },\n    /* 上传文件大小限制(MB) */\n    fileSize: {\n      type: Number,\n      default: 5,\n    },\n    /* 类型（base64格式、url格式） */\n    type: {\n      type: String,\n      default: \"url\",\n    }\n  },\n  data() {\n    return {\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/common/upload\", // 上传的图片服务器地址\n      headers: {\n        Authorization: \"Bearer \" + getToken()\n      },\n      Quill: null,\n      currentValue: \"\",\n      options: {\n        theme: \"snow\",\n        bounds: document.body,\n        debug: \"warn\",\n        modules: {\n          // 工具栏配置\n          toolbar: [\n            [\"bold\", \"italic\", \"underline\", \"strike\"],       // 加粗 斜体 下划线 删除线\n            [\"blockquote\", \"code-block\"],                    // 引用  代码块\n            [{ list: \"ordered\" }, { list: \"bullet\" }],       // 有序、无序列表\n            [{ indent: \"-1\" }, { indent: \"+1\" }],            // 缩进\n            [{ size: [\"small\", false, \"large\", \"huge\"] }],   // 字体大小\n            [{ header: [1, 2, 3, 4, 5, 6, false] }],         // 标题\n            [{ color: [] }, { background: [] }],             // 字体颜色、字体背景颜色\n            [{ align: [] }],                                 // 对齐方式\n            [\"clean\"],                                       // 清除文本格式\n            [\"link\", \"image\", \"video\"]                       // 链接、图片、视频\n          ],\n        },\n        placeholder: \"请输入内容\",\n        readOnly: this.readOnly,\n      },\n    }\n  },\n  computed: {\n    styles() {\n      let style = {}\n      if (this.minHeight) {\n        style.minHeight = `${this.minHeight}px`\n      }\n      if (this.height) {\n        style.height = `${this.height}px`\n      }\n      return style\n    }\n  },\n  watch: {\n    value: {\n      handler(val) {\n        if (val !== this.currentValue) {\n          this.currentValue = val === null ? \"\" : val\n          if (this.Quill) {\n            this.Quill.clipboard.dangerouslyPasteHTML(this.currentValue)\n          }\n        }\n      },\n      immediate: true,\n    },\n  },\n  mounted() {\n    this.init()\n  },\n  beforeDestroy() {\n    this.Quill = null\n  },\n  methods: {\n    init() {\n      const editor = this.$refs.editor\n      this.Quill = new Quill(editor, this.options)\n      // 如果设置了上传地址则自定义图片上传事件\n      if (this.type == 'url') {\n        let toolbar = this.Quill.getModule(\"toolbar\")\n        toolbar.addHandler(\"image\", (value) => {\n          if (value) {\n            this.$refs.upload.$children[0].$refs.input.click()\n          } else {\n            this.quill.format(\"image\", false)\n          }\n        })\n        this.Quill.root.addEventListener('paste', this.handlePasteCapture, true)\n      }\n      this.Quill.clipboard.dangerouslyPasteHTML(this.currentValue)\n      this.Quill.on(\"text-change\", (delta, oldDelta, source) => {\n        const html = this.$refs.editor.children[0].innerHTML\n        const text = this.Quill.getText()\n        const quill = this.Quill\n        this.currentValue = html\n        this.$emit(\"input\", html)\n        this.$emit(\"on-change\", { html, text, quill })\n      })\n      this.Quill.on(\"text-change\", (delta, oldDelta, source) => {\n        this.$emit(\"on-text-change\", delta, oldDelta, source)\n      })\n      this.Quill.on(\"selection-change\", (range, oldRange, source) => {\n        this.$emit(\"on-selection-change\", range, oldRange, source)\n      })\n      this.Quill.on(\"editor-change\", (eventName, ...args) => {\n        this.$emit(\"on-editor-change\", eventName, ...args)\n      })\n    },\n    // 上传前校检格式和大小\n    handleBeforeUpload(file) {\n      const type = [\"image/jpeg\", \"image/jpg\", \"image/png\", \"image/svg\"]\n      const isJPG = type.includes(file.type)\n      // 检验文件格式\n      if (!isJPG) {\n        this.$message.error(`图片格式错误!`)\n        return false\n      }\n      // 校检文件大小\n      if (this.fileSize) {\n        const isLt = file.size / 1024 / 1024 < this.fileSize\n        if (!isLt) {\n          this.$message.error(`上传文件大小不能超过 ${this.fileSize} MB!`)\n          return false\n        }\n      }\n      return true\n    },\n    handleUploadSuccess(res, file) {\n      // 如果上传成功\n      if (res.code == 200) {\n        // 获取富文本组件实例\n        let quill = this.Quill\n        // 获取光标所在位置\n        let length = quill.getSelection().index\n        // 插入图片  res.url为服务器返回的图片地址\n        quill.insertEmbed(length, \"image\", process.env.VUE_APP_BASE_API + res.fileName)\n        // 调整光标到最后\n        quill.setSelection(length + 1)\n      } else {\n        this.$message.error(\"图片插入失败\")\n      }\n    },\n    handleUploadError() {\n      this.$message.error(\"图片插入失败\")\n    },\n    // 复制粘贴图片处理\n    handlePasteCapture(e) {\n      const clipboard = e.clipboardData || window.clipboardData\n      if (clipboard && clipboard.items) {\n        for (let i = 0; i < clipboard.items.length; i++) {\n          const item = clipboard.items[i]\n          if (item.type.indexOf('image') !== -1) {\n            e.preventDefault()\n            const file = item.getAsFile()\n            this.insertImage(file)\n          }\n        }\n      }\n    },\n    insertImage(file) {\n      const formData = new FormData()\n      formData.append(\"file\", file)\n      axios.post(this.uploadUrl, formData, { headers: { \"Content-Type\": \"multipart/form-data\", Authorization: this.headers.Authorization } }).then(res => {\n        this.handleUploadSuccess(res.data)\n      })\n    }\n  }\n}\n</script>\n\n<style>\n.editor, .ql-toolbar {\n  white-space: pre-wrap !important;\n  line-height: normal !important;\n}\n.quill-img {\n  display: none;\n}\n.ql-snow .ql-tooltip[data-mode=\"link\"]::before {\n  content: \"请输入链接地址:\";\n}\n.ql-snow .ql-tooltip.ql-editing a.ql-action::after {\n  border-right: 0px;\n  content: \"保存\";\n  padding-right: 0px;\n}\n.ql-snow .ql-tooltip[data-mode=\"video\"]::before {\n  content: \"请输入视频地址:\";\n}\n.ql-snow .ql-picker.ql-size .ql-picker-label::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item::before {\n  content: \"14px\";\n}\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"small\"]::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"small\"]::before {\n  content: \"10px\";\n}\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"large\"]::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"large\"]::before {\n  content: \"18px\";\n}\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"huge\"]::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"huge\"]::before {\n  content: \"32px\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item::before {\n  content: \"文本\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"1\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"1\"]::before {\n  content: \"标题1\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"2\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"2\"]::before {\n  content: \"标题2\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"3\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"3\"]::before {\n  content: \"标题3\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"4\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"4\"]::before {\n  content: \"标题4\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"5\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"5\"]::before {\n  content: \"标题5\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"6\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"6\"]::before {\n  content: \"标题6\";\n}\n.ql-snow .ql-picker.ql-font .ql-picker-label::before,\n.ql-snow .ql-picker.ql-font .ql-picker-item::before {\n  content: \"标准字体\";\n}\n.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=\"serif\"]::before,\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=\"serif\"]::before {\n  content: \"衬线字体\";\n}\n.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=\"monospace\"]::before,\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=\"monospace\"]::before {\n  content: \"等宽字体\";\n}\n</style>\n"]}]}