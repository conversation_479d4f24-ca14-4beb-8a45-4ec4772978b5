{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/system/role/selectUser.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/system/role/selectUser.vue", "mtime": 1753510684536}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["selectUser.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "selectUser.vue", "sourceRoot": "src/views/system/role", "sourcesContent": ["<template>\n  <!-- 授权用户 -->\n  <el-dialog title=\"选择用户\" :visible.sync=\"visible\" width=\"800px\" top=\"5vh\" append-to-body>\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\">\n      <el-form-item label=\"用户名称\" prop=\"userName\">\n        <el-input\n          v-model=\"queryParams.userName\"\n          placeholder=\"请输入用户名称\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"手机号码\" prop=\"phonenumber\">\n        <el-input\n          v-model=\"queryParams.phonenumber\"\n          placeholder=\"请输入手机号码\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n    <el-row>\n      <el-table @row-click=\"clickRow\" ref=\"table\" :data=\"userList\" @selection-change=\"handleSelectionChange\" height=\"260px\">\n        <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n        <el-table-column label=\"用户名称\" prop=\"userName\" :show-overflow-tooltip=\"true\" />\n        <el-table-column label=\"用户昵称\" prop=\"nickName\" :show-overflow-tooltip=\"true\" />\n        <el-table-column label=\"邮箱\" prop=\"email\" :show-overflow-tooltip=\"true\" />\n        <el-table-column label=\"手机\" prop=\"phonenumber\" :show-overflow-tooltip=\"true\" />\n        <el-table-column label=\"状态\" align=\"center\" prop=\"status\">\n          <template slot-scope=\"scope\">\n            <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\n          <template slot-scope=\"scope\">\n            <span>{{ parseTime(scope.row.createTime) }}</span>\n          </template>\n        </el-table-column>\n      </el-table>\n      <pagination\n        v-show=\"total>0\"\n        :total=\"total\"\n        :page.sync=\"queryParams.pageNum\"\n        :limit.sync=\"queryParams.pageSize\"\n        @pagination=\"getList\"\n      />\n    </el-row>\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button type=\"primary\" @click=\"handleSelectUser\">确 定</el-button>\n      <el-button @click=\"visible = false\">取 消</el-button>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport { unallocatedUserList, authUserSelectAll } from \"@/api/system/role\"\nexport default {\n  dicts: ['sys_normal_disable'],\n  props: {\n    // 角色编号\n    roleId: {\n      type: [Number, String]\n    }\n  },\n  data() {\n    return {\n      // 遮罩层\n      visible: false,\n      // 选中数组值\n      userIds: [],\n      // 总条数\n      total: 0,\n      // 未授权用户数据\n      userList: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        roleId: undefined,\n        userName: undefined,\n        phonenumber: undefined\n      }\n    }\n  },\n  methods: {\n    // 显示弹框\n    show() {\n      this.queryParams.roleId = this.roleId\n      this.getList()\n      this.visible = true\n    },\n    clickRow(row) {\n      this.$refs.table.toggleRowSelection(row)\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.userIds = selection.map(item => item.userId)\n    },\n    // 查询表数据\n    getList() {\n      unallocatedUserList(this.queryParams).then(res => {\n        this.userList = res.rows\n        this.total = res.total\n      })\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1\n      this.getList()\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\")\n      this.handleQuery()\n    },\n    /** 选择授权用户操作 */\n    handleSelectUser() {\n      const roleId = this.queryParams.roleId\n      const userIds = this.userIds.join(\",\")\n      if (userIds == \"\") {\n        this.$modal.msgError(\"请选择要分配的用户\")\n        return\n      }\n      authUserSelectAll({ roleId: roleId, userIds: userIds }).then(res => {\n        this.$modal.msgSuccess(res.msg)\n        this.visible = false\n        this.$emit(\"ok\")\n      }) \n    }\n  }\n}\n</script>\n"]}]}