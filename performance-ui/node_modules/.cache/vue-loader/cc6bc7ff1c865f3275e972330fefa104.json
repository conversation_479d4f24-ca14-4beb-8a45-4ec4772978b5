{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/performance/plan/dept.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/performance/plan/dept.vue", "mtime": 1754317402895}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3REZXB0LCBnZXREZXB0LCBhZGREZXB0LCB1cGRhdGVEZXB0LCBkZWxEZXB0LCBleHBvcnREZXB0LCBiYXRjaEV4cG9ydERlcHQsIGRvd25sb2FkRXhjZWxUZW1wbGF0ZSB9IGZyb20gJ0AvYXBpL3BlcmZvcm1hbmNlL2RlcHQnCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIkRlcHRQbGFuIiwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgaWRzOiBbXSwKICAgICAgc2luZ2xlOiB0cnVlLAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgdG90YWw6IDAsCiAgICAgIGxpc3Q6IFtdLAogICAgICB0aXRsZTogIiIsCiAgICAgIG9wZW46IGZhbHNlLAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIGRlcHROYW1lOiBudWxsLAogICAgICAgIHRhc2tUeXBlOiBudWxsCiAgICAgIH0sCiAgICAgIGZvcm06IHt9LAogICAgICBydWxlczogewogICAgICAgIGRlcHROYW1lOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi56eR5a6k5ZCN56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sCiAgICAgICAgICB7IG1heDogMTAwLCBtZXNzYWdlOiAi56eR5a6k5ZCN56ew6ZW/5bqm5LiN6IO96LaF6L+HMTAw5Liq5a2X56ymIiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIHRhc2tUeXBlOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5Lu75Yqh57G75Z6L5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sCiAgICAgICAgICB7IG1heDogMTAwLCBtZXNzYWdlOiAi5Lu75Yqh57G75Z6L6ZW/5bqm5LiN6IO96LaF6L+HMTAw5Liq5a2X56ymIiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIHRhc2tDb250ZW50OiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi57up5pWI5Lu75Yqh5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sCiAgICAgICAgICB7IG1heDogMTAwMCwgbWVzc2FnZTogIue7qeaViOS7u+WKoemVv+W6puS4jeiDvei2hei/hzEwMDDkuKrlrZfnrKYiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgdGFyZ2V0TWVhc3VyZTogWwogICAgICAgICAgeyBtYXg6IDEwMDAsIG1lc3NhZ2U6ICLnm67moIflj4rmjqrmlr3plb/luqbkuI3og73otoXov4cxMDAw5Liq5a2X56ymIiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIGV2YWxTdGFuZGFyZDogWwogICAgICAgICAgeyBtYXg6IDEwMDAsIG1lc3NhZ2U6ICLor4Tku7fmoIflh4bplb/luqbkuI3og73otoXov4cxMDAw5Liq5a2X56ymIiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIHNjb3JlV2VpZ2h0OiBbCiAgICAgICAgICB7IG1heDogNTAsIG1lc3NhZ2U6ICLliIblgLzmiJbmnYPph43plb/luqbkuI3og73otoXov4c1MOS4quWtl+espiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBwcmluY2lwYWw6IFsKICAgICAgICAgIHsgbWF4OiAxMDAsIG1lc3NhZ2U6ICLotKPku7vkurrplb/luqbkuI3og73otoXov4cxMDDkuKrlrZfnrKYiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgZGVhZGxpbmU6IFsKICAgICAgICAgIHsgbWF4OiAxMDAsIG1lc3NhZ2U6ICLlrozmiJDml7bpmZDplb/luqbkuI3og73otoXov4cxMDDkuKrlrZfnrKYiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0KICAgICAgfSwKICAgICAgaW1wb3J0VXJsOiBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgIi9wZXJmb3JtYW5jZS9wbGFuL2RlcHQvaW1wb3J0RXhjZWwiLAogICAgICB1cGxvYWRIZWFkZXJzOiB7fQogICAgfQogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgLy8g6K6+572u5LiK5Lyg6K6k6K+B5aS0CiAgICB0aGlzLnVwbG9hZEhlYWRlcnMgPSB7CiAgICAgIEF1dGhvcml6YXRpb246ICdCZWFyZXIgJyArIHRoaXMuJHN0b3JlLmdldHRlcnMudG9rZW4KICAgIH07CiAgfSwKICBtZXRob2RzOiB7CiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBsaXN0RGVwdCh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmxpc3QgPSByZXNwb25zZS5yb3dzOwogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIGNhbmNlbCgpIHsKICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIGlkOiBudWxsLAogICAgICAgIGRlcHROYW1lOiBudWxsLAogICAgICAgIHRhc2tUeXBlOiBudWxsLAogICAgICAgIHRhc2tDb250ZW50OiBudWxsLAogICAgICAgIHRhcmdldE1lYXN1cmU6IG51bGwsCiAgICAgICAgZXZhbFN0YW5kYXJkOiBudWxsLAogICAgICAgIHNjb3JlV2VpZ2h0OiBudWxsLAogICAgICAgIHByaW5jaXBhbDogbnVsbCwKICAgICAgICBkZWFkbGluZTogbnVsbAogICAgICB9OwogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOwogICAgfSwKICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5pZCkKICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoIT09MQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGgKICAgIH0sCiAgICBoYW5kbGVBZGQoKSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDnp5HlrqTnu6nmlYjorqHliJIiOwogICAgfSwKICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICBjb25zdCBpZCA9IHJvdy5pZCB8fCB0aGlzLmlkcwogICAgICBnZXREZXB0KGlkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOwogICAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnnp5HlrqTnu6nmlYjorqHliJIiOwogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuiOt+WPluaVsOaNruWksei0pSIpOwogICAgICB9KTsKICAgIH0sCiAgICBzdWJtaXRGb3JtKCkgewogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgaWYgKHRoaXMuZm9ybS5pZCAhPSBudWxsKSB7CiAgICAgICAgICAgIHVwZGF0ZURlcHQodGhpcy5mb3JtKS50aGVuKCgpID0+IHsKICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsKICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLkv67mlLnlpLHotKUiKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBhZGREZXB0KHRoaXMuZm9ybSkudGhlbigoKSA9PiB7CiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7CiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi5paw5aKe5aSx6LSlIik7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlRGVsZXRlKHJvdykgewogICAgICBjb25zdCBpZHMgPSByb3cgPyBbcm93LmlkXSA6IHRoaXMuaWRzOwogICAgICBjb25zdCBtZXNzYWdlID0gcm93CiAgICAgICAgPyBg5piv5ZCm56Gu6K6k5Yig6Zmk56eR5a6kIiR7cm93LmRlcHROYW1lfSLnmoTnu6nmlYjorqHliJLmlbDmja7pobnvvJ9gCiAgICAgICAgOiBg5piv5ZCm56Gu6K6k5Yig6Zmk6YCJ5Lit55qEJHtpZHMubGVuZ3RofeadoeenkeWupOe7qeaViOiuoeWIkuaVsOaNrumhue+8n2A7CgogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKG1lc3NhZ2UpLnRoZW4oZnVuY3Rpb24oKSB7CiAgICAgICAgcmV0dXJuIGRlbERlcHQoaWRzLmpvaW4oJywnKSk7CiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOwogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7CiAgICB9LAogICAgaGFuZGxlRXhwb3J0U2VsZWN0ZWQoKSB7CiAgICAgIGlmICh0aGlzLmlkcy5sZW5ndGggPT09IDApIHsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6K+36YCJ5oup6KaB5a+85Ye655qE5pWw5o2uIik7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIC8vIOWmguaenOWPqumAieaLqeS6huS4gOadoeaVsOaNru+8jOS9v+eUqOWNleS4quWvvOWHugogICAgICBpZiAodGhpcy5pZHMubGVuZ3RoID09PSAxKSB7CiAgICAgICAgY29uc3Qgc2VsZWN0ZWRSb3cgPSB0aGlzLmxpc3QuZmluZChpdGVtID0+IGl0ZW0uaWQgPT09IHRoaXMuaWRzWzBdKTsKICAgICAgICB0aGlzLmhhbmRsZUV4cG9ydFNpbmdsZShzZWxlY3RlZFJvdyk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIC8vIOWkmuadoeaVsOaNruS9v+eUqOaJuemHj+WvvOWHugogICAgICB0aGlzLiRtb2RhbC5sb2FkaW5nKCLmraPlnKjlr7zlh7rmlbDmja7vvIzor7fnqI3lgJkuLi4iKTsKICAgICAgYmF0Y2hFeHBvcnREZXB0KHRoaXMuaWRzKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICBjb25zdCBibG9iID0gbmV3IEJsb2IoW3Jlc3BvbnNlXSwgeyAKICAgICAgICAgIHR5cGU6ICdhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQud29yZHByb2Nlc3NpbmdtbC5kb2N1bWVudCcgCiAgICAgICAgfSk7CiAgICAgICAgY29uc3QgbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKTsKICAgICAgICBsaW5rLmhyZWYgPSB3aW5kb3cuVVJMLmNyZWF0ZU9iamVjdFVSTChibG9iKTsKICAgICAgICBsaW5rLmRvd25sb2FkID0gJ+enkeWupOe7qeaViOiuoeWIki5kb2N4JzsKICAgICAgICBsaW5rLmNsaWNrKCk7CiAgICAgICAgd2luZG93LlVSTC5yZXZva2VPYmplY3RVUkwobGluay5ocmVmKTsKICAgICAgICB0aGlzLiRtb2RhbC5jbG9zZUxvYWRpbmcoKTsKICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgIHRoaXMuJG1vZGFsLmNsb3NlTG9hZGluZygpOwogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVFeHBvcnRTaW5nbGUocm93KSB7CiAgICAgIHRoaXMuJG1vZGFsLmxvYWRpbmcoIuato+WcqOWvvOWHuuaVsOaNru+8jOivt+eojeWAmS4uLiIpOwogICAgICBleHBvcnREZXB0KHJvdy5pZCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgY29uc3QgYmxvYiA9IG5ldyBCbG9iKFtyZXNwb25zZV0sIHsgCiAgICAgICAgICB0eXBlOiAnYXBwbGljYXRpb24vdm5kLm9wZW54bWxmb3JtYXRzLW9mZmljZWRvY3VtZW50LndvcmRwcm9jZXNzaW5nbWwuZG9jdW1lbnQnIAogICAgICAgIH0pOwogICAgICAgIGNvbnN0IGxpbmsgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdhJyk7CiAgICAgICAgbGluay5ocmVmID0gd2luZG93LlVSTC5jcmVhdGVPYmplY3RVUkwoYmxvYik7CiAgICAgICAgbGluay5kb3dubG9hZCA9IGAke3Jvdy5kZXB0TmFtZX1f57up5pWI6K6h5YiSLmRvY3hgOwogICAgICAgIGxpbmsuY2xpY2soKTsKICAgICAgICB3aW5kb3cuVVJMLnJldm9rZU9iamVjdFVSTChsaW5rLmhyZWYpOwogICAgICAgIHRoaXMuJG1vZGFsLmNsb3NlTG9hZGluZygpOwogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgdGhpcy4kbW9kYWwuY2xvc2VMb2FkaW5nKCk7CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZUltcG9ydFN1Y2Nlc3MocmVzcG9uc2UpIHsKICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWvvOWFpeaIkOWKnyIpOwogICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKHJlc3BvbnNlLm1zZyB8fCAi5a+85YWl5aSx6LSlIik7CiAgICAgIH0KICAgIH0sCiAgICBoYW5kbGVJbXBvcnRFcnJvcihlcnJvcikgewogICAgICBjb25zb2xlLmVycm9yKCLlr7zlhaXlpLHotKU6IiwgZXJyb3IpOwogICAgICBpZiAoZXJyb3Iuc3RhdHVzID09PSA0MDEpIHsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6K6k6K+B5aSx6LSl77yM6K+36YeN5paw55m75b2VIik7CiAgICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ0xvZ091dCcpLnRoZW4oKCkgPT4gewogICAgICAgICAgbG9jYXRpb24uaHJlZiA9ICcvbG9naW4nOwogICAgICAgIH0pOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLlr7zlhaXlpLHotKXvvIzor7fmo4Dmn6Xmlofku7bmoLzlvI8iKTsKICAgICAgfQogICAgfSwKICAgIGJlZm9yZUltcG9ydFVwbG9hZChmaWxlKSB7CiAgICAgIGNvbnN0IGlzRXhjZWwgPSBmaWxlLnR5cGUgPT09ICdhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQuc3ByZWFkc2hlZXRtbC5zaGVldCcgfHwKICAgICAgICAgICAgICAgICAgICAgIGZpbGUudHlwZSA9PT0gJ2FwcGxpY2F0aW9uL3ZuZC5tcy1leGNlbCc7CiAgICAgIGlmICghaXNFeGNlbCkgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCflj6rog73kuIrkvKBFeGNlbOaWh+S7tuagvOW8jyEnKTsKICAgICAgfQogICAgICByZXR1cm4gaXNFeGNlbDsKICAgIH0sCiAgICBoYW5kbGVEb3dubG9hZFRlbXBsYXRlKCkgewogICAgICBkb3dubG9hZEV4Y2VsVGVtcGxhdGUoKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICBjb25zdCBibG9iID0gbmV3IEJsb2IoW3Jlc3BvbnNlXSwgewogICAgICAgICAgdHlwZTogJ2FwcGxpY2F0aW9uL3ZuZC5vcGVueG1sZm9ybWF0cy1vZmZpY2Vkb2N1bWVudC5zcHJlYWRzaGVldG1sLnNoZWV0JwogICAgICAgIH0pOwogICAgICAgIGNvbnN0IGxpbmsgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdhJyk7CiAgICAgICAgbGluay5ocmVmID0gd2luZG93LlVSTC5jcmVhdGVPYmplY3RVUkwoYmxvYik7CiAgICAgICAgbGluay5kb3dubG9hZCA9ICfnp5HlrqTnu6nmlYjorqHliJLmqKHmnb8ueGxzeCc7CiAgICAgICAgbGluay5jbGljaygpOwogICAgICAgIHdpbmRvdy5VUkwucmV2b2tlT2JqZWN0VVJMKGxpbmsuaHJlZik7CiAgICAgIH0pOwogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["dept.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmJA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "dept.vue", "sourceRoot": "src/views/performance/plan", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"科室名称\" prop=\"deptName\">\n        <el-input\n          v-model=\"queryParams.deptName\"\n          placeholder=\"请输入科室名称\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"任务类型\" prop=\"taskType\">\n        <el-input\n          v-model=\"queryParams.taskType\"\n          placeholder=\"请输入任务类型\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\" v-hasPermi=\"['performance:plan:dept:add']\">新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"danger\" plain icon=\"el-icon-delete\" size=\"mini\" :disabled=\"multiple\" @click=\"handleDelete\" v-hasPermi=\"['performance:plan:dept:remove']\">删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" :disabled=\"multiple\" @click=\"handleExportSelected\" v-hasPermi=\"['performance:plan:dept:export']\">导出Word</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-upload\n          class=\"upload-demo\"\n          :action=\"importUrl\"\n          :headers=\"uploadHeaders\"\n          :on-success=\"handleImportSuccess\"\n          :on-error=\"handleImportError\"\n          :before-upload=\"beforeImportUpload\"\n          :show-file-list=\"false\"\n          style=\"display: inline-block;\"\n          v-hasPermi=\"['performance:plan:dept:import']\">\n          <el-button type=\"info\" plain icon=\"el-icon-upload2\" size=\"mini\">导入Excel</el-button>\n        </el-upload>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"info\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"handleDownloadTemplate\" v-hasPermi=\"['performance:plan:dept:list']\">下载模板</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table \n      v-loading=\"loading\"\n      :data=\"list\" \n      @selection-change=\"handleSelectionChange\" \n      ref=\"table\" \n      :row-key=\"row => row.id\" \n      :border=\"true\" \n      style=\"width: 100%\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column prop=\"deptName\" label=\"科室名称\" width=\"120\"/>\n      <el-table-column prop=\"taskType\" label=\"任务类型\" width=\"120\"/>\n      <el-table-column prop=\"taskContent\" label=\"绩效任务\" min-width=\"200\" show-overflow-tooltip/>\n      <el-table-column prop=\"targetMeasure\" label=\"目标及措施\" min-width=\"200\" show-overflow-tooltip/>\n      <el-table-column prop=\"evalStandard\" label=\"评价标准\" min-width=\"200\" show-overflow-tooltip/>\n      <el-table-column prop=\"scoreWeight\" label=\"分值或权重\" width=\"120\"/>\n      <el-table-column prop=\"principal\" label=\"责任人\" width=\"100\"/>\n      <el-table-column prop=\"deadline\" label=\"完成时限\" width=\"120\"/>\n      <el-table-column label=\"操作\" align=\"center\" width=\"180\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['performance:plan:dept:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['performance:plan:dept:remove']\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"科室名称\" prop=\"deptName\">\n              <el-input v-model=\"form.deptName\" placeholder=\"请输入科室名称\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"任务类型\" prop=\"taskType\">\n              <el-input v-model=\"form.taskType\" placeholder=\"请输入任务类型\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-form-item label=\"绩效任务\" prop=\"taskContent\">\n          <el-input v-model=\"form.taskContent\" type=\"textarea\" placeholder=\"请输入绩效任务\" />\n        </el-form-item>\n        <el-form-item label=\"目标及措施\" prop=\"targetMeasure\">\n          <el-input v-model=\"form.targetMeasure\" type=\"textarea\" placeholder=\"请输入目标及措施\" />\n        </el-form-item>\n        <el-form-item label=\"评价标准\" prop=\"evalStandard\">\n          <el-input v-model=\"form.evalStandard\" type=\"textarea\" placeholder=\"请输入评价标准\" />\n        </el-form-item>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"分值或权重\" prop=\"scoreWeight\">\n              <el-input v-model=\"form.scoreWeight\" placeholder=\"请输入分值或权重\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"责任人\" prop=\"principal\">\n              <el-input v-model=\"form.principal\" placeholder=\"请输入责任人\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-form-item label=\"完成时限\" prop=\"deadline\">\n          <el-input v-model=\"form.deadline\" placeholder=\"请输入完成时限\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n<script>\nimport { listDept, getDept, addDept, updateDept, delDept, exportDept, batchExportDept, downloadExcelTemplate } from '@/api/performance/dept'\n\nexport default {\n  name: \"DeptPlan\",\n  data() {\n    return {\n      loading: true,\n      ids: [],\n      single: true,\n      multiple: true,\n      showSearch: true,\n      total: 0,\n      list: [],\n      title: \"\",\n      open: false,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        deptName: null,\n        taskType: null\n      },\n      form: {},\n      rules: {\n        deptName: [\n          { required: true, message: \"科室名称不能为空\", trigger: \"blur\" },\n          { max: 100, message: \"科室名称长度不能超过100个字符\", trigger: \"blur\" }\n        ],\n        taskType: [\n          { required: true, message: \"任务类型不能为空\", trigger: \"blur\" },\n          { max: 100, message: \"任务类型长度不能超过100个字符\", trigger: \"blur\" }\n        ],\n        taskContent: [\n          { required: true, message: \"绩效任务不能为空\", trigger: \"blur\" },\n          { max: 1000, message: \"绩效任务长度不能超过1000个字符\", trigger: \"blur\" }\n        ],\n        targetMeasure: [\n          { max: 1000, message: \"目标及措施长度不能超过1000个字符\", trigger: \"blur\" }\n        ],\n        evalStandard: [\n          { max: 1000, message: \"评价标准长度不能超过1000个字符\", trigger: \"blur\" }\n        ],\n        scoreWeight: [\n          { max: 50, message: \"分值或权重长度不能超过50个字符\", trigger: \"blur\" }\n        ],\n        principal: [\n          { max: 100, message: \"责任人长度不能超过100个字符\", trigger: \"blur\" }\n        ],\n        deadline: [\n          { max: 100, message: \"完成时限长度不能超过100个字符\", trigger: \"blur\" }\n        ]\n      },\n      importUrl: process.env.VUE_APP_BASE_API + \"/performance/plan/dept/importExcel\",\n      uploadHeaders: {}\n    }\n  },\n  created() {\n    this.getList();\n    // 设置上传认证头\n    this.uploadHeaders = {\n      Authorization: 'Bearer ' + this.$store.getters.token\n    };\n  },\n  methods: {\n    getList() {\n      this.loading = true;\n      listDept(this.queryParams).then(response => {\n        this.list = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    reset() {\n      this.form = {\n        id: null,\n        deptName: null,\n        taskType: null,\n        taskContent: null,\n        targetMeasure: null,\n        evalStandard: null,\n        scoreWeight: null,\n        principal: null,\n        deadline: null\n      };\n      this.resetForm(\"form\");\n    },\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加科室绩效计划\";\n    },\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids\n      getDept(id).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改科室绩效计划\";\n      }).catch(() => {\n        this.$modal.msgError(\"获取数据失败\");\n      });\n    },\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id != null) {\n            updateDept(this.form).then(() => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            }).catch(() => {\n              this.$modal.msgError(\"修改失败\");\n            });\n          } else {\n            addDept(this.form).then(() => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            }).catch(() => {\n              this.$modal.msgError(\"新增失败\");\n            });\n          }\n        }\n      });\n    },\n    handleDelete(row) {\n      const ids = row ? [row.id] : this.ids;\n      const message = row\n        ? `是否确认删除科室\"${row.deptName}\"的绩效计划数据项？`\n        : `是否确认删除选中的${ids.length}条科室绩效计划数据项？`;\n\n      this.$modal.confirm(message).then(function() {\n        return delDept(ids.join(','));\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    handleExportSelected() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要导出的数据\");\n        return;\n      }\n      // 如果只选择了一条数据，使用单个导出\n      if (this.ids.length === 1) {\n        const selectedRow = this.list.find(item => item.id === this.ids[0]);\n        this.handleExportSingle(selectedRow);\n        return;\n      }\n      // 多条数据使用批量导出\n      this.$modal.loading(\"正在导出数据，请稍候...\");\n      batchExportDept(this.ids).then(response => {\n        const blob = new Blob([response], { \n          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' \n        });\n        const link = document.createElement('a');\n        link.href = window.URL.createObjectURL(blob);\n        link.download = '科室绩效计划.docx';\n        link.click();\n        window.URL.revokeObjectURL(link.href);\n        this.$modal.closeLoading();\n      }).catch(() => {\n        this.$modal.closeLoading();\n      });\n    },\n    handleExportSingle(row) {\n      this.$modal.loading(\"正在导出数据，请稍候...\");\n      exportDept(row.id).then(response => {\n        const blob = new Blob([response], { \n          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' \n        });\n        const link = document.createElement('a');\n        link.href = window.URL.createObjectURL(blob);\n        link.download = `${row.deptName}_绩效计划.docx`;\n        link.click();\n        window.URL.revokeObjectURL(link.href);\n        this.$modal.closeLoading();\n      }).catch(() => {\n        this.$modal.closeLoading();\n      });\n    },\n    handleImportSuccess(response) {\n      if (response.code === 200) {\n        this.$modal.msgSuccess(\"导入成功\");\n        this.getList();\n      } else {\n        this.$modal.msgError(response.msg || \"导入失败\");\n      }\n    },\n    handleImportError(error) {\n      console.error(\"导入失败:\", error);\n      if (error.status === 401) {\n        this.$modal.msgError(\"认证失败，请重新登录\");\n        this.$store.dispatch('LogOut').then(() => {\n          location.href = '/login';\n        });\n      } else {\n        this.$modal.msgError(\"导入失败，请检查文件格式\");\n      }\n    },\n    beforeImportUpload(file) {\n      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\n                      file.type === 'application/vnd.ms-excel';\n      if (!isExcel) {\n        this.$modal.msgError('只能上传Excel文件格式!');\n      }\n      return isExcel;\n    },\n    handleDownloadTemplate() {\n      downloadExcelTemplate().then(response => {\n        const blob = new Blob([response], {\n          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\n        });\n        const link = document.createElement('a');\n        link.href = window.URL.createObjectURL(blob);\n        link.download = '科室绩效计划模板.xlsx';\n        link.click();\n        window.URL.revokeObjectURL(link.href);\n      });\n    }\n  }\n}\n</script> \n\n<style scoped>\n.upload-demo {\n  display: inline-block;\n}\n</style> "]}]}