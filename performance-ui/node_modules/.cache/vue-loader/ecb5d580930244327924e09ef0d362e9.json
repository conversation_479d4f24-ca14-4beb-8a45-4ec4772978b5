{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/dashboard/RaddarChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/dashboard/RaddarChart.vue", "mtime": 1753510684532}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["RaddarChart.vue"], "names": [], "mappings": ";;;;;AAKA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "RaddarChart.vue", "sourceRoot": "src/views/dashboard", "sourcesContent": ["<template>\n  <div :class=\"className\" :style=\"{height:height,width:width}\" />\n</template>\n\n<script>\nimport * as echarts from 'echarts'\nrequire('echarts/theme/macarons') // echarts theme\nimport resize from './mixins/resize'\n\nconst animationDuration = 3000\n\nexport default {\n  mixins: [resize],\n  props: {\n    className: {\n      type: String,\n      default: 'chart'\n    },\n    width: {\n      type: String,\n      default: '100%'\n    },\n    height: {\n      type: String,\n      default: '300px'\n    }\n  },\n  data() {\n    return {\n      chart: null\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.initChart()\n    })\n  },\n  beforeDestroy() {\n    if (!this.chart) {\n      return\n    }\n    this.chart.dispose()\n    this.chart = null\n  },\n  methods: {\n    initChart() {\n      this.chart = echarts.init(this.$el, 'macarons')\n\n      this.chart.setOption({\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: { // 坐标轴指示器，坐标轴触发有效\n            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'\n          }\n        },\n        radar: {\n          radius: '66%',\n          center: ['50%', '42%'],\n          splitNumber: 8,\n          splitArea: {\n            areaStyle: {\n              color: 'rgba(127,95,132,.3)',\n              opacity: 1,\n              shadowBlur: 45,\n              shadowColor: 'rgba(0,0,0,.5)',\n              shadowOffsetX: 0,\n              shadowOffsetY: 15\n            }\n          },\n          indicator: [\n            { name: 'Sales', max: 10000 },\n            { name: 'Administration', max: 20000 },\n            { name: 'Information Techology', max: 20000 },\n            { name: 'Customer Support', max: 20000 },\n            { name: 'Development', max: 20000 },\n            { name: 'Marketing', max: 20000 }\n          ]\n        },\n        legend: {\n          left: 'center',\n          bottom: '10',\n          data: ['Allocated Budget', 'Expected Spending', 'Actual Spending']\n        },\n        series: [{\n          type: 'radar',\n          symbolSize: 0,\n          areaStyle: {\n            normal: {\n              shadowBlur: 13,\n              shadowColor: 'rgba(0,0,0,.2)',\n              shadowOffsetX: 0,\n              shadowOffsetY: 10,\n              opacity: 1\n            }\n          },\n          data: [\n            {\n              value: [5000, 7000, 12000, 11000, 15000, 14000],\n              name: 'Allocated Budget'\n            },\n            {\n              value: [4000, 9000, 15000, 15000, 13000, 11000],\n              name: 'Expected Spending'\n            },\n            {\n              value: [5500, 11000, 12000, 15000, 12000, 12000],\n              name: 'Actual Spending'\n            }\n          ],\n          animationDuration: animationDuration\n        }]\n      })\n    }\n  }\n}\n</script>\n"]}]}