{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/evaluation/organization/index.vue?vue&type=style&index=0&id=4f59fb02&scoped=true&lang=scss", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/evaluation/organization/index.vue", "mtime": 1753510684533}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/css-loader/dist/cjs.js", "mtime": 1753510683027}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1753510684373}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/postcss-loader/src/index.js", "mtime": 1753510684004}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/sass-loader/dist/cjs.js", "mtime": 1753510684159}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5zZWFyY2gtZm9ybSB7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKfQoubWI4IHsKICBtYXJnaW4tYm90dG9tOiA4cHg7Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkXA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/evaluation/organization", "sourcesContent": ["<script>\nimport { listOrganization, getOrganization, addOrganization, updateOrganization, delOrganization, importWord, exportWord, downloadTemplate } from '@/api/performance/organization'\nimport { getToken } from '@/utils/auth'\n\nexport default {\n  name: 'OrganizationEvaluation',\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 组织绩效考核测评表表格数据\n      tableData: [],\n      // 弹出层标题\n      title: '',\n      // 是否显示弹出层\n      open: false,\n      // 查询参数\n      queryParams: {\n        taskType: null,\n        responsibleDept: null,\n        status: '0'\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        taskType: [\n          { required: true, message: '任务类型不能为空', trigger: 'blur' }\n        ],\n        performanceTask: [\n          { required: true, message: '绩效任务不能为空', trigger: 'blur' }\n        ],\n        responsibleDept: [\n          { required: true, message: '责任科室不能为空', trigger: 'blur' }\n        ]\n      },\n      // 上传参数\n      upload: {\n        // 是否显示弹出层\n        open: false,\n        // 弹出层标题\n        title: '',\n        // 是否禁用上传\n        isUploading: false,\n        // 设置上传的请求头部\n        headers: { Authorization: 'Bearer ' + getToken() },\n        // 上传的地址\n        url: process.env.VUE_APP_BASE_API + '/performance/organization/importWord'\n      }\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    /** 查询组织绩效考核测评表列表 */\n    getList() {\n      this.loading = true\n      listOrganization(this.queryParams).then(response => {\n        this.tableData = response.rows\n        this.total = response.total\n        this.loading = false\n      })\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false\n      this.reset()\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: null,\n        serialNumber: null,\n        taskType: null,\n        taskSource: null,\n        performanceTask: null,\n        targetMeasures: null,\n        responsibleDept: null,\n        scoreWeight: null,\n        responsibleLeader: null,\n        completionDeadline: null,\n        evaluationScore: null,\n        status: '0',\n        remark: null\n      }\n      this.resetForm('form')\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.getList()\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm('queryForm')\n      this.handleQuery()\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length !== 1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset()\n      this.open = true\n      this.title = '添加组织绩效考核测评表'\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset()\n      const id = row.id || this.ids[0]\n      getOrganization(id).then(response => {\n        this.form = response.data\n        this.open = true\n        this.title = '修改组织绩效考核测评表'\n      })\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs['form'].validate(valid => {\n        if (valid) {\n          if (this.form.id != null) {\n            updateOrganization(this.form).then(response => {\n              this.$modal.msgSuccess('修改成功')\n              this.open = false\n              this.getList()\n            })\n          } else {\n            addOrganization(this.form).then(response => {\n              this.$modal.msgSuccess('新增成功')\n              this.open = false\n              this.getList()\n            })\n          }\n        }\n      })\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row.id || this.ids\n      this.$modal.confirm('是否确认删除组织绩效考核测评表编号为\"' + ids + '\"的数据项？').then(() => {\n        return delOrganization(ids)\n      }).then(() => {\n        this.getList()\n        this.$modal.msgSuccess('删除成功')\n      }).catch(() => {})\n    },\n    /** 导入按钮操作 */\n    handleImport() {\n      this.upload.open = true\n      this.upload.title = '导入组织绩效考核测评表数据'\n    },\n    /** 下载模板操作 */\n    handleDownloadTemplate() {\n      downloadTemplate().then(response => {\n        const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })\n        const link = document.createElement('a')\n        link.href = window.URL.createObjectURL(blob)\n        link.download = '组织绩效考核测评表模板.docx'\n        link.click()\n        window.URL.revokeObjectURL(link.href)\n      })\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      const ids = this.ids.length ? this.ids : []\n      this.$modal.confirm('是否确认导出所选组织绩效考核测评表数据？').then(() => {\n        exportWord(ids).then(response => {\n          const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })\n          const link = document.createElement('a')\n          link.href = window.URL.createObjectURL(blob)\n          link.download = '组织绩效考核测评表.docx'\n          link.click()\n          window.URL.revokeObjectURL(link.href)\n        })\n      }).catch(() => {})\n    },\n    // 文件上传中处理\n    handleFileUploadProgress(event, file, fileList) {\n      this.upload.isUploading = true\n    },\n    // 文件上传成功处理\n    handleFileSuccess(response, file, fileList) {\n      this.upload.open = false\n      this.upload.isUploading = false\n      this.$refs.upload.clearFiles()\n      this.$alert(response.msg, '导入结果', { dangerouslyUseHTMLString: true })\n      this.getList()\n    },\n    // 提交上传文件\n    submitFileForm() {\n      this.$refs.upload.submit()\n    }\n  }\n}\n</script>\n\n<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" class=\"search-form\">\n      <el-form-item label=\"任务类型\" prop=\"taskType\">\n        <el-input v-model=\"queryParams.taskType\" placeholder=\"请输入任务类型\" clearable />\n      </el-form-item>\n      <el-form-item label=\"责任科室\" prop=\"responsibleDept\">\n        <el-input v-model=\"queryParams.responsibleDept\" placeholder=\"请输入责任科室\" clearable />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" @click=\"handleAdd\">新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"success\" plain icon=\"el-icon-edit\" :disabled=\"single\" @click=\"handleUpdate\">修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"danger\" plain icon=\"el-icon-delete\" :disabled=\"multiple\" @click=\"handleDelete\">删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"warning\" plain icon=\"el-icon-upload\" @click=\"handleImport\">导入</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"info\" plain icon=\"el-icon-download\" @click=\"handleDownloadTemplate\">下载模板</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"info\" plain icon=\"el-icon-download\" :disabled=\"multiple\" @click=\"handleExport\">导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"tableData\" @selection-change=\"handleSelectionChange\" border>\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"序号\" align=\"center\" prop=\"serialNumber\" width=\"80\" />\n      <el-table-column label=\"任务类型\" align=\"center\" prop=\"taskType\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"任务来源\" align=\"center\" prop=\"taskSource\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"绩效任务\" align=\"center\" prop=\"performanceTask\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"目标及措施\" align=\"center\" prop=\"targetMeasures\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"责任科室\" align=\"center\" prop=\"responsibleDept\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"分值及权重\" align=\"center\" prop=\"scoreWeight\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"责任领导\" align=\"center\" prop=\"responsibleLeader\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"完成时限\" align=\"center\" prop=\"completionDeadline\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"评价分值\" align=\"center\" prop=\"evaluationScore\" :show-overflow-tooltip=\"true\" />\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleUpdate(scope.row)\">修改</el-button>\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleDelete(scope.row)\">删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改组织绩效考核测评表对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"780px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"序号\" prop=\"serialNumber\">\n              <el-input-number v-model=\"form.serialNumber\" :min=\"1\" :max=\"999\" controls-position=\"right\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"任务类型\" prop=\"taskType\">\n              <el-input v-model=\"form.taskType\" placeholder=\"请输入任务类型\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"任务来源\" prop=\"taskSource\">\n              <el-input v-model=\"form.taskSource\" placeholder=\"请输入任务来源\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-form-item label=\"绩效任务\" prop=\"performanceTask\">\n          <el-input v-model=\"form.performanceTask\" type=\"textarea\" placeholder=\"请输入绩效任务\" />\n        </el-form-item>\n        <el-form-item label=\"目标及措施\" prop=\"targetMeasures\">\n          <el-input v-model=\"form.targetMeasures\" type=\"textarea\" placeholder=\"请输入目标及措施\" />\n        </el-form-item>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"责任科室\" prop=\"responsibleDept\">\n              <el-input v-model=\"form.responsibleDept\" placeholder=\"请输入责任科室\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"分值及权重\" prop=\"scoreWeight\">\n              <el-input v-model=\"form.scoreWeight\" placeholder=\"请输入分值及权重\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"责任领导\" prop=\"responsibleLeader\">\n              <el-input v-model=\"form.responsibleLeader\" placeholder=\"请输入责任领导\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"完成时限\" prop=\"completionDeadline\">\n              <el-input v-model=\"form.completionDeadline\" placeholder=\"请输入完成时限\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"评价分值\" prop=\"evaluationScore\">\n              <el-input-number v-model=\"form.evaluationScore\" :precision=\"2\" :step=\"0.1\" :min=\"0\" :max=\"100\" controls-position=\"right\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入备注\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 导入对话框 -->\n    <el-dialog :title=\"upload.title\" :visible.sync=\"upload.open\" width=\"400px\" append-to-body>\n      <el-upload\n        ref=\"upload\"\n        :limit=\"1\"\n        accept=\".docx\"\n        :headers=\"upload.headers\"\n        :action=\"upload.url\"\n        :disabled=\"upload.isUploading\"\n        :on-progress=\"handleFileUploadProgress\"\n        :on-success=\"handleFileSuccess\"\n        :auto-upload=\"false\"\n        drag\n      >\n        <i class=\"el-icon-upload\"></i>\n        <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\n        <div class=\"el-upload__tip\" slot=\"tip\">只能上传docx文件，且不超过10MB</div>\n      </el-upload>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitFileForm\">确 定</el-button>\n        <el-button @click=\"upload.open = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<style scoped lang=\"scss\">\n.search-form {\n  margin-bottom: 20px;\n}\n.mb8 {\n  margin-bottom: 8px;\n}\n</style>\n"]}]}