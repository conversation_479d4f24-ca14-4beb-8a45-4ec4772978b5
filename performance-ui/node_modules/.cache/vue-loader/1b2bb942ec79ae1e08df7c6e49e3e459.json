{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/system/user/profile/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/system/user/profile/index.vue", "mtime": 1753510684536}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB1c2VyQXZhdGFyIGZyb20gIi4vdXNlckF2YXRhciIKaW1wb3J0IHVzZXJJbmZvIGZyb20gIi4vdXNlckluZm8iCmltcG9ydCByZXNldFB3ZCBmcm9tICIuL3Jlc2V0UHdkIgppbXBvcnQgeyBnZXRVc2VyUHJvZmlsZSB9IGZyb20gIkAvYXBpL3N5c3RlbS91c2VyIgoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJQcm9maWxlIiwKICBjb21wb25lbnRzOiB7IHVzZXJBdmF0YXIsIHVzZXJJbmZvLCByZXNldFB3ZCB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB1c2VyOiB7fSwKICAgICAgcm9sZUdyb3VwOiB7fSwKICAgICAgcG9zdEdyb3VwOiB7fSwKICAgICAgc2VsZWN0ZWRUYWI6ICJ1c2VyaW5mbyIKICAgIH0KICB9LAogIGNyZWF0ZWQoKSB7CiAgICBjb25zdCBhY3RpdmVUYWIgPSB0aGlzLiRyb3V0ZS5wYXJhbXMgJiYgdGhpcy4kcm91dGUucGFyYW1zLmFjdGl2ZVRhYgogICAgaWYgKGFjdGl2ZVRhYikgewogICAgICB0aGlzLnNlbGVjdGVkVGFiID0gYWN0aXZlVGFiCiAgICB9CiAgICB0aGlzLmdldFVzZXIoKQogIH0sCiAgbWV0aG9kczogewogICAgZ2V0VXNlcigpIHsKICAgICAgZ2V0VXNlclByb2ZpbGUoKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLnVzZXIgPSByZXNwb25zZS5kYXRhCiAgICAgICAgdGhpcy5yb2xlR3JvdXAgPSByZXNwb25zZS5yb2xlR3JvdXAKICAgICAgICB0aGlzLnBvc3RHcm91cCA9IHJlc3BvbnNlLnBvc3RHcm91cAogICAgICB9KQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6DA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/profile", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"20\">\n      <el-col :span=\"6\" :xs=\"24\">\n        <el-card class=\"box-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>个人信息</span>\n          </div>\n          <div>\n            <div class=\"text-center\">\n              <userAvatar />\n            </div>\n            <ul class=\"list-group list-group-striped\">\n              <li class=\"list-group-item\">\n                <svg-icon icon-class=\"user\" />用户名称\n                <div class=\"pull-right\">{{ user.userName }}</div>\n              </li>\n              <li class=\"list-group-item\">\n                <svg-icon icon-class=\"phone\" />手机号码\n                <div class=\"pull-right\">{{ user.phonenumber }}</div>\n              </li>\n              <li class=\"list-group-item\">\n                <svg-icon icon-class=\"email\" />用户邮箱\n                <div class=\"pull-right\">{{ user.email }}</div>\n              </li>\n              <li class=\"list-group-item\">\n                <svg-icon icon-class=\"tree\" />所属部门\n                <div class=\"pull-right\" v-if=\"user.dept\">{{ user.dept.deptName }} / {{ postGroup }}</div>\n              </li>\n              <li class=\"list-group-item\">\n                <svg-icon icon-class=\"peoples\" />所属角色\n                <div class=\"pull-right\">{{ roleGroup }}</div>\n              </li>\n              <li class=\"list-group-item\">\n                <svg-icon icon-class=\"date\" />创建日期\n                <div class=\"pull-right\">{{ user.createTime }}</div>\n              </li>\n            </ul>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"18\" :xs=\"24\">\n        <el-card>\n          <div slot=\"header\" class=\"clearfix\">\n            <span>基本资料</span>\n          </div>\n          <el-tabs v-model=\"selectedTab\">\n            <el-tab-pane label=\"基本资料\" name=\"userinfo\">\n              <userInfo :user=\"user\" />\n            </el-tab-pane>\n            <el-tab-pane label=\"修改密码\" name=\"resetPwd\">\n              <resetPwd />\n            </el-tab-pane>\n          </el-tabs>\n        </el-card>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport userAvatar from \"./userAvatar\"\nimport userInfo from \"./userInfo\"\nimport resetPwd from \"./resetPwd\"\nimport { getUserProfile } from \"@/api/system/user\"\n\nexport default {\n  name: \"Profile\",\n  components: { userAvatar, userInfo, resetPwd },\n  data() {\n    return {\n      user: {},\n      roleGroup: {},\n      postGroup: {},\n      selectedTab: \"userinfo\"\n    }\n  },\n  created() {\n    const activeTab = this.$route.params && this.$route.params.activeTab\n    if (activeTab) {\n      this.selectedTab = activeTab\n    }\n    this.getUser()\n  },\n  methods: {\n    getUser() {\n      getUserProfile().then(response => {\n        this.user = response.data\n        this.roleGroup = response.roleGroup\n        this.postGroup = response.postGroup\n      })\n    }\n  }\n}\n</script>\n"]}]}