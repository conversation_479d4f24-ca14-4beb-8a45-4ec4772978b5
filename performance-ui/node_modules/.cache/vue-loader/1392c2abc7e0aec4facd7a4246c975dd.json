{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/performance/personnel/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/performance/personnel/index.vue", "mtime": 1754489749513}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgbGlzdFBlcnNvbm5lbFBsYW4sCiAgZ2V0UGVyc29ubmVsUGxhbiwKICBkZWxQZXJzb25uZWxQbGFuLAogIGFkZFBlcnNvbm5lbFBsYW4sCiAgdXBkYXRlUGVyc29ubmVsUGxhbiwKICBleHBvcnRQZXJzb25uZWxQbGFuLAogIGJhdGNoRXhwb3J0UGVyc29ubmVsUGxhbiwKICBpbXBvcnRQZXJzb25uZWxQbGFuLAogIGRvd25sb2FkVGVtcGxhdGUKfSBmcm9tICJAL2FwaS9wZXJmb3JtYW5jZS9wZXJzb25uZWwiOwppbXBvcnQgewogIGxpc3RQZXJzb25uZWxUYXNrLAogIGdldFBlcnNvbm5lbFRhc2ssCiAgZGVsUGVyc29ubmVsVGFzaywKICBhZGRQZXJzb25uZWxUYXNrLAogIHVwZGF0ZVBlcnNvbm5lbFRhc2sKfSBmcm9tICJAL2FwaS9wZXJmb3JtYW5jZS9wZXJzb25uZWxUYXNrIjsKaW1wb3J0IHsKICBsaXN0UGVyc29ubmVsWWVhcmx5UGxhbiwKICBnZXRQZXJzb25uZWxZZWFybHlQbGFuLAogIGRlbFBlcnNvbm5lbFllYXJseVBsYW4sCiAgYWRkUGVyc29ubmVsWWVhcmx5UGxhbiwKICB1cGRhdGVQZXJzb25uZWxZZWFybHlQbGFuLAogIGdldFBlcnNvbmFsSW5kaWNhdG9ycywKICBnZXRDb21tb25JbmRpY2F0b3JzLAogIGRvd25sb2FkWWVhcmx5RXhjZWxUZW1wbGF0ZQp9IGZyb20gIkAvYXBpL3BlcmZvcm1hbmNlL3BlcnNvbm5lbFllYXJseSI7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIlBlcnNvbm5lbFBsYW4iLAogIGRpY3RzOiBbJ3BlcmZvcm1hbmNlX3N0YXR1cyddLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyBUYWLnm7jlhbMKICAgICAgYWN0aXZlVGFiOiAncXVhcnRlcmx5JywKCiAgICAgIC8vIOWto+W6pue7qeaViOiuoeWIkuebuOWFswogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICBpZHM6IFtdLAogICAgICBzaW5nbGU6IHRydWUsCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICBzaG93U2VhcmNoOiB0cnVlLAogICAgICB0b3RhbDogMCwKICAgICAgcGxhbkxpc3Q6IFtdLAogICAgICB0YXNrTGlzdDogW10sCiAgICAgIHBlcnNvbmFsSW5kaWNhdG9yTGlzdDogW10sCiAgICAgIGNvbW1vbkluZGljYXRvckxpc3Q6IFtdLAogICAgICBjdXJyZW50UGxhbjogbnVsbCwKICAgICAgdGl0bGU6ICIiLAogICAgICB0YXNrVGl0bGU6ICIiLAogICAgICBvcGVuOiBmYWxzZSwKICAgICAgdGFza09wZW46IGZhbHNlLAogICAgICBwZXJzb25hbEluZGljYXRvck9wZW46IGZhbHNlLAogICAgICBjb21tb25JbmRpY2F0b3JPcGVuOiBmYWxzZSwKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBuYW1lOiBudWxsLAogICAgICAgIGRlcGFydG1lbnQ6IG51bGwsCiAgICAgICAgc3RhdHVzOiBudWxsCiAgICAgIH0sCiAgICAgIGZvcm06IHt9LAogICAgICB0YXNrRm9ybToge30sCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgbmFtZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWnk+WQjeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBkZXBhcnRtZW50OiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi56eR5a6k5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdCiAgICAgIH0sCiAgICAgIHRhc2tSdWxlczogewogICAgICAgIHRhc2tUeXBlOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5Lu75Yqh57G75Z6L5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIHRhc2tDb250ZW50OiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5Lu75Yqh5YaF5a655LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIHNjb3JlOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5YiG5YC85LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdCiAgICAgIH0sCiAgICAgIGltcG9ydFVybDogcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArICIvcGVyZm9ybWFuY2UvcGVyc29ubmVsL2ltcG9ydCIsCiAgICAgIHVwbG9hZEhlYWRlcnM6IHt9LAoKICAgICAgLy8g5bm05bqm57up5pWI6K6h5YiS55u45YWzCiAgICAgIHllYXJseUxvYWRpbmc6IHRydWUsCiAgICAgIHllYXJseUlkczogW10sCiAgICAgIHllYXJseU11bHRpcGxlOiB0cnVlLAogICAgICB5ZWFybHlUb3RhbDogMCwKICAgICAgeWVhcmx5UGxhbkxpc3Q6IFtdLAogICAgICB5ZWFybHlRdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIG5hbWU6IG51bGwsCiAgICAgICAgZGVwYXJ0bWVudDogbnVsbCwKICAgICAgICB5ZWFyOiBudWxsCiAgICAgIH0sCiAgICAgIHllYXJseUltcG9ydFVybDogcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArICIvcGVyZm9ybWFuY2UvcGVyc29ubmVsL3llYXJseS9pbXBvcnRFeGNlbCIKICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCk7CiAgICAvLyDorr7nva7kuIrkvKDorqTor4HlpLQKICAgIHRoaXMudXBsb2FkSGVhZGVycyA9IHsKICAgICAgQXV0aG9yaXphdGlvbjogJ0JlYXJlciAnICsgdGhpcy4kc3RvcmUuZ2V0dGVycy50b2tlbgogICAgfTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGdldExpc3QoKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGxpc3RQZXJzb25uZWxQbGFuKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMucGxhbkxpc3QgPSByZXNwb25zZS5yb3dzOwogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgZ2V0VGFza0xpc3QocGxhbklkKSB7CiAgICAgIGlmICghcGxhbklkKSByZXR1cm47CiAgICAgIGxpc3RQZXJzb25uZWxUYXNrKHsgcGxhbklkOiBwbGFuSWQgfSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy50YXNrTGlzdCA9IHJlc3BvbnNlLnJvd3MgfHwgW107CiAgICAgIH0pOwogICAgfSwKICAgIGNhbmNlbCgpIHsKICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICBjYW5jZWxUYXNrKCkgewogICAgICB0aGlzLnRhc2tPcGVuID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXRUYXNrKCk7CiAgICB9LAogICAgcmVzZXQoKSB7CiAgICAgIHRoaXMuZm9ybSA9IHsKICAgICAgICBpZDogbnVsbCwKICAgICAgICBuYW1lOiBudWxsLAogICAgICAgIGRlcGFydG1lbnQ6IG51bGwsCiAgICAgICAgc3RhdHVzOiAiRFJBRlQiCiAgICAgIH07CiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7CiAgICB9LAogICAgcmVzZXRUYXNrKCkgewogICAgICB0aGlzLnRhc2tGb3JtID0gewogICAgICAgIGlkOiBudWxsLAogICAgICAgIHBsYW5JZDogbnVsbCwKICAgICAgICB0YXNrVHlwZTogbnVsbCwKICAgICAgICB0YXNrQ29udGVudDogbnVsbCwKICAgICAgICB0YXJnZXRNZWFzdXJlczogbnVsbCwKICAgICAgICBldmFsdWF0aW9uU3RhbmRhcmQ6IG51bGwsCiAgICAgICAgc2NvcmU6IG51bGwsCiAgICAgICAgcmVzcG9uc2libGU6IG51bGwsCiAgICAgICAgZGVhZGxpbmU6IG51bGwKICAgICAgfTsKICAgICAgdGhpcy5yZXNldEZvcm0oInRhc2tGb3JtIik7CiAgICB9LAogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5pZCkKICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoIT09MQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGgKICAgIH0sCiAgICBoYW5kbGVDdXJyZW50Q2hhbmdlKGN1cnJlbnRSb3cpIHsKICAgICAgdGhpcy5jdXJyZW50UGxhbiA9IGN1cnJlbnRSb3c7CiAgICAgIGlmIChjdXJyZW50Um93KSB7CiAgICAgICAgdGhpcy5nZXRUYXNrTGlzdChjdXJyZW50Um93LmlkKTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnRhc2tMaXN0ID0gW107CiAgICAgIH0KICAgIH0sCiAgICBoYW5kbGVBZGQoKSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDnu6nmlYjorqHliJIiOwogICAgfSwKICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICBjb25zdCBpZCA9IHJvdy5pZCB8fCB0aGlzLmlkcwogICAgICBnZXRQZXJzb25uZWxQbGFuKGlkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOwogICAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnnu6nmlYjorqHliJIiOwogICAgICB9KTsKICAgIH0sCiAgICBzdWJtaXRGb3JtKCkgewogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgaWYgKHRoaXMuZm9ybS5pZCAhPSBudWxsKSB7CiAgICAgICAgICAgIHVwZGF0ZVBlcnNvbm5lbFBsYW4odGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsKICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBhZGRQZXJzb25uZWxQbGFuKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7CiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlRGVsZXRlKHJvdykgewogICAgICBjb25zdCBpZHMgPSByb3cuaWQgfHwgdGhpcy5pZHM7CiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOe7qeaViOiuoeWIkue8luWPt+S4uiInICsgaWRzICsgJyLnmoTmlbDmja7pobnvvJ8nKS50aGVuKGZ1bmN0aW9uKCkgewogICAgICAgIHJldHVybiBkZWxQZXJzb25uZWxQbGFuKGlkcyk7CiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOwogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7CiAgICB9LAogICAgaGFuZGxlQWRkVGFzaygpIHsKICAgICAgaWYgKCF0aGlzLmN1cnJlbnRQbGFuKSB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuivt+WFiOmAieaLqeS4gOS4que7qeaViOiuoeWIkiIpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICB0aGlzLnJlc2V0VGFzaygpOwogICAgICB0aGlzLnRhc2tGb3JtLnBsYW5JZCA9IHRoaXMuY3VycmVudFBsYW4uaWQ7CiAgICAgIHRoaXMudGFza09wZW4gPSB0cnVlOwogICAgICB0aGlzLnRhc2tUaXRsZSA9ICLmt7vliqDnu6nmlYjku7vliqEiOwogICAgfSwKICAgIGhhbmRsZVVwZGF0ZVRhc2socm93KSB7CiAgICAgIHRoaXMucmVzZXRUYXNrKCk7CiAgICAgIGNvbnN0IGlkID0gcm93LmlkOwogICAgICBnZXRQZXJzb25uZWxUYXNrKGlkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLnRhc2tGb3JtID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICB0aGlzLnRhc2tPcGVuID0gdHJ1ZTsKICAgICAgICB0aGlzLnRhc2tUaXRsZSA9ICLkv67mlLnnu6nmlYjku7vliqEiOwogICAgICB9KTsKICAgIH0sCiAgICBzdWJtaXRUYXNrRm9ybSgpIHsKICAgICAgdGhpcy4kcmVmc1sidGFza0Zvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBpZiAodGhpcy50YXNrRm9ybS5pZCAhPSBudWxsKSB7CiAgICAgICAgICAgIHVwZGF0ZVBlcnNvbm5lbFRhc2sodGhpcy50YXNrRm9ybSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7CiAgICAgICAgICAgICAgdGhpcy50YXNrT3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuZ2V0VGFza0xpc3QodGhpcy5jdXJyZW50UGxhbi5pZCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgYWRkUGVyc29ubmVsVGFzayh0aGlzLnRhc2tGb3JtKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsKICAgICAgICAgICAgICB0aGlzLnRhc2tPcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgdGhpcy5nZXRUYXNrTGlzdCh0aGlzLmN1cnJlbnRQbGFuLmlkKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVEZWxldGVUYXNrKHJvdykgewogICAgICBjb25zdCBpZCA9IHJvdy5pZDsKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk6K+l5Lu75Yqh5pWw5o2u6aG577yfJykudGhlbihmdW5jdGlvbigpIHsKICAgICAgICByZXR1cm4gZGVsUGVyc29ubmVsVGFzayhpZCk7CiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuZ2V0VGFza0xpc3QodGhpcy5jdXJyZW50UGxhbi5pZCk7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7CiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsKICAgIH0sCiAgICBoYW5kbGVFeHBvcnRTZWxlY3RlZCgpIHsKICAgICAgaWYgKHRoaXMuaWRzLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1dhcm5pbmcoIuivt+mAieaLqeimgeWvvOWHuueahOaVsOaNriIpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTlr7zlh7rpgInkuK3nmoQnICsgdGhpcy5pZHMubGVuZ3RoICsgJ+adoeaVsOaNru+8nycpLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuJG1vZGFsLmxvYWRpbmcoIuato+WcqOWvvOWHuuaVsOaNru+8jOivt+eojeWAmS4uLiIpOwogICAgICAgIGJhdGNoRXhwb3J0UGVyc29ubmVsUGxhbih0aGlzLmlkcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICBjb25zdCBibG9iID0gbmV3IEJsb2IoW3Jlc3BvbnNlXSwgewogICAgICAgICAgICB0eXBlOiAnYXBwbGljYXRpb24vdm5kLm9wZW54bWxmb3JtYXRzLW9mZmljZWRvY3VtZW50LndvcmRwcm9jZXNzaW5nbWwuZG9jdW1lbnQnCiAgICAgICAgICB9KTsKICAgICAgICAgIGNvbnN0IGxpbmsgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdhJyk7CiAgICAgICAgICBsaW5rLmhyZWYgPSB3aW5kb3cuVVJMLmNyZWF0ZU9iamVjdFVSTChibG9iKTsKICAgICAgICAgIGxpbmsuZG93bmxvYWQgPSAn5Liq5Lq657up5pWI6K6h5YiSXycgKyBuZXcgRGF0ZSgpLmdldFRpbWUoKSArICcuZG9jeCc7CiAgICAgICAgICBsaW5rLmNsaWNrKCk7CiAgICAgICAgICB3aW5kb3cuVVJMLnJldm9rZU9iamVjdFVSTChsaW5rLmhyZWYpOwogICAgICAgICAgdGhpcy4kbW9kYWwuY2xvc2VMb2FkaW5nKCk7CiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLlr7zlh7rmiJDlip8iKTsKICAgICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgICB0aGlzLiRtb2RhbC5jbG9zZUxvYWRpbmcoKTsKICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLlr7zlh7rlpLHotKUiKTsKICAgICAgICB9KTsKICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOwogICAgfSwKICAgIGhhbmRsZUltcG9ydFN1Y2Nlc3MocmVzcG9uc2UpIHsKICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWvvOWFpeaIkOWKnyIpOwogICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKHJlc3BvbnNlLm1zZyB8fCAi5a+85YWl5aSx6LSlIik7CiAgICAgIH0KICAgIH0sCiAgICBoYW5kbGVJbXBvcnRFcnJvcihlcnJvcikgewogICAgICBjb25zb2xlLmVycm9yKCLlr7zlhaXlpLHotKU6IiwgZXJyb3IpOwogICAgICBpZiAoZXJyb3Iuc3RhdHVzID09PSA0MDEpIHsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6K6k6K+B5aSx6LSl77yM6K+36YeN5paw55m75b2VIik7CiAgICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ0xvZ091dCcpLnRoZW4oKCkgPT4gewogICAgICAgICAgbG9jYXRpb24uaHJlZiA9ICcvbG9naW4nOwogICAgICAgIH0pOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLlr7zlhaXlpLHotKXvvIzor7fmo4Dmn6Xmlofku7bmoLzlvI8iKTsKICAgICAgfQogICAgfSwKICAgIGJlZm9yZUltcG9ydFVwbG9hZChmaWxlKSB7CiAgICAgIGNvbnN0IGlzRXhjZWwgPSBmaWxlLnR5cGUgPT09ICdhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQud29yZHByb2Nlc3NpbmdtbC5kb2N1bWVudCc7CiAgICAgIGlmICghaXNFeGNlbCkgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCflj6rog73kuIrkvKBXb3Jk5paH5Lu277yBJyk7CiAgICAgIH0KICAgICAgY29uc3QgaXNMdDJNID0gZmlsZS5zaXplIC8gMTAyNCAvIDEwMjQgPCAyOwogICAgICBpZiAoIWlzTHQyTSkgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfkuIrkvKDmlofku7blpKflsI/kuI3og73otoXov4cgMk1CIScpOwogICAgICB9CiAgICAgIHJldHVybiBpc0V4Y2VsICYmIGlzTHQyTTsKICAgIH0sCiAgICBoYW5kbGVEb3dubG9hZFRlbXBsYXRlKCkgewogICAgICB0aGlzLiRtb2RhbC5sb2FkaW5nKCLmraPlnKjkuIvovb3mqKHmnb/vvIzor7fnqI3lgJkuLi4iKTsKICAgICAgZG93bmxvYWRUZW1wbGF0ZSgpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIGNvbnN0IGJsb2IgPSBuZXcgQmxvYihbcmVzcG9uc2VdLCB7CiAgICAgICAgICB0eXBlOiAnYXBwbGljYXRpb24vdm5kLm9wZW54bWxmb3JtYXRzLW9mZmljZWRvY3VtZW50LndvcmRwcm9jZXNzaW5nbWwuZG9jdW1lbnQnCiAgICAgICAgfSk7CiAgICAgICAgY29uc3QgbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKTsKICAgICAgICBsaW5rLmhyZWYgPSB3aW5kb3cuVVJMLmNyZWF0ZU9iamVjdFVSTChibG9iKTsKICAgICAgICBsaW5rLmRvd25sb2FkID0gJ+S4quS6uue7qeaViOiuoeWIkuaooeadvy5kb2N4JzsKICAgICAgICBsaW5rLmNsaWNrKCk7CiAgICAgICAgd2luZG93LlVSTC5yZXZva2VPYmplY3RVUkwobGluay5ocmVmKTsKICAgICAgICB0aGlzLiRtb2RhbC5jbG9zZUxvYWRpbmcoKTsKICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgIHRoaXMuJG1vZGFsLmNsb3NlTG9hZGluZygpOwogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLkuIvovb3mqKHmnb/lpLHotKUiKTsKICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlVmlld1BlcnNvbmFsSW5kaWNhdG9ycyhyb3cpIHsKICAgICAgLy8g6I635Y+W5Liq5oCn5oyH5qCH5pWw5o2uICjmjIfmoIfnsbvlnosgPSAxKQogICAgICBsaXN0UGVyc29ubmVsVGFzayh7IHBsYW5JZDogcm93LmlkLCBpbmRpY2F0b3JUeXBlOiAxIH0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMucGVyc29uYWxJbmRpY2F0b3JMaXN0ID0gcmVzcG9uc2Uucm93cyB8fCBbXTsKICAgICAgICB0aGlzLnBlcnNvbmFsSW5kaWNhdG9yT3BlbiA9IHRydWU7CiAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6I635Y+W5Liq5oCn5oyH5qCH5pWw5o2u5aSx6LSlIik7CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZVZpZXdDb21tb25JbmRpY2F0b3JzKHJvdykgewogICAgICAvLyDojrflj5blhbHmgKfmjIfmoIfmlbDmja4gKOaMh+agh+exu+WeiyA9IDIpCiAgICAgIGxpc3RQZXJzb25uZWxUYXNrKHsgcGxhbklkOiByb3cuaWQsIGluZGljYXRvclR5cGU6IDIgfSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5jb21tb25JbmRpY2F0b3JMaXN0ID0gcmVzcG9uc2Uucm93cyB8fCBbXTsKICAgICAgICB0aGlzLmNvbW1vbkluZGljYXRvck9wZW4gPSB0cnVlOwogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuiOt+WPluWFseaAp+aMh+agh+aVsOaNruWksei0pSIpOwogICAgICB9KTsKICAgIH0sCgogICAgLy8g5bm05bqm57up5pWI6K6h5YiS55u45YWz5pa55rOVCiAgICBoYW5kbGVUYWJDbGljayh0YWIpIHsKICAgICAgaWYgKHRhYi5uYW1lID09PSAneWVhcmx5JykgewogICAgICAgIHRoaXMuZ2V0WWVhcmx5TGlzdCgpOwogICAgICB9IGVsc2UgaWYgKHRhYi5uYW1lID09PSAncXVhcnRlcmx5JykgewogICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICB9CiAgICB9LAoKICAgIGdldFllYXJseUxpc3QoKSB7CiAgICAgIHRoaXMueWVhcmx5TG9hZGluZyA9IHRydWU7CiAgICAgIGxpc3RQZXJzb25uZWxZZWFybHlQbGFuKHRoaXMueWVhcmx5UXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMueWVhcmx5UGxhbkxpc3QgPSByZXNwb25zZS5yb3dzOwogICAgICAgIHRoaXMueWVhcmx5VG90YWwgPSByZXNwb25zZS50b3RhbDsKICAgICAgICB0aGlzLnllYXJseUxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAoKICAgIGhhbmRsZVllYXJseVF1ZXJ5KCkgewogICAgICB0aGlzLnllYXJseVF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLmdldFllYXJseUxpc3QoKTsKICAgIH0sCgogICAgcmVzZXRZZWFybHlRdWVyeSgpIHsKICAgICAgdGhpcy5yZXNldEZvcm0oInllYXJseVF1ZXJ5Rm9ybSIpOwogICAgICB0aGlzLmhhbmRsZVllYXJseVF1ZXJ5KCk7CiAgICB9LAoKICAgIGhhbmRsZVllYXJseVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy55ZWFybHlJZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5pZCk7CiAgICAgIHRoaXMueWVhcmx5TXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsKICAgIH0sCgogICAgaGFuZGxlWWVhcmx5RGVsZXRlKHJvdykgewogICAgICBjb25zdCBpZHMgPSByb3cgPyBbcm93LmlkXSA6IHRoaXMueWVhcmx5SWRzOwogICAgICBjb25zdCBtZXNzYWdlID0gcm93CiAgICAgICAgPyBg5piv5ZCm56Gu6K6k5Yig6ZmkIiR7cm93Lm5hbWV9IueahOW5tOW6pue7qeaViOiuoeWIkuaVsOaNrumhue+8n2AKICAgICAgICA6IGDmmK/lkKbnoa7orqTliKDpmaTpgInkuK3nmoQke2lkcy5sZW5ndGh95p2h5bm05bqm57up5pWI6K6h5YiS5pWw5o2u6aG577yfYDsKCiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0obWVzc2FnZSkudGhlbihmdW5jdGlvbigpIHsKICAgICAgICByZXR1cm4gZGVsUGVyc29ubmVsWWVhcmx5UGxhbihpZHMuam9pbignLCcpKTsKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy5nZXRZZWFybHlMaXN0KCk7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7CiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsKICAgIH0sCgogICAgYmVmb3JlWWVhcmx5SW1wb3J0VXBsb2FkKGZpbGUpIHsKICAgICAgY29uc3QgaXNFeGNlbCA9IGZpbGUudHlwZSA9PT0gJ2FwcGxpY2F0aW9uL3ZuZC5vcGVueG1sZm9ybWF0cy1vZmZpY2Vkb2N1bWVudC5zcHJlYWRzaGVldG1sLnNoZWV0JyB8fAogICAgICAgICAgICAgICAgICAgICAgZmlsZS50eXBlID09PSAnYXBwbGljYXRpb24vdm5kLm1zLWV4Y2VsJzsKICAgICAgaWYgKCFpc0V4Y2VsKSB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoJ+WPquiDveS4iuS8oEV4Y2Vs5paH5Lu25qC85byPKC54bHN45oiWLnhscykhJyk7CiAgICAgIH0KICAgICAgcmV0dXJuIGlzRXhjZWw7CiAgICB9LAoKICAgIGhhbmRsZVllYXJseUltcG9ydFN1Y2Nlc3MocmVzcG9uc2UpIHsKICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoJ+WvvOWFpeaIkOWKnycpOwogICAgICAgIHRoaXMuZ2V0WWVhcmx5TGlzdCgpOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKHJlc3BvbnNlLm1zZyB8fCAn5a+85YWl5aSx6LSlJyk7CiAgICAgIH0KICAgIH0sCgogICAgaGFuZGxlWWVhcmx5SW1wb3J0RXJyb3IoZXJyb3IpIHsKICAgICAgY29uc29sZS5lcnJvcigi5a+85YWl5aSx6LSlOiIsIGVycm9yKTsKICAgICAgaWYgKGVycm9yLnN0YXR1cyA9PT0gNDAxKSB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuiupOivgeWksei0pe+8jOivt+mHjeaWsOeZu+W9lSIpOwogICAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCdMb2dPdXQnKS50aGVuKCgpID0+IHsKICAgICAgICAgIGxvY2F0aW9uLmhyZWYgPSAnL2xvZ2luJzsKICAgICAgICB9KTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi5a+85YWl5aSx6LSl77yM6K+35qOA5p+l5paH5Lu25qC85byPIik7CiAgICAgIH0KICAgIH0sCgogICAgaGFuZGxlWWVhcmx5RG93bmxvYWRUZW1wbGF0ZSgpIHsKICAgICAgZG93bmxvYWRZZWFybHlFeGNlbFRlbXBsYXRlKCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgY29uc3QgYmxvYiA9IG5ldyBCbG9iKFtyZXNwb25zZV0sIHsKICAgICAgICAgIHR5cGU6ICdhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQuc3ByZWFkc2hlZXRtbC5zaGVldCcKICAgICAgICB9KTsKICAgICAgICBjb25zdCBsaW5rID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnYScpOwogICAgICAgIGxpbmsuaHJlZiA9IHdpbmRvdy5VUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpOwogICAgICAgIGxpbmsuZG93bmxvYWQgPSAn5Liq5Lq65bm05bqm57up5pWI6K6h5YiS5qih5p2/Lnhsc3gnOwogICAgICAgIGxpbmsuY2xpY2soKTsKICAgICAgICB3aW5kb3cuVVJMLnJldm9rZU9iamVjdFVSTChsaW5rLmhyZWYpOwogICAgICB9KTsKICAgIH0sCgogICAgaGFuZGxlVmlld1llYXJseVBlcnNvbmFsSW5kaWNhdG9ycyhyb3cpIHsKICAgICAgZ2V0UGVyc29uYWxJbmRpY2F0b3JzKHJvdy5pZCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5wZXJzb25hbEluZGljYXRvckxpc3QgPSByZXNwb25zZS5kYXRhIHx8IFtdOwogICAgICAgIHRoaXMucGVyc29uYWxJbmRpY2F0b3JPcGVuID0gdHJ1ZTsKICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLojrflj5bkuKrmgKfmjIfmoIfmlbDmja7lpLHotKUiKTsKICAgICAgfSk7CiAgICB9LAoKICAgIGhhbmRsZVZpZXdZZWFybHlDb21tb25JbmRpY2F0b3JzKHJvdykgewogICAgICBnZXRDb21tb25JbmRpY2F0b3JzKHJvdy5pZCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5jb21tb25JbmRpY2F0b3JMaXN0ID0gcmVzcG9uc2UuZGF0YSB8fCBbXTsKICAgICAgICB0aGlzLmNvbW1vbkluZGljYXRvck9wZW4gPSB0cnVlOwogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuiOt+WPluWFseaAp+aMh+agh+aVsOaNruWksei0pSIpOwogICAgICB9KTsKICAgIH0KICB9Cn07Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAq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file": "index.vue", "sourceRoot": "src/views/performance/personnel", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-tabs v-model=\"activeTab\" @tab-click=\"handleTabClick\">\n      <!-- 个人季度绩效计划 Tab -->\n      <el-tab-pane label=\"个人季度绩效计划\" name=\"quarterly\">\n        <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n          <el-form-item label=\"姓名\" prop=\"name\">\n            <el-input\n              v-model=\"queryParams.name\"\n              placeholder=\"请输入姓名\"\n              clearable\n              @keyup.enter.native=\"handleQuery\"\n            />\n          </el-form-item>\n          <el-form-item label=\"科室\" prop=\"department\">\n            <el-input\n              v-model=\"queryParams.department\"\n              placeholder=\"请输入科室\"\n              clearable\n              @keyup.enter.native=\"handleQuery\"\n            />\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n          </el-form-item>\n        </el-form>\n\n        <el-row :gutter=\"10\" class=\"mb8\">\n          <el-col :span=\"1.5\">\n            <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" :disabled=\"multiple\" @click=\"handleExportSelected\">导出Word</el-button>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button type=\"danger\" plain icon=\"el-icon-delete\" size=\"mini\" :disabled=\"multiple\" @click=\"handleDelete\">删除</el-button>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-upload\n              class=\"upload-demo\"\n              :action=\"importUrl\"\n              :headers=\"uploadHeaders\"\n              :on-success=\"handleImportSuccess\"\n              :on-error=\"handleImportError\"\n              :before-upload=\"beforeImportUpload\"\n              :show-file-list=\"false\"\n              style=\"display: inline-block;\">\n              <el-button type=\"info\" plain icon=\"el-icon-upload2\" size=\"mini\">导入Word</el-button>\n            </el-upload>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button type=\"info\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"handleDownloadTemplate\">下载模板</el-button>\n          </el-col>\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n        </el-row>\n\n        <el-card class=\"box-card\" style=\"margin-bottom: 20px;\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span style=\"font-weight: bold;\">个人季度绩效计划列表</span>\n          </div>\n          <el-table\n            v-loading=\"loading\"\n            :data=\"planList\"\n            @selection-change=\"handleSelectionChange\"\n            @current-change=\"handleCurrentChange\"\n            highlight-current-row\n            :height=\"300\">\n            <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n            <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" width=\"100\" />\n            <el-table-column label=\"科室\" align=\"center\" prop=\"department\" width=\"150\" />\n            <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\n              <template slot-scope=\"scope\">\n                <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"操作\" align=\"center\" width=\"200\" class-name=\"small-padding fixed-width\">\n              <template slot-scope=\"scope\">\n                <el-button size=\"mini\" type=\"text\" icon=\"el-icon-s-data\" @click=\"handleViewPersonalIndicators(scope.row)\">个性指标</el-button>\n                <el-button size=\"mini\" type=\"text\" icon=\"el-icon-data-analysis\" @click=\"handleViewCommonIndicators(scope.row)\">共性指标</el-button>\n                <el-button size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleDelete(scope.row)\">删除</el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n          <pagination\n            v-show=\"total>0\"\n            :total=\"total\"\n            :page.sync=\"queryParams.pageNum\"\n            :limit.sync=\"queryParams.pageSize\"\n            @pagination=\"getList\"\n          />\n        </el-card>\n      </el-tab-pane>\n\n      <!-- 个人年度绩效计划 Tab -->\n      <el-tab-pane label=\"个人年度绩效计划\" name=\"yearly\">\n        <el-form :model=\"yearlyQueryParams\" ref=\"yearlyQueryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n          <el-form-item label=\"姓名\" prop=\"name\">\n            <el-input\n              v-model=\"yearlyQueryParams.name\"\n              placeholder=\"请输入姓名\"\n              clearable\n              @keyup.enter.native=\"handleYearlyQuery\"\n            />\n          </el-form-item>\n          <el-form-item label=\"科室\" prop=\"department\">\n            <el-input\n              v-model=\"yearlyQueryParams.department\"\n              placeholder=\"请输入科室\"\n              clearable\n              @keyup.enter.native=\"handleYearlyQuery\"\n            />\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleYearlyQuery\">搜索</el-button>\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetYearlyQuery\">重置</el-button>\n          </el-form-item>\n        </el-form>\n\n        <el-row :gutter=\"10\" class=\"mb8\">\n          <el-col :span=\"1.5\">\n            <el-button type=\"danger\" plain icon=\"el-icon-delete\" size=\"mini\" :disabled=\"yearlyMultiple\" @click=\"handleYearlyDelete\">删除</el-button>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-upload\n              class=\"upload-demo\"\n              :action=\"yearlyImportUrl\"\n              :headers=\"uploadHeaders\"\n              :on-success=\"handleYearlyImportSuccess\"\n              :on-error=\"handleYearlyImportError\"\n              :before-upload=\"beforeYearlyImportUpload\"\n              :show-file-list=\"false\"\n              style=\"display: inline-block;\">\n              <el-button type=\"info\" plain icon=\"el-icon-upload2\" size=\"mini\">导入Excel</el-button>\n            </el-upload>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button type=\"info\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"handleYearlyDownloadTemplate\">下载模板</el-button>\n          </el-col>\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getYearlyList\"></right-toolbar>\n        </el-row>\n\n        <el-card class=\"box-card\" style=\"margin-bottom: 20px;\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span style=\"font-weight: bold;\">个人年度绩效计划列表</span>\n          </div>\n          <el-table\n            v-loading=\"yearlyLoading\"\n            :data=\"yearlyPlanList\"\n            @selection-change=\"handleYearlySelectionChange\"\n            highlight-current-row\n            :height=\"300\">\n            <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n            <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" width=\"100\" />\n            <el-table-column label=\"科室\" align=\"center\" prop=\"department\" width=\"150\" />\n            <el-table-column label=\"年份\" align=\"center\" prop=\"year\" width=\"100\" />\n            <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\n              <template slot-scope=\"scope\">\n                <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"操作\" align=\"center\" width=\"200\" class-name=\"small-padding fixed-width\">\n              <template slot-scope=\"scope\">\n                <el-button size=\"mini\" type=\"text\" icon=\"el-icon-s-data\" @click=\"handleViewYearlyPersonalIndicators(scope.row)\">个性指标</el-button>\n                <el-button size=\"mini\" type=\"text\" icon=\"el-icon-data-analysis\" @click=\"handleViewYearlyCommonIndicators(scope.row)\">共性指标</el-button>\n                <el-button size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleYearlyDelete(scope.row)\">删除</el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n          <pagination\n            v-show=\"yearlyTotal>0\"\n            :total=\"yearlyTotal\"\n            :page.sync=\"yearlyQueryParams.pageNum\"\n            :limit.sync=\"yearlyQueryParams.pageSize\"\n            @pagination=\"getYearlyList\"\n          />\n        </el-card>\n      </el-tab-pane>\n    </el-tabs>\n\n\n\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n        <el-form-item label=\"姓名\" prop=\"name\">\n          <el-input v-model=\"form.name\" placeholder=\"请输入姓名\" />\n        </el-form-item>\n        <el-form-item label=\"科室\" prop=\"department\">\n          <el-input v-model=\"form.department\" placeholder=\"请输入科室\" />\n        </el-form-item>\n        <!-- <el-form-item label=\"状态\" prop=\"status\">\n          <el-radio-group v-model=\"form.status\">\n            <el-radio label=\"DRAFT\">草稿</el-radio>\n            <el-radio label=\"SUBMITTED\">已提交</el-radio>\n            <el-radio label=\"APPROVED\">已审核</el-radio>\n          </el-radio-group>\n        </el-form-item> -->\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <el-dialog :title=\"taskTitle\" :visible.sync=\"taskOpen\" width=\"800px\" append-to-body>\n      <el-form ref=\"taskForm\" :model=\"taskForm\" :rules=\"taskRules\" label-width=\"100px\">\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"任务类型\" prop=\"taskType\">\n              <el-input v-model=\"taskForm.taskType\" placeholder=\"请输入任务类型\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"分值\" prop=\"score\">\n              <el-input-number v-model=\"taskForm.score\" :min=\"0\" :max=\"100\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-form-item label=\"任务内容\" prop=\"taskContent\">\n          <el-input v-model=\"taskForm.taskContent\" type=\"textarea\" placeholder=\"请输入任务内容\" />\n        </el-form-item>\n        <el-form-item label=\"目标及措施\" prop=\"targetMeasures\">\n          <el-input v-model=\"taskForm.targetMeasures\" type=\"textarea\" placeholder=\"请输入目标及措施\" />\n        </el-form-item>\n        <el-form-item label=\"评价标准\" prop=\"evaluationStandard\">\n          <el-input v-model=\"taskForm.evaluationStandard\" type=\"textarea\" placeholder=\"请输入评价标准\" />\n        </el-form-item>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"责任人\" prop=\"responsible\">\n              <el-input v-model=\"taskForm.responsible\" placeholder=\"请输入责任人\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"完成时限\" prop=\"deadline\">\n              <el-date-picker\n                v-model=\"taskForm.deadline\"\n                type=\"date\"\n                placeholder=\"选择完成时限\"\n                format=\"yyyy-MM-dd\"\n                value-format=\"yyyy-MM-dd\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitTaskForm\">确 定</el-button>\n        <el-button @click=\"cancelTask\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 个性指标弹窗 -->\n    <el-dialog title=\"个性指标\" :visible.sync=\"personalIndicatorOpen\" width=\"80%\" append-to-body>\n      <el-table :data=\"personalIndicatorList\" style=\"width: 100%\" border>\n        <el-table-column label=\"序号\" prop=\"seqNo\" width=\"60\" align=\"center\" />\n        <el-table-column label=\"任务类型\" prop=\"taskCategory\" width=\"120\" align=\"center\" />\n        <el-table-column label=\"绩效任务\" prop=\"performanceTask\" min-width=\"200\" show-overflow-tooltip />\n        <el-table-column label=\"目标及措施\" prop=\"targetMeasures\" min-width=\"200\" show-overflow-tooltip />\n        <el-table-column label=\"评价标准\" prop=\"evaluationCriteria\" min-width=\"200\" show-overflow-tooltip />\n        <el-table-column label=\"分值或权重\" prop=\"scoreWeight\" width=\"100\" align=\"center\" />\n        <el-table-column label=\"责任分类\" prop=\"responsibilityCategory\" width=\"120\" align=\"center\" />\n        <el-table-column label=\"完成时限\" prop=\"completionTime\" width=\"120\" align=\"center\" />\n      </el-table>\n    </el-dialog>\n\n    <!-- 共性指标弹窗 -->\n    <el-dialog title=\"共性指标\" :visible.sync=\"commonIndicatorOpen\" width=\"60%\" append-to-body>\n      <el-table :data=\"commonIndicatorList\" style=\"width: 100%\" border>\n        <el-table-column label=\"序号\" prop=\"seqNo\" width=\"60\" align=\"center\" />\n        <el-table-column label=\"责任分类\" prop=\"responsibilityCategory\" min-width=\"250\" show-overflow-tooltip />\n        <el-table-column label=\"评价标准\" prop=\"evaluationStandard\" min-width=\"250\" show-overflow-tooltip />\n        <el-table-column label=\"分值或权重\" prop=\"scoreWeight\" width=\"120\" align=\"center\" />\n        <el-table-column label=\"完成时限\" prop=\"completionTime\" width=\"120\" align=\"center\" />\n      </el-table>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  listPersonnelPlan,\n  getPersonnelPlan,\n  delPersonnelPlan,\n  addPersonnelPlan,\n  updatePersonnelPlan,\n  exportPersonnelPlan,\n  batchExportPersonnelPlan,\n  importPersonnelPlan,\n  downloadTemplate\n} from \"@/api/performance/personnel\";\nimport {\n  listPersonnelTask,\n  getPersonnelTask,\n  delPersonnelTask,\n  addPersonnelTask,\n  updatePersonnelTask\n} from \"@/api/performance/personnelTask\";\nimport {\n  listPersonnelYearlyPlan,\n  getPersonnelYearlyPlan,\n  delPersonnelYearlyPlan,\n  addPersonnelYearlyPlan,\n  updatePersonnelYearlyPlan,\n  getPersonalIndicators,\n  getCommonIndicators,\n  downloadYearlyExcelTemplate\n} from \"@/api/performance/personnelYearly\";\n\nexport default {\n  name: \"PersonnelPlan\",\n  dicts: ['performance_status'],\n  data() {\n    return {\n      // Tab相关\n      activeTab: 'quarterly',\n\n      // 季度绩效计划相关\n      loading: true,\n      ids: [],\n      single: true,\n      multiple: true,\n      showSearch: true,\n      total: 0,\n      planList: [],\n      taskList: [],\n      personalIndicatorList: [],\n      commonIndicatorList: [],\n      currentPlan: null,\n      title: \"\",\n      taskTitle: \"\",\n      open: false,\n      taskOpen: false,\n      personalIndicatorOpen: false,\n      commonIndicatorOpen: false,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        name: null,\n        department: null,\n        status: null\n      },\n      form: {},\n      taskForm: {},\n      rules: {\n        name: [\n          { required: true, message: \"姓名不能为空\", trigger: \"blur\" }\n        ],\n        department: [\n          { required: true, message: \"科室不能为空\", trigger: \"blur\" }\n        ]\n      },\n      taskRules: {\n        taskType: [\n          { required: true, message: \"任务类型不能为空\", trigger: \"blur\" }\n        ],\n        taskContent: [\n          { required: true, message: \"任务内容不能为空\", trigger: \"blur\" }\n        ],\n        score: [\n          { required: true, message: \"分值不能为空\", trigger: \"blur\" }\n        ]\n      },\n      importUrl: process.env.VUE_APP_BASE_API + \"/performance/personnel/import\",\n      uploadHeaders: {},\n\n      // 年度绩效计划相关\n      yearlyLoading: true,\n      yearlyIds: [],\n      yearlyMultiple: true,\n      yearlyTotal: 0,\n      yearlyPlanList: [],\n      yearlyQueryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        name: null,\n        department: null,\n        year: null\n      },\n      yearlyImportUrl: process.env.VUE_APP_BASE_API + \"/performance/personnel/yearly/importExcel\"\n    };\n  },\n  created() {\n    this.getList();\n    // 设置上传认证头\n    this.uploadHeaders = {\n      Authorization: 'Bearer ' + this.$store.getters.token\n    };\n  },\n  methods: {\n    getList() {\n      this.loading = true;\n      listPersonnelPlan(this.queryParams).then(response => {\n        this.planList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    getTaskList(planId) {\n      if (!planId) return;\n      listPersonnelTask({ planId: planId }).then(response => {\n        this.taskList = response.rows || [];\n      });\n    },\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    cancelTask() {\n      this.taskOpen = false;\n      this.resetTask();\n    },\n    reset() {\n      this.form = {\n        id: null,\n        name: null,\n        department: null,\n        status: \"DRAFT\"\n      };\n      this.resetForm(\"form\");\n    },\n    resetTask() {\n      this.taskForm = {\n        id: null,\n        planId: null,\n        taskType: null,\n        taskContent: null,\n        targetMeasures: null,\n        evaluationStandard: null,\n        score: null,\n        responsible: null,\n        deadline: null\n      };\n      this.resetForm(\"taskForm\");\n    },\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    handleCurrentChange(currentRow) {\n      this.currentPlan = currentRow;\n      if (currentRow) {\n        this.getTaskList(currentRow.id);\n      } else {\n        this.taskList = [];\n      }\n    },\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加绩效计划\";\n    },\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids\n      getPersonnelPlan(id).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改绩效计划\";\n      });\n    },\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id != null) {\n            updatePersonnelPlan(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addPersonnelPlan(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    handleDelete(row) {\n      const ids = row.id || this.ids;\n      this.$modal.confirm('是否确认删除绩效计划编号为\"' + ids + '\"的数据项？').then(function() {\n        return delPersonnelPlan(ids);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    handleAddTask() {\n      if (!this.currentPlan) {\n        this.$modal.msgError(\"请先选择一个绩效计划\");\n        return;\n      }\n      this.resetTask();\n      this.taskForm.planId = this.currentPlan.id;\n      this.taskOpen = true;\n      this.taskTitle = \"添加绩效任务\";\n    },\n    handleUpdateTask(row) {\n      this.resetTask();\n      const id = row.id;\n      getPersonnelTask(id).then(response => {\n        this.taskForm = response.data;\n        this.taskOpen = true;\n        this.taskTitle = \"修改绩效任务\";\n      });\n    },\n    submitTaskForm() {\n      this.$refs[\"taskForm\"].validate(valid => {\n        if (valid) {\n          if (this.taskForm.id != null) {\n            updatePersonnelTask(this.taskForm).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.taskOpen = false;\n              this.getTaskList(this.currentPlan.id);\n            });\n          } else {\n            addPersonnelTask(this.taskForm).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.taskOpen = false;\n              this.getTaskList(this.currentPlan.id);\n            });\n          }\n        }\n      });\n    },\n    handleDeleteTask(row) {\n      const id = row.id;\n      this.$modal.confirm('是否确认删除该任务数据项？').then(function() {\n        return delPersonnelTask(id);\n      }).then(() => {\n        this.getTaskList(this.currentPlan.id);\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    handleExportSelected() {\n      if (this.ids.length === 0) {\n        this.$modal.msgWarning(\"请选择要导出的数据\");\n        return;\n      }\n      this.$modal.confirm('是否确认导出选中的' + this.ids.length + '条数据？').then(() => {\n        this.$modal.loading(\"正在导出数据，请稍候...\");\n        batchExportPersonnelPlan(this.ids).then(response => {\n          const blob = new Blob([response], {\n            type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'\n          });\n          const link = document.createElement('a');\n          link.href = window.URL.createObjectURL(blob);\n          link.download = '个人绩效计划_' + new Date().getTime() + '.docx';\n          link.click();\n          window.URL.revokeObjectURL(link.href);\n          this.$modal.closeLoading();\n          this.$modal.msgSuccess(\"导出成功\");\n        }).catch(() => {\n          this.$modal.closeLoading();\n          this.$modal.msgError(\"导出失败\");\n        });\n      }).catch(() => {});\n    },\n    handleImportSuccess(response) {\n      if (response.code === 200) {\n        this.$modal.msgSuccess(\"导入成功\");\n        this.getList();\n      } else {\n        this.$modal.msgError(response.msg || \"导入失败\");\n      }\n    },\n    handleImportError(error) {\n      console.error(\"导入失败:\", error);\n      if (error.status === 401) {\n        this.$modal.msgError(\"认证失败，请重新登录\");\n        this.$store.dispatch('LogOut').then(() => {\n          location.href = '/login';\n        });\n      } else {\n        this.$modal.msgError(\"导入失败，请检查文件格式\");\n      }\n    },\n    beforeImportUpload(file) {\n      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';\n      if (!isExcel) {\n        this.$modal.msgError('只能上传Word文件！');\n      }\n      const isLt2M = file.size / 1024 / 1024 < 2;\n      if (!isLt2M) {\n        this.$modal.msgError('上传文件大小不能超过 2MB!');\n      }\n      return isExcel && isLt2M;\n    },\n    handleDownloadTemplate() {\n      this.$modal.loading(\"正在下载模板，请稍候...\");\n      downloadTemplate().then(response => {\n        const blob = new Blob([response], {\n          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'\n        });\n        const link = document.createElement('a');\n        link.href = window.URL.createObjectURL(blob);\n        link.download = '个人绩效计划模板.docx';\n        link.click();\n        window.URL.revokeObjectURL(link.href);\n        this.$modal.closeLoading();\n      }).catch(() => {\n        this.$modal.closeLoading();\n        this.$modal.msgError(\"下载模板失败\");\n      });\n    },\n    handleViewPersonalIndicators(row) {\n      // 获取个性指标数据 (指标类型 = 1)\n      listPersonnelTask({ planId: row.id, indicatorType: 1 }).then(response => {\n        this.personalIndicatorList = response.rows || [];\n        this.personalIndicatorOpen = true;\n      }).catch(() => {\n        this.$modal.msgError(\"获取个性指标数据失败\");\n      });\n    },\n    handleViewCommonIndicators(row) {\n      // 获取共性指标数据 (指标类型 = 2)\n      listPersonnelTask({ planId: row.id, indicatorType: 2 }).then(response => {\n        this.commonIndicatorList = response.rows || [];\n        this.commonIndicatorOpen = true;\n      }).catch(() => {\n        this.$modal.msgError(\"获取共性指标数据失败\");\n      });\n    },\n\n    // 年度绩效计划相关方法\n    handleTabClick(tab) {\n      if (tab.name === 'yearly') {\n        this.getYearlyList();\n      } else if (tab.name === 'quarterly') {\n        this.getList();\n      }\n    },\n\n    getYearlyList() {\n      this.yearlyLoading = true;\n      listPersonnelYearlyPlan(this.yearlyQueryParams).then(response => {\n        this.yearlyPlanList = response.rows;\n        this.yearlyTotal = response.total;\n        this.yearlyLoading = false;\n      });\n    },\n\n    handleYearlyQuery() {\n      this.yearlyQueryParams.pageNum = 1;\n      this.getYearlyList();\n    },\n\n    resetYearlyQuery() {\n      this.resetForm(\"yearlyQueryForm\");\n      this.handleYearlyQuery();\n    },\n\n    handleYearlySelectionChange(selection) {\n      this.yearlyIds = selection.map(item => item.id);\n      this.yearlyMultiple = !selection.length;\n    },\n\n    handleYearlyDelete(row) {\n      const ids = row ? [row.id] : this.yearlyIds;\n      const message = row\n        ? `是否确认删除\"${row.name}\"的年度绩效计划数据项？`\n        : `是否确认删除选中的${ids.length}条年度绩效计划数据项？`;\n\n      this.$modal.confirm(message).then(function() {\n        return delPersonnelYearlyPlan(ids.join(','));\n      }).then(() => {\n        this.getYearlyList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n\n    beforeYearlyImportUpload(file) {\n      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\n                      file.type === 'application/vnd.ms-excel';\n      if (!isExcel) {\n        this.$modal.msgError('只能上传Excel文件格式(.xlsx或.xls)!');\n      }\n      return isExcel;\n    },\n\n    handleYearlyImportSuccess(response) {\n      if (response.code === 200) {\n        this.$modal.msgSuccess('导入成功');\n        this.getYearlyList();\n      } else {\n        this.$modal.msgError(response.msg || '导入失败');\n      }\n    },\n\n    handleYearlyImportError(error) {\n      console.error(\"导入失败:\", error);\n      if (error.status === 401) {\n        this.$modal.msgError(\"认证失败，请重新登录\");\n        this.$store.dispatch('LogOut').then(() => {\n          location.href = '/login';\n        });\n      } else {\n        this.$modal.msgError(\"导入失败，请检查文件格式\");\n      }\n    },\n\n    handleYearlyDownloadTemplate() {\n      downloadYearlyExcelTemplate().then(response => {\n        const blob = new Blob([response], {\n          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\n        });\n        const link = document.createElement('a');\n        link.href = window.URL.createObjectURL(blob);\n        link.download = '个人年度绩效计划模板.xlsx';\n        link.click();\n        window.URL.revokeObjectURL(link.href);\n      });\n    },\n\n    handleViewYearlyPersonalIndicators(row) {\n      getPersonalIndicators(row.id).then(response => {\n        this.personalIndicatorList = response.data || [];\n        this.personalIndicatorOpen = true;\n      }).catch(() => {\n        this.$modal.msgError(\"获取个性指标数据失败\");\n      });\n    },\n\n    handleViewYearlyCommonIndicators(row) {\n      getCommonIndicators(row.id).then(response => {\n        this.commonIndicatorList = response.data || [];\n        this.commonIndicatorOpen = true;\n      }).catch(() => {\n        this.$modal.msgError(\"获取共性指标数据失败\");\n      });\n    }\n  }\n};\n</script>\n\n<style scoped>\n.box-card {\n  margin-bottom: 20px;\n}\n</style>\n"]}]}