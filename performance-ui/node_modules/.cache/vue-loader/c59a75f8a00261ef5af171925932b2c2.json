{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/DictTag/index.vue?vue&type=style&index=0&id=7e7e1b87&scoped=true&lang=css", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/DictTag/index.vue", "mtime": 1753510684527}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/css-loader/dist/cjs.js", "mtime": 1753510683027}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1753510684373}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/postcss-loader/src/index.js", "mtime": 1753510684004}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5lbC10YWcgKyAuZWwtdGFnIHsKICBtYXJnaW4tbGVmdDogMTBweDsKfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqFA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/DictTag", "sourcesContent": ["<template>\n  <div>\n    <template v-for=\"(item, index) in options\">\n      <template v-if=\"values.includes(item.value)\">\n        <span\n          v-if=\"(item.raw.listClass == 'default' || item.raw.listClass == '') && (item.raw.cssClass == '' || item.raw.cssClass == null)\"\n          :key=\"item.value\"\n          :index=\"index\"\n          :class=\"item.raw.cssClass\"\n          >{{ item.label + ' ' }}</span\n        >\n        <el-tag\n          v-else\n          :disable-transitions=\"true\"\n          :key=\"item.value\"\n          :index=\"index\"\n          :type=\"item.raw.listClass == 'primary' ? '' : item.raw.listClass\"\n          :class=\"item.raw.cssClass\"\n        >\n          {{ item.label + ' ' }}\n        </el-tag>\n      </template>\n    </template>\n    <template v-if=\"unmatch && showValue\">\n      {{ unmatchArray | handleArray }}\n    </template>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"DictTag\",\n  props: {\n    options: {\n      type: Array,\n      default: null,\n    },\n    value: [Number, String, Array],\n    // 当未找到匹配的数据时，显示value\n    showValue: {\n      type: Boolean,\n      default: true,\n    },\n    separator: {\n      type: String,\n      default: \",\"\n    }\n  },\n  data() {\n    return {\n      unmatchArray: [], // 记录未匹配的项\n    }\n  },\n  computed: {\n    values() {\n      if (this.value === null || typeof this.value === 'undefined' || this.value === '') return []\n      return Array.isArray(this.value) ? this.value.map(item => '' + item) : String(this.value).split(this.separator)\n    },\n    unmatch() {\n      this.unmatchArray = []\n      // 没有value不显示\n      if (this.value === null || typeof this.value === 'undefined' || this.value === '' || this.options.length === 0) return false\n      // 传入值为数组\n      let unmatch = false // 添加一个标志来判断是否有未匹配项\n      this.values.forEach(item => {\n        if (!this.options.some(v => v.value === item)) {\n          this.unmatchArray.push(item)\n          unmatch = true // 如果有未匹配项，将标志设置为true\n        }\n      })\n      return unmatch // 返回标志的值\n    },\n\n  },\n  filters: {\n    handleArray(array) {\n      if (array.length === 0) return ''\n      return array.reduce((pre, cur) => {\n        return pre + ' ' + cur\n      })\n    },\n  }\n}\n</script>\n<style scoped>\n.el-tag + .el-tag {\n  margin-left: 10px;\n}\n</style>\n"]}]}