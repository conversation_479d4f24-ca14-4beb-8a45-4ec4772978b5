{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/utils/dynamicTitle.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/utils/dynamicTitle.js", "mtime": 1753510684531}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCIvVXNlcnMvbWF6aWhhby9EZXNrdG9wL2Rldi9wZXJmb3JtYW5jZS9wZXJmb3JtYW5jZS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMudXNlRHluYW1pY1RpdGxlID0gdXNlRHluYW1pY1RpdGxlOwp2YXIgX3N0b3JlID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3N0b3JlIikpOwp2YXIgX3NldHRpbmdzID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3NldHRpbmdzIikpOwovKioKICog5Yqo5oCB5L+u5pS55qCH6aKYCiAqLwpmdW5jdGlvbiB1c2VEeW5hbWljVGl0bGUoKSB7CiAgaWYgKF9zdG9yZS5kZWZhdWx0LnN0YXRlLnNldHRpbmdzLmR5bmFtaWNUaXRsZSkgewogICAgZG9jdW1lbnQudGl0bGUgPSBfc3RvcmUuZGVmYXVsdC5zdGF0ZS5zZXR0aW5ncy50aXRsZSArICcgLSAnICsgX3NldHRpbmdzLmRlZmF1bHQudGl0bGU7CiAgfSBlbHNlIHsKICAgIGRvY3VtZW50LnRpdGxlID0gX3NldHRpbmdzLmRlZmF1bHQudGl0bGU7CiAgfQp9"}, {"version": 3, "names": ["_store", "_interopRequireDefault", "require", "_settings", "useDynamicTitle", "store", "state", "settings", "dynamicTitle", "document", "title", "defaultSettings"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/utils/dynamicTitle.js"], "sourcesContent": ["import store from '@/store'\nimport defaultSettings from '@/settings'\n\n/**\n * 动态修改标题\n */\nexport function useDynamicTitle() {\n  if (store.state.settings.dynamicTitle) {\n    document.title = store.state.settings.title + ' - ' + defaultSettings.title\n  } else {\n    document.title = defaultSettings.title\n  }\n}"], "mappings": ";;;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,SAAA,GAAAF,sBAAA,CAAAC,OAAA;AAEA;AACA;AACA;AACO,SAASE,eAAeA,CAAA,EAAG;EAChC,IAAIC,cAAK,CAACC,KAAK,CAACC,QAAQ,CAACC,YAAY,EAAE;IACrCC,QAAQ,CAACC,KAAK,GAAGL,cAAK,CAACC,KAAK,CAACC,QAAQ,CAACG,KAAK,GAAG,KAAK,GAAGC,iBAAe,CAACD,KAAK;EAC7E,CAAC,MAAM;IACLD,QAAQ,CAACC,KAAK,GAAGC,iBAAe,CAACD,KAAK;EACxC;AACF", "ignoreList": []}]}