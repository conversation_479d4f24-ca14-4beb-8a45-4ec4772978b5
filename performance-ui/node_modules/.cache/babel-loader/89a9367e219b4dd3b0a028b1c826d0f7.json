{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/modules/clipboard.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/modules/clipboard.js", "mtime": 1753510684095}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_parchment", "require", "_quill<PERSON><PERSON><PERSON>", "_interopRequireDefault", "_block", "_logger", "_module", "_quill", "_align", "_background", "_code", "_color", "_direction", "_font", "_size", "_keyboard", "_index", "debug", "logger", "CLIPBOARD_CONFIG", "Node", "TEXT_NODE", "matchText", "matchNewline", "matchBreak", "ELEMENT_NODE", "matchBlot", "matchAttributor", "matchStyles", "matchIndent", "matchList", "matchCodeBlock", "matchTable", "createMatchAlias", "matchIgnore", "ATTRIBUTE_ATTRIBUTORS", "AlignAttribute", "DirectionAttribute", "reduce", "memo", "attr", "keyName", "STYLE_ATTRIBUTORS", "AlignStyle", "BackgroundStyle", "ColorStyle", "DirectionStyle", "FontStyle", "SizeStyle", "Clipboard", "exports", "default", "_Module", "quill", "options", "_this$options$matcher", "_this", "_classCallCheck2", "_callSuper2", "root", "addEventListener", "e", "onCaptureCopy", "onCapturePaste", "bind", "matchers", "concat", "for<PERSON>ach", "_ref", "_ref6", "_slicedToArray2", "selector", "matcher", "addMatcher", "_inherits2", "_createClass2", "key", "value", "push", "convert", "_ref2", "html", "text", "formats", "arguments", "length", "undefined", "CodeBlock", "blotName", "Delta", "insert", "_defineProperty2", "delta", "convertHTML", "deltaEndsWith", "ops", "attributes", "table", "compose", "retain", "delete", "normalizeHTML", "doc", "normalizeExternalHTML", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "container", "body", "nodeMatches", "WeakMap", "_this$prepareMatching", "prepareMatching", "_this$prepareMatching2", "elementMatchers", "textMatchers", "traverse", "scroll", "dangerouslyPasteHTML", "index", "source", "<PERSON><PERSON><PERSON>", "sources", "API", "setContents", "setSelection", "SILENT", "paste", "updateContents", "_e$clipboardData", "_e$clipboardData2", "isCut", "defaultPrevented", "preventDefault", "_this$quill$selection", "selection", "getRange", "_this$quill$selection2", "range", "_this$onCopy", "onCopy", "clipboardData", "setData", "deleteRange", "normalizeURIList", "urlList", "split", "filter", "url", "join", "_e$clipboardData3", "_e$clipboardData4", "_e$clipboardData6", "isEnabled", "getSelection", "getData", "_e$clipboardData5", "files", "Array", "from", "uploader", "upload", "_doc$body$firstElemen", "childElementCount", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "tagName", "onPaste", "getText", "getSemanticHTML", "_ref3", "getFormat", "pastedDelta", "log", "USER", "scrollSelectionIntoView", "pair", "_pair", "querySelectorAll", "node", "has", "matches", "get", "set", "<PERSON><PERSON><PERSON>", "applyFormat", "format", "query", "newDelta", "op", "_objectSpread2", "endText", "i", "slice", "isLine", "Element", "match", "prototype", "EmbedBlot", "includes", "toLowerCase", "isBetweenInlineElements", "previousElementSibling", "nextElement<PERSON><PERSON>ling", "preNodes", "isPre", "parentNode", "nodeType", "childNodes", "childNode", "<PERSON><PERSON><PERSON><PERSON>", "reducedDelta", "_node", "Attributor", "keys", "classes", "ClassAttributor", "styles", "StyleAttributor", "name", "<PERSON><PERSON>", "ATTRIBUTE", "attrName", "Object", "entries", "_ref4", "_ref8", "embed", "BlockBlot", "language", "indent", "parent", "composed", "element", "list", "checkedAttr", "getAttribute", "HTMLParagraphElement", "nextS<PERSON>ling", "BlockEmbed", "<PERSON><PERSON><PERSON><PERSON>", "_style$fontWeight", "style", "fontStyle", "italic", "textDecoration", "underline", "strike", "fontWeight", "startsWith", "parseInt", "bold", "_ref5", "_ref9", "parseFloat", "textIndent", "_node$parentElement", "_node$parentElement2", "parentElement", "rows", "row", "indexOf", "_node$parentElement3", "data", "trim", "replacer", "collapse", "replaced", "replace", "previousSibling"], "sources": ["../../src/modules/clipboard.ts"], "sourcesContent": ["import type { ScrollBlot } from 'parchment';\nimport {\n  Attributor,\n  BlockBlot,\n  ClassAttributor,\n  EmbedB<PERSON>,\n  Scope,\n  StyleAttributor,\n} from 'parchment';\nimport Delta from 'quill-delta';\nimport { BlockEmbed } from '../blots/block.js';\nimport type { EmitterSource } from '../core/emitter.js';\nimport logger from '../core/logger.js';\nimport Module from '../core/module.js';\nimport Quill from '../core/quill.js';\nimport type { Range } from '../core/selection.js';\nimport { AlignAttribute, AlignStyle } from '../formats/align.js';\nimport { BackgroundStyle } from '../formats/background.js';\nimport CodeBlock from '../formats/code.js';\nimport { ColorStyle } from '../formats/color.js';\nimport { DirectionAttribute, DirectionStyle } from '../formats/direction.js';\nimport { FontStyle } from '../formats/font.js';\nimport { SizeStyle } from '../formats/size.js';\nimport { deleteRange } from './keyboard.js';\nimport normalizeExternalHTML from './normalizeExternalHTML/index.js';\n\nconst debug = logger('quill:clipboard');\n\ntype Selector = string | Node['TEXT_NODE'] | Node['ELEMENT_NODE'];\ntype Matcher = (node: Node, delta: Delta, scroll: ScrollBlot) => Delta;\n\nconst CLIPBOARD_CONFIG: [Selector, Matcher][] = [\n  [Node.TEXT_NODE, matchText],\n  [Node.TEXT_NODE, matchNewline],\n  ['br', matchBreak],\n  [Node.ELEMENT_NODE, matchNewline],\n  [Node.ELEMENT_NODE, matchBlot],\n  [Node.ELEMENT_NODE, matchAttributor],\n  [Node.ELEMENT_NODE, matchStyles],\n  ['li', matchIndent],\n  ['ol, ul', matchList],\n  ['pre', matchCodeBlock],\n  ['tr', matchTable],\n  ['b', createMatchAlias('bold')],\n  ['i', createMatchAlias('italic')],\n  ['strike', createMatchAlias('strike')],\n  ['style', matchIgnore],\n];\n\nconst ATTRIBUTE_ATTRIBUTORS = [AlignAttribute, DirectionAttribute].reduce(\n  (memo: Record<string, Attributor>, attr) => {\n    memo[attr.keyName] = attr;\n    return memo;\n  },\n  {},\n);\n\nconst STYLE_ATTRIBUTORS = [\n  AlignStyle,\n  BackgroundStyle,\n  ColorStyle,\n  DirectionStyle,\n  FontStyle,\n  SizeStyle,\n].reduce((memo: Record<string, Attributor>, attr) => {\n  memo[attr.keyName] = attr;\n  return memo;\n}, {});\n\ninterface ClipboardOptions {\n  matchers: [Selector, Matcher][];\n}\n\nclass Clipboard extends Module<ClipboardOptions> {\n  static DEFAULTS: ClipboardOptions = {\n    matchers: [],\n  };\n\n  matchers: [Selector, Matcher][];\n\n  constructor(quill: Quill, options: Partial<ClipboardOptions>) {\n    super(quill, options);\n    this.quill.root.addEventListener('copy', (e) =>\n      this.onCaptureCopy(e, false),\n    );\n    this.quill.root.addEventListener('cut', (e) => this.onCaptureCopy(e, true));\n    this.quill.root.addEventListener('paste', this.onCapturePaste.bind(this));\n    this.matchers = [];\n    CLIPBOARD_CONFIG.concat(this.options.matchers ?? []).forEach(\n      ([selector, matcher]) => {\n        this.addMatcher(selector, matcher);\n      },\n    );\n  }\n\n  addMatcher(selector: Selector, matcher: Matcher) {\n    this.matchers.push([selector, matcher]);\n  }\n\n  convert(\n    { html, text }: { html?: string; text?: string },\n    formats: Record<string, unknown> = {},\n  ) {\n    if (formats[CodeBlock.blotName]) {\n      return new Delta().insert(text || '', {\n        [CodeBlock.blotName]: formats[CodeBlock.blotName],\n      });\n    }\n    if (!html) {\n      return new Delta().insert(text || '', formats);\n    }\n    const delta = this.convertHTML(html);\n    // Remove trailing newline\n    if (\n      deltaEndsWith(delta, '\\n') &&\n      (delta.ops[delta.ops.length - 1].attributes == null || formats.table)\n    ) {\n      return delta.compose(new Delta().retain(delta.length() - 1).delete(1));\n    }\n    return delta;\n  }\n\n  protected normalizeHTML(doc: Document) {\n    normalizeExternalHTML(doc);\n  }\n\n  protected convertHTML(html: string) {\n    const doc = new DOMParser().parseFromString(html, 'text/html');\n    this.normalizeHTML(doc);\n    const container = doc.body;\n    const nodeMatches = new WeakMap();\n    const [elementMatchers, textMatchers] = this.prepareMatching(\n      container,\n      nodeMatches,\n    );\n    return traverse(\n      this.quill.scroll,\n      container,\n      elementMatchers,\n      textMatchers,\n      nodeMatches,\n    );\n  }\n\n  dangerouslyPasteHTML(html: string, source?: EmitterSource): void;\n  dangerouslyPasteHTML(\n    index: number,\n    html: string,\n    source?: EmitterSource,\n  ): void;\n  dangerouslyPasteHTML(\n    index: number | string,\n    html?: string,\n    source: EmitterSource = Quill.sources.API,\n  ) {\n    if (typeof index === 'string') {\n      const delta = this.convert({ html: index, text: '' });\n      // @ts-expect-error\n      this.quill.setContents(delta, html);\n      this.quill.setSelection(0, Quill.sources.SILENT);\n    } else {\n      const paste = this.convert({ html, text: '' });\n      this.quill.updateContents(\n        new Delta().retain(index).concat(paste),\n        source,\n      );\n      this.quill.setSelection(index + paste.length(), Quill.sources.SILENT);\n    }\n  }\n\n  onCaptureCopy(e: ClipboardEvent, isCut = false) {\n    if (e.defaultPrevented) return;\n    e.preventDefault();\n    const [range] = this.quill.selection.getRange();\n    if (range == null) return;\n    const { html, text } = this.onCopy(range, isCut);\n    e.clipboardData?.setData('text/plain', text);\n    e.clipboardData?.setData('text/html', html);\n    if (isCut) {\n      deleteRange({ range, quill: this.quill });\n    }\n  }\n\n  /*\n   * https://www.iana.org/assignments/media-types/text/uri-list\n   */\n  private normalizeURIList(urlList: string) {\n    return (\n      urlList\n        .split(/\\r?\\n/)\n        // Ignore all comments\n        .filter((url) => url[0] !== '#')\n        .join('\\n')\n    );\n  }\n\n  onCapturePaste(e: ClipboardEvent) {\n    if (e.defaultPrevented || !this.quill.isEnabled()) return;\n    e.preventDefault();\n    const range = this.quill.getSelection(true);\n    if (range == null) return;\n    const html = e.clipboardData?.getData('text/html');\n    let text = e.clipboardData?.getData('text/plain');\n    if (!html && !text) {\n      const urlList = e.clipboardData?.getData('text/uri-list');\n      if (urlList) {\n        text = this.normalizeURIList(urlList);\n      }\n    }\n    const files = Array.from(e.clipboardData?.files || []);\n    if (!html && files.length > 0) {\n      this.quill.uploader.upload(range, files);\n      return;\n    }\n    if (html && files.length > 0) {\n      const doc = new DOMParser().parseFromString(html, 'text/html');\n      if (\n        doc.body.childElementCount === 1 &&\n        doc.body.firstElementChild?.tagName === 'IMG'\n      ) {\n        this.quill.uploader.upload(range, files);\n        return;\n      }\n    }\n    this.onPaste(range, { html, text });\n  }\n\n  onCopy(range: Range, isCut: boolean): { html: string; text: string };\n  onCopy(range: Range) {\n    const text = this.quill.getText(range);\n    const html = this.quill.getSemanticHTML(range);\n    return { html, text };\n  }\n\n  onPaste(range: Range, { text, html }: { text?: string; html?: string }) {\n    const formats = this.quill.getFormat(range.index);\n    const pastedDelta = this.convert({ text, html }, formats);\n    debug.log('onPaste', pastedDelta, { text, html });\n    const delta = new Delta()\n      .retain(range.index)\n      .delete(range.length)\n      .concat(pastedDelta);\n    this.quill.updateContents(delta, Quill.sources.USER);\n    // range.length contributes to delta.length()\n    this.quill.setSelection(\n      delta.length() - range.length,\n      Quill.sources.SILENT,\n    );\n    this.quill.scrollSelectionIntoView();\n  }\n\n  prepareMatching(container: Element, nodeMatches: WeakMap<Node, Matcher[]>) {\n    const elementMatchers: Matcher[] = [];\n    const textMatchers: Matcher[] = [];\n    this.matchers.forEach((pair) => {\n      const [selector, matcher] = pair;\n      switch (selector) {\n        case Node.TEXT_NODE:\n          textMatchers.push(matcher);\n          break;\n        case Node.ELEMENT_NODE:\n          elementMatchers.push(matcher);\n          break;\n        default:\n          Array.from(container.querySelectorAll(selector)).forEach((node) => {\n            if (nodeMatches.has(node)) {\n              const matches = nodeMatches.get(node);\n              matches?.push(matcher);\n            } else {\n              nodeMatches.set(node, [matcher]);\n            }\n          });\n          break;\n      }\n    });\n    return [elementMatchers, textMatchers];\n  }\n}\n\nfunction applyFormat(\n  delta: Delta,\n  format: string,\n  value: unknown,\n  scroll: ScrollBlot,\n): Delta {\n  if (!scroll.query(format)) {\n    return delta;\n  }\n\n  return delta.reduce((newDelta, op) => {\n    if (!op.insert) return newDelta;\n    if (op.attributes && op.attributes[format]) {\n      return newDelta.push(op);\n    }\n    const formats = value ? { [format]: value } : {};\n    return newDelta.insert(op.insert, { ...formats, ...op.attributes });\n  }, new Delta());\n}\n\nfunction deltaEndsWith(delta: Delta, text: string) {\n  let endText = '';\n  for (\n    let i = delta.ops.length - 1;\n    i >= 0 && endText.length < text.length;\n    --i // eslint-disable-line no-plusplus\n  ) {\n    const op = delta.ops[i];\n    if (typeof op.insert !== 'string') break;\n    endText = op.insert + endText;\n  }\n  return endText.slice(-1 * text.length) === text;\n}\n\nfunction isLine(node: Node, scroll: ScrollBlot) {\n  if (!(node instanceof Element)) return false;\n  const match = scroll.query(node);\n  // @ts-expect-error\n  if (match && match.prototype instanceof EmbedBlot) return false;\n\n  return [\n    'address',\n    'article',\n    'blockquote',\n    'canvas',\n    'dd',\n    'div',\n    'dl',\n    'dt',\n    'fieldset',\n    'figcaption',\n    'figure',\n    'footer',\n    'form',\n    'h1',\n    'h2',\n    'h3',\n    'h4',\n    'h5',\n    'h6',\n    'header',\n    'iframe',\n    'li',\n    'main',\n    'nav',\n    'ol',\n    'output',\n    'p',\n    'pre',\n    'section',\n    'table',\n    'td',\n    'tr',\n    'ul',\n    'video',\n  ].includes(node.tagName.toLowerCase());\n}\n\nfunction isBetweenInlineElements(node: HTMLElement, scroll: ScrollBlot) {\n  return (\n    node.previousElementSibling &&\n    node.nextElementSibling &&\n    !isLine(node.previousElementSibling, scroll) &&\n    !isLine(node.nextElementSibling, scroll)\n  );\n}\n\nconst preNodes = new WeakMap();\nfunction isPre(node: Node | null) {\n  if (node == null) return false;\n  if (!preNodes.has(node)) {\n    // @ts-expect-error\n    if (node.tagName === 'PRE') {\n      preNodes.set(node, true);\n    } else {\n      preNodes.set(node, isPre(node.parentNode));\n    }\n  }\n  return preNodes.get(node);\n}\n\nfunction traverse(\n  scroll: ScrollBlot,\n  node: ChildNode,\n  elementMatchers: Matcher[],\n  textMatchers: Matcher[],\n  nodeMatches: WeakMap<Node, Matcher[]>,\n): Delta {\n  // Post-order\n  if (node.nodeType === node.TEXT_NODE) {\n    return textMatchers.reduce((delta: Delta, matcher) => {\n      return matcher(node, delta, scroll);\n    }, new Delta());\n  }\n  if (node.nodeType === node.ELEMENT_NODE) {\n    return Array.from(node.childNodes || []).reduce((delta, childNode) => {\n      let childrenDelta = traverse(\n        scroll,\n        childNode,\n        elementMatchers,\n        textMatchers,\n        nodeMatches,\n      );\n      if (childNode.nodeType === node.ELEMENT_NODE) {\n        childrenDelta = elementMatchers.reduce((reducedDelta, matcher) => {\n          return matcher(childNode as HTMLElement, reducedDelta, scroll);\n        }, childrenDelta);\n        childrenDelta = (nodeMatches.get(childNode) || []).reduce(\n          (reducedDelta, matcher) => {\n            return matcher(childNode, reducedDelta, scroll);\n          },\n          childrenDelta,\n        );\n      }\n      return delta.concat(childrenDelta);\n    }, new Delta());\n  }\n  return new Delta();\n}\n\nfunction createMatchAlias(format: string) {\n  return (_node: Element, delta: Delta, scroll: ScrollBlot) => {\n    return applyFormat(delta, format, true, scroll);\n  };\n}\n\nfunction matchAttributor(node: HTMLElement, delta: Delta, scroll: ScrollBlot) {\n  const attributes = Attributor.keys(node);\n  const classes = ClassAttributor.keys(node);\n  const styles = StyleAttributor.keys(node);\n  const formats: Record<string, string | undefined> = {};\n  attributes\n    .concat(classes)\n    .concat(styles)\n    .forEach((name) => {\n      let attr = scroll.query(name, Scope.ATTRIBUTE) as Attributor;\n      if (attr != null) {\n        formats[attr.attrName] = attr.value(node);\n        if (formats[attr.attrName]) return;\n      }\n      attr = ATTRIBUTE_ATTRIBUTORS[name];\n      if (attr != null && (attr.attrName === name || attr.keyName === name)) {\n        formats[attr.attrName] = attr.value(node) || undefined;\n      }\n      attr = STYLE_ATTRIBUTORS[name];\n      if (attr != null && (attr.attrName === name || attr.keyName === name)) {\n        attr = STYLE_ATTRIBUTORS[name];\n        formats[attr.attrName] = attr.value(node) || undefined;\n      }\n    });\n\n  return Object.entries(formats).reduce(\n    (newDelta, [name, value]) => applyFormat(newDelta, name, value, scroll),\n    delta,\n  );\n}\n\nfunction matchBlot(node: Node, delta: Delta, scroll: ScrollBlot) {\n  const match = scroll.query(node);\n  if (match == null) return delta;\n  // @ts-expect-error\n  if (match.prototype instanceof EmbedBlot) {\n    const embed = {};\n    // @ts-expect-error\n    const value = match.value(node);\n    if (value != null) {\n      // @ts-expect-error\n      embed[match.blotName] = value;\n      // @ts-expect-error\n      return new Delta().insert(embed, match.formats(node, scroll));\n    }\n  } else {\n    // @ts-expect-error\n    if (match.prototype instanceof BlockBlot && !deltaEndsWith(delta, '\\n')) {\n      delta.insert('\\n');\n    }\n    if (\n      'blotName' in match &&\n      'formats' in match &&\n      typeof match.formats === 'function'\n    ) {\n      return applyFormat(\n        delta,\n        match.blotName,\n        match.formats(node, scroll),\n        scroll,\n      );\n    }\n  }\n  return delta;\n}\n\nfunction matchBreak(node: Node, delta: Delta) {\n  if (!deltaEndsWith(delta, '\\n')) {\n    delta.insert('\\n');\n  }\n  return delta;\n}\n\nfunction matchCodeBlock(node: Node, delta: Delta, scroll: ScrollBlot) {\n  const match = scroll.query('code-block');\n  const language =\n    match && 'formats' in match && typeof match.formats === 'function'\n      ? match.formats(node, scroll)\n      : true;\n  return applyFormat(delta, 'code-block', language, scroll);\n}\n\nfunction matchIgnore() {\n  return new Delta();\n}\n\nfunction matchIndent(node: Node, delta: Delta, scroll: ScrollBlot) {\n  const match = scroll.query(node);\n  if (\n    match == null ||\n    // @ts-expect-error\n    match.blotName !== 'list' ||\n    !deltaEndsWith(delta, '\\n')\n  ) {\n    return delta;\n  }\n  let indent = -1;\n  let parent = node.parentNode;\n  while (parent != null) {\n    // @ts-expect-error\n    if (['OL', 'UL'].includes(parent.tagName)) {\n      indent += 1;\n    }\n    parent = parent.parentNode;\n  }\n  if (indent <= 0) return delta;\n  return delta.reduce((composed, op) => {\n    if (!op.insert) return composed;\n    if (op.attributes && typeof op.attributes.indent === 'number') {\n      return composed.push(op);\n    }\n    return composed.insert(op.insert, { indent, ...(op.attributes || {}) });\n  }, new Delta());\n}\n\nfunction matchList(node: Node, delta: Delta, scroll: ScrollBlot) {\n  const element = node as Element;\n  let list = element.tagName === 'OL' ? 'ordered' : 'bullet';\n\n  const checkedAttr = element.getAttribute('data-checked');\n  if (checkedAttr) {\n    list = checkedAttr === 'true' ? 'checked' : 'unchecked';\n  }\n\n  return applyFormat(delta, 'list', list, scroll);\n}\n\nfunction matchNewline(node: Node, delta: Delta, scroll: ScrollBlot) {\n  if (!deltaEndsWith(delta, '\\n')) {\n    if (\n      isLine(node, scroll) &&\n      (node.childNodes.length > 0 || node instanceof HTMLParagraphElement)\n    ) {\n      return delta.insert('\\n');\n    }\n    if (delta.length() > 0 && node.nextSibling) {\n      let nextSibling: Node | null = node.nextSibling;\n      while (nextSibling != null) {\n        if (isLine(nextSibling, scroll)) {\n          return delta.insert('\\n');\n        }\n        const match = scroll.query(nextSibling);\n        // @ts-expect-error\n        if (match && match.prototype instanceof BlockEmbed) {\n          return delta.insert('\\n');\n        }\n        nextSibling = nextSibling.firstChild;\n      }\n    }\n  }\n  return delta;\n}\n\nfunction matchStyles(node: HTMLElement, delta: Delta, scroll: ScrollBlot) {\n  const formats: Record<string, unknown> = {};\n  const style: Partial<CSSStyleDeclaration> = node.style || {};\n  if (style.fontStyle === 'italic') {\n    formats.italic = true;\n  }\n  if (style.textDecoration === 'underline') {\n    formats.underline = true;\n  }\n  if (style.textDecoration === 'line-through') {\n    formats.strike = true;\n  }\n  if (\n    style.fontWeight?.startsWith('bold') ||\n    // @ts-expect-error Fix me later\n    parseInt(style.fontWeight, 10) >= 700\n  ) {\n    formats.bold = true;\n  }\n  delta = Object.entries(formats).reduce(\n    (newDelta, [name, value]) => applyFormat(newDelta, name, value, scroll),\n    delta,\n  );\n  // @ts-expect-error\n  if (parseFloat(style.textIndent || 0) > 0) {\n    // Could be 0.5in\n    return new Delta().insert('\\t').concat(delta);\n  }\n  return delta;\n}\n\nfunction matchTable(\n  node: HTMLTableRowElement,\n  delta: Delta,\n  scroll: ScrollBlot,\n) {\n  const table =\n    node.parentElement?.tagName === 'TABLE'\n      ? node.parentElement\n      : node.parentElement?.parentElement;\n  if (table != null) {\n    const rows = Array.from(table.querySelectorAll('tr'));\n    const row = rows.indexOf(node) + 1;\n    return applyFormat(delta, 'table', row, scroll);\n  }\n  return delta;\n}\n\nfunction matchText(node: HTMLElement, delta: Delta, scroll: ScrollBlot) {\n  // @ts-expect-error\n  let text = node.data;\n  // Word represents empty line with <o:p>&nbsp;</o:p>\n  if (node.parentElement?.tagName === 'O:P') {\n    return delta.insert(text.trim());\n  }\n  if (!isPre(node)) {\n    if (\n      text.trim().length === 0 &&\n      text.includes('\\n') &&\n      !isBetweenInlineElements(node, scroll)\n    ) {\n      return delta;\n    }\n    const replacer = (collapse: unknown, match: string) => {\n      const replaced = match.replace(/[^\\u00a0]/g, ''); // \\u00a0 is nbsp;\n      return replaced.length < 1 && collapse ? ' ' : replaced;\n    };\n    text = text.replace(/\\r\\n/g, ' ').replace(/\\n/g, ' ');\n    text = text.replace(/\\s\\s+/g, replacer.bind(replacer, true)); // collapse whitespace\n    if (\n      (node.previousSibling == null &&\n        node.parentElement != null &&\n        isLine(node.parentElement, scroll)) ||\n      (node.previousSibling instanceof Element &&\n        isLine(node.previousSibling, scroll))\n    ) {\n      text = text.replace(/^\\s+/, replacer.bind(replacer, false));\n    }\n    if (\n      (node.nextSibling == null &&\n        node.parentElement != null &&\n        isLine(node.parentElement, scroll)) ||\n      (node.nextSibling instanceof Element && isLine(node.nextSibling, scroll))\n    ) {\n      text = text.replace(/\\s+$/, replacer.bind(replacer, false));\n    }\n  }\n  return delta.insert(text);\n}\n\nexport {\n  Clipboard as default,\n  matchAttributor,\n  matchBlot,\n  matchNewline,\n  matchText,\n  traverse,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAAA,UAAA,GAAAC,OAAA;AAQA,IAAAC,WAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AAEA,IAAAI,OAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,OAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,MAAA,GAAAJ,sBAAA,CAAAF,OAAA;AAEA,IAAAO,MAAA,GAAAP,OAAA;AACA,IAAAQ,WAAA,GAAAR,OAAA;AACA,IAAAS,KAAA,GAAAP,sBAAA,CAAAF,OAAA;AACA,IAAAU,MAAA,GAAAV,OAAA;AACA,IAAAW,UAAA,GAAAX,OAAA;AACA,IAAAY,KAAA,GAAAZ,OAAA;AACA,IAAAa,KAAA,GAAAb,OAAA;AACA,IAAAc,SAAA,GAAAd,OAAA;AACA,IAAAe,MAAA,GAAAb,sBAAA,CAAAF,OAAA;AAEA,IAAMgB,KAAK,GAAG,IAAAC,eAAM,EAAC,iBAAiB,CAAC;AAKvC,IAAMC,gBAAuC,GAAG,CAC9C,CAACC,IAAI,CAACC,SAAS,EAAEC,SAAS,CAAC,EAC3B,CAACF,IAAI,CAACC,SAAS,EAAEE,YAAY,CAAC,EAC9B,CAAC,IAAI,EAAEC,UAAU,CAAC,EAClB,CAACJ,IAAI,CAACK,YAAY,EAAEF,YAAY,CAAC,EACjC,CAACH,IAAI,CAACK,YAAY,EAAEC,SAAS,CAAC,EAC9B,CAACN,IAAI,CAACK,YAAY,EAAEE,eAAe,CAAC,EACpC,CAACP,IAAI,CAACK,YAAY,EAAEG,WAAW,CAAC,EAChC,CAAC,IAAI,EAAEC,WAAW,CAAC,EACnB,CAAC,QAAQ,EAAEC,SAAS,CAAC,EACrB,CAAC,KAAK,EAAEC,cAAc,CAAC,EACvB,CAAC,IAAI,EAAEC,UAAU,CAAC,EAClB,CAAC,GAAG,EAAEC,gBAAgB,CAAC,MAAM,CAAC,CAAC,EAC/B,CAAC,GAAG,EAAEA,gBAAgB,CAAC,QAAQ,CAAC,CAAC,EACjC,CAAC,QAAQ,EAAEA,gBAAgB,CAAC,QAAQ,CAAC,CAAC,EACtC,CAAC,OAAO,EAAEC,WAAW,CAAC,CACvB;AAED,IAAMC,qBAAqB,GAAG,CAACC,qBAAc,EAAEC,6BAAkB,CAAC,CAACC,MAAM,CACvE,UAACC,IAAgC,EAAEC,IAAI,EAAK;EAC1CD,IAAI,CAACC,IAAI,CAACC,OAAO,CAAC,GAAGD,IAAI;EACzB,OAAOD,IAAI;AACb,CAAC,EACD,CAAC,CACH,CAAC;AAED,IAAMG,iBAAiB,GAAG,CACxBC,iBAAU,EACVC,2BAAe,EACfC,iBAAU,EACVC,yBAAc,EACdC,eAAS,EACTC,eAAS,CACV,CAACV,MAAM,CAAC,UAACC,IAAgC,EAAEC,IAAI,EAAK;EACnDD,IAAI,CAACC,IAAI,CAACC,OAAO,CAAC,GAAGD,IAAI;EACzB,OAAOD,IAAI;AACb,CAAC,EAAE,CAAC,CAAC,CAAC;AAAA,IAMAU,SAAS,GAAAC,OAAA,CAAAC,OAAA,0BAAAC,OAAA;EAOb,SAAAH,UAAYI,KAAY,EAAEC,OAAkC,EAAE;IAAA,IAAAC,qBAAA;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAAN,OAAA,QAAAF,SAAA;IAC5DO,KAAA,OAAAE,WAAA,CAAAP,OAAA,QAAAF,SAAA,GAAMI,KAAK,EAAEC,OAAO;IACpBE,KAAA,CAAKH,KAAK,CAACM,IAAI,CAACC,gBAAgB,CAAC,MAAM,EAAG,UAAAC,CAAC;MAAA,OACzCL,KAAA,CAAKM,aAAa,CAACD,CAAC,EAAE,KAAK,CAC7B;IAAA,EAAC;IACDL,KAAA,CAAKH,KAAK,CAACM,IAAI,CAACC,gBAAgB,CAAC,KAAK,EAAG,UAAAC,CAAC;MAAA,OAAKL,KAAA,CAAKM,aAAa,CAACD,CAAC,EAAE,IAAI,CAAC;IAAA,EAAC;IAC3EL,KAAA,CAAKH,KAAK,CAACM,IAAI,CAACC,gBAAgB,CAAC,OAAO,EAAEJ,KAAA,CAAKO,cAAc,CAACC,IAAI,CAAAR,KAAK,CAAC,CAAC;IACzEA,KAAA,CAAKS,QAAQ,GAAG,EAAE;IAClB9C,gBAAgB,CAAC+C,MAAM,EAAAX,qBAAA,GAACC,KAAA,CAAKF,OAAO,CAACW,QAAQ,cAAAV,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC,CAACY,OAAO,CAC1D,UAAAC,IAAA,EAAyB;MAAA,IAAAC,KAAA,OAAAC,eAAA,CAAAnB,OAAA,EAALiB,IAAA;QAAlBG,QAAQ,GAAAF,KAAA;QAAEG,OAAO,GAAAH,KAAA;MACjBb,KAAA,CAAKiB,UAAU,CAACF,QAAQ,EAAEC,OAAO,CAAC;IACpC,CACF,CAAC;IAAA,OAAAhB,KAAA;EACH;EAAA,IAAAkB,UAAA,CAAAvB,OAAA,EAAAF,SAAA,EAAAG,OAAA;EAAA,WAAAuB,aAAA,CAAAxB,OAAA,EAAAF,SAAA;IAAA2B,GAAA;IAAAC,KAAA,EAEA,SAAAJ,UAAUA,CAACF,QAAkB,EAAEC,OAAgB,EAAE;MAC/C,IAAI,CAACP,QAAQ,CAACa,IAAI,CAAC,CAACP,QAAQ,EAAEC,OAAO,CAAC,CAAC;IACzC;EAAA;IAAAI,GAAA;IAAAC,KAAA,EAEA,SAAAE,OAAOA,CAAAC,KAAA,EAGL;MAAA,IAFEC,IAAI,GAA0CD,KAAA,CAA9CC,IAAI;QAAEC,IAAA,GAAwCF,KAAA,CAAxCE,IAAA;MAAwC,IAChDC,OAAgC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAErC,IAAID,OAAO,CAACI,aAAS,CAACC,QAAQ,CAAC,EAAE;QAC/B,OAAO,IAAIC,mBAAK,CAAC,CAAC,CAACC,MAAM,CAACR,IAAI,IAAI,EAAE,MAAAS,gBAAA,CAAAxC,OAAA,MACjCoC,aAAS,CAACC,QAAQ,EAAGL,OAAO,CAACI,aAAS,CAACC,QAAQ,EACjD,CAAC;MACJ;MACA,IAAI,CAACP,IAAI,EAAE;QACT,OAAO,IAAIQ,mBAAK,CAAC,CAAC,CAACC,MAAM,CAACR,IAAI,IAAI,EAAE,EAAEC,OAAO,CAAC;MAChD;MACA,IAAMS,KAAK,GAAG,IAAI,CAACC,WAAW,CAACZ,IAAI,CAAC;MACpC;MACA,IACEa,aAAa,CAACF,KAAK,EAAE,IAAI,CAAC,KACzBA,KAAK,CAACG,GAAG,CAACH,KAAK,CAACG,GAAG,CAACV,MAAM,GAAG,CAAC,CAAC,CAACW,UAAU,IAAI,IAAI,IAAIb,OAAO,CAACc,KAAK,CAAC,EACrE;QACA,OAAOL,KAAK,CAACM,OAAO,CAAC,IAAIT,mBAAK,CAAC,CAAC,CAACU,MAAM,CAACP,KAAK,CAACP,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAACe,MAAM,CAAC,CAAC,CAAC,CAAC;MACxE;MACA,OAAOR,KAAK;IACd;EAAA;IAAAhB,GAAA;IAAAC,KAAA,EAEU,SAAAwB,aAAaA,CAACC,GAAa,EAAE;MACrC,IAAAC,cAAqB,EAACD,GAAG,CAAC;IAC5B;EAAA;IAAA1B,GAAA;IAAAC,KAAA,EAEU,SAAAgB,WAAWA,CAACZ,IAAY,EAAE;MAClC,IAAMqB,GAAG,GAAG,IAAIE,SAAS,CAAC,CAAC,CAACC,eAAe,CAACxB,IAAI,EAAE,WAAW,CAAC;MAC9D,IAAI,CAACoB,aAAa,CAACC,GAAG,CAAC;MACvB,IAAMI,SAAS,GAAGJ,GAAG,CAACK,IAAI;MAC1B,IAAMC,WAAW,GAAG,IAAIC,OAAO,CAAC,CAAC;MACjC,IAAAC,qBAAA,GAAwC,IAAI,CAACC,eAAe,CAC1DL,SAAS,EACTE,WACF,CAAC;QAAAI,sBAAA,OAAA1C,eAAA,CAAAnB,OAAA,EAAA2D,qBAAA;QAHMG,eAAe,GAAAD,sBAAA;QAAEE,YAAY,GAAAF,sBAAA;MAIpC,OAAOG,QAAQ,CACb,IAAI,CAAC9D,KAAK,CAAC+D,MAAM,EACjBV,SAAS,EACTO,eAAe,EACfC,YAAY,EACZN,WACF,CAAC;IACH;EAAA;IAAAhC,GAAA;IAAAC,KAAA,EAQA,SAAAwC,oBAAoBA,CAClBC,KAAsB,EACtBrC,IAAa,EAEb;MAAA,IADAsC,MAAqB,GAAAnC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGoC,cAAK,CAACC,OAAO,CAACC,GAAG;MAEzC,IAAI,OAAOJ,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAM1B,KAAK,GAAG,IAAI,CAACb,OAAO,CAAC;UAAEE,IAAI,EAAEqC,KAAK;UAAEpC,IAAI,EAAE;QAAG,CAAC,CAAC;QACrD;QACA,IAAI,CAAC7B,KAAK,CAACsE,WAAW,CAAC/B,KAAK,EAAEX,IAAI,CAAC;QACnC,IAAI,CAAC5B,KAAK,CAACuE,YAAY,CAAC,CAAC,EAAEJ,cAAK,CAACC,OAAO,CAACI,MAAM,CAAC;MAClD,CAAC,MAAM;QACL,IAAMC,KAAK,GAAG,IAAI,CAAC/C,OAAO,CAAC;UAAEE,IAAI,EAAJA,IAAI;UAAEC,IAAI,EAAE;QAAG,CAAC,CAAC;QAC9C,IAAI,CAAC7B,KAAK,CAAC0E,cAAc,CACvB,IAAItC,mBAAK,CAAC,CAAC,CAACU,MAAM,CAACmB,KAAK,CAAC,CAACpD,MAAM,CAAC4D,KAAK,CAAC,EACvCP,MACF,CAAC;QACD,IAAI,CAAClE,KAAK,CAACuE,YAAY,CAACN,KAAK,GAAGQ,KAAK,CAACzC,MAAM,CAAC,CAAC,EAAEmC,cAAK,CAACC,OAAO,CAACI,MAAM,CAAC;MACvE;IACF;EAAA;IAAAjD,GAAA;IAAAC,KAAA,EAEA,SAAAf,aAAaA,CAACD,CAAiB,EAAiB;MAAA,IAAAmE,gBAAA,EAAAC,iBAAA;MAAA,IAAfC,KAAK,GAAA9C,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;MAC5C,IAAIvB,CAAC,CAACsE,gBAAgB,EAAE;MACxBtE,CAAC,CAACuE,cAAc,CAAC,CAAC;MAClB,IAAAC,qBAAA,GAAgB,IAAI,CAAChF,KAAK,CAACiF,SAAS,CAACC,QAAQ,CAAC,CAAC;QAAAC,sBAAA,OAAAlE,eAAA,CAAAnB,OAAA,EAAAkF,qBAAA;QAAxCI,KAAK,GAAAD,sBAAA;MACZ,IAAIC,KAAK,IAAI,IAAI,EAAE;MACnB,IAAAC,YAAA,GAAuB,IAAI,CAACC,MAAM,CAACF,KAAK,EAAEP,KAAK,CAAC;QAAxCjD,IAAI,GAAAyD,YAAA,CAAJzD,IAAI;QAAEC,IAAA,GAAAwD,YAAA,CAAAxD,IAAA;MACd,CAAA8C,gBAAA,GAAAnE,CAAC,CAAC+E,aAAa,cAAAZ,gBAAA,eAAfA,gBAAA,CAAiBa,OAAO,CAAC,YAAY,EAAE3D,IAAI,CAAC;MAC5C,CAAA+C,iBAAA,GAAApE,CAAC,CAAC+E,aAAa,cAAAX,iBAAA,eAAfA,iBAAA,CAAiBY,OAAO,CAAC,WAAW,EAAE5D,IAAI,CAAC;MAC3C,IAAIiD,KAAK,EAAE;QACT,IAAAY,qBAAW,EAAC;UAAEL,KAAK,EAALA,KAAK;UAAEpF,KAAK,EAAE,IAAI,CAACA;QAAM,CAAC,CAAC;MAC3C;IACF;;IAEA;AACF;AACA;EAFE;IAAAuB,GAAA;IAAAC,KAAA,EAGQ,SAAAkE,gBAAgBA,CAACC,OAAe,EAAE;MACxC,OACEA,OAAO,CACJC,KAAK,CAAC,OAAO;MACd;MAAA,CACCC,MAAM,CAAE,UAAAC,GAAG;QAAA,OAAKA,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG;MAAA,EAAC,CAC/BC,IAAI,CAAC,IAAI,CAAC;IAEjB;EAAA;IAAAxE,GAAA;IAAAC,KAAA,EAEA,SAAAd,cAAcA,CAACF,CAAiB,EAAE;MAAA,IAAAwF,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA;MAChC,IAAI1F,CAAC,CAACsE,gBAAgB,IAAI,CAAC,IAAI,CAAC9E,KAAK,CAACmG,SAAS,CAAC,CAAC,EAAE;MACnD3F,CAAC,CAACuE,cAAc,CAAC,CAAC;MAClB,IAAMK,KAAK,GAAG,IAAI,CAACpF,KAAK,CAACoG,YAAY,CAAC,IAAI,CAAC;MAC3C,IAAIhB,KAAK,IAAI,IAAI,EAAE;MACnB,IAAMxD,IAAI,IAAAoE,iBAAA,GAAGxF,CAAC,CAAC+E,aAAa,cAAAS,iBAAA,uBAAfA,iBAAA,CAAiBK,OAAO,CAAC,WAAW,CAAC;MAClD,IAAIxE,IAAI,IAAAoE,iBAAA,GAAGzF,CAAC,CAAC+E,aAAa,cAAAU,iBAAA,uBAAfA,iBAAA,CAAiBI,OAAO,CAAC,YAAY,CAAC;MACjD,IAAI,CAACzE,IAAI,IAAI,CAACC,IAAI,EAAE;QAAA,IAAAyE,iBAAA;QAClB,IAAMX,OAAO,IAAAW,iBAAA,GAAG9F,CAAC,CAAC+E,aAAa,cAAAe,iBAAA,uBAAfA,iBAAA,CAAiBD,OAAO,CAAC,eAAe,CAAC;QACzD,IAAIV,OAAO,EAAE;UACX9D,IAAI,GAAG,IAAI,CAAC6D,gBAAgB,CAACC,OAAO,CAAC;QACvC;MACF;MACA,IAAMY,KAAK,GAAGC,KAAK,CAACC,IAAI,CAAC,EAAAP,iBAAA,GAAA1F,CAAC,CAAC+E,aAAa,cAAAW,iBAAA,uBAAfA,iBAAA,CAAiBK,KAAK,KAAI,EAAE,CAAC;MACtD,IAAI,CAAC3E,IAAI,IAAI2E,KAAK,CAACvE,MAAM,GAAG,CAAC,EAAE;QAC7B,IAAI,CAAChC,KAAK,CAAC0G,QAAQ,CAACC,MAAM,CAACvB,KAAK,EAAEmB,KAAK,CAAC;QACxC;MACF;MACA,IAAI3E,IAAI,IAAI2E,KAAK,CAACvE,MAAM,GAAG,CAAC,EAAE;QAAA,IAAA4E,qBAAA;QAC5B,IAAM3D,GAAG,GAAG,IAAIE,SAAS,CAAC,CAAC,CAACC,eAAe,CAACxB,IAAI,EAAE,WAAW,CAAC;QAC9D,IACEqB,GAAG,CAACK,IAAI,CAACuD,iBAAiB,KAAK,CAAC,IAChC,EAAAD,qBAAA,GAAA3D,GAAG,CAACK,IAAI,CAACwD,iBAAiB,cAAAF,qBAAA,uBAA1BA,qBAAA,CAA4BG,OAAO,MAAK,KAAK,EAC7C;UACA,IAAI,CAAC/G,KAAK,CAAC0G,QAAQ,CAACC,MAAM,CAACvB,KAAK,EAAEmB,KAAK,CAAC;UACxC;QACF;MACF;MACA,IAAI,CAACS,OAAO,CAAC5B,KAAK,EAAE;QAAExD,IAAI,EAAJA,IAAI;QAAEC,IAAA,EAAAA;MAAK,CAAC,CAAC;IACrC;EAAA;IAAAN,GAAA;IAAAC,KAAA,EAGA,SAAA8D,MAAMA,CAACF,KAAY,EAAE;MACnB,IAAMvD,IAAI,GAAG,IAAI,CAAC7B,KAAK,CAACiH,OAAO,CAAC7B,KAAK,CAAC;MACtC,IAAMxD,IAAI,GAAG,IAAI,CAAC5B,KAAK,CAACkH,eAAe,CAAC9B,KAAK,CAAC;MAC9C,OAAO;QAAExD,IAAI,EAAJA,IAAI;QAAEC,IAAA,EAAAA;MAAK,CAAC;IACvB;EAAA;IAAAN,GAAA;IAAAC,KAAA,EAEA,SAAAwF,OAAOA,CAAC5B,KAAY,EAAA+B,KAAA,EAAoD;MAAA,IAAhDtF,IAAI,GAA0CsF,KAAA,CAA9CtF,IAAI;QAAED,IAAA,GAAwCuF,KAAA,CAAxCvF,IAAA;MAC5B,IAAME,OAAO,GAAG,IAAI,CAAC9B,KAAK,CAACoH,SAAS,CAAChC,KAAK,CAACnB,KAAK,CAAC;MACjD,IAAMoD,WAAW,GAAG,IAAI,CAAC3F,OAAO,CAAC;QAAEG,IAAI,EAAJA,IAAI;QAAED,IAAA,EAAAA;MAAK,CAAC,EAAEE,OAAO,CAAC;MACzDlE,KAAK,CAAC0J,GAAG,CAAC,SAAS,EAAED,WAAW,EAAE;QAAExF,IAAI,EAAJA,IAAI;QAAED,IAAA,EAAAA;MAAK,CAAC,CAAC;MACjD,IAAMW,KAAK,GAAG,IAAIH,mBAAK,CAAC,CAAC,CACtBU,MAAM,CAACsC,KAAK,CAACnB,KAAK,CAAC,CACnBlB,MAAM,CAACqC,KAAK,CAACpD,MAAM,CAAC,CACpBnB,MAAM,CAACwG,WAAW,CAAC;MACtB,IAAI,CAACrH,KAAK,CAAC0E,cAAc,CAACnC,KAAK,EAAE4B,cAAK,CAACC,OAAO,CAACmD,IAAI,CAAC;MACpD;MACA,IAAI,CAACvH,KAAK,CAACuE,YAAY,CACrBhC,KAAK,CAACP,MAAM,CAAC,CAAC,GAAGoD,KAAK,CAACpD,MAAM,EAC7BmC,cAAK,CAACC,OAAO,CAACI,MAChB,CAAC;MACD,IAAI,CAACxE,KAAK,CAACwH,uBAAuB,CAAC,CAAC;IACtC;EAAA;IAAAjG,GAAA;IAAAC,KAAA,EAEA,SAAAkC,eAAeA,CAACL,SAAkB,EAAEE,WAAqC,EAAE;MACzE,IAAMK,eAA0B,GAAG,EAAE;MACrC,IAAMC,YAAuB,GAAG,EAAE;MAClC,IAAI,CAACjD,QAAQ,CAACE,OAAO,CAAE,UAAA2G,IAAI,EAAK;QAC9B,IAAAC,KAAA,OAAAzG,eAAA,CAAAnB,OAAA,EAA4B2H,IAAI;UAAzBvG,QAAQ,GAAAwG,KAAA;UAAEvG,OAAO,GAAAuG,KAAA;QACxB,QAAQxG,QAAQ;UACd,KAAKnD,IAAI,CAACC,SAAS;YACjB6F,YAAY,CAACpC,IAAI,CAACN,OAAO,CAAC;YAC1B;UACF,KAAKpD,IAAI,CAACK,YAAY;YACpBwF,eAAe,CAACnC,IAAI,CAACN,OAAO,CAAC;YAC7B;UACF;YACEqF,KAAK,CAACC,IAAI,CAACpD,SAAS,CAACsE,gBAAgB,CAACzG,QAAQ,CAAC,CAAC,CAACJ,OAAO,CAAE,UAAA8G,IAAI,EAAK;cACjE,IAAIrE,WAAW,CAACsE,GAAG,CAACD,IAAI,CAAC,EAAE;gBACzB,IAAME,OAAO,GAAGvE,WAAW,CAACwE,GAAG,CAACH,IAAI,CAAC;gBACrCE,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAErG,IAAI,CAACN,OAAO,CAAC;cACxB,CAAC,MAAM;gBACLoC,WAAW,CAACyE,GAAG,CAACJ,IAAI,EAAE,CAACzG,OAAO,CAAC,CAAC;cAClC;YACF,CAAC,CAAC;YACF;QACJ;MACF,CAAC,CAAC;MACF,OAAO,CAACyC,eAAe,EAAEC,YAAY,CAAC;IACxC;EAAA;AAAA,EA3MsBoE,eAAM;AAAA,IAAA3F,gBAAA,CAAAxC,OAAA,EAAxBF,SAAS,cACuB;EAClCgB,QAAQ,EAAE;AACZ,CAAC;AA2MH,SAASsH,WAAWA,CAClB3F,KAAY,EACZ4F,MAAc,EACd3G,KAAc,EACduC,MAAkB,EACX;EACP,IAAI,CAACA,MAAM,CAACqE,KAAK,CAACD,MAAM,CAAC,EAAE;IACzB,OAAO5F,KAAK;EACd;EAEA,OAAOA,KAAK,CAACtD,MAAM,CAAC,UAACoJ,QAAQ,EAAEC,EAAE,EAAK;IACpC,IAAI,CAACA,EAAE,CAACjG,MAAM,EAAE,OAAOgG,QAAQ;IAC/B,IAAIC,EAAE,CAAC3F,UAAU,IAAI2F,EAAE,CAAC3F,UAAU,CAACwF,MAAM,CAAC,EAAE;MAC1C,OAAOE,QAAQ,CAAC5G,IAAI,CAAC6G,EAAE,CAAC;IAC1B;IACA,IAAMxG,OAAO,GAAGN,KAAK,OAAAc,gBAAA,CAAAxC,OAAA,MAAMqI,MAAM,EAAG3G,KAAA,IAAU,CAAC,CAAC;IAChD,OAAO6G,QAAQ,CAAChG,MAAM,CAACiG,EAAE,CAACjG,MAAM,MAAAkG,cAAA,CAAAzI,OAAA,MAAAyI,cAAA,CAAAzI,OAAA,MAAOgC,OAAO,GAAKwG,EAAE,CAAC3F,UAAA,CAAY,CAAC;EACrE,CAAC,EAAE,IAAIP,mBAAK,CAAC,CAAC,CAAC;AACjB;AAEA,SAASK,aAAaA,CAACF,KAAY,EAAEV,IAAY,EAAE;EACjD,IAAI2G,OAAO,GAAG,EAAE;EAChB,KACE,IAAIC,CAAC,GAAGlG,KAAK,CAACG,GAAG,CAACV,MAAM,GAAG,CAAC,EAC5ByG,CAAC,IAAI,CAAC,IAAID,OAAO,CAACxG,MAAM,GAAGH,IAAI,CAACG,MAAM,EACtC,EAAEyG,CAAC,CAAC;EAAA,EACJ;IACA,IAAMH,EAAE,GAAG/F,KAAK,CAACG,GAAG,CAAC+F,CAAC,CAAC;IACvB,IAAI,OAAOH,EAAE,CAACjG,MAAM,KAAK,QAAQ,EAAE;IACnCmG,OAAO,GAAGF,EAAE,CAACjG,MAAM,GAAGmG,OAAO;EAC/B;EACA,OAAOA,OAAO,CAACE,KAAK,CAAC,CAAC,CAAC,GAAG7G,IAAI,CAACG,MAAM,CAAC,KAAKH,IAAI;AACjD;AAEA,SAAS8G,MAAMA,CAACf,IAAU,EAAE7D,MAAkB,EAAE;EAC9C,IAAI,EAAE6D,IAAI,YAAYgB,OAAO,CAAC,EAAE,OAAO,KAAK;EAC5C,IAAMC,KAAK,GAAG9E,MAAM,CAACqE,KAAK,CAACR,IAAI,CAAC;EAChC;EACA,IAAIiB,KAAK,IAAIA,KAAK,CAACC,SAAS,YAAYC,oBAAS,EAAE,OAAO,KAAK;EAE/D,OAAO,CACL,SAAS,EACT,SAAS,EACT,YAAY,EACZ,QAAQ,EACR,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,UAAU,EACV,YAAY,EACZ,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,KAAK,EACL,IAAI,EACJ,QAAQ,EACR,GAAG,EACH,KAAK,EACL,SAAS,EACT,OAAO,EACP,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,OAAO,CACR,CAACC,QAAQ,CAACpB,IAAI,CAACb,OAAO,CAACkC,WAAW,CAAC,CAAC,CAAC;AACxC;AAEA,SAASC,uBAAuBA,CAACtB,IAAiB,EAAE7D,MAAkB,EAAE;EACtE,OACE6D,IAAI,CAACuB,sBAAsB,IAC3BvB,IAAI,CAACwB,kBAAkB,IACvB,CAACT,MAAM,CAACf,IAAI,CAACuB,sBAAsB,EAAEpF,MAAM,CAAC,IAC5C,CAAC4E,MAAM,CAACf,IAAI,CAACwB,kBAAkB,EAAErF,MAAM,CAAC;AAE5C;AAEA,IAAMsF,QAAQ,GAAG,IAAI7F,OAAO,CAAC,CAAC;AAC9B,SAAS8F,KAAKA,CAAC1B,IAAiB,EAAE;EAChC,IAAIA,IAAI,IAAI,IAAI,EAAE,OAAO,KAAK;EAC9B,IAAI,CAACyB,QAAQ,CAACxB,GAAG,CAACD,IAAI,CAAC,EAAE;IACvB;IACA,IAAIA,IAAI,CAACb,OAAO,KAAK,KAAK,EAAE;MAC1BsC,QAAQ,CAACrB,GAAG,CAACJ,IAAI,EAAE,IAAI,CAAC;IAC1B,CAAC,MAAM;MACLyB,QAAQ,CAACrB,GAAG,CAACJ,IAAI,EAAE0B,KAAK,CAAC1B,IAAI,CAAC2B,UAAU,CAAC,CAAC;IAC5C;EACF;EACA,OAAOF,QAAQ,CAACtB,GAAG,CAACH,IAAI,CAAC;AAC3B;AAEA,SAAS9D,QAAQA,CACfC,MAAkB,EAClB6D,IAAe,EACfhE,eAA0B,EAC1BC,YAAuB,EACvBN,WAAqC,EAC9B;EACP;EACA,IAAIqE,IAAI,CAAC4B,QAAQ,KAAK5B,IAAI,CAAC5J,SAAS,EAAE;IACpC,OAAO6F,YAAY,CAAC5E,MAAM,CAAC,UAACsD,KAAY,EAAEpB,OAAO,EAAK;MACpD,OAAOA,OAAO,CAACyG,IAAI,EAAErF,KAAK,EAAEwB,MAAM,CAAC;IACrC,CAAC,EAAE,IAAI3B,mBAAK,CAAC,CAAC,CAAC;EACjB;EACA,IAAIwF,IAAI,CAAC4B,QAAQ,KAAK5B,IAAI,CAACxJ,YAAY,EAAE;IACvC,OAAOoI,KAAK,CAACC,IAAI,CAACmB,IAAI,CAAC6B,UAAU,IAAI,EAAE,CAAC,CAACxK,MAAM,CAAC,UAACsD,KAAK,EAAEmH,SAAS,EAAK;MACpE,IAAIC,aAAa,GAAG7F,QAAQ,CAC1BC,MAAM,EACN2F,SAAS,EACT9F,eAAe,EACfC,YAAY,EACZN,WACF,CAAC;MACD,IAAImG,SAAS,CAACF,QAAQ,KAAK5B,IAAI,CAACxJ,YAAY,EAAE;QAC5CuL,aAAa,GAAG/F,eAAe,CAAC3E,MAAM,CAAC,UAAC2K,YAAY,EAAEzI,OAAO,EAAK;UAChE,OAAOA,OAAO,CAACuI,SAAS,EAAiBE,YAAY,EAAE7F,MAAM,CAAC;QAChE,CAAC,EAAE4F,aAAa,CAAC;QACjBA,aAAa,GAAG,CAACpG,WAAW,CAACwE,GAAG,CAAC2B,SAAS,CAAC,IAAI,EAAE,EAAEzK,MAAM,CACvD,UAAC2K,YAAY,EAAEzI,OAAO,EAAK;UACzB,OAAOA,OAAO,CAACuI,SAAS,EAAEE,YAAY,EAAE7F,MAAM,CAAC;QACjD,CAAC,EACD4F,aACF,CAAC;MACH;MACA,OAAOpH,KAAK,CAAC1B,MAAM,CAAC8I,aAAa,CAAC;IACpC,CAAC,EAAE,IAAIvH,mBAAK,CAAC,CAAC,CAAC;EACjB;EACA,OAAO,IAAIA,mBAAK,CAAC,CAAC;AACpB;AAEA,SAASxD,gBAAgBA,CAACuJ,MAAc,EAAE;EACxC,OAAO,UAAC0B,KAAc,EAAEtH,KAAY,EAAEwB,MAAkB,EAAK;IAC3D,OAAOmE,WAAW,CAAC3F,KAAK,EAAE4F,MAAM,EAAE,IAAI,EAAEpE,MAAM,CAAC;EACjD,CAAC;AACH;AAEA,SAASzF,eAAeA,CAACsJ,IAAiB,EAAErF,KAAY,EAAEwB,MAAkB,EAAE;EAC5E,IAAMpB,UAAU,GAAGmH,qBAAU,CAACC,IAAI,CAACnC,IAAI,CAAC;EACxC,IAAMoC,OAAO,GAAGC,0BAAe,CAACF,IAAI,CAACnC,IAAI,CAAC;EAC1C,IAAMsC,MAAM,GAAGC,0BAAe,CAACJ,IAAI,CAACnC,IAAI,CAAC;EACzC,IAAM9F,OAA2C,GAAG,CAAC,CAAC;EACtDa,UAAU,CACP9B,MAAM,CAACmJ,OAAO,CAAC,CACfnJ,MAAM,CAACqJ,MAAM,CAAC,CACdpJ,OAAO,CAAE,UAAAsJ,IAAI,EAAK;IACjB,IAAIjL,IAAI,GAAG4E,MAAM,CAACqE,KAAK,CAACgC,IAAI,EAAEC,gBAAK,CAACC,SAAS,CAAe;IAC5D,IAAInL,IAAI,IAAI,IAAI,EAAE;MAChB2C,OAAO,CAAC3C,IAAI,CAACoL,QAAQ,CAAC,GAAGpL,IAAI,CAACqC,KAAK,CAACoG,IAAI,CAAC;MACzC,IAAI9F,OAAO,CAAC3C,IAAI,CAACoL,QAAQ,CAAC,EAAE;IAC9B;IACApL,IAAI,GAAGL,qBAAqB,CAACsL,IAAI,CAAC;IAClC,IAAIjL,IAAI,IAAI,IAAI,KAAKA,IAAI,CAACoL,QAAQ,KAAKH,IAAI,IAAIjL,IAAI,CAACC,OAAO,KAAKgL,IAAI,CAAC,EAAE;MACrEtI,OAAO,CAAC3C,IAAI,CAACoL,QAAQ,CAAC,GAAGpL,IAAI,CAACqC,KAAK,CAACoG,IAAI,CAAC,IAAI3F,SAAS;IACxD;IACA9C,IAAI,GAAGE,iBAAiB,CAAC+K,IAAI,CAAC;IAC9B,IAAIjL,IAAI,IAAI,IAAI,KAAKA,IAAI,CAACoL,QAAQ,KAAKH,IAAI,IAAIjL,IAAI,CAACC,OAAO,KAAKgL,IAAI,CAAC,EAAE;MACrEjL,IAAI,GAAGE,iBAAiB,CAAC+K,IAAI,CAAC;MAC9BtI,OAAO,CAAC3C,IAAI,CAACoL,QAAQ,CAAC,GAAGpL,IAAI,CAACqC,KAAK,CAACoG,IAAI,CAAC,IAAI3F,SAAS;IACxD;EACF,CAAC,CAAC;EAEJ,OAAOuI,MAAM,CAACC,OAAO,CAAC3I,OAAO,CAAC,CAAC7C,MAAM,CACnC,UAACoJ,QAAQ,EAAAqC,KAAA;IAAA,IAAAC,KAAA,OAAA1J,eAAA,CAAAnB,OAAA,EAAe4K,KAAA;MAAZN,IAAI,GAAAO,KAAA;MAAEnJ,KAAK,GAAAmJ,KAAA;IAAC,OAAKzC,WAAW,CAACG,QAAQ,EAAE+B,IAAI,EAAE5I,KAAK,EAAEuC,MAAM,CAAC;EAAA,GACvExB,KACF,CAAC;AACH;AAEA,SAASlE,SAASA,CAACuJ,IAAU,EAAErF,KAAY,EAAEwB,MAAkB,EAAE;EAC/D,IAAM8E,KAAK,GAAG9E,MAAM,CAACqE,KAAK,CAACR,IAAI,CAAC;EAChC,IAAIiB,KAAK,IAAI,IAAI,EAAE,OAAOtG,KAAK;EAC/B;EACA,IAAIsG,KAAK,CAACC,SAAS,YAAYC,oBAAS,EAAE;IACxC,IAAM6B,KAAK,GAAG,CAAC,CAAC;IAChB;IACA,IAAMpJ,KAAK,GAAGqH,KAAK,CAACrH,KAAK,CAACoG,IAAI,CAAC;IAC/B,IAAIpG,KAAK,IAAI,IAAI,EAAE;MACjB;MACAoJ,KAAK,CAAC/B,KAAK,CAAC1G,QAAQ,CAAC,GAAGX,KAAK;MAC7B;MACA,OAAO,IAAIY,mBAAK,CAAC,CAAC,CAACC,MAAM,CAACuI,KAAK,EAAE/B,KAAK,CAAC/G,OAAO,CAAC8F,IAAI,EAAE7D,MAAM,CAAC,CAAC;IAC/D;EACF,CAAC,MAAM;IACL;IACA,IAAI8E,KAAK,CAACC,SAAS,YAAY+B,oBAAS,IAAI,CAACpI,aAAa,CAACF,KAAK,EAAE,IAAI,CAAC,EAAE;MACvEA,KAAK,CAACF,MAAM,CAAC,IAAI,CAAC;IACpB;IACA,IACE,UAAU,IAAIwG,KAAK,IACnB,SAAS,IAAIA,KAAK,IAClB,OAAOA,KAAK,CAAC/G,OAAO,KAAK,UAAU,EACnC;MACA,OAAOoG,WAAW,CAChB3F,KAAK,EACLsG,KAAK,CAAC1G,QAAQ,EACd0G,KAAK,CAAC/G,OAAO,CAAC8F,IAAI,EAAE7D,MAAM,CAAC,EAC3BA,MACF,CAAC;IACH;EACF;EACA,OAAOxB,KAAK;AACd;AAEA,SAASpE,UAAUA,CAACyJ,IAAU,EAAErF,KAAY,EAAE;EAC5C,IAAI,CAACE,aAAa,CAACF,KAAK,EAAE,IAAI,CAAC,EAAE;IAC/BA,KAAK,CAACF,MAAM,CAAC,IAAI,CAAC;EACpB;EACA,OAAOE,KAAK;AACd;AAEA,SAAS7D,cAAcA,CAACkJ,IAAU,EAAErF,KAAY,EAAEwB,MAAkB,EAAE;EACpE,IAAM8E,KAAK,GAAG9E,MAAM,CAACqE,KAAK,CAAC,YAAY,CAAC;EACxC,IAAM0C,QAAQ,GACZjC,KAAK,IAAI,SAAS,IAAIA,KAAK,IAAI,OAAOA,KAAK,CAAC/G,OAAO,KAAK,UAAU,GAC9D+G,KAAK,CAAC/G,OAAO,CAAC8F,IAAI,EAAE7D,MAAM,CAAC,GAC3B,IAAI;EACV,OAAOmE,WAAW,CAAC3F,KAAK,EAAE,YAAY,EAAEuI,QAAQ,EAAE/G,MAAM,CAAC;AAC3D;AAEA,SAASlF,WAAWA,CAAA,EAAG;EACrB,OAAO,IAAIuD,mBAAK,CAAC,CAAC;AACpB;AAEA,SAAS5D,WAAWA,CAACoJ,IAAU,EAAErF,KAAY,EAAEwB,MAAkB,EAAE;EACjE,IAAM8E,KAAK,GAAG9E,MAAM,CAACqE,KAAK,CAACR,IAAI,CAAC;EAChC,IACEiB,KAAK,IAAI,IAAI;EACb;EACAA,KAAK,CAAC1G,QAAQ,KAAK,MAAM,IACzB,CAACM,aAAa,CAACF,KAAK,EAAE,IAAI,CAAC,EAC3B;IACA,OAAOA,KAAK;EACd;EACA,IAAIwI,MAAM,GAAG,CAAC,CAAC;EACf,IAAIC,MAAM,GAAGpD,IAAI,CAAC2B,UAAU;EAC5B,OAAOyB,MAAM,IAAI,IAAI,EAAE;IACrB;IACA,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAChC,QAAQ,CAACgC,MAAM,CAACjE,OAAO,CAAC,EAAE;MACzCgE,MAAM,IAAI,CAAC;IACb;IACAC,MAAM,GAAGA,MAAM,CAACzB,UAAU;EAC5B;EACA,IAAIwB,MAAM,IAAI,CAAC,EAAE,OAAOxI,KAAK;EAC7B,OAAOA,KAAK,CAACtD,MAAM,CAAC,UAACgM,QAAQ,EAAE3C,EAAE,EAAK;IACpC,IAAI,CAACA,EAAE,CAACjG,MAAM,EAAE,OAAO4I,QAAQ;IAC/B,IAAI3C,EAAE,CAAC3F,UAAU,IAAI,OAAO2F,EAAE,CAAC3F,UAAU,CAACoI,MAAM,KAAK,QAAQ,EAAE;MAC7D,OAAOE,QAAQ,CAACxJ,IAAI,CAAC6G,EAAE,CAAC;IAC1B;IACA,OAAO2C,QAAQ,CAAC5I,MAAM,CAACiG,EAAE,CAACjG,MAAM,MAAAkG,cAAA,CAAAzI,OAAA;MAAIiL,MAAM,EAANA;IAAM,GAAMzC,EAAE,CAAC3F,UAAU,IAAI,CAAC,CAAC,CAAG,CAAC;EACzE,CAAC,EAAE,IAAIP,mBAAK,CAAC,CAAC,CAAC;AACjB;AAEA,SAAS3D,SAASA,CAACmJ,IAAU,EAAErF,KAAY,EAAEwB,MAAkB,EAAE;EAC/D,IAAMmH,OAAO,GAAGtD,IAAe;EAC/B,IAAIuD,IAAI,GAAGD,OAAO,CAACnE,OAAO,KAAK,IAAI,GAAG,SAAS,GAAG,QAAQ;EAE1D,IAAMqE,WAAW,GAAGF,OAAO,CAACG,YAAY,CAAC,cAAc,CAAC;EACxD,IAAID,WAAW,EAAE;IACfD,IAAI,GAAGC,WAAW,KAAK,MAAM,GAAG,SAAS,GAAG,WAAW;EACzD;EAEA,OAAOlD,WAAW,CAAC3F,KAAK,EAAE,MAAM,EAAE4I,IAAI,EAAEpH,MAAM,CAAC;AACjD;AAEA,SAAS7F,YAAYA,CAAC0J,IAAU,EAAErF,KAAY,EAAEwB,MAAkB,EAAE;EAClE,IAAI,CAACtB,aAAa,CAACF,KAAK,EAAE,IAAI,CAAC,EAAE;IAC/B,IACEoG,MAAM,CAACf,IAAI,EAAE7D,MAAM,CAAC,KACnB6D,IAAI,CAAC6B,UAAU,CAACzH,MAAM,GAAG,CAAC,IAAI4F,IAAI,YAAY0D,oBAAoB,CAAC,EACpE;MACA,OAAO/I,KAAK,CAACF,MAAM,CAAC,IAAI,CAAC;IAC3B;IACA,IAAIE,KAAK,CAACP,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI4F,IAAI,CAAC2D,WAAW,EAAE;MAC1C,IAAIA,WAAwB,GAAG3D,IAAI,CAAC2D,WAAW;MAC/C,OAAOA,WAAW,IAAI,IAAI,EAAE;QAC1B,IAAI5C,MAAM,CAAC4C,WAAW,EAAExH,MAAM,CAAC,EAAE;UAC/B,OAAOxB,KAAK,CAACF,MAAM,CAAC,IAAI,CAAC;QAC3B;QACA,IAAMwG,KAAK,GAAG9E,MAAM,CAACqE,KAAK,CAACmD,WAAW,CAAC;QACvC;QACA,IAAI1C,KAAK,IAAIA,KAAK,CAACC,SAAS,YAAY0C,iBAAU,EAAE;UAClD,OAAOjJ,KAAK,CAACF,MAAM,CAAC,IAAI,CAAC;QAC3B;QACAkJ,WAAW,GAAGA,WAAW,CAACE,UAAU;MACtC;IACF;EACF;EACA,OAAOlJ,KAAK;AACd;AAEA,SAAShE,WAAWA,CAACqJ,IAAiB,EAAErF,KAAY,EAAEwB,MAAkB,EAAE;EAAA,IAAA2H,iBAAA;EACxE,IAAM5J,OAAgC,GAAG,CAAC,CAAC;EAC3C,IAAM6J,KAAmC,GAAG/D,IAAI,CAAC+D,KAAK,IAAI,CAAC,CAAC;EAC5D,IAAIA,KAAK,CAACC,SAAS,KAAK,QAAQ,EAAE;IAChC9J,OAAO,CAAC+J,MAAM,GAAG,IAAI;EACvB;EACA,IAAIF,KAAK,CAACG,cAAc,KAAK,WAAW,EAAE;IACxChK,OAAO,CAACiK,SAAS,GAAG,IAAI;EAC1B;EACA,IAAIJ,KAAK,CAACG,cAAc,KAAK,cAAc,EAAE;IAC3ChK,OAAO,CAACkK,MAAM,GAAG,IAAI;EACvB;EACA,IACE,CAAAN,iBAAA,GAAAC,KAAK,CAACM,UAAU,cAAAP,iBAAA,eAAhBA,iBAAA,CAAkBQ,UAAU,CAAC,MAAM,CAAC;EACpC;EACAC,QAAQ,CAACR,KAAK,CAACM,UAAU,EAAE,EAAE,CAAC,IAAI,GAAG,EACrC;IACAnK,OAAO,CAACsK,IAAI,GAAG,IAAI;EACrB;EACA7J,KAAK,GAAGiI,MAAM,CAACC,OAAO,CAAC3I,OAAO,CAAC,CAAC7C,MAAM,CACpC,UAACoJ,QAAQ,EAAAgE,KAAA;IAAA,IAAAC,KAAA,OAAArL,eAAA,CAAAnB,OAAA,EAAeuM,KAAA;MAAZjC,IAAI,GAAAkC,KAAA;MAAE9K,KAAK,GAAA8K,KAAA;IAAC,OAAKpE,WAAW,CAACG,QAAQ,EAAE+B,IAAI,EAAE5I,KAAK,EAAEuC,MAAM,CAAC;EAAA,GACvExB,KACF,CAAC;EACD;EACA,IAAIgK,UAAU,CAACZ,KAAK,CAACa,UAAU,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE;IACzC;IACA,OAAO,IAAIpK,mBAAK,CAAC,CAAC,CAACC,MAAM,CAAC,IAAI,CAAC,CAACxB,MAAM,CAAC0B,KAAK,CAAC;EAC/C;EACA,OAAOA,KAAK;AACd;AAEA,SAAS5D,UAAUA,CACjBiJ,IAAyB,EACzBrF,KAAY,EACZwB,MAAkB,EAClB;EAAA,IAAA0I,mBAAA,EAAAC,oBAAA;EACA,IAAM9J,KAAK,GACT,EAAA6J,mBAAA,GAAA7E,IAAI,CAAC+E,aAAa,cAAAF,mBAAA,uBAAlBA,mBAAA,CAAoB1F,OAAO,MAAK,OAAO,GACnCa,IAAI,CAAC+E,aAAa,IAAAD,oBAAA,GAClB9E,IAAI,CAAC+E,aAAa,cAAAD,oBAAA,uBAAlBA,oBAAA,CAAoBC,aAAa;EACvC,IAAI/J,KAAK,IAAI,IAAI,EAAE;IACjB,IAAMgK,IAAI,GAAGpG,KAAK,CAACC,IAAI,CAAC7D,KAAK,CAAC+E,gBAAgB,CAAC,IAAI,CAAC,CAAC;IACrD,IAAMkF,GAAG,GAAGD,IAAI,CAACE,OAAO,CAAClF,IAAI,CAAC,GAAG,CAAC;IAClC,OAAOM,WAAW,CAAC3F,KAAK,EAAE,OAAO,EAAEsK,GAAG,EAAE9I,MAAM,CAAC;EACjD;EACA,OAAOxB,KAAK;AACd;AAEA,SAAStE,SAASA,CAAC2J,IAAiB,EAAErF,KAAY,EAAEwB,MAAkB,EAAE;EAAA,IAAAgJ,oBAAA;EACtE;EACA,IAAIlL,IAAI,GAAG+F,IAAI,CAACoF,IAAI;EACpB;EACA,IAAI,EAAAD,oBAAA,GAAAnF,IAAI,CAAC+E,aAAa,cAAAI,oBAAA,uBAAlBA,oBAAA,CAAoBhG,OAAO,MAAK,KAAK,EAAE;IACzC,OAAOxE,KAAK,CAACF,MAAM,CAACR,IAAI,CAACoL,IAAI,CAAC,CAAC,CAAC;EAClC;EACA,IAAI,CAAC3D,KAAK,CAAC1B,IAAI,CAAC,EAAE;IAChB,IACE/F,IAAI,CAACoL,IAAI,CAAC,CAAC,CAACjL,MAAM,KAAK,CAAC,IACxBH,IAAI,CAACmH,QAAQ,CAAC,IAAI,CAAC,IACnB,CAACE,uBAAuB,CAACtB,IAAI,EAAE7D,MAAM,CAAC,EACtC;MACA,OAAOxB,KAAK;IACd;IACA,IAAM2K,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,QAAiB,EAAEtE,KAAa,EAAK;MACrD,IAAMuE,QAAQ,GAAGvE,KAAK,CAACwE,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,CAAC;MAClD,OAAOD,QAAQ,CAACpL,MAAM,GAAG,CAAC,IAAImL,QAAQ,GAAG,GAAG,GAAGC,QAAQ;IACzD,CAAC;IACDvL,IAAI,GAAGA,IAAI,CAACwL,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;IACrDxL,IAAI,GAAGA,IAAI,CAACwL,OAAO,CAAC,QAAQ,EAAEH,QAAQ,CAACvM,IAAI,CAACuM,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IAC9D,IACGtF,IAAI,CAAC0F,eAAe,IAAI,IAAI,IAC3B1F,IAAI,CAAC+E,aAAa,IAAI,IAAI,IAC1BhE,MAAM,CAACf,IAAI,CAAC+E,aAAa,EAAE5I,MAAM,CAAC,IACnC6D,IAAI,CAAC0F,eAAe,YAAY1E,OAAO,IACtCD,MAAM,CAACf,IAAI,CAAC0F,eAAe,EAAEvJ,MAAM,CAAE,EACvC;MACAlC,IAAI,GAAGA,IAAI,CAACwL,OAAO,CAAC,MAAM,EAAEH,QAAQ,CAACvM,IAAI,CAACuM,QAAQ,EAAE,KAAK,CAAC,CAAC;IAC7D;IACA,IACGtF,IAAI,CAAC2D,WAAW,IAAI,IAAI,IACvB3D,IAAI,CAAC+E,aAAa,IAAI,IAAI,IAC1BhE,MAAM,CAACf,IAAI,CAAC+E,aAAa,EAAE5I,MAAM,CAAC,IACnC6D,IAAI,CAAC2D,WAAW,YAAY3C,OAAO,IAAID,MAAM,CAACf,IAAI,CAAC2D,WAAW,EAAExH,MAAM,CAAE,EACzE;MACAlC,IAAI,GAAGA,IAAI,CAACwL,OAAO,CAAC,MAAM,EAAEH,QAAQ,CAACvM,IAAI,CAACuM,QAAQ,EAAE,KAAK,CAAC,CAAC;IAC7D;EACF;EACA,OAAO3K,KAAK,CAACF,MAAM,CAACR,IAAI,CAAC;AAC3B", "ignoreList": []}]}