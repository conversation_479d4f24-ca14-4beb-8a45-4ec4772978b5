{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/system/dept.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/system/dept.js", "mtime": 1753510684517}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCIvVXNlcnMvbWF6aWhhby9EZXNrdG9wL2Rldi9wZXJmb3JtYW5jZS9wZXJmb3JtYW5jZS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuYWRkRGVwdCA9IGFkZERlcHQ7CmV4cG9ydHMuZGVsRGVwdCA9IGRlbERlcHQ7CmV4cG9ydHMuZ2V0RGVwdCA9IGdldERlcHQ7CmV4cG9ydHMubGlzdERlcHQgPSBsaXN0RGVwdDsKZXhwb3J0cy5saXN0RGVwdEV4Y2x1ZGVDaGlsZCA9IGxpc3REZXB0RXhjbHVkZUNoaWxkOwpleHBvcnRzLnVwZGF0ZURlcHQgPSB1cGRhdGVEZXB0Owp2YXIgX3JlcXVlc3QgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvdXRpbHMvcmVxdWVzdCIpKTsKLy8g5p+l6K+i6YOo6Zeo5YiX6KGoCmZ1bmN0aW9uIGxpc3REZXB0KHF1ZXJ5KSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvc3lzdGVtL2RlcHQvbGlzdCcsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDmn6Xor6Lpg6jpl6jliJfooajvvIjmjpLpmaToioLngrnvvIkKZnVuY3Rpb24gbGlzdERlcHRFeGNsdWRlQ2hpbGQoZGVwdElkKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvc3lzdGVtL2RlcHQvbGlzdC9leGNsdWRlLycgKyBkZXB0SWQsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8vIOafpeivoumDqOmXqOivpue7hgpmdW5jdGlvbiBnZXREZXB0KGRlcHRJZCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS9kZXB0LycgKyBkZXB0SWQsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8vIOaWsOWinumDqOmXqApmdW5jdGlvbiBhZGREZXB0KGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9zeXN0ZW0vZGVwdCcsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5L+u5pS56YOo6ZeoCmZ1bmN0aW9uIHVwZGF0ZURlcHQoZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS9kZXB0JywKICAgIG1ldGhvZDogJ3B1dCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOWIoOmZpOmDqOmXqApmdW5jdGlvbiBkZWxEZXB0KGRlcHRJZCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS9kZXB0LycgKyBkZXB0SWQsCiAgICBtZXRob2Q6ICdkZWxldGUnCiAgfSk7Cn0="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listDept", "query", "request", "url", "method", "params", "listDept<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deptId", "getDept", "addDept", "data", "updateDept", "delDept"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/system/dept.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询部门列表\nexport function listDept(query) {\n  return request({\n    url: '/system/dept/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询部门列表（排除节点）\nexport function listDeptExcludeChild(deptId) {\n  return request({\n    url: '/system/dept/list/exclude/' + deptId,\n    method: 'get'\n  })\n}\n\n// 查询部门详细\nexport function getDept(deptId) {\n  return request({\n    url: '/system/dept/' + deptId,\n    method: 'get'\n  })\n}\n\n// 新增部门\nexport function addDept(data) {\n  return request({\n    url: '/system/dept',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改部门\nexport function updateDept(data) {\n  return request({\n    url: '/system/dept',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除部门\nexport function delDept(deptId) {\n  return request({\n    url: '/system/dept/' + deptId,\n    method: 'delete'\n  })\n}"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,oBAAoBA,CAACC,MAAM,EAAE;EAC3C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B,GAAGI,MAAM;IAC1CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,OAAOA,CAACD,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,OAAOA,CAACL,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}