{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/RuoYi/Doc/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/RuoYi/Doc/index.vue", "mtime": 1753510684528}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7Ci8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAnUnVvWWlEb2MnLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB1cmw6ICdodHRwOi8vZG9jLnJ1b3lpLnZpcC9ydW95aS12dWUnCiAgICB9OwogIH0sCiAgbWV0aG9kczogewogICAgZ290bzogZnVuY3Rpb24gZ290bygpIHsKICAgICAgd2luZG93Lm9wZW4odGhpcy51cmwpOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["name", "data", "url", "methods", "goto", "window", "open"], "sources": ["src/components/RuoYi/Doc/index.vue"], "sourcesContent": ["<template>\n  <div>\n    <svg-icon icon-class=\"question\" @click=\"goto\" />\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'RuoYiDoc',\n  data() {\n    return {\n      url: 'http://doc.ruoyi.vip/ruoyi-vue'\n    }\n  },\n  methods: {\n    goto() {\n      window.open(this.url)\n    }\n  }\n}\n</script>"], "mappings": ";;;;;;;;;;;;iCAOA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,GAAA;IACA;EACA;EACAC,OAAA;IACAC,IAAA,WAAAA,KAAA;MACAC,MAAA,CAAAC,IAAA,MAAAJ,GAAA;IACA;EACA;AACA", "ignoreList": []}]}