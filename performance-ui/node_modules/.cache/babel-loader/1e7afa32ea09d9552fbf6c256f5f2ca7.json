{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/system/config.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/system/config.js", "mtime": 1753510684517}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listConfig", "query", "request", "url", "method", "params", "getConfig", "configId", "getConfigKey", "config<PERSON><PERSON>", "addConfig", "data", "updateConfig", "delConfig", "refreshCache"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/system/config.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询参数列表\nexport function listConfig(query) {\n  return request({\n    url: '/system/config/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询参数详细\nexport function getConfig(configId) {\n  return request({\n    url: '/system/config/' + configId,\n    method: 'get'\n  })\n}\n\n// 根据参数键名查询参数值\nexport function getConfigKey(configKey) {\n  return request({\n    url: '/system/config/configKey/' + configKey,\n    method: 'get'\n  })\n}\n\n// 新增参数配置\nexport function addConfig(data) {\n  return request({\n    url: '/system/config',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改参数配置\nexport function updateConfig(data) {\n  return request({\n    url: '/system/config',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除参数配置\nexport function delConfig(configId) {\n  return request({\n    url: '/system/config/' + configId,\n    method: 'delete'\n  })\n}\n\n// 刷新参数缓存\nexport function refreshCache() {\n  return request({\n    url: '/system/config/refreshCache',\n    method: 'delete'\n  })\n}\n"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,UAAUA,CAACC,KAAK,EAAE;EAChC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,SAASA,CAACC,QAAQ,EAAE;EAClC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,iBAAiB,GAAGI,QAAQ;IACjCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,YAAYA,CAACC,SAAS,EAAE;EACtC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B,GAAGM,SAAS;IAC5CL,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,SAASA,CAACC,IAAI,EAAE;EAC9B,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,MAAM;IACdO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,YAAYA,CAACD,IAAI,EAAE;EACjC,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,KAAK;IACbO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,SAASA,CAACN,QAAQ,EAAE;EAClC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,iBAAiB,GAAGI,QAAQ;IACjCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,YAAYA,CAAA,EAAG;EAC7B,OAAO,IAAAZ,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}