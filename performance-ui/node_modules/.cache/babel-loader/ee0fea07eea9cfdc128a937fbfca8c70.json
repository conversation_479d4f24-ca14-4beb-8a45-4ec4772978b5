{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/RightToolbar/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/RightToolbar/index.vue", "mtime": 1753510684528}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "data", "value", "title", "open", "props", "showSearch", "type", "Boolean", "default", "columns", "Array", "search", "showColumnsType", "String", "gutter", "Number", "computed", "style", "ret", "marginRight", "concat", "isChecked", "get", "every", "col", "visible", "set", "isIndeterminate", "some", "created", "item", "push", "parseInt", "methods", "toggleSearch", "$emit", "refresh", "dataChange", "key", "includes", "showColumn", "checkboxChange", "event", "label", "filter", "toggleCheckAll", "newValue", "for<PERSON>ach"], "sources": ["src/components/RightToolbar/index.vue"], "sourcesContent": ["<template>\n  <div class=\"top-right-btn\" :style=\"style\">\n    <el-row>\n      <el-tooltip class=\"item\" effect=\"dark\" :content=\"showSearch ? '隐藏搜索' : '显示搜索'\" placement=\"top\" v-if=\"search\">\n        <el-button size=\"mini\" circle icon=\"el-icon-search\" @click=\"toggleSearch()\" />\n      </el-tooltip>\n      <el-tooltip class=\"item\" effect=\"dark\" content=\"刷新\" placement=\"top\">\n        <el-button size=\"mini\" circle icon=\"el-icon-refresh\" @click=\"refresh()\" />\n      </el-tooltip>\n      <el-tooltip class=\"item\" effect=\"dark\" content=\"显隐列\" placement=\"top\" v-if=\"columns\">\n        <el-button size=\"mini\" circle icon=\"el-icon-menu\" @click=\"showColumn()\" v-if=\"showColumnsType == 'transfer'\"/>\n        <el-dropdown trigger=\"click\" :hide-on-click=\"false\" style=\"padding-left: 12px\" v-if=\"showColumnsType == 'checkbox'\">\n          <el-button size=\"mini\" circle icon=\"el-icon-menu\" />\n          <el-dropdown-menu slot=\"dropdown\">\n            <!-- 全选/反选 按钮 -->\n            <el-dropdown-item>\n              <el-checkbox :indeterminate=\"isIndeterminate\" v-model=\"isChecked\" @change=\"toggleCheckAll\"> 列展示 </el-checkbox>\n            </el-dropdown-item>\n            <div class=\"check-line\"></div>\n            <template v-for=\"item in columns\">\n              <el-dropdown-item :key=\"item.key\">\n                <el-checkbox v-model=\"item.visible\" @change=\"checkboxChange($event, item.label)\" :label=\"item.label\" />\n              </el-dropdown-item>\n            </template>\n          </el-dropdown-menu>\n        </el-dropdown>\n      </el-tooltip>\n    </el-row>\n    <el-dialog :title=\"title\" :visible.sync=\"open\" append-to-body>\n      <el-transfer\n        :titles=\"['显示', '隐藏']\"\n        v-model=\"value\"\n        :data=\"columns\"\n        @change=\"dataChange\"\n      ></el-transfer>\n    </el-dialog>\n  </div>\n</template>\n<script>\nexport default {\n  name: \"RightToolbar\",\n  data() {\n    return {\n      // 显隐数据\n      value: [],\n      // 弹出层标题\n      title: \"显示/隐藏\",\n      // 是否显示弹出层\n      open: false\n    }\n  },\n  props: {\n    /* 是否显示检索条件 */\n    showSearch: {\n      type: Boolean,\n      default: true\n    },\n    /* 显隐列信息 */\n    columns: {\n      type: Array\n    },\n    /* 是否显示检索图标 */\n    search: {\n      type: Boolean,\n      default: true\n    },\n    /* 显隐列类型（transfer穿梭框、checkbox复选框） */\n    showColumnsType: {\n      type: String,\n      default: \"checkbox\"\n    },\n    /* 右外边距 */\n    gutter: {\n      type: Number,\n      default: 10\n    },\n  },\n  computed: {\n    style() {\n      const ret = {}\n      if (this.gutter) {\n        ret.marginRight = `${this.gutter / 2}px`\n      }\n      return ret\n    },\n    isChecked: {\n      get() {\n        return this.columns.every((col) => col.visible)\n      },\n      set() {}\n    },\n    isIndeterminate() {\n      return this.columns.some((col) => col.visible) && !this.isChecked\n    }\n  },\n  created() {\n    if (this.showColumnsType == 'transfer') {\n      // 显隐列初始默认隐藏列\n      for (let item in this.columns) {\n        if (this.columns[item].visible === false) {\n          this.value.push(parseInt(item))\n        }\n      }\n    }\n  },\n  methods: {\n    // 搜索\n    toggleSearch() {\n      this.$emit(\"update:showSearch\", !this.showSearch)\n    },\n    // 刷新\n    refresh() {\n      this.$emit(\"queryTable\")\n    },\n    // 右侧列表元素变化\n    dataChange(data) {\n      for (let item in this.columns) {\n        const key = this.columns[item].key\n        this.columns[item].visible = !data.includes(key)\n      }\n    },\n    // 打开显隐列dialog\n    showColumn() {\n      this.open = true\n    },\n    // 单勾选\n    checkboxChange(event, label) {\n      this.columns.filter(item => item.label == label)[0].visible = event\n    },\n    // 切换全选/反选\n    toggleCheckAll() {\n      const newValue = !this.isChecked\n      this.columns.forEach((col) => (col.visible = newValue))\n    }\n  },\n}\n</script>\n<style lang=\"scss\" scoped>\n::v-deep .el-transfer__button {\n  border-radius: 50%;\n  padding: 12px;\n  display: block;\n  margin-left: 0px;\n}\n::v-deep .el-transfer__button:first-child {\n  margin-bottom: 10px;\n}\n.check-line {\n  width: 90%;\n  height: 1px;\n  background-color: #ccc;\n  margin: 3px auto;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAuCA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,KAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;IACA;EACA;EACAC,KAAA;IACA;IACAC,UAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACA;IACAC,OAAA;MACAH,IAAA,EAAAI;IACA;IACA;IACAC,MAAA;MACAL,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACA;IACAI,eAAA;MACAN,IAAA,EAAAO,MAAA;MACAL,OAAA;IACA;IACA;IACAM,MAAA;MACAR,IAAA,EAAAS,MAAA;MACAP,OAAA;IACA;EACA;EACAQ,QAAA;IACAC,KAAA,WAAAA,MAAA;MACA,IAAAC,GAAA;MACA,SAAAJ,MAAA;QACAI,GAAA,CAAAC,WAAA,MAAAC,MAAA,MAAAN,MAAA;MACA;MACA,OAAAI,GAAA;IACA;IACAG,SAAA;MACAC,GAAA,WAAAA,IAAA;QACA,YAAAb,OAAA,CAAAc,KAAA,WAAAC,GAAA;UAAA,OAAAA,GAAA,CAAAC,OAAA;QAAA;MACA;MACAC,GAAA,WAAAA,IAAA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA,YAAAlB,OAAA,CAAAmB,IAAA,WAAAJ,GAAA;QAAA,OAAAA,GAAA,CAAAC,OAAA;MAAA,YAAAJ,SAAA;IACA;EACA;EACAQ,OAAA,WAAAA,QAAA;IACA,SAAAjB,eAAA;MACA;MACA,SAAAkB,IAAA,SAAArB,OAAA;QACA,SAAAA,OAAA,CAAAqB,IAAA,EAAAL,OAAA;UACA,KAAAxB,KAAA,CAAA8B,IAAA,CAAAC,QAAA,CAAAF,IAAA;QACA;MACA;IACA;EACA;EACAG,OAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,KAAA,4BAAA9B,UAAA;IACA;IACA;IACA+B,OAAA,WAAAA,QAAA;MACA,KAAAD,KAAA;IACA;IACA;IACAE,UAAA,WAAAA,WAAArC,IAAA;MACA,SAAA8B,IAAA,SAAArB,OAAA;QACA,IAAA6B,GAAA,QAAA7B,OAAA,CAAAqB,IAAA,EAAAQ,GAAA;QACA,KAAA7B,OAAA,CAAAqB,IAAA,EAAAL,OAAA,IAAAzB,IAAA,CAAAuC,QAAA,CAAAD,GAAA;MACA;IACA;IACA;IACAE,UAAA,WAAAA,WAAA;MACA,KAAArC,IAAA;IACA;IACA;IACAsC,cAAA,WAAAA,eAAAC,KAAA,EAAAC,KAAA;MACA,KAAAlC,OAAA,CAAAmC,MAAA,WAAAd,IAAA;QAAA,OAAAA,IAAA,CAAAa,KAAA,IAAAA,KAAA;MAAA,MAAAlB,OAAA,GAAAiB,KAAA;IACA;IACA;IACAG,cAAA,WAAAA,eAAA;MACA,IAAAC,QAAA,SAAAzB,SAAA;MACA,KAAAZ,OAAA,CAAAsC,OAAA,WAAAvB,GAAA;QAAA,OAAAA,GAAA,CAAAC,OAAA,GAAAqB,QAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}