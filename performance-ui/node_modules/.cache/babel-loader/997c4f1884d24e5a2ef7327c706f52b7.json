{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/store/getters.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/store/getters.js", "mtime": 1753510684530}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["getters", "sidebar", "state", "app", "size", "device", "dict", "visitedViews", "tagsView", "cachedViews", "token", "user", "avatar", "id", "name", "nick<PERSON><PERSON>", "introduction", "roles", "permissions", "permission_routes", "permission", "routes", "topbarRouters", "defaultRoutes", "sidebarRouters", "_default", "exports", "default"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/store/getters.js"], "sourcesContent": ["const getters = {\n  sidebar: state => state.app.sidebar,\n  size: state => state.app.size,\n  device: state => state.app.device,\n  dict: state => state.dict.dict,\n  visitedViews: state => state.tagsView.visitedViews,\n  cachedViews: state => state.tagsView.cachedViews,\n  token: state => state.user.token,\n  avatar: state => state.user.avatar,\n  id: state => state.user.id,\n  name: state => state.user.name,\n  nickName: state => state.user.nickName,\n  introduction: state => state.user.introduction,\n  roles: state => state.user.roles,\n  permissions: state => state.user.permissions,\n  permission_routes: state => state.permission.routes,\n  topbarRouters: state => state.permission.topbarRouters,\n  defaultRoutes: state => state.permission.defaultRoutes,\n  sidebarRouters: state => state.permission.sidebarRouters\n}\nexport default getters\n"], "mappings": ";;;;;;;AAAA,IAAMA,OAAO,GAAG;EACdC,OAAO,EAAE,SAATA,OAAOA,CAAEC,KAAK;IAAA,OAAIA,KAAK,CAACC,GAAG,CAACF,OAAO;EAAA;EACnCG,IAAI,EAAE,SAANA,IAAIA,CAAEF,KAAK;IAAA,OAAIA,KAAK,CAACC,GAAG,CAACC,IAAI;EAAA;EAC7BC,MAAM,EAAE,SAARA,MAAMA,CAAEH,KAAK;IAAA,OAAIA,KAAK,CAACC,GAAG,CAACE,MAAM;EAAA;EACjCC,IAAI,EAAE,SAANA,IAAIA,CAAEJ,KAAK;IAAA,OAAIA,KAAK,CAACI,IAAI,CAACA,IAAI;EAAA;EAC9BC,YAAY,EAAE,SAAdA,YAAYA,CAAEL,KAAK;IAAA,OAAIA,KAAK,CAACM,QAAQ,CAACD,YAAY;EAAA;EAClDE,WAAW,EAAE,SAAbA,WAAWA,CAAEP,KAAK;IAAA,OAAIA,KAAK,CAACM,QAAQ,CAACC,WAAW;EAAA;EAChDC,KAAK,EAAE,SAAPA,KAAKA,CAAER,KAAK;IAAA,OAAIA,KAAK,CAACS,IAAI,CAACD,KAAK;EAAA;EAChCE,MAAM,EAAE,SAARA,MAAMA,CAAEV,KAAK;IAAA,OAAIA,KAAK,CAACS,IAAI,CAACC,MAAM;EAAA;EAClCC,EAAE,EAAE,SAAJA,EAAEA,CAAEX,KAAK;IAAA,OAAIA,KAAK,CAACS,IAAI,CAACE,EAAE;EAAA;EAC1BC,IAAI,EAAE,SAANA,IAAIA,CAAEZ,KAAK;IAAA,OAAIA,KAAK,CAACS,IAAI,CAACG,IAAI;EAAA;EAC9BC,QAAQ,EAAE,SAAVA,QAAQA,CAAEb,KAAK;IAAA,OAAIA,KAAK,CAACS,IAAI,CAACI,QAAQ;EAAA;EACtCC,YAAY,EAAE,SAAdA,YAAYA,CAAEd,KAAK;IAAA,OAAIA,KAAK,CAACS,IAAI,CAACK,YAAY;EAAA;EAC9CC,KAAK,EAAE,SAAPA,KAAKA,CAAEf,KAAK;IAAA,OAAIA,KAAK,CAACS,IAAI,CAACM,KAAK;EAAA;EAChCC,WAAW,EAAE,SAAbA,WAAWA,CAAEhB,KAAK;IAAA,OAAIA,KAAK,CAACS,IAAI,CAACO,WAAW;EAAA;EAC5CC,iBAAiB,EAAE,SAAnBA,iBAAiBA,CAAEjB,KAAK;IAAA,OAAIA,KAAK,CAACkB,UAAU,CAACC,MAAM;EAAA;EACnDC,aAAa,EAAE,SAAfA,aAAaA,CAAEpB,KAAK;IAAA,OAAIA,KAAK,CAACkB,UAAU,CAACE,aAAa;EAAA;EACtDC,aAAa,EAAE,SAAfA,aAAaA,CAAErB,KAAK;IAAA,OAAIA,KAAK,CAACkB,UAAU,CAACG,aAAa;EAAA;EACtDC,cAAc,EAAE,SAAhBA,cAAcA,CAAEtB,KAAK;IAAA,OAAIA,KAAK,CAACkB,UAAU,CAACI,cAAc;EAAA;AAC1D,CAAC;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GACc3B,OAAO", "ignoreList": []}]}