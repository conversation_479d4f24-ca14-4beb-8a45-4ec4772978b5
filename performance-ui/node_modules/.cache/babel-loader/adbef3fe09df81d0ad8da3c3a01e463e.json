{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/system/user/profile/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/system/user/profile/index.vue", "mtime": 1753510684536}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCIvVXNlcnMvbWF6aWhhby9EZXNrdG9wL2Rldi9wZXJmb3JtYW5jZS9wZXJmb3JtYW5jZS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKdmFyIF91c2VyQXZhdGFyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIuL3VzZXJBdmF0YXIiKSk7CnZhciBfdXNlckluZm8gPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi4vdXNlckluZm8iKSk7CnZhciBfcmVzZXRQd2QgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi4vcmVzZXRQd2QiKSk7CnZhciBfdXNlciA9IHJlcXVpcmUoIkAvYXBpL3N5c3RlbS91c2VyIik7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAiUHJvZmlsZSIsCiAgY29tcG9uZW50czogewogICAgdXNlckF2YXRhcjogX3VzZXJBdmF0YXIuZGVmYXVsdCwKICAgIHVzZXJJbmZvOiBfdXNlckluZm8uZGVmYXVsdCwKICAgIHJlc2V0UHdkOiBfcmVzZXRQd2QuZGVmYXVsdAogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHVzZXI6IHt9LAogICAgICByb2xlR3JvdXA6IHt9LAogICAgICBwb3N0R3JvdXA6IHt9LAogICAgICBzZWxlY3RlZFRhYjogInVzZXJpbmZvIgogICAgfTsKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB2YXIgYWN0aXZlVGFiID0gdGhpcy4kcm91dGUucGFyYW1zICYmIHRoaXMuJHJvdXRlLnBhcmFtcy5hY3RpdmVUYWI7CiAgICBpZiAoYWN0aXZlVGFiKSB7CiAgICAgIHRoaXMuc2VsZWN0ZWRUYWIgPSBhY3RpdmVUYWI7CiAgICB9CiAgICB0aGlzLmdldFVzZXIoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGdldFVzZXI6IGZ1bmN0aW9uIGdldFVzZXIoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgICgwLCBfdXNlci5nZXRVc2VyUHJvZmlsZSkoKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzLnVzZXIgPSByZXNwb25zZS5kYXRhOwogICAgICAgIF90aGlzLnJvbGVHcm91cCA9IHJlc3BvbnNlLnJvbGVHcm91cDsKICAgICAgICBfdGhpcy5wb3N0R3JvdXAgPSByZXNwb25zZS5wb3N0R3JvdXA7CiAgICAgIH0pOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["_userAvatar", "_interopRequireDefault", "require", "_userInfo", "_resetPwd", "_user", "name", "components", "userAvatar", "userInfo", "resetPwd", "data", "user", "roleGroup", "postGroup", "selectedTab", "created", "activeTab", "$route", "params", "getUser", "methods", "_this", "getUserProfile", "then", "response"], "sources": ["src/views/system/user/profile/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"20\">\n      <el-col :span=\"6\" :xs=\"24\">\n        <el-card class=\"box-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>个人信息</span>\n          </div>\n          <div>\n            <div class=\"text-center\">\n              <userAvatar />\n            </div>\n            <ul class=\"list-group list-group-striped\">\n              <li class=\"list-group-item\">\n                <svg-icon icon-class=\"user\" />用户名称\n                <div class=\"pull-right\">{{ user.userName }}</div>\n              </li>\n              <li class=\"list-group-item\">\n                <svg-icon icon-class=\"phone\" />手机号码\n                <div class=\"pull-right\">{{ user.phonenumber }}</div>\n              </li>\n              <li class=\"list-group-item\">\n                <svg-icon icon-class=\"email\" />用户邮箱\n                <div class=\"pull-right\">{{ user.email }}</div>\n              </li>\n              <li class=\"list-group-item\">\n                <svg-icon icon-class=\"tree\" />所属部门\n                <div class=\"pull-right\" v-if=\"user.dept\">{{ user.dept.deptName }} / {{ postGroup }}</div>\n              </li>\n              <li class=\"list-group-item\">\n                <svg-icon icon-class=\"peoples\" />所属角色\n                <div class=\"pull-right\">{{ roleGroup }}</div>\n              </li>\n              <li class=\"list-group-item\">\n                <svg-icon icon-class=\"date\" />创建日期\n                <div class=\"pull-right\">{{ user.createTime }}</div>\n              </li>\n            </ul>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"18\" :xs=\"24\">\n        <el-card>\n          <div slot=\"header\" class=\"clearfix\">\n            <span>基本资料</span>\n          </div>\n          <el-tabs v-model=\"selectedTab\">\n            <el-tab-pane label=\"基本资料\" name=\"userinfo\">\n              <userInfo :user=\"user\" />\n            </el-tab-pane>\n            <el-tab-pane label=\"修改密码\" name=\"resetPwd\">\n              <resetPwd />\n            </el-tab-pane>\n          </el-tabs>\n        </el-card>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport userAvatar from \"./userAvatar\"\nimport userInfo from \"./userInfo\"\nimport resetPwd from \"./resetPwd\"\nimport { getUserProfile } from \"@/api/system/user\"\n\nexport default {\n  name: \"Profile\",\n  components: { userAvatar, userInfo, resetPwd },\n  data() {\n    return {\n      user: {},\n      roleGroup: {},\n      postGroup: {},\n      selectedTab: \"userinfo\"\n    }\n  },\n  created() {\n    const activeTab = this.$route.params && this.$route.params.activeTab\n    if (activeTab) {\n      this.selectedTab = activeTab\n    }\n    this.getUser()\n  },\n  methods: {\n    getUser() {\n      getUserProfile().then(response => {\n        this.user = response.data\n        this.roleGroup = response.roleGroup\n        this.postGroup = response.postGroup\n      })\n    }\n  }\n}\n</script>\n"], "mappings": ";;;;;;;AA6DA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,SAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,SAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAI,IAAA;EACAC,UAAA;IAAAC,UAAA,EAAAA,mBAAA;IAAAC,QAAA,EAAAA,iBAAA;IAAAC,QAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;MACAC,SAAA;MACAC,SAAA;MACAC,WAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,IAAAC,SAAA,QAAAC,MAAA,CAAAC,MAAA,SAAAD,MAAA,CAAAC,MAAA,CAAAF,SAAA;IACA,IAAAA,SAAA;MACA,KAAAF,WAAA,GAAAE,SAAA;IACA;IACA,KAAAG,OAAA;EACA;EACAC,OAAA;IACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,IAAAC,oBAAA,IAAAC,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAV,IAAA,GAAAa,QAAA,CAAAd,IAAA;QACAW,KAAA,CAAAT,SAAA,GAAAY,QAAA,CAAAZ,SAAA;QACAS,KAAA,CAAAR,SAAA,GAAAW,QAAA,CAAAX,SAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}