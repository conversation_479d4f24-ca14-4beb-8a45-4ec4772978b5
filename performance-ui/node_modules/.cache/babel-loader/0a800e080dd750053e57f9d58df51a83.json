{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/layout/components/Copyright/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/layout/components/Copyright/index.vue", "mtime": 1753510684529}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7Ci8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBjb21wdXRlZDogewogICAgdmlzaWJsZTogZnVuY3Rpb24gdmlzaWJsZSgpIHsKICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLnN0YXRlLnNldHRpbmdzLmZvb3RlclZpc2libGU7CiAgICB9LAogICAgY29udGVudDogZnVuY3Rpb24gY29udGVudCgpIHsKICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLnN0YXRlLnNldHRpbmdzLmZvb3RlckNvbnRlbnQ7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["computed", "visible", "$store", "state", "settings", "footerVisible", "content", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/layout/components/Copyright/index.vue"], "sourcesContent": ["<template>\n  <footer v-if=\"visible\" class=\"copyright\">\n    <span>{{ content }}</span>\n  </footer>\n</template>\n\n<script>\nexport default {\n  computed: {\n    visible() {\n      return this.$store.state.settings.footerVisible\n    },\n    content() {\n      return this.$store.state.settings.footerContent\n    }\n  }\n}\n</script>\n\n<style scoped>\n.copyright {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 36px;\n  padding: 10px 20px;\n  text-align: right;\n  background-color: #f8f8f8;\n  color: #666;\n  font-size: 14px;\n  border-top: 1px solid #e7e7e7;\n  z-index: 999;\n}\n</style>"], "mappings": ";;;;;;;;;;;;iCAOA;EACAA,QAAA;IACAC,OAAA,WAAAA,QAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,aAAA;IACA;IACAC,OAAA,WAAAA,QAAA;MACA,YAAAJ,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAG,aAAA;IACA;EACA;AACA", "ignoreList": []}]}