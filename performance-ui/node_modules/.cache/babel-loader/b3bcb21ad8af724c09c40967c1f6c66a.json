{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/themes/bubble.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/themes/bubble.js", "mtime": 1753510684098}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_lodashEs", "require", "_emitter", "_interopRequireDefault", "_base", "_interopRequireWildcard", "_selection", "_icons", "_quill", "TOOLBAR_CONFIG", "header", "BubbleTooltip", "exports", "_BaseTooltip", "quill", "bounds", "_this", "_classCallCheck2", "default", "_callSuper2", "on", "Emitter", "events", "EDITOR_CHANGE", "type", "range", "oldRange", "source", "SELECTION_CHANGE", "length", "sources", "USER", "show", "root", "style", "left", "width", "concat", "offsetWidth", "lines", "getLines", "index", "getBounds", "position", "lastLine", "getIndex", "Math", "min", "indexBounds", "Range", "document", "activeElement", "textbox", "hasFocus", "hide", "_inherits2", "_createClass2", "key", "value", "listen", "_this2", "_superPropGet2", "querySelector", "addEventListener", "classList", "remove", "SCROLL_OPTIMIZE", "setTimeout", "contains", "getSelection", "cancel", "reference", "shift", "arrow", "marginLeft", "BaseTooltip", "_defineProperty2", "join", "BubbleTheme", "_BaseTheme", "options", "_this3", "modules", "toolbar", "container", "add", "extendToolbar", "tooltip", "append<PERSON><PERSON><PERSON>", "buildButtons", "querySelectorAll", "icons", "buildPickers", "BaseTheme", "DEFAULTS", "merge", "handlers", "link", "format", "<PERSON><PERSON><PERSON>", "theme", "edit"], "sources": ["../../src/themes/bubble.ts"], "sourcesContent": ["import { merge } from 'lodash-es';\nimport Emitter from '../core/emitter.js';\nimport BaseTheme, { BaseTooltip } from './base.js';\nimport { Range } from '../core/selection.js';\nimport type { Bounds } from '../core/selection.js';\nimport icons from '../ui/icons.js';\nimport Quill from '../core/quill.js';\nimport type { ThemeOptions } from '../core/theme.js';\nimport type Toolbar from '../modules/toolbar.js';\nimport type { ToolbarConfig } from '../modules/toolbar.js';\n\nconst TOOLBAR_CONFIG: ToolbarConfig = [\n  ['bold', 'italic', 'link'],\n  [{ header: 1 }, { header: 2 }, 'blockquote'],\n];\n\nclass BubbleTooltip extends BaseTooltip {\n  static TEMPLATE = [\n    '<span class=\"ql-tooltip-arrow\"></span>',\n    '<div class=\"ql-tooltip-editor\">',\n    '<input type=\"text\" data-formula=\"e=mc^2\" data-link=\"https://quilljs.com\" data-video=\"Embed URL\">',\n    '<a class=\"ql-close\"></a>',\n    '</div>',\n  ].join('');\n\n  constructor(quill: Quill, bounds?: HTMLElement) {\n    super(quill, bounds);\n    this.quill.on(\n      Emitter.events.EDITOR_CHANGE,\n      (type, range, oldRange, source) => {\n        if (type !== Emitter.events.SELECTION_CHANGE) return;\n        if (\n          range != null &&\n          range.length > 0 &&\n          source === Emitter.sources.USER\n        ) {\n          this.show();\n          // Lock our width so we will expand beyond our offsetParent boundaries\n          this.root.style.left = '0px';\n          this.root.style.width = '';\n          this.root.style.width = `${this.root.offsetWidth}px`;\n          const lines = this.quill.getLines(range.index, range.length);\n          if (lines.length === 1) {\n            const bounds = this.quill.getBounds(range);\n            if (bounds != null) {\n              this.position(bounds);\n            }\n          } else {\n            const lastLine = lines[lines.length - 1];\n            const index = this.quill.getIndex(lastLine);\n            const length = Math.min(\n              lastLine.length() - 1,\n              range.index + range.length - index,\n            );\n            const indexBounds = this.quill.getBounds(new Range(index, length));\n            if (indexBounds != null) {\n              this.position(indexBounds);\n            }\n          }\n        } else if (\n          document.activeElement !== this.textbox &&\n          this.quill.hasFocus()\n        ) {\n          this.hide();\n        }\n      },\n    );\n  }\n\n  listen() {\n    super.listen();\n    // @ts-expect-error Fix me later\n    this.root.querySelector('.ql-close').addEventListener('click', () => {\n      this.root.classList.remove('ql-editing');\n    });\n    this.quill.on(Emitter.events.SCROLL_OPTIMIZE, () => {\n      // Let selection be restored by toolbar handlers before repositioning\n      setTimeout(() => {\n        if (this.root.classList.contains('ql-hidden')) return;\n        const range = this.quill.getSelection();\n        if (range != null) {\n          const bounds = this.quill.getBounds(range);\n          if (bounds != null) {\n            this.position(bounds);\n          }\n        }\n      }, 1);\n    });\n  }\n\n  cancel() {\n    this.show();\n  }\n\n  position(reference: Bounds) {\n    const shift = super.position(reference);\n    const arrow = this.root.querySelector('.ql-tooltip-arrow');\n    // @ts-expect-error\n    arrow.style.marginLeft = '';\n    if (shift !== 0) {\n      // @ts-expect-error\n      arrow.style.marginLeft = `${-1 * shift - arrow.offsetWidth / 2}px`;\n    }\n    return shift;\n  }\n}\n\nclass BubbleTheme extends BaseTheme {\n  tooltip: BubbleTooltip;\n\n  constructor(quill: Quill, options: ThemeOptions) {\n    if (\n      options.modules.toolbar != null &&\n      options.modules.toolbar.container == null\n    ) {\n      options.modules.toolbar.container = TOOLBAR_CONFIG;\n    }\n    super(quill, options);\n    this.quill.container.classList.add('ql-bubble');\n  }\n\n  extendToolbar(toolbar: Toolbar) {\n    // @ts-expect-error\n    this.tooltip = new BubbleTooltip(this.quill, this.options.bounds);\n    if (toolbar.container != null) {\n      this.tooltip.root.appendChild<HTMLElement>(toolbar.container);\n      this.buildButtons(toolbar.container.querySelectorAll('button'), icons);\n      this.buildPickers(toolbar.container.querySelectorAll('select'), icons);\n    }\n  }\n}\nBubbleTheme.DEFAULTS = merge({}, BaseTheme.DEFAULTS, {\n  modules: {\n    toolbar: {\n      handlers: {\n        link(value: string) {\n          if (!value) {\n            this.quill.format('link', false, Quill.sources.USER);\n          } else {\n            // @ts-expect-error\n            this.quill.theme.tooltip.edit();\n          }\n        },\n      },\n    },\n  },\n} satisfies ThemeOptions);\n\nexport { BubbleTooltip, BubbleTheme as default };\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,KAAA,GAAAC,uBAAA,CAAAJ,OAAA;AACA,IAAAK,UAAA,GAAAL,OAAA;AAEA,IAAAM,MAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,MAAA,GAAAL,sBAAA,CAAAF,OAAA;AAKA,IAAMQ,cAA6B,GAAG,CACpC,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC,EAC1B,CAAC;EAAEC,MAAM,EAAE;AAAE,CAAC,EAAE;EAAEA,MAAM,EAAE;AAAE,CAAC,EAAE,YAAY,CAAC,CAC7C;AAAA,IAEKC,aAAa,GAAAC,OAAA,CAAAD,aAAA,0BAAAE,YAAA;EASjB,SAAAF,cAAYG,KAAY,EAAEC,MAAoB,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAP,aAAA;IAC9CK,KAAA,OAAAG,WAAA,CAAAD,OAAA,QAAAP,aAAA,GAAMG,KAAK,EAAEC,MAAM;IACnBC,KAAA,CAAKF,KAAK,CAACM,EAAE,CACXC,gBAAO,CAACC,MAAM,CAACC,aAAa,EAC5B,UAACC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAK;MACjC,IAAIH,IAAI,KAAKH,gBAAO,CAACC,MAAM,CAACM,gBAAgB,EAAE;MAC9C,IACEH,KAAK,IAAI,IAAI,IACbA,KAAK,CAACI,MAAM,GAAG,CAAC,IAChBF,MAAM,KAAKN,gBAAO,CAACS,OAAO,CAACC,IAAI,EAC/B;QACAf,KAAA,CAAKgB,IAAI,CAAC,CAAC;QACX;QACAhB,KAAA,CAAKiB,IAAI,CAACC,KAAK,CAACC,IAAI,GAAG,KAAK;QAC5BnB,KAAA,CAAKiB,IAAI,CAACC,KAAK,CAACE,KAAK,GAAG,EAAE;QAC1BpB,KAAA,CAAKiB,IAAI,CAACC,KAAK,CAACE,KAAK,MAAAC,MAAA,CAAMrB,KAAA,CAAKiB,IAAI,CAACK,WAAY,OAAG;QACpD,IAAMC,KAAK,GAAGvB,KAAA,CAAKF,KAAK,CAAC0B,QAAQ,CAACf,KAAK,CAACgB,KAAK,EAAEhB,KAAK,CAACI,MAAM,CAAC;QAC5D,IAAIU,KAAK,CAACV,MAAM,KAAK,CAAC,EAAE;UACtB,IAAMd,OAAM,GAAGC,KAAA,CAAKF,KAAK,CAAC4B,SAAS,CAACjB,KAAK,CAAC;UAC1C,IAAIV,OAAM,IAAI,IAAI,EAAE;YAClBC,KAAA,CAAK2B,QAAQ,CAAC5B,OAAM,CAAC;UACvB;QACF,CAAC,MAAM;UACL,IAAM6B,QAAQ,GAAGL,KAAK,CAACA,KAAK,CAACV,MAAM,GAAG,CAAC,CAAC;UACxC,IAAMY,KAAK,GAAGzB,KAAA,CAAKF,KAAK,CAAC+B,QAAQ,CAACD,QAAQ,CAAC;UAC3C,IAAMf,MAAM,GAAGiB,IAAI,CAACC,GAAG,CACrBH,QAAQ,CAACf,MAAM,CAAC,CAAC,GAAG,CAAC,EACrBJ,KAAK,CAACgB,KAAK,GAAGhB,KAAK,CAACI,MAAM,GAAGY,KAC/B,CAAC;UACD,IAAMO,WAAW,GAAGhC,KAAA,CAAKF,KAAK,CAAC4B,SAAS,CAAC,IAAIO,gBAAK,CAACR,KAAK,EAAEZ,MAAM,CAAC,CAAC;UAClE,IAAImB,WAAW,IAAI,IAAI,EAAE;YACvBhC,KAAA,CAAK2B,QAAQ,CAACK,WAAW,CAAC;UAC5B;QACF;MACF,CAAC,MAAM,IACLE,QAAQ,CAACC,aAAa,KAAKnC,KAAA,CAAKoC,OAAO,IACvCpC,KAAA,CAAKF,KAAK,CAACuC,QAAQ,CAAC,CAAC,EACrB;QACArC,KAAA,CAAKsC,IAAI,CAAC,CAAC;MACb;IACF,CACF,CAAC;IAAA,OAAAtC,KAAA;EACH;EAAA,IAAAuC,UAAA,CAAArC,OAAA,EAAAP,aAAA,EAAAE,YAAA;EAAA,WAAA2C,aAAA,CAAAtC,OAAA,EAAAP,aAAA;IAAA8C,GAAA;IAAAC,KAAA,EAEA,SAAAC,MAAMA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACP,IAAAC,cAAA,CAAA3C,OAAA,EAAAP,aAAA;MACA;MACA,IAAI,CAACsB,IAAI,CAAC6B,aAAa,CAAC,WAAW,CAAC,CAACC,gBAAgB,CAAC,OAAO,EAAE,YAAM;QACnEH,MAAI,CAAC3B,IAAI,CAAC+B,SAAS,CAACC,MAAM,CAAC,YAAY,CAAC;MAC1C,CAAC,CAAC;MACF,IAAI,CAACnD,KAAK,CAACM,EAAE,CAACC,gBAAO,CAACC,MAAM,CAAC4C,eAAe,EAAE,YAAM;QAClD;QACAC,UAAU,CAAC,YAAM;UACf,IAAIP,MAAI,CAAC3B,IAAI,CAAC+B,SAAS,CAACI,QAAQ,CAAC,WAAW,CAAC,EAAE;UAC/C,IAAM3C,KAAK,GAAGmC,MAAI,CAAC9C,KAAK,CAACuD,YAAY,CAAC,CAAC;UACvC,IAAI5C,KAAK,IAAI,IAAI,EAAE;YACjB,IAAMV,MAAM,GAAG6C,MAAI,CAAC9C,KAAK,CAAC4B,SAAS,CAACjB,KAAK,CAAC;YAC1C,IAAIV,MAAM,IAAI,IAAI,EAAE;cAClB6C,MAAI,CAACjB,QAAQ,CAAC5B,MAAM,CAAC;YACvB;UACF;QACF,CAAC,EAAE,CAAC,CAAC;MACP,CAAC,CAAC;IACJ;EAAA;IAAA0C,GAAA;IAAAC,KAAA,EAEA,SAAAY,MAAMA,CAAA,EAAG;MACP,IAAI,CAACtC,IAAI,CAAC,CAAC;IACb;EAAA;IAAAyB,GAAA;IAAAC,KAAA,EAEA,SAAAf,QAAQA,CAAC4B,SAAiB,EAAE;MAC1B,IAAMC,KAAK,OAAAX,cAAA,CAAA3C,OAAA,EAAAP,aAAA,wBAAkB4D,SAAS,EAAC;MACvC,IAAME,KAAK,GAAG,IAAI,CAACxC,IAAI,CAAC6B,aAAa,CAAC,mBAAmB,CAAC;MAC1D;MACAW,KAAK,CAACvC,KAAK,CAACwC,UAAU,GAAG,EAAE;MAC3B,IAAIF,KAAK,KAAK,CAAC,EAAE;QACf;QACAC,KAAK,CAACvC,KAAK,CAACwC,UAAU,MAAArC,MAAA,CAAM,CAAC,CAAC,GAAGmC,KAAK,GAAGC,KAAK,CAACnC,WAAW,GAAG,CAAE,OAAG;MACpE;MACA,OAAOkC,KAAK;IACd;EAAA;AAAA,EAxF0BG,iBAAW;AAAA,IAAAC,gBAAA,CAAA1D,OAAA,EAAjCP,aAAa,cACC,CAChB,wCAAwC,EACxC,iCAAiC,EACjC,kGAAkG,EAClG,0BAA0B,EAC1B,QAAQ,CACT,CAACkE,IAAI,CAAC,EAAE,CAAC;AAAA,IAoFNC,WAAW,GAAAlE,OAAA,CAAAM,OAAA,0BAAA6D,UAAA;EAGf,SAAAD,YAAYhE,KAAY,EAAEkE,OAAqB,EAAE;IAAA,IAAAC,MAAA;IAAA,IAAAhE,gBAAA,CAAAC,OAAA,QAAA4D,WAAA;IAC/C,IACEE,OAAO,CAACE,OAAO,CAACC,OAAO,IAAI,IAAI,IAC/BH,OAAO,CAACE,OAAO,CAACC,OAAO,CAACC,SAAS,IAAI,IAAI,EACzC;MACAJ,OAAO,CAACE,OAAO,CAACC,OAAO,CAACC,SAAS,GAAG3E,cAAc;IACpD;IACAwE,MAAA,OAAA9D,WAAA,CAAAD,OAAA,QAAA4D,WAAA,GAAMhE,KAAK,EAAEkE,OAAO;IACpBC,MAAA,CAAKnE,KAAK,CAACsE,SAAS,CAACpB,SAAS,CAACqB,GAAG,CAAC,WAAW,CAAC;IAAA,OAAAJ,MAAA;EACjD;EAAA,IAAA1B,UAAA,CAAArC,OAAA,EAAA4D,WAAA,EAAAC,UAAA;EAAA,WAAAvB,aAAA,CAAAtC,OAAA,EAAA4D,WAAA;IAAArB,GAAA;IAAAC,KAAA,EAEA,SAAA4B,aAAaA,CAACH,OAAgB,EAAE;MAC9B;MACA,IAAI,CAACI,OAAO,GAAG,IAAI5E,aAAa,CAAC,IAAI,CAACG,KAAK,EAAE,IAAI,CAACkE,OAAO,CAACjE,MAAM,CAAC;MACjE,IAAIoE,OAAO,CAACC,SAAS,IAAI,IAAI,EAAE;QAC7B,IAAI,CAACG,OAAO,CAACtD,IAAI,CAACuD,WAAW,CAAcL,OAAO,CAACC,SAAS,CAAC;QAC7D,IAAI,CAACK,YAAY,CAACN,OAAO,CAACC,SAAS,CAACM,gBAAgB,CAAC,QAAQ,CAAC,EAAEC,cAAK,CAAC;QACtE,IAAI,CAACC,YAAY,CAACT,OAAO,CAACC,SAAS,CAACM,gBAAgB,CAAC,QAAQ,CAAC,EAAEC,cAAK,CAAC;MACxE;IACF;EAAA;AAAA,EAtBwBE,aAAS;AAwBnCf,WAAW,CAACgB,QAAQ,GAAG,IAAAC,eAAK,EAAC,CAAC,CAAC,EAAEF,aAAS,CAACC,QAAQ,EAAE;EACnDZ,OAAO,EAAE;IACPC,OAAO,EAAE;MACPa,QAAQ,EAAE;QACRC,IAAI,WAAJA,IAAIA,CAACvC,KAAa,EAAE;UAClB,IAAI,CAACA,KAAK,EAAE;YACV,IAAI,CAAC5C,KAAK,CAACoF,MAAM,CAAC,MAAM,EAAE,KAAK,EAAEC,cAAK,CAACrE,OAAO,CAACC,IAAI,CAAC;UACtD,CAAC,MAAM;YACL;YACA,IAAI,CAACjB,KAAK,CAACsF,KAAK,CAACb,OAAO,CAACc,IAAI,CAAC,CAAC;UACjC;QACF;MACF;IACF;EACF;AACF,CAAwB,CAAC", "ignoreList": []}]}