{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/performance/yearly.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/performance/yearly.js", "mtime": 1753510684517}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listYearly", "query", "request", "url", "method", "params", "get<PERSON>early", "id", "addYearly", "data", "update<PERSON>early", "<PERSON><PERSON><PERSON><PERSON>", "ids", "exportYearly", "responseType", "downloadTemplateYearly", "downloadExcelTemplateYearly"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/performance/yearly.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询年度绩效列表\nexport function listYearly(query) {\n  return request({\n    url: '/performance/yearly/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询年度绩效详细\nexport function getYearly(id) {\n  return request({\n    url: '/performance/yearly/' + id,\n    method: 'get'\n  })\n}\n\n// 新增年度绩效\nexport function addYearly(data) {\n  return request({\n    url: '/performance/yearly',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改年度绩效\nexport function updateYearly(data) {\n  return request({\n    url: '/performance/yearly',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除年度绩效\nexport function delYearly(ids) {\n  return request({\n    url: '/performance/yearly/' + ids,\n    method: 'delete'\n  })\n}\n\n// 导出年度绩效\nexport function exportYearly(ids) {\n  return request({\n    url: '/performance/yearly/export',\n    method: 'post',\n    data: ids,\n    responseType: 'blob'\n  })\n}\n\n// 下载年度绩效Word模板\nexport function downloadTemplateYearly() {\n    return request({\n        url: '/performance/yearly/importTemplate',\n        method: 'get',\n        responseType: 'blob'\n    });\n}\n\n// 下载年度绩效Excel模板\nexport function downloadExcelTemplateYearly() {\n    return request({\n        url: '/performance/yearly/importExcelTemplate',\n        method: 'get',\n        responseType: 'blob'\n    });\n} "], "mappings": ";;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,UAAUA,CAACC,KAAK,EAAE;EAChC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,SAASA,CAACC,EAAE,EAAE;EAC5B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB,GAAGI,EAAE;IAChCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,SAASA,CAACC,IAAI,EAAE;EAC9B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,YAAYA,CAACD,IAAI,EAAE;EACjC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,SAASA,CAACC,GAAG,EAAE;EAC7B,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB,GAAGS,GAAG;IACjCR,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,YAAYA,CAACD,GAAG,EAAE;EAChC,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEG,GAAG;IACTE,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,sBAAsBA,CAAA,EAAG;EACrC,OAAO,IAAAb,gBAAO,EAAC;IACXC,GAAG,EAAE,oCAAoC;IACzCC,MAAM,EAAE,KAAK;IACbU,YAAY,EAAE;EAClB,CAAC,CAAC;AACN;;AAEA;AACO,SAASE,2BAA2BA,CAAA,EAAG;EAC1C,OAAO,IAAAd,gBAAO,EAAC;IACXC,GAAG,EAAE,yCAAyC;IAC9CC,MAAM,EAAE,KAAK;IACbU,YAAY,EAAE;EAClB,CAAC,CAAC;AACN", "ignoreList": []}]}