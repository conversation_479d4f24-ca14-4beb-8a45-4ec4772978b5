{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/formats/formula.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/formats/formula.js", "mtime": 1753510684093}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_embed", "_interopRequireDefault", "require", "Formula", "_Embed", "_classCallCheck2", "default", "_callSuper2", "arguments", "_inherits2", "_createClass2", "key", "value", "html", "_this$value", "formula", "concat", "create", "window", "katex", "Error", "node", "_superPropGet2", "render", "throwOnError", "errorColor", "setAttribute", "domNode", "getAttribute", "Embed", "_defineProperty2", "_default", "exports"], "sources": ["../../src/formats/formula.ts"], "sourcesContent": ["import Embed from '../blots/embed.js';\n\nclass Formula extends Embed {\n  static blotName = 'formula';\n  static className = 'ql-formula';\n  static tagName = 'SPAN';\n\n  static create(value: string) {\n    // @ts-expect-error\n    if (window.katex == null) {\n      throw new Error('Formula module requires KaTeX.');\n    }\n    const node = super.create(value) as Element;\n    if (typeof value === 'string') {\n      // @ts-expect-error\n      window.katex.render(value, node, {\n        throwOnError: false,\n        errorColor: '#f00',\n      });\n      node.setAttribute('data-value', value);\n    }\n    return node;\n  }\n\n  static value(domNode: Element) {\n    return domNode.getAttribute('data-value');\n  }\n\n  html() {\n    const { formula } = this.value();\n    return `<span>${formula}</span>`;\n  }\n}\n\nexport default Formula;\n"], "mappings": ";;;;;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAqC,IAE/BC,OAAO,0BAAAC,MAAA;EAAA,SAAAD,QAAA;IAAA,IAAAE,gBAAA,CAAAC,OAAA,QAAAH,OAAA;IAAA,WAAAI,WAAA,CAAAD,OAAA,QAAAH,OAAA,EAAAK,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAH,OAAA,EAAAC,MAAA;EAAA,WAAAM,aAAA,CAAAJ,OAAA,EAAAH,OAAA;IAAAQ,GAAA;IAAAC,KAAA,EA0BX,SAAAC,IAAIA,CAAA,EAAG;MACL,IAAAC,WAAA,GAAoB,IAAI,CAACF,KAAK,CAAC,CAAC;QAAxBG,OAAA,GAAAD,WAAA,CAAAC,OAAA;MACR,gBAAAC,MAAA,CAAgBD,OAAQ;IAC1B;EAAA;IAAAJ,GAAA;IAAAC,KAAA,EAxBA,SAAOK,MAAMA,CAACL,KAAa,EAAE;MAC3B;MACA,IAAIM,MAAM,CAACC,KAAK,IAAI,IAAI,EAAE;QACxB,MAAM,IAAIC,KAAK,CAAC,gCAAgC,CAAC;MACnD;MACA,IAAMC,IAAI,OAAAC,cAAA,CAAAhB,OAAA,EAAAH,OAAA,sBAAgBS,KAAK,EAAY;MAC3C,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B;QACAM,MAAM,CAACC,KAAK,CAACI,MAAM,CAACX,KAAK,EAAES,IAAI,EAAE;UAC/BG,YAAY,EAAE,KAAK;UACnBC,UAAU,EAAE;QACd,CAAC,CAAC;QACFJ,IAAI,CAACK,YAAY,CAAC,YAAY,EAAEd,KAAK,CAAC;MACxC;MACA,OAAOS,IAAI;IACb;EAAA;IAAAV,GAAA;IAAAC,KAAA,EAEA,SAAOA,KAAKA,CAACe,OAAgB,EAAE;MAC7B,OAAOA,OAAO,CAACC,YAAY,CAAC,YAAY,CAAC;IAC3C;EAAA;AAAA,EAxBoBC,cAAK;AAAA,IAAAC,gBAAA,CAAAxB,OAAA,EAArBH,OAAO,cACO,SAAS;AAAA,IAAA2B,gBAAA,CAAAxB,OAAA,EADvBH,OAAO,eAEQ,YAAY;AAAA,IAAA2B,gBAAA,CAAAxB,OAAA,EAF3BH,OAAO,aAGM,MAAM;AAAA,IAAA4B,QAAA,GAAAC,OAAA,CAAA1B,OAAA,GA6BVH,OAAO", "ignoreList": []}]}