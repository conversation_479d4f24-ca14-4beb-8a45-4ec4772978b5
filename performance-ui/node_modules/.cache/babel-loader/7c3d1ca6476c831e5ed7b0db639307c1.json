{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/blots/embed.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/blots/embed.js", "mtime": 1753510684085}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_parchment", "require", "_text", "_interopRequireDefault", "GUARD_TEXT", "Embed", "_EmbedBlot", "scroll", "node", "_this", "_classCallCheck2", "default", "_callSuper2", "contentNode", "document", "createElement", "setAttribute", "Array", "from", "domNode", "childNodes", "for<PERSON>ach", "childNode", "append<PERSON><PERSON><PERSON>", "leftGuard", "createTextNode", "<PERSON><PERSON><PERSON>", "_inherits2", "_createClass2", "key", "value", "index", "offset", "_superPropGet2", "restore", "range", "textNode", "text", "data", "split", "join", "prev", "TextBlot", "prevLength", "length", "insertAt", "startNode", "startOffset", "parent", "insertBefore", "create", "next", "update", "mutations", "context", "_this2", "mutation", "type", "target", "EmbedBlot", "_default", "exports"], "sources": ["../../src/blots/embed.ts"], "sourcesContent": ["import type { ScrollBlot } from 'parchment';\nimport { EmbedBlot } from 'parchment';\nimport TextBlot from './text.js';\n\nconst GUARD_TEXT = '\\uFEFF';\n\nexport interface EmbedContextRange {\n  startNode: Node | Text;\n  startOffset: number;\n  endNode?: Node | Text;\n  endOffset?: number;\n}\n\nclass Embed extends EmbedBlot {\n  contentNode: HTMLSpanElement;\n  leftGuard: Text;\n  rightGuard: Text;\n\n  constructor(scroll: ScrollBlot, node: Node) {\n    super(scroll, node);\n    this.contentNode = document.createElement('span');\n    this.contentNode.setAttribute('contenteditable', 'false');\n    Array.from(this.domNode.childNodes).forEach((childNode) => {\n      this.contentNode.appendChild(childNode);\n    });\n    this.leftGuard = document.createTextNode(GUARD_TEXT);\n    this.rightGuard = document.createTextNode(GUARD_TEXT);\n    this.domNode.appendChild(this.leftGuard);\n    this.domNode.appendChild(this.contentNode);\n    this.domNode.appendChild(this.rightGuard);\n  }\n\n  index(node: Node, offset: number) {\n    if (node === this.leftGuard) return 0;\n    if (node === this.rightGuard) return 1;\n    return super.index(node, offset);\n  }\n\n  restore(node: Text): EmbedContextRange | null {\n    let range: EmbedContextRange | null = null;\n    let textNode: Text;\n    const text = node.data.split(GUARD_TEXT).join('');\n    if (node === this.leftGuard) {\n      if (this.prev instanceof TextBlot) {\n        const prevLength = this.prev.length();\n        this.prev.insertAt(prevLength, text);\n        range = {\n          startNode: this.prev.domNode,\n          startOffset: prevLength + text.length,\n        };\n      } else {\n        textNode = document.createTextNode(text);\n        this.parent.insertBefore(this.scroll.create(textNode), this);\n        range = {\n          startNode: textNode,\n          startOffset: text.length,\n        };\n      }\n    } else if (node === this.rightGuard) {\n      if (this.next instanceof TextBlot) {\n        this.next.insertAt(0, text);\n        range = {\n          startNode: this.next.domNode,\n          startOffset: text.length,\n        };\n      } else {\n        textNode = document.createTextNode(text);\n        this.parent.insertBefore(this.scroll.create(textNode), this.next);\n        range = {\n          startNode: textNode,\n          startOffset: text.length,\n        };\n      }\n    }\n    node.data = GUARD_TEXT;\n    return range;\n  }\n\n  update(mutations: MutationRecord[], context: Record<string, unknown>) {\n    mutations.forEach((mutation) => {\n      if (\n        mutation.type === 'characterData' &&\n        (mutation.target === this.leftGuard ||\n          mutation.target === this.rightGuard)\n      ) {\n        const range = this.restore(mutation.target as Text);\n        if (range) context.range = range;\n      }\n    });\n  }\n}\n\nexport default Embed;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AACA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAC,sBAAA,CAAAF,OAAA;AAEA,IAAMG,UAAU,GAAG,QAAQ;AAAA,IASrBC,KAAK,0BAAAC,UAAA;EAKT,SAAAD,MAAYE,MAAkB,EAAEC,IAAU,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAN,KAAA;IAC1CI,KAAA,OAAAG,WAAA,CAAAD,OAAA,QAAAN,KAAA,GAAME,MAAM,EAAEC,IAAI;IAClBC,KAAA,CAAKI,WAAW,GAAGC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IACjDN,KAAA,CAAKI,WAAW,CAACG,YAAY,CAAC,iBAAiB,EAAE,OAAO,CAAC;IACzDC,KAAK,CAACC,IAAI,CAACT,KAAA,CAAKU,OAAO,CAACC,UAAU,CAAC,CAACC,OAAO,CAAE,UAAAC,SAAS,EAAK;MACzDb,KAAA,CAAKI,WAAW,CAACU,WAAW,CAACD,SAAS,CAAC;IACzC,CAAC,CAAC;IACFb,KAAA,CAAKe,SAAS,GAAGV,QAAQ,CAACW,cAAc,CAACrB,UAAU,CAAC;IACpDK,KAAA,CAAKiB,UAAU,GAAGZ,QAAQ,CAACW,cAAc,CAACrB,UAAU,CAAC;IACrDK,KAAA,CAAKU,OAAO,CAACI,WAAW,CAACd,KAAA,CAAKe,SAAS,CAAC;IACxCf,KAAA,CAAKU,OAAO,CAACI,WAAW,CAACd,KAAA,CAAKI,WAAW,CAAC;IAC1CJ,KAAA,CAAKU,OAAO,CAACI,WAAW,CAACd,KAAA,CAAKiB,UAAU,CAAC;IAAA,OAAAjB,KAAA;EAC3C;EAAA,IAAAkB,UAAA,CAAAhB,OAAA,EAAAN,KAAA,EAAAC,UAAA;EAAA,WAAAsB,aAAA,CAAAjB,OAAA,EAAAN,KAAA;IAAAwB,GAAA;IAAAC,KAAA,EAEA,SAAAC,KAAKA,CAACvB,IAAU,EAAEwB,MAAc,EAAE;MAChC,IAAIxB,IAAI,KAAK,IAAI,CAACgB,SAAS,EAAE,OAAO,CAAC;MACrC,IAAIhB,IAAI,KAAK,IAAI,CAACkB,UAAU,EAAE,OAAO,CAAC;MACtC,WAAAO,cAAA,CAAAtB,OAAA,EAAAN,KAAA,qBAAmBG,IAAI,EAAEwB,MAAM;IACjC;EAAA;IAAAH,GAAA;IAAAC,KAAA,EAEA,SAAAI,OAAOA,CAAC1B,IAAU,EAA4B;MAC5C,IAAI2B,KAA+B,GAAG,IAAI;MAC1C,IAAIC,QAAc;MAClB,IAAMC,IAAI,GAAG7B,IAAI,CAAC8B,IAAI,CAACC,KAAK,CAACnC,UAAU,CAAC,CAACoC,IAAI,CAAC,EAAE,CAAC;MACjD,IAAIhC,IAAI,KAAK,IAAI,CAACgB,SAAS,EAAE;QAC3B,IAAI,IAAI,CAACiB,IAAI,YAAYC,aAAQ,EAAE;UACjC,IAAMC,UAAU,GAAG,IAAI,CAACF,IAAI,CAACG,MAAM,CAAC,CAAC;UACrC,IAAI,CAACH,IAAI,CAACI,QAAQ,CAACF,UAAU,EAAEN,IAAI,CAAC;UACpCF,KAAK,GAAG;YACNW,SAAS,EAAE,IAAI,CAACL,IAAI,CAACtB,OAAO;YAC5B4B,WAAW,EAAEJ,UAAU,GAAGN,IAAI,CAACO;UACjC,CAAC;QACH,CAAC,MAAM;UACLR,QAAQ,GAAGtB,QAAQ,CAACW,cAAc,CAACY,IAAI,CAAC;UACxC,IAAI,CAACW,MAAM,CAACC,YAAY,CAAC,IAAI,CAAC1C,MAAM,CAAC2C,MAAM,CAACd,QAAQ,CAAC,EAAE,IAAI,CAAC;UAC5DD,KAAK,GAAG;YACNW,SAAS,EAAEV,QAAQ;YACnBW,WAAW,EAAEV,IAAI,CAACO;UACpB,CAAC;QACH;MACF,CAAC,MAAM,IAAIpC,IAAI,KAAK,IAAI,CAACkB,UAAU,EAAE;QACnC,IAAI,IAAI,CAACyB,IAAI,YAAYT,aAAQ,EAAE;UACjC,IAAI,CAACS,IAAI,CAACN,QAAQ,CAAC,CAAC,EAAER,IAAI,CAAC;UAC3BF,KAAK,GAAG;YACNW,SAAS,EAAE,IAAI,CAACK,IAAI,CAAChC,OAAO;YAC5B4B,WAAW,EAAEV,IAAI,CAACO;UACpB,CAAC;QACH,CAAC,MAAM;UACLR,QAAQ,GAAGtB,QAAQ,CAACW,cAAc,CAACY,IAAI,CAAC;UACxC,IAAI,CAACW,MAAM,CAACC,YAAY,CAAC,IAAI,CAAC1C,MAAM,CAAC2C,MAAM,CAACd,QAAQ,CAAC,EAAE,IAAI,CAACe,IAAI,CAAC;UACjEhB,KAAK,GAAG;YACNW,SAAS,EAAEV,QAAQ;YACnBW,WAAW,EAAEV,IAAI,CAACO;UACpB,CAAC;QACH;MACF;MACApC,IAAI,CAAC8B,IAAI,GAAGlC,UAAU;MACtB,OAAO+B,KAAK;IACd;EAAA;IAAAN,GAAA;IAAAC,KAAA,EAEA,SAAAsB,MAAMA,CAACC,SAA2B,EAAEC,OAAgC,EAAE;MAAA,IAAAC,MAAA;MACpEF,SAAS,CAAChC,OAAO,CAAE,UAAAmC,QAAQ,EAAK;QAC9B,IACEA,QAAQ,CAACC,IAAI,KAAK,eAAe,KAChCD,QAAQ,CAACE,MAAM,KAAKH,MAAI,CAAC/B,SAAS,IACjCgC,QAAQ,CAACE,MAAM,KAAKH,MAAI,CAAC7B,UAAU,CAAC,EACtC;UACA,IAAMS,KAAK,GAAGoB,MAAI,CAACrB,OAAO,CAACsB,QAAQ,CAACE,MAAc,CAAC;UACnD,IAAIvB,KAAK,EAAEmB,OAAO,CAACnB,KAAK,GAAGA,KAAK;QAClC;MACF,CAAC,CAAC;IACJ;EAAA;AAAA,EA5EkBwB,oBAAS;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAlD,OAAA,GA+EdN,KAAK", "ignoreList": []}]}