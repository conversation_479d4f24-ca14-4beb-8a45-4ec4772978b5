{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/store/modules/permission.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/store/modules/permission.js", "mtime": 1753510684531}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_auth", "_interopRequireDefault", "require", "_router", "_interopRequireWildcard3", "_menu", "_index", "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "_InnerLink", "permission", "state", "routes", "addRoutes", "defaultRoutes", "topbarRouters", "sidebarRouters", "mutations", "SET_ROUTES", "constantRoutes", "concat", "SET_DEFAULT_ROUTES", "SET_TOPBAR_ROUTES", "SET_SIDEBAR_ROUTERS", "actions", "GenerateRoutes", "_ref", "commit", "Promise", "resolve", "getRouters", "then", "res", "sdata", "JSON", "parse", "stringify", "data", "rdata", "sidebarRoutes", "filterAsyncRouter", "rewriteRoutes", "asyncRoutes", "filterDynamicRoutes", "dynamicRoutes", "push", "path", "redirect", "hidden", "router", "console", "log", "asyncRouterMap", "lastRouter", "arguments", "length", "undefined", "type", "filter", "route", "children", "filterChildren", "component", "Layout", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "InnerLink", "loadView", "childrenMap", "for<PERSON>ach", "el", "permissions", "auth", "hasPermiOr", "roles", "hasRoleOr", "exports", "view", "process", "env", "NODE_ENV", "specifier", "r", "s", "_interopRequireWildcard2", "default", "_default"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/store/modules/permission.js"], "sourcesContent": ["import auth from '@/plugins/auth'\nimport router, { constantRoutes, dynamicRoutes } from '@/router'\nimport { getRouters } from '@/api/menu'\nimport Layout from '@/layout/index'\nimport ParentView from '@/components/ParentView'\nimport InnerLink from '@/layout/components/InnerLink'\n\nconst permission = {\n  state: {\n    routes: [],\n    addRoutes: [],\n    defaultRoutes: [],\n    topbarRouters: [],\n    sidebarRouters: []\n  },\n  mutations: {\n    SET_ROUTES: (state, routes) => {\n      state.addRoutes = routes\n      state.routes = constantRoutes.concat(routes)\n    },\n    SET_DEFAULT_ROUTES: (state, routes) => {\n      state.defaultRoutes = constantRoutes.concat(routes)\n    },\n    SET_TOPBAR_ROUTES: (state, routes) => {\n      state.topbarRouters = routes\n    },\n    SET_SIDEBAR_ROUTERS: (state, routes) => {\n      state.sidebarRouters = routes\n    },\n  },\n  actions: {\n    // 生成路由\n    GenerateRoutes({ commit }) {\n      return new Promise(resolve => {\n        // 向后端请求路由数据\n        getRouters().then(res => {\n          const sdata = JSON.parse(JSON.stringify(res.data))\n          const rdata = JSON.parse(JSON.stringify(res.data))\n          const sidebarRoutes = filterAsyncRouter(sdata)\n          const rewriteRoutes = filterAsyncRouter(rdata, false, true)\n          const asyncRoutes = filterDynamicRoutes(dynamicRoutes)\n          rewriteRoutes.push({ path: '*', redirect: '/404', hidden: true })\n          router.addRoutes(asyncRoutes)\n          commit('SET_ROUTES', rewriteRoutes)\n          commit('SET_SIDEBAR_ROUTERS', sidebarRoutes)\n          console.log('最终sidebarRouters数据:', constantRoutes.concat(sidebarRoutes))\n          commit('SET_DEFAULT_ROUTES', sidebarRoutes)\n          commit('SET_TOPBAR_ROUTES', sidebarRoutes)\n          resolve(rewriteRoutes)\n        })\n      })\n    }\n  }\n}\n\n// 遍历后台传来的路由字符串，转换为组件对象\nfunction filterAsyncRouter(asyncRouterMap, lastRouter = false, type = false) {\n  return asyncRouterMap.filter(route => {\n    if (type && route.children) {\n      route.children = filterChildren(route.children)\n    }\n    if (route.component) {\n      // Layout ParentView 组件特殊处理\n      if (route.component === 'Layout') {\n        route.component = Layout\n      } else if (route.component === 'ParentView') {\n        route.component = ParentView\n      } else if (route.component === 'InnerLink') {\n        route.component = InnerLink\n      } else {\n        route.component = loadView(route.component)\n      }\n    }\n    if (route.children != null && route.children && route.children.length) {\n      route.children = filterAsyncRouter(route.children, route, type)\n    } else {\n      delete route['children']\n      delete route['redirect']\n    }\n    return true\n  })\n}\n\nfunction filterChildren(childrenMap, lastRouter = false) {\n  var children = []\n  childrenMap.forEach(el => {\n    el.path = lastRouter ? lastRouter.path + '/' + el.path : el.path\n    if (el.children && el.children.length && el.component === 'ParentView') {\n      children = children.concat(filterChildren(el.children, el))\n    } else {\n      children.push(el)\n    }\n  })\n  return children\n}\n\n// 动态路由遍历，验证是否具备权限\nexport function filterDynamicRoutes(routes) {\n  const res = []\n  routes.forEach(route => {\n    if (route.permissions) {\n      if (auth.hasPermiOr(route.permissions)) {\n        res.push(route)\n      }\n    } else if (route.roles) {\n      if (auth.hasRoleOr(route.roles)) {\n        res.push(route)\n      }\n    }\n  })\n  return res\n}\n\nexport const loadView = (view) => {\n  if (process.env.NODE_ENV === 'development') {\n    return (resolve) => require([`@/views/${view}`], resolve)\n  } else {\n    // 使用 import 实现生产环境的路由懒加载\n    return () => import(`@/views/${view}`)\n  }\n}\n\nexport default permission\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,KAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAC,wBAAA,CAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,WAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,UAAA,GAAAP,sBAAA,CAAAC,OAAA;AAEA,IAAMO,UAAU,GAAG;EACjBC,KAAK,EAAE;IACLC,MAAM,EAAE,EAAE;IACVC,SAAS,EAAE,EAAE;IACbC,aAAa,EAAE,EAAE;IACjBC,aAAa,EAAE,EAAE;IACjBC,cAAc,EAAE;EAClB,CAAC;EACDC,SAAS,EAAE;IACTC,UAAU,EAAE,SAAZA,UAAUA,CAAGP,KAAK,EAAEC,MAAM,EAAK;MAC7BD,KAAK,CAACE,SAAS,GAAGD,MAAM;MACxBD,KAAK,CAACC,MAAM,GAAGO,sBAAc,CAACC,MAAM,CAACR,MAAM,CAAC;IAC9C,CAAC;IACDS,kBAAkB,EAAE,SAApBA,kBAAkBA,CAAGV,KAAK,EAAEC,MAAM,EAAK;MACrCD,KAAK,CAACG,aAAa,GAAGK,sBAAc,CAACC,MAAM,CAACR,MAAM,CAAC;IACrD,CAAC;IACDU,iBAAiB,EAAE,SAAnBA,iBAAiBA,CAAGX,KAAK,EAAEC,MAAM,EAAK;MACpCD,KAAK,CAACI,aAAa,GAAGH,MAAM;IAC9B,CAAC;IACDW,mBAAmB,EAAE,SAArBA,mBAAmBA,CAAGZ,KAAK,EAAEC,MAAM,EAAK;MACtCD,KAAK,CAACK,cAAc,GAAGJ,MAAM;IAC/B;EACF,CAAC;EACDY,OAAO,EAAE;IACP;IACAC,cAAc,WAAdA,cAAcA,CAAAC,IAAA,EAAa;MAAA,IAAVC,MAAM,GAAAD,IAAA,CAANC,MAAM;MACrB,OAAO,IAAIC,OAAO,CAAC,UAAAC,OAAO,EAAI;QAC5B;QACA,IAAAC,gBAAU,EAAC,CAAC,CAACC,IAAI,CAAC,UAAAC,GAAG,EAAI;UACvB,IAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACJ,GAAG,CAACK,IAAI,CAAC,CAAC;UAClD,IAAMC,KAAK,GAAGJ,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACJ,GAAG,CAACK,IAAI,CAAC,CAAC;UAClD,IAAME,aAAa,GAAGC,iBAAiB,CAACP,KAAK,CAAC;UAC9C,IAAMQ,aAAa,GAAGD,iBAAiB,CAACF,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC;UAC3D,IAAMI,WAAW,GAAGC,mBAAmB,CAACC,qBAAa,CAAC;UACtDH,aAAa,CAACI,IAAI,CAAC;YAAEC,IAAI,EAAE,GAAG;YAAEC,QAAQ,EAAE,MAAM;YAAEC,MAAM,EAAE;UAAK,CAAC,CAAC;UACjEC,eAAM,CAACpC,SAAS,CAAC6B,WAAW,CAAC;UAC7Bf,MAAM,CAAC,YAAY,EAAEc,aAAa,CAAC;UACnCd,MAAM,CAAC,qBAAqB,EAAEY,aAAa,CAAC;UAC5CW,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEhC,sBAAc,CAACC,MAAM,CAACmB,aAAa,CAAC,CAAC;UACxEZ,MAAM,CAAC,oBAAoB,EAAEY,aAAa,CAAC;UAC3CZ,MAAM,CAAC,mBAAmB,EAAEY,aAAa,CAAC;UAC1CV,OAAO,CAACY,aAAa,CAAC;QACxB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF;AACF,CAAC;;AAED;AACA,SAASD,iBAAiBA,CAACY,cAAc,EAAoC;EAAA,IAAlCC,UAAU,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAAA,IAAEG,IAAI,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EACzE,OAAOF,cAAc,CAACM,MAAM,CAAC,UAAAC,KAAK,EAAI;IACpC,IAAIF,IAAI,IAAIE,KAAK,CAACC,QAAQ,EAAE;MAC1BD,KAAK,CAACC,QAAQ,GAAGC,cAAc,CAACF,KAAK,CAACC,QAAQ,CAAC;IACjD;IACA,IAAID,KAAK,CAACG,SAAS,EAAE;MACnB;MACA,IAAIH,KAAK,CAACG,SAAS,KAAK,QAAQ,EAAE;QAChCH,KAAK,CAACG,SAAS,GAAGC,cAAM;MAC1B,CAAC,MAAM,IAAIJ,KAAK,CAACG,SAAS,KAAK,YAAY,EAAE;QAC3CH,KAAK,CAACG,SAAS,GAAGE,mBAAU;MAC9B,CAAC,MAAM,IAAIL,KAAK,CAACG,SAAS,KAAK,WAAW,EAAE;QAC1CH,KAAK,CAACG,SAAS,GAAGG,kBAAS;MAC7B,CAAC,MAAM;QACLN,KAAK,CAACG,SAAS,GAAGI,QAAQ,CAACP,KAAK,CAACG,SAAS,CAAC;MAC7C;IACF;IACA,IAAIH,KAAK,CAACC,QAAQ,IAAI,IAAI,IAAID,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACL,MAAM,EAAE;MACrEI,KAAK,CAACC,QAAQ,GAAGpB,iBAAiB,CAACmB,KAAK,CAACC,QAAQ,EAAED,KAAK,EAAEF,IAAI,CAAC;IACjE,CAAC,MAAM;MACL,OAAOE,KAAK,CAAC,UAAU,CAAC;MACxB,OAAOA,KAAK,CAAC,UAAU,CAAC;IAC1B;IACA,OAAO,IAAI;EACb,CAAC,CAAC;AACJ;AAEA,SAASE,cAAcA,CAACM,WAAW,EAAsB;EAAA,IAApBd,UAAU,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EACrD,IAAIM,QAAQ,GAAG,EAAE;EACjBO,WAAW,CAACC,OAAO,CAAC,UAAAC,EAAE,EAAI;IACxBA,EAAE,CAACvB,IAAI,GAAGO,UAAU,GAAGA,UAAU,CAACP,IAAI,GAAG,GAAG,GAAGuB,EAAE,CAACvB,IAAI,GAAGuB,EAAE,CAACvB,IAAI;IAChE,IAAIuB,EAAE,CAACT,QAAQ,IAAIS,EAAE,CAACT,QAAQ,CAACL,MAAM,IAAIc,EAAE,CAACP,SAAS,KAAK,YAAY,EAAE;MACtEF,QAAQ,GAAGA,QAAQ,CAACxC,MAAM,CAACyC,cAAc,CAACQ,EAAE,CAACT,QAAQ,EAAES,EAAE,CAAC,CAAC;IAC7D,CAAC,MAAM;MACLT,QAAQ,CAACf,IAAI,CAACwB,EAAE,CAAC;IACnB;EACF,CAAC,CAAC;EACF,OAAOT,QAAQ;AACjB;;AAEA;AACO,SAASjB,mBAAmBA,CAAC/B,MAAM,EAAE;EAC1C,IAAMoB,GAAG,GAAG,EAAE;EACdpB,MAAM,CAACwD,OAAO,CAAC,UAAAT,KAAK,EAAI;IACtB,IAAIA,KAAK,CAACW,WAAW,EAAE;MACrB,IAAIC,aAAI,CAACC,UAAU,CAACb,KAAK,CAACW,WAAW,CAAC,EAAE;QACtCtC,GAAG,CAACa,IAAI,CAACc,KAAK,CAAC;MACjB;IACF,CAAC,MAAM,IAAIA,KAAK,CAACc,KAAK,EAAE;MACtB,IAAIF,aAAI,CAACG,SAAS,CAACf,KAAK,CAACc,KAAK,CAAC,EAAE;QAC/BzC,GAAG,CAACa,IAAI,CAACc,KAAK,CAAC;MACjB;IACF;EACF,CAAC,CAAC;EACF,OAAO3B,GAAG;AACZ;AAEO,IAAMkC,QAAQ,GAAAS,OAAA,CAAAT,QAAA,GAAG,SAAXA,QAAQA,CAAIU,IAAI,EAAK;EAChC,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;IAC1C,OAAO,UAAClD,OAAO;MAAA,OAAK1B,OAAO,CAAC,YAAAiB,MAAA,CAAYwD,IAAI,EAAG,EAAE/C,OAAO,CAAC;IAAA;EAC3D,CAAC,MAAM;IACL;IACA,OAAO;MAAA,iBAAAmD,SAAA;QAAA,WAAApD,OAAA,WAAAqD,CAAA;UAAA,OAAAA,CAAA,CAAAD,SAAA;QAAA,GAAAjD,IAAA,WAAAmD,CAAA;UAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjF,OAAA,CAAA+E,CAAA;QAAA;MAAA,aAAA9D,MAAA,CAAwBwD,IAAI;IAAA,CAAG;EACxC;AACF,CAAC;AAAA,IAAAS,QAAA,GAAAV,OAAA,CAAAS,OAAA,GAEc1E,UAAU", "ignoreList": []}]}