{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/ui/color-picker.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/ui/color-picker.js", "mtime": 1753510684099}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCIvVXNlcnMvbWF6aWhhby9EZXNrdG9wL2Rldi9wZXJmb3JtYW5jZS9wZXJmb3JtYW5jZS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmZpbGwuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmZyb20uanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnNsaWNlLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuaXRlcmF0b3IuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5jb25zdHJ1Y3Rvci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmZvci1lYWNoLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLmZvci1lYWNoLmpzIik7CnZhciBfY2xhc3NDYWxsQ2hlY2syID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIvVXNlcnMvbWF6aWhhby9EZXNrdG9wL2Rldi9wZXJmb3JtYW5jZS9wZXJmb3JtYW5jZS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9jbGFzc0NhbGxDaGVjay5qcyIpKTsKdmFyIF9jcmVhdGVDbGFzczIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi9Vc2Vycy9tYXppaGFvL0Rlc2t0b3AvZGV2L3BlcmZvcm1hbmNlL3BlcmZvcm1hbmNlLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2NyZWF0ZUNsYXNzLmpzIikpOwp2YXIgX2NhbGxTdXBlcjIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi9Vc2Vycy9tYXppaGFvL0Rlc2t0b3AvZGV2L3BlcmZvcm1hbmNlL3BlcmZvcm1hbmNlLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2NhbGxTdXBlci5qcyIpKTsKdmFyIF9zdXBlclByb3BHZXQyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIvVXNlcnMvbWF6aWhhby9EZXNrdG9wL2Rldi9wZXJmb3JtYW5jZS9wZXJmb3JtYW5jZS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9zdXBlclByb3BHZXQuanMiKSk7CnZhciBfaW5oZXJpdHMyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIvVXNlcnMvbWF6aWhhby9EZXNrdG9wL2Rldi9wZXJmb3JtYW5jZS9wZXJmb3JtYW5jZS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbmhlcml0cy5qcyIpKTsKdmFyIF9waWNrZXIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi4vcGlja2VyLmpzIikpOwp2YXIgQ29sb3JQaWNrZXIgPSAvKiNfX1BVUkVfXyovZnVuY3Rpb24gKF9QaWNrZXIpIHsKICBmdW5jdGlvbiBDb2xvclBpY2tlcihzZWxlY3QsIGxhYmVsKSB7CiAgICB2YXIgX3RoaXM7CiAgICAoMCwgX2NsYXNzQ2FsbENoZWNrMi5kZWZhdWx0KSh0aGlzLCBDb2xvclBpY2tlcik7CiAgICBfdGhpcyA9ICgwLCBfY2FsbFN1cGVyMi5kZWZhdWx0KSh0aGlzLCBDb2xvclBpY2tlciwgW3NlbGVjdF0pOwogICAgX3RoaXMubGFiZWwuaW5uZXJIVE1MID0gbGFiZWw7CiAgICBfdGhpcy5jb250YWluZXIuY2xhc3NMaXN0LmFkZCgncWwtY29sb3ItcGlja2VyJyk7CiAgICBBcnJheS5mcm9tKF90aGlzLmNvbnRhaW5lci5xdWVyeVNlbGVjdG9yQWxsKCcucWwtcGlja2VyLWl0ZW0nKSkuc2xpY2UoMCwgNykuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICBpdGVtLmNsYXNzTGlzdC5hZGQoJ3FsLXByaW1hcnknKTsKICAgIH0pOwogICAgcmV0dXJuIF90aGlzOwogIH0KICAoMCwgX2luaGVyaXRzMi5kZWZhdWx0KShDb2xvclBpY2tlciwgX1BpY2tlcik7CiAgcmV0dXJuICgwLCBfY3JlYXRlQ2xhc3MyLmRlZmF1bHQpKENvbG9yUGlja2VyLCBbewogICAga2V5OiAiYnVpbGRJdGVtIiwKICAgIHZhbHVlOiBmdW5jdGlvbiBidWlsZEl0ZW0ob3B0aW9uKSB7CiAgICAgIHZhciBpdGVtID0gKDAsIF9zdXBlclByb3BHZXQyLmRlZmF1bHQpKENvbG9yUGlja2VyLCAiYnVpbGRJdGVtIiwgdGhpcywgMykoW29wdGlvbl0pOwogICAgICBpdGVtLnN0eWxlLmJhY2tncm91bmRDb2xvciA9IG9wdGlvbi5nZXRBdHRyaWJ1dGUoJ3ZhbHVlJykgfHwgJyc7CiAgICAgIHJldHVybiBpdGVtOwogICAgfQogIH0sIHsKICAgIGtleTogInNlbGVjdEl0ZW0iLAogICAgdmFsdWU6IGZ1bmN0aW9uIHNlbGVjdEl0ZW0oaXRlbSwgdHJpZ2dlcikgewogICAgICAoMCwgX3N1cGVyUHJvcEdldDIuZGVmYXVsdCkoQ29sb3JQaWNrZXIsICJzZWxlY3RJdGVtIiwgdGhpcywgMykoW2l0ZW0sIHRyaWdnZXJdKTsKICAgICAgdmFyIGNvbG9yTGFiZWwgPSB0aGlzLmxhYmVsLnF1ZXJ5U2VsZWN0b3IoJy5xbC1jb2xvci1sYWJlbCcpOwogICAgICB2YXIgdmFsdWUgPSBpdGVtID8gaXRlbS5nZXRBdHRyaWJ1dGUoJ2RhdGEtdmFsdWUnKSB8fCAnJyA6ICcnOwogICAgICBpZiAoY29sb3JMYWJlbCkgewogICAgICAgIGlmIChjb2xvckxhYmVsLnRhZ05hbWUgPT09ICdsaW5lJykgewogICAgICAgICAgY29sb3JMYWJlbC5zdHlsZS5zdHJva2UgPSB2YWx1ZTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgY29sb3JMYWJlbC5zdHlsZS5maWxsID0gdmFsdWU7CiAgICAgICAgfQogICAgICB9CiAgICB9CiAgfV0pOwp9KF9waWNrZXIuZGVmYXVsdCk7CnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IENvbG9yUGlja2VyOw=="}, {"version": 3, "names": ["_picker", "_interopRequireDefault", "require", "ColorPicker", "_Picker", "select", "label", "_this", "_classCallCheck2", "default", "_callSuper2", "innerHTML", "container", "classList", "add", "Array", "from", "querySelectorAll", "slice", "for<PERSON>ach", "item", "_inherits2", "_createClass2", "key", "value", "buildItem", "option", "_superPropGet2", "style", "backgroundColor", "getAttribute", "selectItem", "trigger", "colorLabel", "querySelector", "tagName", "stroke", "fill", "Picker", "_default", "exports"], "sources": ["../../src/ui/color-picker.ts"], "sourcesContent": ["import Picker from './picker.js';\n\nclass ColorPicker extends Picker {\n  constructor(select: HTMLSelectElement, label: string) {\n    super(select);\n    this.label.innerHTML = label;\n    this.container.classList.add('ql-color-picker');\n    Array.from(this.container.querySelectorAll('.ql-picker-item'))\n      .slice(0, 7)\n      .forEach((item) => {\n        item.classList.add('ql-primary');\n      });\n  }\n\n  buildItem(option: HTMLOptionElement) {\n    const item = super.buildItem(option);\n    item.style.backgroundColor = option.getAttribute('value') || '';\n    return item;\n  }\n\n  selectItem(item: HTMLElement | null, trigger?: boolean) {\n    super.selectItem(item, trigger);\n    const colorLabel = this.label.querySelector<HTMLElement>('.ql-color-label');\n    const value = item ? item.getAttribute('data-value') || '' : '';\n    if (colorLabel) {\n      if (colorLabel.tagName === 'line') {\n        colorLabel.style.stroke = value;\n      } else {\n        colorLabel.style.fill = value;\n      }\n    }\n  }\n}\n\nexport default ColorPicker;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,OAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAgC,IAE1BC,WAAW,0BAAAC,OAAA;EACf,SAAAD,YAAYE,MAAyB,EAAEC,KAAa,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAN,WAAA;IACpDI,KAAA,OAAAG,WAAA,CAAAD,OAAA,QAAAN,WAAA,GAAME,MAAM;IACZE,KAAA,CAAKD,KAAK,CAACK,SAAS,GAAGL,KAAK;IAC5BC,KAAA,CAAKK,SAAS,CAACC,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAC/CC,KAAK,CAACC,IAAI,CAACT,KAAA,CAAKK,SAAS,CAACK,gBAAgB,CAAC,iBAAiB,CAAC,CAAC,CAC3DC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACXC,OAAO,CAAE,UAAAC,IAAI,EAAK;MACjBA,IAAI,CAACP,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;IAClC,CAAC,CAAC;IAAA,OAAAP,KAAA;EACN;EAAA,IAAAc,UAAA,CAAAZ,OAAA,EAAAN,WAAA,EAAAC,OAAA;EAAA,WAAAkB,aAAA,CAAAb,OAAA,EAAAN,WAAA;IAAAoB,GAAA;IAAAC,KAAA,EAEA,SAAAC,SAASA,CAACC,MAAyB,EAAE;MACnC,IAAMN,IAAI,OAAAO,cAAA,CAAAlB,OAAA,EAAAN,WAAA,yBAAmBuB,MAAM,EAAC;MACpCN,IAAI,CAACQ,KAAK,CAACC,eAAe,GAAGH,MAAM,CAACI,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE;MAC/D,OAAOV,IAAI;IACb;EAAA;IAAAG,GAAA;IAAAC,KAAA,EAEA,SAAAO,UAAUA,CAACX,IAAwB,EAAEY,OAAiB,EAAE;MACtD,IAAAL,cAAA,CAAAlB,OAAA,EAAAN,WAAA,0BAAiBiB,IAAI,EAAEY,OAAO;MAC9B,IAAMC,UAAU,GAAG,IAAI,CAAC3B,KAAK,CAAC4B,aAAa,CAAc,iBAAiB,CAAC;MAC3E,IAAMV,KAAK,GAAGJ,IAAI,GAAGA,IAAI,CAACU,YAAY,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,EAAE;MAC/D,IAAIG,UAAU,EAAE;QACd,IAAIA,UAAU,CAACE,OAAO,KAAK,MAAM,EAAE;UACjCF,UAAU,CAACL,KAAK,CAACQ,MAAM,GAAGZ,KAAK;QACjC,CAAC,MAAM;UACLS,UAAU,CAACL,KAAK,CAACS,IAAI,GAAGb,KAAK;QAC/B;MACF;IACF;EAAA;AAAA,EA7BwBc,eAAM;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAA/B,OAAA,GAgCjBN,WAAW", "ignoreList": []}]}