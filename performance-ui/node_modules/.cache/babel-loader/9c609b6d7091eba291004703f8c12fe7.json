{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/directive/module/clipboard.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/directive/module/clipboard.js", "mtime": 1753510684529}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_clipboard", "_interopRequireDefault", "require", "_default", "exports", "default", "bind", "el", "binding", "vnode", "arg", "_vClipBoard_success", "value", "_vClipBoard_error", "clipboard", "Clipboard", "text", "action", "on", "e", "callback", "_vClipBoard", "update", "unbind", "_vClipboard", "destroy"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/directive/module/clipboard.js"], "sourcesContent": ["/**\n* v-clipboard 文字复制剪贴\n* Copyright (c) 2021 ruoyi\n*/\n\nimport Clipboard from 'clipboard'\nexport default {\n  bind(el, binding, vnode) {\n    switch (binding.arg) {\n      case 'success':\n        el._vClipBoard_success = binding.value\n        break\n      case 'error':\n        el._vClipBoard_error = binding.value\n        break\n      default: {\n        const clipboard = new Clipboard(el, {\n          text: () => binding.value,\n          action: () => binding.arg === 'cut' ? 'cut' : 'copy'\n        })\n        clipboard.on('success', e => {\n          const callback = el._vClipBoard_success\n          callback && callback(e)\n        })\n        clipboard.on('error', e => {\n          const callback = el._vClipBoard_error\n          callback && callback(e)\n        })\n        el._vClipBoard = clipboard\n      }\n    }\n  },\n  update(el, binding) {\n    if (binding.arg === 'success') {\n      el._vClipBoard_success = binding.value\n    } else if (binding.arg === 'error') {\n      el._vClipBoard_error = binding.value\n    } else {\n      el._vClipBoard.text = function () { return binding.value }\n      el._vClipBoard.action = () => binding.arg === 'cut' ? 'cut' : 'copy'\n    }\n  },\n  unbind(el, binding) {\n    if (!el._vClipboard) return\n    if (binding.arg === 'success') {\n      delete el._vClipBoard_success\n    } else if (binding.arg === 'error') {\n      delete el._vClipBoard_error\n    } else {\n      el._vClipBoard.destroy()\n      delete el._vClipBoard\n    }\n  }\n}\n"], "mappings": ";;;;;;;AAKA,IAAAA,UAAA,GAAAC,sBAAA,CAAAC,OAAA;AALA;AACA;AACA;AACA;AAHA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAMe;EACbC,IAAI,WAAJA,IAAIA,CAACC,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAE;IACvB,QAAQD,OAAO,CAACE,GAAG;MACjB,KAAK,SAAS;QACZH,EAAE,CAACI,mBAAmB,GAAGH,OAAO,CAACI,KAAK;QACtC;MACF,KAAK,OAAO;QACVL,EAAE,CAACM,iBAAiB,GAAGL,OAAO,CAACI,KAAK;QACpC;MACF;QAAS;UACP,IAAME,SAAS,GAAG,IAAIC,kBAAS,CAACR,EAAE,EAAE;YAClCS,IAAI,EAAE,SAANA,IAAIA,CAAA;cAAA,OAAQR,OAAO,CAACI,KAAK;YAAA;YACzBK,MAAM,EAAE,SAARA,MAAMA,CAAA;cAAA,OAAQT,OAAO,CAACE,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG,MAAM;YAAA;UACtD,CAAC,CAAC;UACFI,SAAS,CAACI,EAAE,CAAC,SAAS,EAAE,UAAAC,CAAC,EAAI;YAC3B,IAAMC,QAAQ,GAAGb,EAAE,CAACI,mBAAmB;YACvCS,QAAQ,IAAIA,QAAQ,CAACD,CAAC,CAAC;UACzB,CAAC,CAAC;UACFL,SAAS,CAACI,EAAE,CAAC,OAAO,EAAE,UAAAC,CAAC,EAAI;YACzB,IAAMC,QAAQ,GAAGb,EAAE,CAACM,iBAAiB;YACrCO,QAAQ,IAAIA,QAAQ,CAACD,CAAC,CAAC;UACzB,CAAC,CAAC;UACFZ,EAAE,CAACc,WAAW,GAAGP,SAAS;QAC5B;IACF;EACF,CAAC;EACDQ,MAAM,WAANA,MAAMA,CAACf,EAAE,EAAEC,OAAO,EAAE;IAClB,IAAIA,OAAO,CAACE,GAAG,KAAK,SAAS,EAAE;MAC7BH,EAAE,CAACI,mBAAmB,GAAGH,OAAO,CAACI,KAAK;IACxC,CAAC,MAAM,IAAIJ,OAAO,CAACE,GAAG,KAAK,OAAO,EAAE;MAClCH,EAAE,CAACM,iBAAiB,GAAGL,OAAO,CAACI,KAAK;IACtC,CAAC,MAAM;MACLL,EAAE,CAACc,WAAW,CAACL,IAAI,GAAG,YAAY;QAAE,OAAOR,OAAO,CAACI,KAAK;MAAC,CAAC;MAC1DL,EAAE,CAACc,WAAW,CAACJ,MAAM,GAAG;QAAA,OAAMT,OAAO,CAACE,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG,MAAM;MAAA;IACtE;EACF,CAAC;EACDa,MAAM,WAANA,MAAMA,CAAChB,EAAE,EAAEC,OAAO,EAAE;IAClB,IAAI,CAACD,EAAE,CAACiB,WAAW,EAAE;IACrB,IAAIhB,OAAO,CAACE,GAAG,KAAK,SAAS,EAAE;MAC7B,OAAOH,EAAE,CAACI,mBAAmB;IAC/B,CAAC,MAAM,IAAIH,OAAO,CAACE,GAAG,KAAK,OAAO,EAAE;MAClC,OAAOH,EAAE,CAACM,iBAAiB;IAC7B,CAAC,MAAM;MACLN,EAAE,CAACc,WAAW,CAACI,OAAO,CAAC,CAAC;MACxB,OAAOlB,EAAE,CAACc,WAAW;IACvB;EACF;AACF,CAAC", "ignoreList": []}]}