{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/directive/dialog/dragWidth.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/directive/dialog/dragWidth.js", "mtime": 1753510684528}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7Ci8qKgogKiB2LWRpYWxvZ0RyYWdXaWR0aCDlj6/mi5bliqjlvLnnqpflrr3luqbvvIjlj7PkvqfovrnvvIkKICogQ29weXJpZ2h0IChjKSAyMDE5IHJ1b3lpCiAqLwp2YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSB7CiAgYmluZDogZnVuY3Rpb24gYmluZChlbCkgewogICAgdmFyIGRyYWdEb20gPSBlbC5xdWVyeVNlbGVjdG9yKCcuZWwtZGlhbG9nJyk7CiAgICB2YXIgbGluZUVsID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnZGl2Jyk7CiAgICBsaW5lRWwuc3R5bGUgPSAnd2lkdGg6IDVweDsgYmFja2dyb3VuZDogaW5oZXJpdDsgaGVpZ2h0OiA4MCU7IHBvc2l0aW9uOiBhYnNvbHV0ZTsgcmlnaHQ6IDA7IHRvcDogMDsgYm90dG9tOiAwOyBtYXJnaW46IGF1dG87IHotaW5kZXg6IDE7IGN1cnNvcjogdy1yZXNpemU7JzsKICAgIGxpbmVFbC5hZGRFdmVudExpc3RlbmVyKCdtb3VzZWRvd24nLCBmdW5jdGlvbiAoZSkgewogICAgICAvLyDpvKDmoIfmjInkuIvvvIzorqHnrpflvZPliY3lhYPntKDot53nprvlj6/op4bljLrnmoTot53nprsKICAgICAgdmFyIGRpc1ggPSBlLmNsaWVudFggLSBlbC5vZmZzZXRMZWZ0OwogICAgICAvLyDlvZPliY3lrr3luqYKICAgICAgdmFyIGN1cldpZHRoID0gZHJhZ0RvbS5vZmZzZXRXaWR0aDsKICAgICAgZG9jdW1lbnQub25tb3VzZW1vdmUgPSBmdW5jdGlvbiAoZSkgewogICAgICAgIGUucHJldmVudERlZmF1bHQoKTsgLy8g56e75Yqo5pe256aB55So6buY6K6k5LqL5Lu2CiAgICAgICAgLy8g6YCa6L+H5LqL5Lu25aeU5omY77yM6K6h566X56e75Yqo55qE6Led56a7CiAgICAgICAgdmFyIGwgPSBlLmNsaWVudFggLSBkaXNYOwogICAgICAgIGRyYWdEb20uc3R5bGUud2lkdGggPSAiIi5jb25jYXQoY3VyV2lkdGggKyBsLCAicHgiKTsKICAgICAgfTsKICAgICAgZG9jdW1lbnQub25tb3VzZXVwID0gZnVuY3Rpb24gKGUpIHsKICAgICAgICBkb2N1bWVudC5vbm1vdXNlbW92ZSA9IG51bGw7CiAgICAgICAgZG9jdW1lbnQub25tb3VzZXVwID0gbnVsbDsKICAgICAgfTsKICAgIH0sIGZhbHNlKTsKICAgIGRyYWdEb20uYXBwZW5kQ2hpbGQobGluZUVsKTsKICB9Cn07"}, {"version": 3, "names": ["_default", "exports", "default", "bind", "el", "dragDom", "querySelector", "lineEl", "document", "createElement", "style", "addEventListener", "e", "disX", "clientX", "offsetLeft", "cur<PERSON><PERSON>th", "offsetWidth", "<PERSON><PERSON><PERSON><PERSON>", "preventDefault", "l", "width", "concat", "onmouseup", "append<PERSON><PERSON><PERSON>"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/directive/dialog/dragWidth.js"], "sourcesContent": ["/**\n * v-dialogDragWidth 可拖动弹窗宽度（右侧边）\n * Copyright (c) 2019 ruoyi\n */\n\nexport default {\n  bind(el) {\n    const dragDom = el.querySelector('.el-dialog')\n    const lineEl = document.createElement('div')\n    lineEl.style = 'width: 5px; background: inherit; height: 80%; position: absolute; right: 0; top: 0; bottom: 0; margin: auto; z-index: 1; cursor: w-resize;'\n    lineEl.addEventListener('mousedown',\n      function (e) {\n        // 鼠标按下，计算当前元素距离可视区的距离\n        const disX = e.clientX - el.offsetLeft\n        // 当前宽度\n        const curWidth = dragDom.offsetWidth\n        document.onmousemove = function (e) {\n          e.preventDefault() // 移动时禁用默认事件\n          // 通过事件委托，计算移动的距离\n          const l = e.clientX - disX\n          dragDom.style.width = `${curWidth + l}px`\n        }\n        document.onmouseup = function (e) {\n          document.onmousemove = null\n          document.onmouseup = null\n        }\n      }, false)\n    dragDom.appendChild(lineEl)\n  }\n}\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AAHA,IAAAA,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAKe;EACbC,IAAI,WAAJA,IAAIA,CAACC,EAAE,EAAE;IACP,IAAMC,OAAO,GAAGD,EAAE,CAACE,aAAa,CAAC,YAAY,CAAC;IAC9C,IAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC5CF,MAAM,CAACG,KAAK,GAAG,4IAA4I;IAC3JH,MAAM,CAACI,gBAAgB,CAAC,WAAW,EACjC,UAAUC,CAAC,EAAE;MACX;MACA,IAAMC,IAAI,GAAGD,CAAC,CAACE,OAAO,GAAGV,EAAE,CAACW,UAAU;MACtC;MACA,IAAMC,QAAQ,GAAGX,OAAO,CAACY,WAAW;MACpCT,QAAQ,CAACU,WAAW,GAAG,UAAUN,CAAC,EAAE;QAClCA,CAAC,CAACO,cAAc,CAAC,CAAC,EAAC;QACnB;QACA,IAAMC,CAAC,GAAGR,CAAC,CAACE,OAAO,GAAGD,IAAI;QAC1BR,OAAO,CAACK,KAAK,CAACW,KAAK,MAAAC,MAAA,CAAMN,QAAQ,GAAGI,CAAC,OAAI;MAC3C,CAAC;MACDZ,QAAQ,CAACe,SAAS,GAAG,UAAUX,CAAC,EAAE;QAChCJ,QAAQ,CAACU,WAAW,GAAG,IAAI;QAC3BV,QAAQ,CAACe,SAAS,GAAG,IAAI;MAC3B,CAAC;IACH,CAAC,EAAE,KAAK,CAAC;IACXlB,OAAO,CAACmB,WAAW,CAACjB,MAAM,CAAC;EAC7B;AACF,CAAC", "ignoreList": []}]}