{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/core/selection.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/core/selection.js", "mtime": 1753510684088}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_parchment", "require", "_lodashEs", "_emitter", "_interopRequireDefault", "_logger", "debug", "logger", "Range", "exports", "_createClass2", "default", "index", "_classCallCheck2", "length", "arguments", "undefined", "Selection", "scroll", "emitter", "_this", "composing", "mouseDown", "root", "domNode", "cursor", "create", "savedRange", "<PERSON><PERSON><PERSON><PERSON>", "lastNative", "handleComposition", "handleDragging", "listenDOM", "document", "setTimeout", "update", "bind", "Emitter", "sources", "USER", "on", "events", "SCROLL_BEFORE_UPDATE", "hasFocus", "native", "getNativeRange", "start", "node", "textNode", "once", "SCROLL_UPDATE", "source", "mutations", "contains", "end", "setNativeRange", "offset", "triggeredByTyping", "some", "mutation", "type", "target", "SILENT", "ignored", "SCROLL_OPTIMIZE", "context", "range", "_context$range", "startNode", "startOffset", "endNode", "endOffset", "key", "value", "_this2", "COMPOSITION_BEFORE_START", "COMPOSITION_END", "parent", "restore", "_this3", "body", "focus", "preventScroll", "setRang<PERSON>", "format", "nativeRange", "collapsed", "query", "<PERSON><PERSON>", "BLOCK", "blot", "find", "LeafBlot", "after", "split", "insertBefore", "attach", "optimize", "data", "getBounds", "<PERSON><PERSON><PERSON><PERSON>", "Math", "min", "_this$scroll$leaf", "leaf", "_this$scroll$leaf2", "_slicedToArray2", "_this$scroll$leaf3", "_this$scroll$leaf4", "next", "_this$scroll$line", "line", "_this$scroll$line2", "_this$scroll$line3", "_this$scroll$line4", "nextLine", "_leaf$position", "position", "_leaf$position2", "createRange", "setStart", "_this$scroll$leaf5", "_this$scroll$leaf6", "_leaf$position3", "_leaf$position4", "setEnd", "getBoundingClientRect", "side", "rect", "Text", "Element", "bottom", "top", "height", "left", "right", "width", "selection", "getSelection", "rangeCount", "getRangeAt", "normalizeNative", "info", "getRange", "isConnected", "normalized", "normalizedToRange", "activeElement", "_this4", "positions", "push", "indexes", "map", "_position", "max", "apply", "_toConsumableArray2", "concat", "startContainer", "endContainer", "for<PERSON>ach", "childNodes", "<PERSON><PERSON><PERSON><PERSON>", "rangeToNative", "_this5", "getPosition", "inclusive", "_this5$scroll$leaf", "_this5$scroll$leaf2", "leafOffset", "force", "parentNode", "_ref", "tagName", "Array", "from", "indexOf", "removeAllRanges", "addRange", "blur", "API", "args", "oldRange", "_this$getRange", "_this$getRange2", "isEqual", "_this$emitter", "SELECTION_CHANGE", "cloneDeep", "emit", "EDITOR_CHANGE", "_this$emitter2", "descendant", "e", "_default"], "sources": ["../../src/core/selection.ts"], "sourcesContent": ["import { LeafBlot, Scope } from 'parchment';\nimport { cloneDeep, isEqual } from 'lodash-es';\nimport Emitter from './emitter.js';\nimport type { EmitterSource } from './emitter.js';\nimport logger from './logger.js';\nimport type Cursor from '../blots/cursor.js';\nimport type Scroll from '../blots/scroll.js';\n\nconst debug = logger('quill:selection');\n\ntype NativeRange = AbstractRange;\n\ninterface NormalizedRange {\n  start: {\n    node: NativeRange['startContainer'];\n    offset: NativeRange['startOffset'];\n  };\n  end: { node: NativeRange['endContainer']; offset: NativeRange['endOffset'] };\n  native: NativeRange;\n}\n\nexport interface Bounds {\n  bottom: number;\n  height: number;\n  left: number;\n  right: number;\n  top: number;\n  width: number;\n}\n\nexport class Range {\n  constructor(\n    public index: number,\n    public length = 0,\n  ) {}\n}\n\nclass Selection {\n  scroll: Scroll;\n  emitter: Emitter;\n  composing: boolean;\n  mouseDown: boolean;\n\n  root: HTMLElement;\n  cursor: Cursor;\n  savedRange: Range;\n  lastRange: Range | null;\n  lastNative: NormalizedRange | null;\n\n  constructor(scroll: Scroll, emitter: Emitter) {\n    this.emitter = emitter;\n    this.scroll = scroll;\n    this.composing = false;\n    this.mouseDown = false;\n    this.root = this.scroll.domNode;\n    // @ts-expect-error\n    this.cursor = this.scroll.create('cursor', this);\n    // savedRange is last non-null range\n    this.savedRange = new Range(0, 0);\n    this.lastRange = this.savedRange;\n    this.lastNative = null;\n    this.handleComposition();\n    this.handleDragging();\n    this.emitter.listenDOM('selectionchange', document, () => {\n      if (!this.mouseDown && !this.composing) {\n        setTimeout(this.update.bind(this, Emitter.sources.USER), 1);\n      }\n    });\n    this.emitter.on(Emitter.events.SCROLL_BEFORE_UPDATE, () => {\n      if (!this.hasFocus()) return;\n      const native = this.getNativeRange();\n      if (native == null) return;\n      if (native.start.node === this.cursor.textNode) return; // cursor.restore() will handle\n      this.emitter.once(\n        Emitter.events.SCROLL_UPDATE,\n        (source, mutations: MutationRecord[]) => {\n          try {\n            if (\n              this.root.contains(native.start.node) &&\n              this.root.contains(native.end.node)\n            ) {\n              this.setNativeRange(\n                native.start.node,\n                native.start.offset,\n                native.end.node,\n                native.end.offset,\n              );\n            }\n            const triggeredByTyping = mutations.some(\n              (mutation) =>\n                mutation.type === 'characterData' ||\n                mutation.type === 'childList' ||\n                (mutation.type === 'attributes' &&\n                  mutation.target === this.root),\n            );\n            this.update(triggeredByTyping ? Emitter.sources.SILENT : source);\n          } catch (ignored) {\n            // ignore\n          }\n        },\n      );\n    });\n    this.emitter.on(Emitter.events.SCROLL_OPTIMIZE, (mutations, context) => {\n      if (context.range) {\n        const { startNode, startOffset, endNode, endOffset } = context.range;\n        this.setNativeRange(startNode, startOffset, endNode, endOffset);\n        this.update(Emitter.sources.SILENT);\n      }\n    });\n    this.update(Emitter.sources.SILENT);\n  }\n\n  handleComposition() {\n    this.emitter.on(Emitter.events.COMPOSITION_BEFORE_START, () => {\n      this.composing = true;\n    });\n    this.emitter.on(Emitter.events.COMPOSITION_END, () => {\n      this.composing = false;\n      if (this.cursor.parent) {\n        const range = this.cursor.restore();\n        if (!range) return;\n        setTimeout(() => {\n          this.setNativeRange(\n            range.startNode,\n            range.startOffset,\n            range.endNode,\n            range.endOffset,\n          );\n        }, 1);\n      }\n    });\n  }\n\n  handleDragging() {\n    this.emitter.listenDOM('mousedown', document.body, () => {\n      this.mouseDown = true;\n    });\n    this.emitter.listenDOM('mouseup', document.body, () => {\n      this.mouseDown = false;\n      this.update(Emitter.sources.USER);\n    });\n  }\n\n  focus() {\n    if (this.hasFocus()) return;\n    this.root.focus({ preventScroll: true });\n    this.setRange(this.savedRange);\n  }\n\n  format(format: string, value: unknown) {\n    this.scroll.update();\n    const nativeRange = this.getNativeRange();\n    if (\n      nativeRange == null ||\n      !nativeRange.native.collapsed ||\n      this.scroll.query(format, Scope.BLOCK)\n    )\n      return;\n    if (nativeRange.start.node !== this.cursor.textNode) {\n      const blot = this.scroll.find(nativeRange.start.node, false);\n      if (blot == null) return;\n      // TODO Give blot ability to not split\n      if (blot instanceof LeafBlot) {\n        const after = blot.split(nativeRange.start.offset);\n        blot.parent.insertBefore(this.cursor, after);\n      } else {\n        // @ts-expect-error TODO: nativeRange.start.node doesn't seem to match function signature\n        blot.insertBefore(this.cursor, nativeRange.start.node); // Should never happen\n      }\n      this.cursor.attach();\n    }\n    this.cursor.format(format, value);\n    this.scroll.optimize();\n    this.setNativeRange(this.cursor.textNode, this.cursor.textNode.data.length);\n    this.update();\n  }\n\n  getBounds(index: number, length = 0) {\n    const scrollLength = this.scroll.length();\n    index = Math.min(index, scrollLength - 1);\n    length = Math.min(index + length, scrollLength - 1) - index;\n    let node: Node;\n    let [leaf, offset] = this.scroll.leaf(index);\n    if (leaf == null) return null;\n    if (length > 0 && offset === leaf.length()) {\n      const [next] = this.scroll.leaf(index + 1);\n      if (next) {\n        const [line] = this.scroll.line(index);\n        const [nextLine] = this.scroll.line(index + 1);\n        if (line === nextLine) {\n          leaf = next;\n          offset = 0;\n        }\n      }\n    }\n    [node, offset] = leaf.position(offset, true);\n    const range = document.createRange();\n    if (length > 0) {\n      range.setStart(node, offset);\n      [leaf, offset] = this.scroll.leaf(index + length);\n      if (leaf == null) return null;\n      [node, offset] = leaf.position(offset, true);\n      range.setEnd(node, offset);\n      return range.getBoundingClientRect();\n    }\n    let side: 'left' | 'right' = 'left';\n    let rect: DOMRect;\n    if (node instanceof Text) {\n      // Return null if the text node is empty because it is\n      // not able to get a useful client rect:\n      // https://github.com/w3c/csswg-drafts/issues/2514.\n      // Empty text nodes are most likely caused by TextBlot#optimize()\n      // not getting called when editor content changes.\n      if (!node.data.length) {\n        return null;\n      }\n      if (offset < node.data.length) {\n        range.setStart(node, offset);\n        range.setEnd(node, offset + 1);\n      } else {\n        range.setStart(node, offset - 1);\n        range.setEnd(node, offset);\n        side = 'right';\n      }\n      rect = range.getBoundingClientRect();\n    } else {\n      if (!(leaf.domNode instanceof Element)) return null;\n      rect = leaf.domNode.getBoundingClientRect();\n      if (offset > 0) side = 'right';\n    }\n    return {\n      bottom: rect.top + rect.height,\n      height: rect.height,\n      left: rect[side],\n      right: rect[side],\n      top: rect.top,\n      width: 0,\n    };\n  }\n\n  getNativeRange(): NormalizedRange | null {\n    const selection = document.getSelection();\n    if (selection == null || selection.rangeCount <= 0) return null;\n    const nativeRange = selection.getRangeAt(0);\n    if (nativeRange == null) return null;\n    const range = this.normalizeNative(nativeRange);\n    debug.info('getNativeRange', range);\n    return range;\n  }\n\n  getRange(): [Range, NormalizedRange] | [null, null] {\n    const root = this.scroll.domNode;\n    if ('isConnected' in root && !root.isConnected) {\n      // document.getSelection() forces layout on Blink, so we trend to\n      // not calling it.\n      return [null, null];\n    }\n    const normalized = this.getNativeRange();\n    if (normalized == null) return [null, null];\n    const range = this.normalizedToRange(normalized);\n    return [range, normalized];\n  }\n\n  hasFocus(): boolean {\n    return (\n      document.activeElement === this.root ||\n      (document.activeElement != null &&\n        contains(this.root, document.activeElement))\n    );\n  }\n\n  normalizedToRange(range: NormalizedRange) {\n    const positions: [Node, number][] = [\n      [range.start.node, range.start.offset],\n    ];\n    if (!range.native.collapsed) {\n      positions.push([range.end.node, range.end.offset]);\n    }\n    const indexes = positions.map((position) => {\n      const [node, offset] = position;\n      const blot = this.scroll.find(node, true);\n      // @ts-expect-error Fix me later\n      const index = blot.offset(this.scroll);\n      if (offset === 0) {\n        return index;\n      }\n      if (blot instanceof LeafBlot) {\n        return index + blot.index(node, offset);\n      }\n      // @ts-expect-error Fix me later\n      return index + blot.length();\n    });\n    const end = Math.min(Math.max(...indexes), this.scroll.length() - 1);\n    const start = Math.min(end, ...indexes);\n    return new Range(start, end - start);\n  }\n\n  normalizeNative(nativeRange: NativeRange) {\n    if (\n      !contains(this.root, nativeRange.startContainer) ||\n      (!nativeRange.collapsed && !contains(this.root, nativeRange.endContainer))\n    ) {\n      return null;\n    }\n    const range = {\n      start: {\n        node: nativeRange.startContainer,\n        offset: nativeRange.startOffset,\n      },\n      end: { node: nativeRange.endContainer, offset: nativeRange.endOffset },\n      native: nativeRange,\n    };\n    [range.start, range.end].forEach((position) => {\n      let { node, offset } = position;\n      while (!(node instanceof Text) && node.childNodes.length > 0) {\n        if (node.childNodes.length > offset) {\n          node = node.childNodes[offset];\n          offset = 0;\n        } else if (node.childNodes.length === offset) {\n          // @ts-expect-error Fix me later\n          node = node.lastChild;\n          if (node instanceof Text) {\n            offset = node.data.length;\n          } else if (node.childNodes.length > 0) {\n            // Container case\n            offset = node.childNodes.length;\n          } else {\n            // Embed case\n            offset = node.childNodes.length + 1;\n          }\n        } else {\n          break;\n        }\n      }\n      position.node = node;\n      position.offset = offset;\n    });\n    return range;\n  }\n\n  rangeToNative(range: Range): [Node | null, number, Node | null, number] {\n    const scrollLength = this.scroll.length();\n\n    const getPosition = (\n      index: number,\n      inclusive: boolean,\n    ): [Node | null, number] => {\n      index = Math.min(scrollLength - 1, index);\n      const [leaf, leafOffset] = this.scroll.leaf(index);\n      return leaf ? leaf.position(leafOffset, inclusive) : [null, -1];\n    };\n    return [\n      ...getPosition(range.index, false),\n      ...getPosition(range.index + range.length, true),\n    ];\n  }\n\n  setNativeRange(\n    startNode: Node | null,\n    startOffset?: number,\n    endNode = startNode,\n    endOffset = startOffset,\n    force = false,\n  ) {\n    debug.info('setNativeRange', startNode, startOffset, endNode, endOffset);\n    if (\n      startNode != null &&\n      (this.root.parentNode == null ||\n        startNode.parentNode == null ||\n        // @ts-expect-error Fix me later\n        endNode.parentNode == null)\n    ) {\n      return;\n    }\n    const selection = document.getSelection();\n    if (selection == null) return;\n    if (startNode != null) {\n      if (!this.hasFocus()) this.root.focus({ preventScroll: true });\n      const { native } = this.getNativeRange() || {};\n      if (\n        native == null ||\n        force ||\n        startNode !== native.startContainer ||\n        startOffset !== native.startOffset ||\n        endNode !== native.endContainer ||\n        endOffset !== native.endOffset\n      ) {\n        if (startNode instanceof Element && startNode.tagName === 'BR') {\n          // @ts-expect-error Fix me later\n          startOffset = Array.from(startNode.parentNode.childNodes).indexOf(\n            startNode,\n          );\n          startNode = startNode.parentNode;\n        }\n        if (endNode instanceof Element && endNode.tagName === 'BR') {\n          // @ts-expect-error Fix me later\n          endOffset = Array.from(endNode.parentNode.childNodes).indexOf(\n            endNode,\n          );\n          endNode = endNode.parentNode;\n        }\n        const range = document.createRange();\n        // @ts-expect-error Fix me later\n        range.setStart(startNode, startOffset);\n        // @ts-expect-error Fix me later\n        range.setEnd(endNode, endOffset);\n        selection.removeAllRanges();\n        selection.addRange(range);\n      }\n    } else {\n      selection.removeAllRanges();\n      this.root.blur();\n    }\n  }\n\n  setRange(range: Range | null, force: boolean, source?: EmitterSource): void;\n  setRange(range: Range | null, source?: EmitterSource): void;\n  setRange(\n    range: Range | null,\n    force: boolean | EmitterSource = false,\n    source: EmitterSource = Emitter.sources.API,\n  ): void {\n    if (typeof force === 'string') {\n      source = force;\n      force = false;\n    }\n    debug.info('setRange', range);\n    if (range != null) {\n      const args = this.rangeToNative(range);\n      this.setNativeRange(...args, force);\n    } else {\n      this.setNativeRange(null);\n    }\n    this.update(source);\n  }\n\n  update(source: EmitterSource = Emitter.sources.USER) {\n    const oldRange = this.lastRange;\n    const [lastRange, nativeRange] = this.getRange();\n    this.lastRange = lastRange;\n    this.lastNative = nativeRange;\n    if (this.lastRange != null) {\n      this.savedRange = this.lastRange;\n    }\n    if (!isEqual(oldRange, this.lastRange)) {\n      if (\n        !this.composing &&\n        nativeRange != null &&\n        nativeRange.native.collapsed &&\n        nativeRange.start.node !== this.cursor.textNode\n      ) {\n        const range = this.cursor.restore();\n        if (range) {\n          this.setNativeRange(\n            range.startNode,\n            range.startOffset,\n            range.endNode,\n            range.endOffset,\n          );\n        }\n      }\n      const args = [\n        Emitter.events.SELECTION_CHANGE,\n        cloneDeep(this.lastRange),\n        cloneDeep(oldRange),\n        source,\n      ];\n      this.emitter.emit(Emitter.events.EDITOR_CHANGE, ...args);\n      if (source !== Emitter.sources.SILENT) {\n        this.emitter.emit(...args);\n      }\n    }\n  }\n}\n\nfunction contains(parent: Node, descendant: Node) {\n  try {\n    // Firefox inserts inaccessible nodes around video elements\n    descendant.parentNode; // eslint-disable-line @typescript-eslint/no-unused-expressions\n  } catch (e) {\n    return false;\n  }\n  return parent.contains(descendant);\n}\n\nexport default Selection;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAC,sBAAA,CAAAH,OAAA;AAEA,IAAAI,OAAA,GAAAD,sBAAA,CAAAH,OAAA;AAIA,IAAMK,KAAK,GAAG,IAAAC,eAAM,EAAC,iBAAiB,CAAC;AAAA,IAsB1BC,KAAK,GAAAC,OAAA,CAAAD,KAAA,oBAAAE,aAAA,CAAAC,OAAA,EAChB,SAAAH,MACSI,KAAa,EAEpB;EAAA,IAAAC,gBAAA,CAAAF,OAAA,QAAAH,KAAA;EAAA,IADOM,MAAM,GAAAC,SAAA,CAAAD,MAAA,QAAAC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC;EAAA,KADVH,KAAa,GAAbA,KAAa;EAAA,KACbE,MAAM,GAANA,MAAM;AACZ;AAAA,IAGCG,SAAS;EAYb,SAAAA,UAAYC,MAAc,EAAEC,OAAgB,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAP,gBAAA,CAAAF,OAAA,QAAAM,SAAA;IAC5C,IAAI,CAACE,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACG,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,IAAI,GAAG,IAAI,CAACL,MAAM,CAACM,OAAO;IAC/B;IACA,IAAI,CAACC,MAAM,GAAG,IAAI,CAACP,MAAM,CAACQ,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC;IAChD;IACA,IAAI,CAACC,UAAU,GAAG,IAAInB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACjC,IAAI,CAACoB,SAAS,GAAG,IAAI,CAACD,UAAU;IAChC,IAAI,CAACE,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACC,cAAc,CAAC,CAAC;IACrB,IAAI,CAACZ,OAAO,CAACa,SAAS,CAAC,iBAAiB,EAAEC,QAAQ,EAAE,YAAM;MACxD,IAAI,CAACb,KAAI,CAACE,SAAS,IAAI,CAACF,KAAI,CAACC,SAAS,EAAE;QACtCa,UAAU,CAACd,KAAI,CAACe,MAAM,CAACC,IAAI,CAAChB,KAAI,EAAEiB,gBAAO,CAACC,OAAO,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC;MAC7D;IACF,CAAC,CAAC;IACF,IAAI,CAACpB,OAAO,CAACqB,EAAE,CAACH,gBAAO,CAACI,MAAM,CAACC,oBAAoB,EAAE,YAAM;MACzD,IAAI,CAACtB,KAAI,CAACuB,QAAQ,CAAC,CAAC,EAAE;MACtB,IAAMC,MAAM,GAAGxB,KAAI,CAACyB,cAAc,CAAC,CAAC;MACpC,IAAID,MAAM,IAAI,IAAI,EAAE;MACpB,IAAIA,MAAM,CAACE,KAAK,CAACC,IAAI,KAAK3B,KAAI,CAACK,MAAM,CAACuB,QAAQ,EAAE,OAAO,CAAC;MACxD5B,KAAI,CAACD,OAAO,CAAC8B,IAAI,CACfZ,gBAAO,CAACI,MAAM,CAACS,aAAa,EAC5B,UAACC,MAAM,EAAEC,SAA2B,EAAK;QACvC,IAAI;UACF,IACEhC,KAAI,CAACG,IAAI,CAAC8B,QAAQ,CAACT,MAAM,CAACE,KAAK,CAACC,IAAI,CAAC,IACrC3B,KAAI,CAACG,IAAI,CAAC8B,QAAQ,CAACT,MAAM,CAACU,GAAG,CAACP,IAAI,CAAC,EACnC;YACA3B,KAAI,CAACmC,cAAc,CACjBX,MAAM,CAACE,KAAK,CAACC,IAAI,EACjBH,MAAM,CAACE,KAAK,CAACU,MAAM,EACnBZ,MAAM,CAACU,GAAG,CAACP,IAAI,EACfH,MAAM,CAACU,GAAG,CAACE,MACb,CAAC;UACH;UACA,IAAMC,iBAAiB,GAAGL,SAAS,CAACM,IAAI,CACrC,UAAAC,QAAQ;YAAA,OACPA,QAAQ,CAACC,IAAI,KAAK,eAAe,IACjCD,QAAQ,CAACC,IAAI,KAAK,WAAW,IAC5BD,QAAQ,CAACC,IAAI,KAAK,YAAY,IAC7BD,QAAQ,CAACE,MAAM,KAAKzC,KAAI,CAACG,IAC/B;UAAA,EAAC;UACDH,KAAI,CAACe,MAAM,CAACsB,iBAAiB,GAAGpB,gBAAO,CAACC,OAAO,CAACwB,MAAM,GAAGX,MAAM,CAAC;QAClE,CAAC,CAAC,OAAOY,OAAO,EAAE;UAChB;QAAA;MAEJ,CACF,CAAC;IACH,CAAC,CAAC;IACF,IAAI,CAAC5C,OAAO,CAACqB,EAAE,CAACH,gBAAO,CAACI,MAAM,CAACuB,eAAe,EAAE,UAACZ,SAAS,EAAEa,OAAO,EAAK;MACtE,IAAIA,OAAO,CAACC,KAAK,EAAE;QACjB,IAAAC,cAAA,GAAuDF,OAAO,CAACC,KAAK;UAA5DE,SAAS,GAAAD,cAAA,CAATC,SAAS;UAAEC,WAAW,GAAAF,cAAA,CAAXE,WAAW;UAAEC,OAAO,GAAAH,cAAA,CAAPG,OAAO;UAAEC,SAAA,GAAAJ,cAAA,CAAAI,SAAA;QACzCnD,KAAI,CAACmC,cAAc,CAACa,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAEC,SAAS,CAAC;QAC/DnD,KAAI,CAACe,MAAM,CAACE,gBAAO,CAACC,OAAO,CAACwB,MAAM,CAAC;MACrC;IACF,CAAC,CAAC;IACF,IAAI,CAAC3B,MAAM,CAACE,gBAAO,CAACC,OAAO,CAACwB,MAAM,CAAC;EACrC;EAAA,WAAApD,aAAA,CAAAC,OAAA,EAAAM,SAAA;IAAAuD,GAAA;IAAAC,KAAA,EAEA,SAAA3C,iBAAiBA,CAAA,EAAG;MAAA,IAAA4C,MAAA;MAClB,IAAI,CAACvD,OAAO,CAACqB,EAAE,CAACH,gBAAO,CAACI,MAAM,CAACkC,wBAAwB,EAAE,YAAM;QAC7DD,MAAI,CAACrD,SAAS,GAAG,IAAI;MACvB,CAAC,CAAC;MACF,IAAI,CAACF,OAAO,CAACqB,EAAE,CAACH,gBAAO,CAACI,MAAM,CAACmC,eAAe,EAAE,YAAM;QACpDF,MAAI,CAACrD,SAAS,GAAG,KAAK;QACtB,IAAIqD,MAAI,CAACjD,MAAM,CAACoD,MAAM,EAAE;UACtB,IAAMX,KAAK,GAAGQ,MAAI,CAACjD,MAAM,CAACqD,OAAO,CAAC,CAAC;UACnC,IAAI,CAACZ,KAAK,EAAE;UACZhC,UAAU,CAAC,YAAM;YACfwC,MAAI,CAACnB,cAAc,CACjBW,KAAK,CAACE,SAAS,EACfF,KAAK,CAACG,WAAW,EACjBH,KAAK,CAACI,OAAO,EACbJ,KAAK,CAACK,SACR,CAAC;UACH,CAAC,EAAE,CAAC,CAAC;QACP;MACF,CAAC,CAAC;IACJ;EAAA;IAAAC,GAAA;IAAAC,KAAA,EAEA,SAAA1C,cAAcA,CAAA,EAAG;MAAA,IAAAgD,MAAA;MACf,IAAI,CAAC5D,OAAO,CAACa,SAAS,CAAC,WAAW,EAAEC,QAAQ,CAAC+C,IAAI,EAAE,YAAM;QACvDD,MAAI,CAACzD,SAAS,GAAG,IAAI;MACvB,CAAC,CAAC;MACF,IAAI,CAACH,OAAO,CAACa,SAAS,CAAC,SAAS,EAAEC,QAAQ,CAAC+C,IAAI,EAAE,YAAM;QACrDD,MAAI,CAACzD,SAAS,GAAG,KAAK;QACtByD,MAAI,CAAC5C,MAAM,CAACE,gBAAO,CAACC,OAAO,CAACC,IAAI,CAAC;MACnC,CAAC,CAAC;IACJ;EAAA;IAAAiC,GAAA;IAAAC,KAAA,EAEA,SAAAQ,KAAKA,CAAA,EAAG;MACN,IAAI,IAAI,CAACtC,QAAQ,CAAC,CAAC,EAAE;MACrB,IAAI,CAACpB,IAAI,CAAC0D,KAAK,CAAC;QAAEC,aAAa,EAAE;MAAK,CAAC,CAAC;MACxC,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACxD,UAAU,CAAC;IAChC;EAAA;IAAA6C,GAAA;IAAAC,KAAA,EAEA,SAAAW,MAAMA,CAACA,OAAc,EAAEX,KAAc,EAAE;MACrC,IAAI,CAACvD,MAAM,CAACiB,MAAM,CAAC,CAAC;MACpB,IAAMkD,WAAW,GAAG,IAAI,CAACxC,cAAc,CAAC,CAAC;MACzC,IACEwC,WAAW,IAAI,IAAI,IACnB,CAACA,WAAW,CAACzC,MAAM,CAAC0C,SAAS,IAC7B,IAAI,CAACpE,MAAM,CAACqE,KAAK,CAACH,OAAM,EAAEI,gBAAK,CAACC,KAAK,CAAC,EAEtC;MACF,IAAIJ,WAAW,CAACvC,KAAK,CAACC,IAAI,KAAK,IAAI,CAACtB,MAAM,CAACuB,QAAQ,EAAE;QACnD,IAAM0C,IAAI,GAAG,IAAI,CAACxE,MAAM,CAACyE,IAAI,CAACN,WAAW,CAACvC,KAAK,CAACC,IAAI,EAAE,KAAK,CAAC;QAC5D,IAAI2C,IAAI,IAAI,IAAI,EAAE;QAClB;QACA,IAAIA,IAAI,YAAYE,mBAAQ,EAAE;UAC5B,IAAMC,KAAK,GAAGH,IAAI,CAACI,KAAK,CAACT,WAAW,CAACvC,KAAK,CAACU,MAAM,CAAC;UAClDkC,IAAI,CAACb,MAAM,CAACkB,YAAY,CAAC,IAAI,CAACtE,MAAM,EAAEoE,KAAK,CAAC;QAC9C,CAAC,MAAM;UACL;UACAH,IAAI,CAACK,YAAY,CAAC,IAAI,CAACtE,MAAM,EAAE4D,WAAW,CAACvC,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC;QAC1D;QACA,IAAI,CAACtB,MAAM,CAACuE,MAAM,CAAC,CAAC;MACtB;MACA,IAAI,CAACvE,MAAM,CAAC2D,MAAM,CAACA,OAAM,EAAEX,KAAK,CAAC;MACjC,IAAI,CAACvD,MAAM,CAAC+E,QAAQ,CAAC,CAAC;MACtB,IAAI,CAAC1C,cAAc,CAAC,IAAI,CAAC9B,MAAM,CAACuB,QAAQ,EAAE,IAAI,CAACvB,MAAM,CAACuB,QAAQ,CAACkD,IAAI,CAACpF,MAAM,CAAC;MAC3E,IAAI,CAACqB,MAAM,CAAC,CAAC;IACf;EAAA;IAAAqC,GAAA;IAAAC,KAAA,EAEA,SAAA0B,SAASA,CAACvF,KAAa,EAAc;MAAA,IAAZE,MAAM,GAAAC,SAAA,CAAAD,MAAA,QAAAC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC;MACjC,IAAMqF,YAAY,GAAG,IAAI,CAAClF,MAAM,CAACJ,MAAM,CAAC,CAAC;MACzCF,KAAK,GAAGyF,IAAI,CAACC,GAAG,CAAC1F,KAAK,EAAEwF,YAAY,GAAG,CAAC,CAAC;MACzCtF,MAAM,GAAGuF,IAAI,CAACC,GAAG,CAAC1F,KAAK,GAAGE,MAAM,EAAEsF,YAAY,GAAG,CAAC,CAAC,GAAGxF,KAAK;MAC3D,IAAImC,IAAU;MACd,IAAAwD,iBAAA,GAAqB,IAAI,CAACrF,MAAM,CAACsF,IAAI,CAAC5F,KAAK,CAAC;QAAA6F,kBAAA,OAAAC,eAAA,CAAA/F,OAAA,EAAA4F,iBAAA;QAAvCC,IAAI,GAAAC,kBAAA;QAAEjD,MAAM,GAAAiD,kBAAA;MACjB,IAAID,IAAI,IAAI,IAAI,EAAE,OAAO,IAAI;MAC7B,IAAI1F,MAAM,GAAG,CAAC,IAAI0C,MAAM,KAAKgD,IAAI,CAAC1F,MAAM,CAAC,CAAC,EAAE;QAC1C,IAAA6F,kBAAA,GAAe,IAAI,CAACzF,MAAM,CAACsF,IAAI,CAAC5F,KAAK,GAAG,CAAC,CAAC;UAAAgG,kBAAA,OAAAF,eAAA,CAAA/F,OAAA,EAAAgG,kBAAA;UAAnCE,IAAI,GAAAD,kBAAA;QACX,IAAIC,IAAI,EAAE;UACR,IAAAC,iBAAA,GAAe,IAAI,CAAC5F,MAAM,CAAC6F,IAAI,CAACnG,KAAK,CAAC;YAAAoG,kBAAA,OAAAN,eAAA,CAAA/F,OAAA,EAAAmG,iBAAA;YAA/BC,IAAI,GAAAC,kBAAA;UACX,IAAAC,kBAAA,GAAmB,IAAI,CAAC/F,MAAM,CAAC6F,IAAI,CAACnG,KAAK,GAAG,CAAC,CAAC;YAAAsG,kBAAA,OAAAR,eAAA,CAAA/F,OAAA,EAAAsG,kBAAA;YAAvCE,QAAQ,GAAAD,kBAAA;UACf,IAAIH,IAAI,KAAKI,QAAQ,EAAE;YACrBX,IAAI,GAAGK,IAAI;YACXrD,MAAM,GAAG,CAAC;UACZ;QACF;MACF;MAAA,IAAA4D,cAAA,GACiBZ,IAAI,CAACa,QAAQ,CAAC7D,MAAM,EAAE,IAAI,CAAC;MAAA,IAAA8D,eAAA,OAAAZ,eAAA,CAAA/F,OAAA,EAAAyG,cAAA;MAA3CrE,IAAI,GAAAuE,eAAA;MAAE9D,MAAM,GAAA8D,eAAA;MACb,IAAMpD,KAAK,GAAGjC,QAAQ,CAACsF,WAAW,CAAC,CAAC;MACpC,IAAIzG,MAAM,GAAG,CAAC,EAAE;QACdoD,KAAK,CAACsD,QAAQ,CAACzE,IAAI,EAAES,MAAM,CAAC;QAAA,IAAAiE,kBAAA,GACX,IAAI,CAACvG,MAAM,CAACsF,IAAI,CAAC5F,KAAK,GAAGE,MAAM,CAAC;QAAA,IAAA4G,kBAAA,OAAAhB,eAAA,CAAA/F,OAAA,EAAA8G,kBAAA;QAAhDjB,IAAI,GAAAkB,kBAAA;QAAElE,MAAM,GAAAkE,kBAAA;QACb,IAAIlB,IAAI,IAAI,IAAI,EAAE,OAAO,IAAI;QAAA,IAAAmB,eAAA,GACZnB,IAAI,CAACa,QAAQ,CAAC7D,MAAM,EAAE,IAAI,CAAC;QAAA,IAAAoE,eAAA,OAAAlB,eAAA,CAAA/F,OAAA,EAAAgH,eAAA;QAA3C5E,IAAI,GAAA6E,eAAA;QAAEpE,MAAM,GAAAoE,eAAA;QACb1D,KAAK,CAAC2D,MAAM,CAAC9E,IAAI,EAAES,MAAM,CAAC;QAC1B,OAAOU,KAAK,CAAC4D,qBAAqB,CAAC,CAAC;MACtC;MACA,IAAIC,IAAsB,GAAG,MAAM;MACnC,IAAIC,IAAa;MACjB,IAAIjF,IAAI,YAAYkF,IAAI,EAAE;QACxB;QACA;QACA;QACA;QACA;QACA,IAAI,CAAClF,IAAI,CAACmD,IAAI,CAACpF,MAAM,EAAE;UACrB,OAAO,IAAI;QACb;QACA,IAAI0C,MAAM,GAAGT,IAAI,CAACmD,IAAI,CAACpF,MAAM,EAAE;UAC7BoD,KAAK,CAACsD,QAAQ,CAACzE,IAAI,EAAES,MAAM,CAAC;UAC5BU,KAAK,CAAC2D,MAAM,CAAC9E,IAAI,EAAES,MAAM,GAAG,CAAC,CAAC;QAChC,CAAC,MAAM;UACLU,KAAK,CAACsD,QAAQ,CAACzE,IAAI,EAAES,MAAM,GAAG,CAAC,CAAC;UAChCU,KAAK,CAAC2D,MAAM,CAAC9E,IAAI,EAAES,MAAM,CAAC;UAC1BuE,IAAI,GAAG,OAAO;QAChB;QACAC,IAAI,GAAG9D,KAAK,CAAC4D,qBAAqB,CAAC,CAAC;MACtC,CAAC,MAAM;QACL,IAAI,EAAEtB,IAAI,CAAChF,OAAO,YAAY0G,OAAO,CAAC,EAAE,OAAO,IAAI;QACnDF,IAAI,GAAGxB,IAAI,CAAChF,OAAO,CAACsG,qBAAqB,CAAC,CAAC;QAC3C,IAAItE,MAAM,GAAG,CAAC,EAAEuE,IAAI,GAAG,OAAO;MAChC;MACA,OAAO;QACLI,MAAM,EAAEH,IAAI,CAACI,GAAG,GAAGJ,IAAI,CAACK,MAAM;QAC9BA,MAAM,EAAEL,IAAI,CAACK,MAAM;QACnBC,IAAI,EAAEN,IAAI,CAACD,IAAI,CAAC;QAChBQ,KAAK,EAAEP,IAAI,CAACD,IAAI,CAAC;QACjBK,GAAG,EAAEJ,IAAI,CAACI,GAAG;QACbI,KAAK,EAAE;MACT,CAAC;IACH;EAAA;IAAAhE,GAAA;IAAAC,KAAA,EAEA,SAAA5B,cAAcA,CAAA,EAA2B;MACvC,IAAM4F,SAAS,GAAGxG,QAAQ,CAACyG,YAAY,CAAC,CAAC;MACzC,IAAID,SAAS,IAAI,IAAI,IAAIA,SAAS,CAACE,UAAU,IAAI,CAAC,EAAE,OAAO,IAAI;MAC/D,IAAMtD,WAAW,GAAGoD,SAAS,CAACG,UAAU,CAAC,CAAC,CAAC;MAC3C,IAAIvD,WAAW,IAAI,IAAI,EAAE,OAAO,IAAI;MACpC,IAAMnB,KAAK,GAAG,IAAI,CAAC2E,eAAe,CAACxD,WAAW,CAAC;MAC/C/E,KAAK,CAACwI,IAAI,CAAC,gBAAgB,EAAE5E,KAAK,CAAC;MACnC,OAAOA,KAAK;IACd;EAAA;IAAAM,GAAA;IAAAC,KAAA,EAEA,SAAAsE,QAAQA,CAAA,EAA4C;MAClD,IAAMxH,IAAI,GAAG,IAAI,CAACL,MAAM,CAACM,OAAO;MAChC,IAAI,aAAa,IAAID,IAAI,IAAI,CAACA,IAAI,CAACyH,WAAW,EAAE;QAC9C;QACA;QACA,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC;MACrB;MACA,IAAMC,UAAU,GAAG,IAAI,CAACpG,cAAc,CAAC,CAAC;MACxC,IAAIoG,UAAU,IAAI,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC;MAC3C,IAAM/E,KAAK,GAAG,IAAI,CAACgF,iBAAiB,CAACD,UAAU,CAAC;MAChD,OAAO,CAAC/E,KAAK,EAAE+E,UAAU,CAAC;IAC5B;EAAA;IAAAzE,GAAA;IAAAC,KAAA,EAEA,SAAA9B,QAAQA,CAAA,EAAY;MAClB,OACEV,QAAQ,CAACkH,aAAa,KAAK,IAAI,CAAC5H,IAAI,IACnCU,QAAQ,CAACkH,aAAa,IAAI,IAAI,IAC7B9F,QAAQ,CAAC,IAAI,CAAC9B,IAAI,EAAEU,QAAQ,CAACkH,aAAa,CAAE;IAElD;EAAA;IAAA3E,GAAA;IAAAC,KAAA,EAEA,SAAAyE,iBAAiBA,CAAChF,KAAsB,EAAE;MAAA,IAAAkF,MAAA;MACxC,IAAMC,SAA2B,GAAG,CAClC,CAACnF,KAAK,CAACpB,KAAK,CAACC,IAAI,EAAEmB,KAAK,CAACpB,KAAK,CAACU,MAAM,CAAC,CACvC;MACD,IAAI,CAACU,KAAK,CAACtB,MAAM,CAAC0C,SAAS,EAAE;QAC3B+D,SAAS,CAACC,IAAI,CAAC,CAACpF,KAAK,CAACZ,GAAG,CAACP,IAAI,EAAEmB,KAAK,CAACZ,GAAG,CAACE,MAAM,CAAC,CAAC;MACpD;MACA,IAAM+F,OAAO,GAAGF,SAAS,CAACG,GAAG,CAAE,UAAAnC,QAAQ,EAAK;QAC1C,IAAAoC,SAAA,OAAA/C,eAAA,CAAA/F,OAAA,EAAuB0G,QAAQ;UAAxBtE,IAAI,GAAA0G,SAAA;UAAEjG,MAAM,GAAAiG,SAAA;QACnB,IAAM/D,IAAI,GAAG0D,MAAI,CAAClI,MAAM,CAACyE,IAAI,CAAC5C,IAAI,EAAE,IAAI,CAAC;QACzC;QACA,IAAMnC,KAAK,GAAG8E,IAAI,CAAClC,MAAM,CAAC4F,MAAI,CAAClI,MAAM,CAAC;QACtC,IAAIsC,MAAM,KAAK,CAAC,EAAE;UAChB,OAAO5C,KAAK;QACd;QACA,IAAI8E,IAAI,YAAYE,mBAAQ,EAAE;UAC5B,OAAOhF,KAAK,GAAG8E,IAAI,CAAC9E,KAAK,CAACmC,IAAI,EAAES,MAAM,CAAC;QACzC;QACA;QACA,OAAO5C,KAAK,GAAG8E,IAAI,CAAC5E,MAAM,CAAC,CAAC;MAC9B,CAAC,CAAC;MACF,IAAMwC,GAAG,GAAG+C,IAAI,CAACC,GAAG,CAACD,IAAI,CAACqD,GAAG,CAAAC,KAAA,CAARtD,IAAI,MAAAuD,mBAAA,CAAAjJ,OAAA,EAAQ4I,OAAO,EAAC,EAAE,IAAI,CAACrI,MAAM,CAACJ,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;MACpE,IAAMgC,KAAK,GAAGuD,IAAI,CAACC,GAAG,CAAAqD,KAAA,CAARtD,IAAI,GAAK/C,GAAG,EAAAuG,MAAA,KAAAD,mBAAA,CAAAjJ,OAAA,EAAK4I,OAAO,GAAC;MACvC,OAAO,IAAI/I,KAAK,CAACsC,KAAK,EAAEQ,GAAG,GAAGR,KAAK,CAAC;IACtC;EAAA;IAAA0B,GAAA;IAAAC,KAAA,EAEA,SAAAoE,eAAeA,CAACxD,WAAwB,EAAE;MACxC,IACE,CAAChC,QAAQ,CAAC,IAAI,CAAC9B,IAAI,EAAE8D,WAAW,CAACyE,cAAc,CAAC,IAC/C,CAACzE,WAAW,CAACC,SAAS,IAAI,CAACjC,QAAQ,CAAC,IAAI,CAAC9B,IAAI,EAAE8D,WAAW,CAAC0E,YAAY,CAAE,EAC1E;QACA,OAAO,IAAI;MACb;MACA,IAAM7F,KAAK,GAAG;QACZpB,KAAK,EAAE;UACLC,IAAI,EAAEsC,WAAW,CAACyE,cAAc;UAChCtG,MAAM,EAAE6B,WAAW,CAAChB;QACtB,CAAC;QACDf,GAAG,EAAE;UAAEP,IAAI,EAAEsC,WAAW,CAAC0E,YAAY;UAAEvG,MAAM,EAAE6B,WAAW,CAACd;QAAU,CAAC;QACtE3B,MAAM,EAAEyC;MACV,CAAC;MACD,CAACnB,KAAK,CAACpB,KAAK,EAAEoB,KAAK,CAACZ,GAAG,CAAC,CAAC0G,OAAO,CAAE,UAAA3C,QAAQ,EAAK;QAC7C,IAAMtE,IAAI,GAAasE,QAAQ,CAAzBtE,IAAI;UAAES,MAAA,GAAW6D,QAAQ,CAAnB7D,MAAA;QACZ,OAAO,EAAET,IAAI,YAAYkF,IAAI,CAAC,IAAIlF,IAAI,CAACkH,UAAU,CAACnJ,MAAM,GAAG,CAAC,EAAE;UAC5D,IAAIiC,IAAI,CAACkH,UAAU,CAACnJ,MAAM,GAAG0C,MAAM,EAAE;YACnCT,IAAI,GAAGA,IAAI,CAACkH,UAAU,CAACzG,MAAM,CAAC;YAC9BA,MAAM,GAAG,CAAC;UACZ,CAAC,MAAM,IAAIT,IAAI,CAACkH,UAAU,CAACnJ,MAAM,KAAK0C,MAAM,EAAE;YAC5C;YACAT,IAAI,GAAGA,IAAI,CAACmH,SAAS;YACrB,IAAInH,IAAI,YAAYkF,IAAI,EAAE;cACxBzE,MAAM,GAAGT,IAAI,CAACmD,IAAI,CAACpF,MAAM;YAC3B,CAAC,MAAM,IAAIiC,IAAI,CAACkH,UAAU,CAACnJ,MAAM,GAAG,CAAC,EAAE;cACrC;cACA0C,MAAM,GAAGT,IAAI,CAACkH,UAAU,CAACnJ,MAAM;YACjC,CAAC,MAAM;cACL;cACA0C,MAAM,GAAGT,IAAI,CAACkH,UAAU,CAACnJ,MAAM,GAAG,CAAC;YACrC;UACF,CAAC,MAAM;YACL;UACF;QACF;QACAuG,QAAQ,CAACtE,IAAI,GAAGA,IAAI;QACpBsE,QAAQ,CAAC7D,MAAM,GAAGA,MAAM;MAC1B,CAAC,CAAC;MACF,OAAOU,KAAK;IACd;EAAA;IAAAM,GAAA;IAAAC,KAAA,EAEA,SAAA0F,aAAaA,CAACjG,KAAY,EAA8C;MAAA,IAAAkG,MAAA;MACtE,IAAMhE,YAAY,GAAG,IAAI,CAAClF,MAAM,CAACJ,MAAM,CAAC,CAAC;MAEzC,IAAMuJ,WAAW,GAAG,SAAdA,WAAWA,CACfzJ,KAAa,EACb0J,SAAkB,EACQ;QAC1B1J,KAAK,GAAGyF,IAAI,CAACC,GAAG,CAACF,YAAY,GAAG,CAAC,EAAExF,KAAK,CAAC;QACzC,IAAA2J,kBAAA,GAA2BH,MAAI,CAAClJ,MAAM,CAACsF,IAAI,CAAC5F,KAAK,CAAC;UAAA4J,mBAAA,OAAA9D,eAAA,CAAA/F,OAAA,EAAA4J,kBAAA;UAA3C/D,IAAI,GAAAgE,mBAAA;UAAEC,UAAU,GAAAD,mBAAA;QACvB,OAAOhE,IAAI,GAAGA,IAAI,CAACa,QAAQ,CAACoD,UAAU,EAAEH,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;MACjE,CAAC;MACD,UAAAT,MAAA,KAAAD,mBAAA,CAAAjJ,OAAA,EACK0J,WAAW,CAACnG,KAAK,CAACtD,KAAK,EAAE,KAAK,CAAC,OAAAgJ,mBAAA,CAAAjJ,OAAA,EAC/B0J,WAAW,CAACnG,KAAK,CAACtD,KAAK,GAAGsD,KAAK,CAACpD,MAAM,EAAE,IAAI,CAAC;IAEpD;EAAA;IAAA0D,GAAA;IAAAC,KAAA,EAEA,SAAAlB,cAAcA,CACZa,SAAsB,EACtBC,WAAoB,EAIpB;MAAA,IAHAC,OAAO,GAAAvD,SAAA,CAAAD,MAAA,QAAAC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGqD,SAAS;MAAA,IACnBG,SAAS,GAAAxD,SAAA,CAAAD,MAAA,QAAAC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGsD,WAAW;MAAA,IACvBqG,KAAK,GAAA3J,SAAA,CAAAD,MAAA,QAAAC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,KAAK;MAEbT,KAAK,CAACwI,IAAI,CAAC,gBAAgB,EAAE1E,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAEC,SAAS,CAAC;MACxE,IACEH,SAAS,IAAI,IAAI,KAChB,IAAI,CAAC7C,IAAI,CAACoJ,UAAU,IAAI,IAAI,IAC3BvG,SAAS,CAACuG,UAAU,IAAI,IAAI;MAC5B;MACArG,OAAO,CAACqG,UAAU,IAAI,IAAI,CAAC,EAC7B;QACA;MACF;MACA,IAAMlC,SAAS,GAAGxG,QAAQ,CAACyG,YAAY,CAAC,CAAC;MACzC,IAAID,SAAS,IAAI,IAAI,EAAE;MACvB,IAAIrE,SAAS,IAAI,IAAI,EAAE;QACrB,IAAI,CAAC,IAAI,CAACzB,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACpB,IAAI,CAAC0D,KAAK,CAAC;UAAEC,aAAa,EAAE;QAAK,CAAC,CAAC;QAC9D,IAAA0F,IAAA,GAAmB,IAAI,CAAC/H,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC;UAAtCD,MAAA,GAAAgI,IAAA,CAAAhI,MAAA;QACR,IACEA,MAAM,IAAI,IAAI,IACd8H,KAAK,IACLtG,SAAS,KAAKxB,MAAM,CAACkH,cAAc,IACnCzF,WAAW,KAAKzB,MAAM,CAACyB,WAAW,IAClCC,OAAO,KAAK1B,MAAM,CAACmH,YAAY,IAC/BxF,SAAS,KAAK3B,MAAM,CAAC2B,SAAS,EAC9B;UACA,IAAIH,SAAS,YAAY8D,OAAO,IAAI9D,SAAS,CAACyG,OAAO,KAAK,IAAI,EAAE;YAC9D;YACAxG,WAAW,GAAGyG,KAAK,CAACC,IAAI,CAAC3G,SAAS,CAACuG,UAAU,CAACV,UAAU,CAAC,CAACe,OAAO,CAC/D5G,SACF,CAAC;YACDA,SAAS,GAAGA,SAAS,CAACuG,UAAU;UAClC;UACA,IAAIrG,OAAO,YAAY4D,OAAO,IAAI5D,OAAO,CAACuG,OAAO,KAAK,IAAI,EAAE;YAC1D;YACAtG,SAAS,GAAGuG,KAAK,CAACC,IAAI,CAACzG,OAAO,CAACqG,UAAU,CAACV,UAAU,CAAC,CAACe,OAAO,CAC3D1G,OACF,CAAC;YACDA,OAAO,GAAGA,OAAO,CAACqG,UAAU;UAC9B;UACA,IAAMzG,KAAK,GAAGjC,QAAQ,CAACsF,WAAW,CAAC,CAAC;UACpC;UACArD,KAAK,CAACsD,QAAQ,CAACpD,SAAS,EAAEC,WAAW,CAAC;UACtC;UACAH,KAAK,CAAC2D,MAAM,CAACvD,OAAO,EAAEC,SAAS,CAAC;UAChCkE,SAAS,CAACwC,eAAe,CAAC,CAAC;UAC3BxC,SAAS,CAACyC,QAAQ,CAAChH,KAAK,CAAC;QAC3B;MACF,CAAC,MAAM;QACLuE,SAAS,CAACwC,eAAe,CAAC,CAAC;QAC3B,IAAI,CAAC1J,IAAI,CAAC4J,IAAI,CAAC,CAAC;MAClB;IACF;EAAA;IAAA3G,GAAA;IAAAC,KAAA,EAIA,SAAAU,QAAQA,CACNjB,KAAmB,EAGb;MAAA,IAFNwG,KAA8B,GAAA3J,SAAA,CAAAD,MAAA,QAAAC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,KAAK;MAAA,IACtCoC,MAAqB,GAAApC,SAAA,CAAAD,MAAA,QAAAC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGsB,gBAAO,CAACC,OAAO,CAAC8I,GAAG;MAE3C,IAAI,OAAOV,KAAK,KAAK,QAAQ,EAAE;QAC7BvH,MAAM,GAAGuH,KAAK;QACdA,KAAK,GAAG,KAAK;MACf;MACApK,KAAK,CAACwI,IAAI,CAAC,UAAU,EAAE5E,KAAK,CAAC;MAC7B,IAAIA,KAAK,IAAI,IAAI,EAAE;QACjB,IAAMmH,IAAI,GAAG,IAAI,CAAClB,aAAa,CAACjG,KAAK,CAAC;QACtC,IAAI,CAACX,cAAc,CAAAoG,KAAA,CAAnB,IAAI,MAAAC,mBAAA,CAAAjJ,OAAA,EAAmB0K,IAAI,EAAAxB,MAAA,EAAEa,KAAK,GAAC;MACrC,CAAC,MAAM;QACL,IAAI,CAACnH,cAAc,CAAC,IAAI,CAAC;MAC3B;MACA,IAAI,CAACpB,MAAM,CAACgB,MAAM,CAAC;IACrB;EAAA;IAAAqB,GAAA;IAAAC,KAAA,EAEA,SAAAtC,MAAMA,CAAA,EAA+C;MAAA,IAA9CgB,MAAqB,GAAApC,SAAA,CAAAD,MAAA,QAAAC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGsB,gBAAO,CAACC,OAAO,CAACC,IAAI;MACjD,IAAM+I,QAAQ,GAAG,IAAI,CAAC1J,SAAS;MAC/B,IAAA2J,cAAA,GAAiC,IAAI,CAACxC,QAAQ,CAAC,CAAC;QAAAyC,eAAA,OAAA9E,eAAA,CAAA/F,OAAA,EAAA4K,cAAA;QAAzC3J,SAAS,GAAA4J,eAAA;QAAEnG,WAAW,GAAAmG,eAAA;MAC7B,IAAI,CAAC5J,SAAS,GAAGA,SAAS;MAC1B,IAAI,CAACC,UAAU,GAAGwD,WAAW;MAC7B,IAAI,IAAI,CAACzD,SAAS,IAAI,IAAI,EAAE;QAC1B,IAAI,CAACD,UAAU,GAAG,IAAI,CAACC,SAAS;MAClC;MACA,IAAI,CAAC,IAAA6J,iBAAO,EAACH,QAAQ,EAAE,IAAI,CAAC1J,SAAS,CAAC,EAAE;QAAA,IAAA8J,aAAA;QACtC,IACE,CAAC,IAAI,CAACrK,SAAS,IACfgE,WAAW,IAAI,IAAI,IACnBA,WAAW,CAACzC,MAAM,CAAC0C,SAAS,IAC5BD,WAAW,CAACvC,KAAK,CAACC,IAAI,KAAK,IAAI,CAACtB,MAAM,CAACuB,QAAQ,EAC/C;UACA,IAAMkB,KAAK,GAAG,IAAI,CAACzC,MAAM,CAACqD,OAAO,CAAC,CAAC;UACnC,IAAIZ,KAAK,EAAE;YACT,IAAI,CAACX,cAAc,CACjBW,KAAK,CAACE,SAAS,EACfF,KAAK,CAACG,WAAW,EACjBH,KAAK,CAACI,OAAO,EACbJ,KAAK,CAACK,SACR,CAAC;UACH;QACF;QACA,IAAM8G,IAAI,GAAG,CACXhJ,gBAAO,CAACI,MAAM,CAACkJ,gBAAgB,EAC/B,IAAAC,mBAAS,EAAC,IAAI,CAAChK,SAAS,CAAC,EACzB,IAAAgK,mBAAS,EAACN,QAAQ,CAAC,EACnBnI,MAAM,CACP;QACD,CAAAuI,aAAA,OAAI,CAACvK,OAAO,EAAC0K,IAAI,CAAAlC,KAAA,CAAA+B,aAAA,GAACrJ,gBAAO,CAACI,MAAM,CAACqJ,aAAa,EAAAjC,MAAA,CAAKwB,IAAI,EAAC;QACxD,IAAIlI,MAAM,KAAKd,gBAAO,CAACC,OAAO,CAACwB,MAAM,EAAE;UAAA,IAAAiI,cAAA;UACrC,CAAAA,cAAA,OAAI,CAAC5K,OAAO,EAAC0K,IAAI,CAAAlC,KAAA,CAAAoC,cAAA,EAAIV,IAAI,CAAC;QAC5B;MACF;IACF;EAAA;AAAA;AAGF,SAAShI,QAAQA,CAACwB,MAAY,EAAEmH,UAAgB,EAAE;EAChD,IAAI;IACF;IACAA,UAAU,CAACrB,UAAU,CAAC,CAAC;EACzB,CAAC,CAAC,OAAOsB,CAAC,EAAE;IACV,OAAO,KAAK;EACd;EACA,OAAOpH,MAAM,CAACxB,QAAQ,CAAC2I,UAAU,CAAC;AACpC;AAAA,IAAAE,QAAA,GAAAzL,OAAA,CAAAE,OAAA,GAEeM,SAAS", "ignoreList": []}]}