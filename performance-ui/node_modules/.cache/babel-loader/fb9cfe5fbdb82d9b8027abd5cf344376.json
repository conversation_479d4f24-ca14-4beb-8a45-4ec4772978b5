{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/core/composition.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/core/composition.js", "mtime": 1753510684086}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_embed", "_interopRequireDefault", "require", "_emitter", "Composition", "scroll", "emitter", "_classCallCheck2", "default", "_defineProperty2", "setupListeners", "_createClass2", "key", "value", "_this", "domNode", "addEventListener", "event", "isComposing", "handleCompositionStart", "queueMicrotask", "handleCompositionEnd", "blot", "target", "Node", "find", "Embed", "emit", "Emitter", "events", "COMPOSITION_BEFORE_START", "batchStart", "COMPOSITION_START", "COMPOSITION_BEFORE_END", "batchEnd", "COMPOSITION_END", "_default", "exports"], "sources": ["../../src/core/composition.ts"], "sourcesContent": ["import Embed from '../blots/embed.js';\nimport type Scroll from '../blots/scroll.js';\nimport Emitter from './emitter.js';\n\nclass Composition {\n  isComposing = false;\n\n  constructor(\n    private scroll: Scroll,\n    private emitter: Emitter,\n  ) {\n    this.setupListeners();\n  }\n\n  private setupListeners() {\n    this.scroll.domNode.addEventListener('compositionstart', (event) => {\n      if (!this.isComposing) {\n        this.handleCompositionStart(event);\n      }\n    });\n\n    this.scroll.domNode.addEventListener('compositionend', (event) => {\n      if (this.isComposing) {\n        // Webkit makes DOM changes after compositionend, so we use microtask to\n        // ensure the order.\n        // https://bugs.webkit.org/show_bug.cgi?id=31902\n        queueMicrotask(() => {\n          this.handleCompositionEnd(event);\n        });\n      }\n    });\n  }\n\n  private handleCompositionStart(event: CompositionEvent) {\n    const blot =\n      event.target instanceof Node\n        ? this.scroll.find(event.target, true)\n        : null;\n\n    if (blot && !(blot instanceof Embed)) {\n      this.emitter.emit(Emitter.events.COMPOSITION_BEFORE_START, event);\n      this.scroll.batchStart();\n      this.emitter.emit(Emitter.events.COMPOSITION_START, event);\n      this.isComposing = true;\n    }\n  }\n\n  private handleCompositionEnd(event: CompositionEvent) {\n    this.emitter.emit(Emitter.events.COMPOSITION_BEFORE_END, event);\n    this.scroll.batchEnd();\n    this.emitter.emit(Emitter.events.COMPOSITION_END, event);\n    this.isComposing = false;\n  }\n}\n\nexport default Composition;\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,QAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAkC,IAE5BE,WAAW;EAGf,SAAAA,YACUC,MAAc,EACdC,OAAgB,EACxB;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAJ,WAAA;IAAA,IAAAK,gBAAA,CAAAD,OAAA,uBALY,KAAK;IAKjB,KAFQH,MAAc,GAAdA,MAAc;IAAA,KACdC,OAAgB,GAAhBA,OAAgB;IAExB,IAAI,CAACI,cAAc,CAAC,CAAC;EACvB;EAAA,WAAAC,aAAA,CAAAH,OAAA,EAAAJ,WAAA;IAAAQ,GAAA;IAAAC,KAAA,EAEQ,SAAAH,cAAcA,CAAA,EAAG;MAAA,IAAAI,KAAA;MACvB,IAAI,CAACT,MAAM,CAACU,OAAO,CAACC,gBAAgB,CAAC,kBAAkB,EAAG,UAAAC,KAAK,EAAK;QAClE,IAAI,CAACH,KAAI,CAACI,WAAW,EAAE;UACrBJ,KAAI,CAACK,sBAAsB,CAACF,KAAK,CAAC;QACpC;MACF,CAAC,CAAC;MAEF,IAAI,CAACZ,MAAM,CAACU,OAAO,CAACC,gBAAgB,CAAC,gBAAgB,EAAG,UAAAC,KAAK,EAAK;QAChE,IAAIH,KAAI,CAACI,WAAW,EAAE;UACpB;UACA;UACA;UACAE,cAAc,CAAC,YAAM;YACnBN,KAAI,CAACO,oBAAoB,CAACJ,KAAK,CAAC;UAClC,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;EAAA;IAAAL,GAAA;IAAAC,KAAA,EAEQ,SAAAM,sBAAsBA,CAACF,KAAuB,EAAE;MACtD,IAAMK,IAAI,GACRL,KAAK,CAACM,MAAM,YAAYC,IAAI,GACxB,IAAI,CAACnB,MAAM,CAACoB,IAAI,CAACR,KAAK,CAACM,MAAM,EAAE,IAAI,CAAC,GACpC,IAAI;MAEV,IAAID,IAAI,IAAI,EAAEA,IAAI,YAAYI,cAAK,CAAC,EAAE;QACpC,IAAI,CAACpB,OAAO,CAACqB,IAAI,CAACC,gBAAO,CAACC,MAAM,CAACC,wBAAwB,EAAEb,KAAK,CAAC;QACjE,IAAI,CAACZ,MAAM,CAAC0B,UAAU,CAAC,CAAC;QACxB,IAAI,CAACzB,OAAO,CAACqB,IAAI,CAACC,gBAAO,CAACC,MAAM,CAACG,iBAAiB,EAAEf,KAAK,CAAC;QAC1D,IAAI,CAACC,WAAW,GAAG,IAAI;MACzB;IACF;EAAA;IAAAN,GAAA;IAAAC,KAAA,EAEQ,SAAAQ,oBAAoBA,CAACJ,KAAuB,EAAE;MACpD,IAAI,CAACX,OAAO,CAACqB,IAAI,CAACC,gBAAO,CAACC,MAAM,CAACI,sBAAsB,EAAEhB,KAAK,CAAC;MAC/D,IAAI,CAACZ,MAAM,CAAC6B,QAAQ,CAAC,CAAC;MACtB,IAAI,CAAC5B,OAAO,CAACqB,IAAI,CAACC,gBAAO,CAACC,MAAM,CAACM,eAAe,EAAElB,KAAK,CAAC;MACxD,IAAI,CAACC,WAAW,GAAG,KAAK;IAC1B;EAAA;AAAA;AAAA,IAAAkB,QAAA,GAAAC,OAAA,CAAA7B,OAAA,GAGaJ,WAAW", "ignoreList": []}]}