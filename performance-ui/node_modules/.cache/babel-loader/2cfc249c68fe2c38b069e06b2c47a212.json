{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/store/modules/settings.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/store/modules/settings.js", "mtime": 1753510684531}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_settings", "_interopRequireDefault", "require", "_dynamicTitle", "sideTheme", "defaultSettings", "showSettings", "topNav", "tagsView", "tagsIcon", "fixedHeader", "sidebarLogo", "dynamicTitle", "footerVisible", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "storageSetting", "JSON", "parse", "localStorage", "getItem", "state", "title", "theme", "undefined", "mutations", "CHANGE_SETTING", "_ref", "key", "value", "hasOwnProperty", "actions", "changeSetting", "_ref2", "data", "commit", "setTitle", "_ref3", "useDynamicTitle", "_default", "exports", "default", "namespaced"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/store/modules/settings.js"], "sourcesContent": ["import defaultSettings from '@/settings'\nimport { useDynamicTitle } from '@/utils/dynamicTitle'\n\nconst { sideTheme, showSettings, topNav, tagsView, tagsIcon, fixedHeader, sidebarLogo, dynamicTitle, footerVisible, footerContent } = defaultSettings\n\nconst storageSetting = JSON.parse(localStorage.getItem('layout-setting')) || ''\nconst state = {\n  title: '',\n  theme: storageSetting.theme || '#409EFF',\n  sideTheme: storageSetting.sideTheme || sideTheme,\n  showSettings: showSettings,\n  topNav: storageSetting.topNav === undefined ? topNav : storageSetting.topNav,\n  tagsView: storageSetting.tagsView === undefined ? tagsView : storageSetting.tagsView,\n  tagsIcon: storageSetting.tagsIcon === undefined ? tagsIcon : storageSetting.tagsIcon,\n  fixedHeader: storageSetting.fixedHeader === undefined ? fixedHeader : storageSetting.fixedHeader,\n  sidebarLogo: storageSetting.sidebarLogo === undefined ? sidebarLogo : storageSetting.sidebarLogo,\n  dynamicTitle: storageSetting.dynamicTitle === undefined ? dynamicTitle : storageSetting.dynamicTitle,\n  footerVisible: storageSetting.footerVisible === undefined ? footerVisible : storageSetting.footerVisible,\n  footerContent: footerContent\n}\nconst mutations = {\n  CHANGE_SETTING: (state, { key, value }) => {\n    if (state.hasOwnProperty(key)) {\n      state[key] = value\n    }\n  }\n}\n\nconst actions = {\n  // 修改布局设置\n  changeSetting({ commit }, data) {\n    commit('CHANGE_SETTING', data)\n  },\n  // 设置网页标题\n  setTitle({ commit }, title) {\n    state.title = title\n    useDynamicTitle()\n  }\n}\n\nexport default {\n  namespaced: true,\n  state,\n  mutations,\n  actions\n}\n\n"], "mappings": ";;;;;;;;AAAA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,aAAA,GAAAD,OAAA;AAEA,IAAQE,SAAS,GAAqHC,iBAAe,CAA7ID,SAAS;EAAEE,YAAY,GAAuGD,iBAAe,CAAlIC,YAAY;EAAEC,MAAM,GAA+FF,iBAAe,CAApHE,MAAM;EAAEC,QAAQ,GAAqFH,iBAAe,CAA5GG,QAAQ;EAAEC,QAAQ,GAA2EJ,iBAAe,CAAlGI,QAAQ;EAAEC,WAAW,GAA8DL,iBAAe,CAAxFK,WAAW;EAAEC,WAAW,GAAiDN,iBAAe,CAA3EM,WAAW;EAAEC,YAAY,GAAmCP,iBAAe,CAA9DO,YAAY;EAAEC,aAAa,GAAoBR,iBAAe,CAAhDQ,aAAa;EAAEC,aAAa,GAAKT,iBAAe,CAAjCS,aAAa;AAEjI,IAAMC,cAAc,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC,CAAC,IAAI,EAAE;AAC/E,IAAMC,KAAK,GAAG;EACZC,KAAK,EAAE,EAAE;EACTC,KAAK,EAAEP,cAAc,CAACO,KAAK,IAAI,SAAS;EACxClB,SAAS,EAAEW,cAAc,CAACX,SAAS,IAAIA,SAAS;EAChDE,YAAY,EAAEA,YAAY;EAC1BC,MAAM,EAAEQ,cAAc,CAACR,MAAM,KAAKgB,SAAS,GAAGhB,MAAM,GAAGQ,cAAc,CAACR,MAAM;EAC5EC,QAAQ,EAAEO,cAAc,CAACP,QAAQ,KAAKe,SAAS,GAAGf,QAAQ,GAAGO,cAAc,CAACP,QAAQ;EACpFC,QAAQ,EAAEM,cAAc,CAACN,QAAQ,KAAKc,SAAS,GAAGd,QAAQ,GAAGM,cAAc,CAACN,QAAQ;EACpFC,WAAW,EAAEK,cAAc,CAACL,WAAW,KAAKa,SAAS,GAAGb,WAAW,GAAGK,cAAc,CAACL,WAAW;EAChGC,WAAW,EAAEI,cAAc,CAACJ,WAAW,KAAKY,SAAS,GAAGZ,WAAW,GAAGI,cAAc,CAACJ,WAAW;EAChGC,YAAY,EAAEG,cAAc,CAACH,YAAY,KAAKW,SAAS,GAAGX,YAAY,GAAGG,cAAc,CAACH,YAAY;EACpGC,aAAa,EAAEE,cAAc,CAACF,aAAa,KAAKU,SAAS,GAAGV,aAAa,GAAGE,cAAc,CAACF,aAAa;EACxGC,aAAa,EAAEA;AACjB,CAAC;AACD,IAAMU,SAAS,GAAG;EAChBC,cAAc,EAAE,SAAhBA,cAAcA,CAAGL,KAAK,EAAAM,IAAA,EAAqB;IAAA,IAAjBC,GAAG,GAAAD,IAAA,CAAHC,GAAG;MAAEC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAClC,IAAIR,KAAK,CAACS,cAAc,CAACF,GAAG,CAAC,EAAE;MAC7BP,KAAK,CAACO,GAAG,CAAC,GAAGC,KAAK;IACpB;EACF;AACF,CAAC;AAED,IAAME,OAAO,GAAG;EACd;EACAC,aAAa,WAAbA,aAAaA,CAAAC,KAAA,EAAaC,IAAI,EAAE;IAAA,IAAhBC,MAAM,GAAAF,KAAA,CAANE,MAAM;IACpBA,MAAM,CAAC,gBAAgB,EAAED,IAAI,CAAC;EAChC,CAAC;EACD;EACAE,QAAQ,WAARA,QAAQA,CAAAC,KAAA,EAAaf,KAAK,EAAE;IAAA,IAAjBa,MAAM,GAAAE,KAAA,CAANF,MAAM;IACfd,KAAK,CAACC,KAAK,GAAGA,KAAK;IACnB,IAAAgB,6BAAe,EAAC,CAAC;EACnB;AACF,CAAC;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEc;EACbC,UAAU,EAAE,IAAI;EAChBrB,KAAK,EAALA,KAAK;EACLI,SAAS,EAATA,SAAS;EACTM,OAAO,EAAPA;AACF,CAAC", "ignoreList": []}]}