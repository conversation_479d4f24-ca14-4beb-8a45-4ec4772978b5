{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/modules/input.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/modules/input.js", "mtime": 1753510684095}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_quill<PERSON><PERSON><PERSON>", "_interopRequireDefault", "require", "_module", "_quill", "_keyboard", "INSERT_TYPES", "Input", "_Module", "quill", "options", "_this", "_classCallCheck2", "default", "_callSuper2", "root", "addEventListener", "event", "handleBeforeInput", "test", "navigator", "userAgent", "on", "<PERSON><PERSON><PERSON>", "events", "COMPOSITION_BEFORE_START", "handleCompositionStart", "_inherits2", "_createClass2", "key", "value", "deleteRange", "range", "replaceText", "text", "arguments", "length", "undefined", "formats", "getFormat", "index", "updateContents", "Delta", "retain", "insert", "sources", "USER", "setSelection", "SILENT", "composition", "isComposing", "defaultPrevented", "includes", "inputType", "staticRange", "getTargetRanges", "collapsed", "getPlainTextFromInputEvent", "normalized", "selection", "normalizeNative", "normalizedToRange", "preventDefault", "getSelection", "<PERSON><PERSON><PERSON>", "_event$dataTransfer", "data", "dataTransfer", "types", "getData", "_default", "exports"], "sources": ["../../src/modules/input.ts"], "sourcesContent": ["import Delta from 'quill-delta';\nimport Module from '../core/module.js';\nimport Quill from '../core/quill.js';\nimport type { Range } from '../core/selection.js';\nimport { deleteRange } from './keyboard.js';\n\nconst INSERT_TYPES = ['insertText', 'insertReplacementText'];\n\nclass Input extends Module {\n  constructor(quill: Quill, options: Record<string, never>) {\n    super(quill, options);\n\n    quill.root.addEventListener('beforeinput', (event) => {\n      this.handleBeforeInput(event);\n    });\n\n    // Gboard with English input on Android triggers `compositionstart` sometimes even\n    // users are not going to type anything.\n    if (!/Android/i.test(navigator.userAgent)) {\n      quill.on(Quill.events.COMPOSITION_BEFORE_START, () => {\n        this.handleCompositionStart();\n      });\n    }\n  }\n\n  private deleteRange(range: Range) {\n    deleteRange({ range, quill: this.quill });\n  }\n\n  private replaceText(range: Range, text = '') {\n    if (range.length === 0) return false;\n\n    if (text) {\n      // Follow the native behavior that inherits the formats of the first character\n      const formats = this.quill.getFormat(range.index, 1);\n      this.deleteRange(range);\n      this.quill.updateContents(\n        new Delta().retain(range.index).insert(text, formats),\n        Quill.sources.USER,\n      );\n    } else {\n      this.deleteRange(range);\n    }\n\n    this.quill.setSelection(range.index + text.length, 0, Quill.sources.SILENT);\n    return true;\n  }\n\n  private handleBeforeInput(event: InputEvent) {\n    if (\n      this.quill.composition.isComposing ||\n      event.defaultPrevented ||\n      !INSERT_TYPES.includes(event.inputType)\n    ) {\n      return;\n    }\n\n    const staticRange = event.getTargetRanges\n      ? event.getTargetRanges()[0]\n      : null;\n    if (!staticRange || staticRange.collapsed === true) {\n      return;\n    }\n\n    const text = getPlainTextFromInputEvent(event);\n    if (text == null) {\n      return;\n    }\n    const normalized = this.quill.selection.normalizeNative(staticRange);\n    const range = normalized\n      ? this.quill.selection.normalizedToRange(normalized)\n      : null;\n    if (range && this.replaceText(range, text)) {\n      event.preventDefault();\n    }\n  }\n\n  private handleCompositionStart() {\n    const range = this.quill.getSelection();\n    if (range) {\n      this.replaceText(range);\n    }\n  }\n}\n\nfunction getPlainTextFromInputEvent(event: InputEvent) {\n  // When `inputType` is \"insertText\":\n  // - `event.data` should be string (Safari uses `event.dataTransfer`).\n  // - `event.dataTransfer` should be null.\n  // When `inputType` is \"insertReplacementText\":\n  // - `event.data` should be null.\n  // - `event.dataTransfer` should contain \"text/plain\" data.\n\n  if (typeof event.data === 'string') {\n    return event.data;\n  }\n  if (event.dataTransfer?.types.includes('text/plain')) {\n    return event.dataTransfer.getData('text/plain');\n  }\n  return null;\n}\n\nexport default Input;\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,MAAA,GAAAH,sBAAA,CAAAC,OAAA;AAEA,IAAAG,SAAA,GAAAH,OAAA;AAEA,IAAMI,YAAY,GAAG,CAAC,YAAY,EAAE,uBAAuB,CAAC;AAAA,IAEtDC,KAAK,0BAAAC,OAAA;EACT,SAAAD,MAAYE,KAAY,EAAEC,OAA8B,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAN,KAAA;IACxDI,KAAA,OAAAG,WAAA,CAAAD,OAAA,QAAAN,KAAA,GAAME,KAAK,EAAEC,OAAO;IAEpBD,KAAK,CAACM,IAAI,CAACC,gBAAgB,CAAC,aAAa,EAAG,UAAAC,KAAK,EAAK;MACpDN,KAAA,CAAKO,iBAAiB,CAACD,KAAK,CAAC;IAC/B,CAAC,CAAC;;IAEF;IACA;IACA,IAAI,CAAC,UAAU,CAACE,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC,EAAE;MACzCZ,KAAK,CAACa,EAAE,CAACC,cAAK,CAACC,MAAM,CAACC,wBAAwB,EAAE,YAAM;QACpDd,KAAA,CAAKe,sBAAsB,CAAC,CAAC;MAC/B,CAAC,CAAC;IACJ;IAAA,OAAAf,KAAA;EACF;EAAA,IAAAgB,UAAA,CAAAd,OAAA,EAAAN,KAAA,EAAAC,OAAA;EAAA,WAAAoB,aAAA,CAAAf,OAAA,EAAAN,KAAA;IAAAsB,GAAA;IAAAC,KAAA,EAEQ,SAAAC,WAAWA,CAACC,KAAY,EAAE;MAChC,IAAAD,qBAAW,EAAC;QAAEC,KAAK,EAALA,KAAK;QAAEvB,KAAK,EAAE,IAAI,CAACA;MAAM,CAAC,CAAC;IAC3C;EAAA;IAAAoB,GAAA;IAAAC,KAAA,EAEQ,SAAAG,WAAWA,CAACD,KAAY,EAAa;MAAA,IAAXE,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;MACzC,IAAIH,KAAK,CAACI,MAAM,KAAK,CAAC,EAAE,OAAO,KAAK;MAEpC,IAAIF,IAAI,EAAE;QACR;QACA,IAAMI,OAAO,GAAG,IAAI,CAAC7B,KAAK,CAAC8B,SAAS,CAACP,KAAK,CAACQ,KAAK,EAAE,CAAC,CAAC;QACpD,IAAI,CAACT,WAAW,CAACC,KAAK,CAAC;QACvB,IAAI,CAACvB,KAAK,CAACgC,cAAc,CACvB,IAAIC,mBAAK,CAAC,CAAC,CAACC,MAAM,CAACX,KAAK,CAACQ,KAAK,CAAC,CAACI,MAAM,CAACV,IAAI,EAAEI,OAAO,CAAC,EACrDf,cAAK,CAACsB,OAAO,CAACC,IAChB,CAAC;MACH,CAAC,MAAM;QACL,IAAI,CAACf,WAAW,CAACC,KAAK,CAAC;MACzB;MAEA,IAAI,CAACvB,KAAK,CAACsC,YAAY,CAACf,KAAK,CAACQ,KAAK,GAAGN,IAAI,CAACE,MAAM,EAAE,CAAC,EAAEb,cAAK,CAACsB,OAAO,CAACG,MAAM,CAAC;MAC3E,OAAO,IAAI;IACb;EAAA;IAAAnB,GAAA;IAAAC,KAAA,EAEQ,SAAAZ,iBAAiBA,CAACD,KAAiB,EAAE;MAC3C,IACE,IAAI,CAACR,KAAK,CAACwC,WAAW,CAACC,WAAW,IAClCjC,KAAK,CAACkC,gBAAgB,IACtB,CAAC7C,YAAY,CAAC8C,QAAQ,CAACnC,KAAK,CAACoC,SAAS,CAAC,EACvC;QACA;MACF;MAEA,IAAMC,WAAW,GAAGrC,KAAK,CAACsC,eAAe,GACrCtC,KAAK,CAACsC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,GAC1B,IAAI;MACR,IAAI,CAACD,WAAW,IAAIA,WAAW,CAACE,SAAS,KAAK,IAAI,EAAE;QAClD;MACF;MAEA,IAAMtB,IAAI,GAAGuB,0BAA0B,CAACxC,KAAK,CAAC;MAC9C,IAAIiB,IAAI,IAAI,IAAI,EAAE;QAChB;MACF;MACA,IAAMwB,UAAU,GAAG,IAAI,CAACjD,KAAK,CAACkD,SAAS,CAACC,eAAe,CAACN,WAAW,CAAC;MACpE,IAAMtB,KAAK,GAAG0B,UAAU,GACpB,IAAI,CAACjD,KAAK,CAACkD,SAAS,CAACE,iBAAiB,CAACH,UAAU,CAAC,GAClD,IAAI;MACR,IAAI1B,KAAK,IAAI,IAAI,CAACC,WAAW,CAACD,KAAK,EAAEE,IAAI,CAAC,EAAE;QAC1CjB,KAAK,CAAC6C,cAAc,CAAC,CAAC;MACxB;IACF;EAAA;IAAAjC,GAAA;IAAAC,KAAA,EAEQ,SAAAJ,sBAAsBA,CAAA,EAAG;MAC/B,IAAMM,KAAK,GAAG,IAAI,CAACvB,KAAK,CAACsD,YAAY,CAAC,CAAC;MACvC,IAAI/B,KAAK,EAAE;QACT,IAAI,CAACC,WAAW,CAACD,KAAK,CAAC;MACzB;IACF;EAAA;AAAA,EA1EkBgC,eAAM;AA6E1B,SAASP,0BAA0BA,CAACxC,KAAiB,EAAE;EAAA,IAAAgD,mBAAA;EACrD;EACA;EACA;EACA;EACA;EACA;;EAEA,IAAI,OAAOhD,KAAK,CAACiD,IAAI,KAAK,QAAQ,EAAE;IAClC,OAAOjD,KAAK,CAACiD,IAAI;EACnB;EACA,KAAAD,mBAAA,GAAIhD,KAAK,CAACkD,YAAY,cAAAF,mBAAA,eAAlBA,mBAAA,CAAoBG,KAAK,CAAChB,QAAQ,CAAC,YAAY,CAAC,EAAE;IACpD,OAAOnC,KAAK,CAACkD,YAAY,CAACE,OAAO,CAAC,YAAY,CAAC;EACjD;EACA,OAAO,IAAI;AACb;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAA1D,OAAA,GAEeN,KAAK", "ignoreList": []}]}