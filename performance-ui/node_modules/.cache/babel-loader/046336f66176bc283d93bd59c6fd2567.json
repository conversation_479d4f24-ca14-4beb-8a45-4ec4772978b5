{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/performance/quarterlyResult.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/performance/quarterlyResult.js", "mtime": 1753510684517}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCIvVXNlcnMvbWF6aWhhby9EZXNrdG9wL2Rldi9wZXJmb3JtYW5jZS9wZXJmb3JtYW5jZS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMubGlzdFF1YXJ0ZXJseUdvb2RHcmFkZSA9IGxpc3RRdWFydGVybHlHb29kR3JhZGU7CmV4cG9ydHMubGlzdFF1YXJ0ZXJseVJlc3VsdCA9IGxpc3RRdWFydGVybHlSZXN1bHQ7CnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwovLyDojrflj5blraPluqbnu5PmnpzliIbnsbvmjpLluo/liJfooagKZnVuY3Rpb24gbGlzdFF1YXJ0ZXJseVJlc3VsdChxdWVyeSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3BlcmZvcm1hbmNlL3F1YXJ0ZXJseS9yZXN1bHQvbGlzdCcsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDojrflj5Yi5aW9IuetieasoeS6uuWRmOWIl+ihqApmdW5jdGlvbiBsaXN0UXVhcnRlcmx5R29vZEdyYWRlKHF1ZXJ5KSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvcGVyZm9ybWFuY2UvcXVhcnRlcmx5L3Jlc3VsdC9nb29kR3JhZGUnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfQ=="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listQuarterlyResult", "query", "request", "url", "method", "params", "listQuarterlyGoodGrade"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/performance/quarterlyResult.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 获取季度结果分类排序列表\nexport function listQuarterlyResult(query) {\n  return request({\n    url: '/performance/quarterly/result/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 获取\"好\"等次人员列表\nexport function listQuarterlyGoodGrade(query) {\n  return request({\n    url: '/performance/quarterly/result/goodGrade',\n    method: 'get',\n    params: query\n  })\n} "], "mappings": ";;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EACzC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,oCAAoC;IACzCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,sBAAsBA,CAACL,KAAK,EAAE;EAC5C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,yCAAyC;IAC9CC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}