{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/modules/uiNode.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/modules/uiNode.js", "mtime": 1753510684097}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_parchment", "require", "_module", "_interopRequireDefault", "_quill", "isMac", "test", "navigator", "platform", "TTL_FOR_VALID_SELECTION_CHANGE", "exports", "canMoveCaretBeforeUINode", "event", "key", "ctrl<PERSON>ey", "UINode", "_Module", "quill", "options", "_this", "_classCallCheck2", "default", "_callSuper2", "_defineProperty2", "handleArrowKeys", "handleNavigationShortcuts", "_inherits2", "_createClass2", "value", "keyboard", "addBinding", "offset", "shift<PERSON>ey", "handler", "range", "_ref", "line", "ParentBlot", "uiNode", "isRTL", "getComputedStyle", "domNode", "setSelection", "index", "length", "<PERSON><PERSON><PERSON>", "sources", "USER", "_this2", "root", "addEventListener", "defaultPrevented", "ensureListeningToSelectionChange", "_this3", "selectionChangeDeadline", "Date", "now", "isListening", "listener", "handleSelectionChange", "document", "once", "selection", "getSelection", "getRangeAt", "collapsed", "startOffset", "scroll", "find", "startContainer", "newRange", "createRange", "setStartAfter", "setEndAfter", "removeAllRanges", "addRange", "<PERSON><PERSON><PERSON>", "_default"], "sources": ["../../src/modules/uiNode.ts"], "sourcesContent": ["import { ParentBlot } from 'parchment';\nimport Module from '../core/module.js';\nimport Quill from '../core/quill.js';\n\nconst isMac = /Mac/i.test(navigator.platform);\n\n// Export for testing\nexport const TTL_FOR_VALID_SELECTION_CHANGE = 100;\n\n// A loose check to determine if the shortcut can move the caret before a UI node:\n// <ANY_PARENT>[CARET]<div class=\"ql-ui\"></div>[CONTENT]</ANY_PARENT>\nconst canMoveCaretBeforeUINode = (event: KeyboardEvent) => {\n  if (\n    event.key === 'ArrowLeft' ||\n    event.key === 'ArrowRight' || // RTL scripts or moving from the end of the previous line\n    event.key === 'ArrowUp' ||\n    event.key === 'ArrowDown' ||\n    event.key === 'Home'\n  ) {\n    return true;\n  }\n\n  if (isMac && event.key === 'a' && event.ctrlKey === true) {\n    return true;\n  }\n\n  return false;\n};\n\nclass UINode extends Module {\n  isListening = false;\n  selectionChangeDeadline = 0;\n\n  constructor(quill: Quill, options: Record<string, never>) {\n    super(quill, options);\n\n    this.handleArrowKeys();\n    this.handleNavigationShortcuts();\n  }\n\n  private handleArrowKeys() {\n    this.quill.keyboard.addBinding({\n      key: ['ArrowLeft', 'ArrowRight'],\n      offset: 0,\n      shiftKey: null,\n      handler(range, { line, event }) {\n        if (!(line instanceof ParentBlot) || !line.uiNode) {\n          return true;\n        }\n\n        const isRTL = getComputedStyle(line.domNode)['direction'] === 'rtl';\n        if (\n          (isRTL && event.key !== 'ArrowRight') ||\n          (!isRTL && event.key !== 'ArrowLeft')\n        ) {\n          return true;\n        }\n\n        this.quill.setSelection(\n          range.index - 1,\n          range.length + (event.shiftKey ? 1 : 0),\n          Quill.sources.USER,\n        );\n        return false;\n      },\n    });\n  }\n\n  private handleNavigationShortcuts() {\n    this.quill.root.addEventListener('keydown', (event) => {\n      if (!event.defaultPrevented && canMoveCaretBeforeUINode(event)) {\n        this.ensureListeningToSelectionChange();\n      }\n    });\n  }\n\n  /**\n   * We only listen to the `selectionchange` event when\n   * there is an intention of moving the caret to the beginning using shortcuts.\n   * This is primarily implemented to prevent infinite loops, as we are changing\n   * the selection within the handler of a `selectionchange` event.\n   */\n  private ensureListeningToSelectionChange() {\n    this.selectionChangeDeadline = Date.now() + TTL_FOR_VALID_SELECTION_CHANGE;\n\n    if (this.isListening) return;\n    this.isListening = true;\n\n    const listener = () => {\n      this.isListening = false;\n\n      if (Date.now() <= this.selectionChangeDeadline) {\n        this.handleSelectionChange();\n      }\n    };\n\n    document.addEventListener('selectionchange', listener, {\n      once: true,\n    });\n  }\n\n  private handleSelectionChange() {\n    const selection = document.getSelection();\n    if (!selection) return;\n    const range = selection.getRangeAt(0);\n    if (range.collapsed !== true || range.startOffset !== 0) return;\n\n    const line = this.quill.scroll.find(range.startContainer);\n    if (!(line instanceof ParentBlot) || !line.uiNode) return;\n\n    const newRange = document.createRange();\n    newRange.setStartAfter(line.uiNode);\n    newRange.setEndAfter(line.uiNode);\n    selection.removeAllRanges();\n    selection.addRange(newRange);\n  }\n}\n\nexport default UINode;\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,MAAA,GAAAD,sBAAA,CAAAF,OAAA;AAEA,IAAMI,KAAK,GAAG,MAAM,CAACC,IAAI,CAACC,SAAS,CAACC,QAAQ,CAAC;;AAE7C;AACO,IAAMC,8BAA8B,GAAAC,OAAA,CAAAD,8BAAA,GAAG,GAAG;;AAEjD;AACA;AACA,IAAME,wBAAwB,GAAI,SAA5BA,wBAAwBA,CAAIC,KAAoB,EAAK;EACzD,IACEA,KAAK,CAACC,GAAG,KAAK,WAAW,IACzBD,KAAK,CAACC,GAAG,KAAK,YAAY;EAAI;EAC9BD,KAAK,CAACC,GAAG,KAAK,SAAS,IACvBD,KAAK,CAACC,GAAG,KAAK,WAAW,IACzBD,KAAK,CAACC,GAAG,KAAK,MAAM,EACpB;IACA,OAAO,IAAI;EACb;EAEA,IAAIR,KAAK,IAAIO,KAAK,CAACC,GAAG,KAAK,GAAG,IAAID,KAAK,CAACE,OAAO,KAAK,IAAI,EAAE;IACxD,OAAO,IAAI;EACb;EAEA,OAAO,KAAK;AACd,CAAC;AAAA,IAEKC,MAAM,0BAAAC,OAAA;EAIV,SAAAD,OAAYE,KAAY,EAAEC,OAA8B,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAN,MAAA;IACxDI,KAAA,OAAAG,WAAA,CAAAD,OAAA,QAAAN,MAAA,GAAME,KAAK,EAAEC,OAAO;IAAC,IAAAK,gBAAA,CAAAF,OAAA,EAAAF,KAAA,iBAJT,KAAK;IAAA,IAAAI,gBAAA,CAAAF,OAAA,EAAAF,KAAA,6BACO,CAAC;IAKzBA,KAAA,CAAKK,eAAe,CAAC,CAAC;IACtBL,KAAA,CAAKM,yBAAyB,CAAC,CAAC;IAAA,OAAAN,KAAA;EAClC;EAAA,IAAAO,UAAA,CAAAL,OAAA,EAAAN,MAAA,EAAAC,OAAA;EAAA,WAAAW,aAAA,CAAAN,OAAA,EAAAN,MAAA;IAAAF,GAAA;IAAAe,KAAA,EAEQ,SAAAJ,eAAeA,CAAA,EAAG;MACxB,IAAI,CAACP,KAAK,CAACY,QAAQ,CAACC,UAAU,CAAC;QAC7BjB,GAAG,EAAE,CAAC,WAAW,EAAE,YAAY,CAAC;QAChCkB,MAAM,EAAE,CAAC;QACTC,QAAQ,EAAE,IAAI;QACdC,OAAO,WAAPA,OAAOA,CAACC,KAAK,EAAAC,IAAA,EAAmB;UAAA,IAAfC,IAAI,GAASD,IAAA,CAAbC,IAAI;YAAExB,KAAA,GAAOuB,IAAA,CAAPvB,KAAA;UACrB,IAAI,EAAEwB,IAAI,YAAYC,qBAAU,CAAC,IAAI,CAACD,IAAI,CAACE,MAAM,EAAE;YACjD,OAAO,IAAI;UACb;UAEA,IAAMC,KAAK,GAAGC,gBAAgB,CAACJ,IAAI,CAACK,OAAO,CAAC,CAAC,WAAW,CAAC,KAAK,KAAK;UACnE,IACGF,KAAK,IAAI3B,KAAK,CAACC,GAAG,KAAK,YAAY,IACnC,CAAC0B,KAAK,IAAI3B,KAAK,CAACC,GAAG,KAAK,WAAY,EACrC;YACA,OAAO,IAAI;UACb;UAEA,IAAI,CAACI,KAAK,CAACyB,YAAY,CACrBR,KAAK,CAACS,KAAK,GAAG,CAAC,EACfT,KAAK,CAACU,MAAM,IAAIhC,KAAK,CAACoB,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC,EACvCa,cAAK,CAACC,OAAO,CAACC,IAChB,CAAC;UACD,OAAO,KAAK;QACd;MACF,CAAC,CAAC;IACJ;EAAA;IAAAlC,GAAA;IAAAe,KAAA,EAEQ,SAAAH,yBAAyBA,CAAA,EAAG;MAAA,IAAAuB,MAAA;MAClC,IAAI,CAAC/B,KAAK,CAACgC,IAAI,CAACC,gBAAgB,CAAC,SAAS,EAAG,UAAAtC,KAAK,EAAK;QACrD,IAAI,CAACA,KAAK,CAACuC,gBAAgB,IAAIxC,wBAAwB,CAACC,KAAK,CAAC,EAAE;UAC9DoC,MAAI,CAACI,gCAAgC,CAAC,CAAC;QACzC;MACF,CAAC,CAAC;IACJ;;IAEA;AACF;AACA;AACA;AACA;AACA;EALE;IAAAvC,GAAA;IAAAe,KAAA,EAMQ,SAAAwB,gCAAgCA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACzC,IAAI,CAACC,uBAAuB,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG/C,8BAA8B;MAE1E,IAAI,IAAI,CAACgD,WAAW,EAAE;MACtB,IAAI,CAACA,WAAW,GAAG,IAAI;MAEvB,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;QACrBL,MAAI,CAACI,WAAW,GAAG,KAAK;QAExB,IAAIF,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIH,MAAI,CAACC,uBAAuB,EAAE;UAC9CD,MAAI,CAACM,qBAAqB,CAAC,CAAC;QAC9B;MACF,CAAC;MAEDC,QAAQ,CAACV,gBAAgB,CAAC,iBAAiB,EAAEQ,QAAQ,EAAE;QACrDG,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;EAAA;IAAAhD,GAAA;IAAAe,KAAA,EAEQ,SAAA+B,qBAAqBA,CAAA,EAAG;MAC9B,IAAMG,SAAS,GAAGF,QAAQ,CAACG,YAAY,CAAC,CAAC;MACzC,IAAI,CAACD,SAAS,EAAE;MAChB,IAAM5B,KAAK,GAAG4B,SAAS,CAACE,UAAU,CAAC,CAAC,CAAC;MACrC,IAAI9B,KAAK,CAAC+B,SAAS,KAAK,IAAI,IAAI/B,KAAK,CAACgC,WAAW,KAAK,CAAC,EAAE;MAEzD,IAAM9B,IAAI,GAAG,IAAI,CAACnB,KAAK,CAACkD,MAAM,CAACC,IAAI,CAAClC,KAAK,CAACmC,cAAc,CAAC;MACzD,IAAI,EAAEjC,IAAI,YAAYC,qBAAU,CAAC,IAAI,CAACD,IAAI,CAACE,MAAM,EAAE;MAEnD,IAAMgC,QAAQ,GAAGV,QAAQ,CAACW,WAAW,CAAC,CAAC;MACvCD,QAAQ,CAACE,aAAa,CAACpC,IAAI,CAACE,MAAM,CAAC;MACnCgC,QAAQ,CAACG,WAAW,CAACrC,IAAI,CAACE,MAAM,CAAC;MACjCwB,SAAS,CAACY,eAAe,CAAC,CAAC;MAC3BZ,SAAS,CAACa,QAAQ,CAACL,QAAQ,CAAC;IAC9B;EAAA;AAAA,EAtFmBM,eAAM;AAAA,IAAAC,QAAA,GAAAnE,OAAA,CAAAW,OAAA,GAyFZN,MAAM", "ignoreList": []}]}