{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/modules/keyboard.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/modules/keyboard.js", "mtime": 1753510684096}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_lodashEs", "require", "_quill<PERSON><PERSON><PERSON>", "_interopRequireWildcard", "_parchment", "_quill", "_interopRequireDefault", "_logger", "_module", "debug", "logger", "SHORTKEY", "exports", "test", "navigator", "platform", "Keyboard", "default", "_Module", "quill", "options", "_this", "_classCallCheck2", "_callSuper2", "bindings", "Object", "keys", "for<PERSON>ach", "name", "addBinding", "key", "shift<PERSON>ey", "handleEnter", "metaKey", "ctrl<PERSON>ey", "altKey", "userAgent", "collapsed", "handleBackspace", "handleDelete", "prefix", "suffix", "handleDeleteRange", "offset", "listen", "_inherits2", "_createClass2", "value", "keyBinding", "_this2", "context", "arguments", "length", "undefined", "handler", "binding", "normalize", "warn", "Array", "isArray", "singleBinding", "_objectSpread2", "push", "_this3", "root", "addEventListener", "evt", "defaultPrevented", "isComposing", "keyCode", "concat", "which", "matches", "filter", "match", "blot", "<PERSON><PERSON><PERSON>", "find", "target", "scroll", "range", "getSelection", "hasFocus", "_this3$quill$getLine", "getLine", "index", "_this3$quill$getLine2", "_slicedToArray2", "line", "_this3$quill$getLeaf", "<PERSON><PERSON><PERSON><PERSON>", "_this3$quill$getLeaf2", "leafStart", "offsetStart", "_ref3", "_ref4", "leafEnd", "offsetEnd", "prefixText", "TextBlot", "slice", "suffixText", "cur<PERSON><PERSON><PERSON><PERSON>", "empty", "format", "getFormat", "event", "prevented", "some", "every", "_typeof2", "isEqual", "call", "preventDefault", "<PERSON><PERSON><PERSON><PERSON>", "formats", "_this$quill$getLine", "_this$quill$getLine2", "delta", "Delta", "retain", "delete", "_this$quill$getLine3", "_this$quill$getLine4", "prev", "isPrevLineEmpty", "statics", "blotName", "curFormats", "prevFormats", "AttributeMap", "diff", "formatDelta", "compose", "updateContents", "sources", "USER", "focus", "_this$quill$getLine5", "_this$quill$getLine6", "_this$quill$getLine7", "_this$quill$getLine8", "next", "nextFormats", "deleteRange", "_this4", "lineFormats", "reduce", "query", "<PERSON><PERSON>", "BLOCK", "insert", "setSelection", "SILENT", "<PERSON><PERSON><PERSON>", "defaultOptions", "bold", "makeFormatHandler", "italic", "underline", "indent", "outdent", "list", "makeCodeBlockHandler", "deleteText", "tab", "table", "history", "cutoff", "formatLine", "_this$quill$getLine9", "_this$quill$getLine0", "scrollSelectionIntoView", "_this$quill$getLine1", "_this$quill$getLine10", "header", "module", "getModule", "_module$getTable", "getTable", "_module$getTable2", "row", "cell", "shift", "tableSide", "blockquote", "_this$quill$getLine11", "_this$quill$getLine12", "trim", "insertText", "_this$quill$getLine13", "_this$quill$getLine14", "numLines", "cur", "makeEmbedArrowHandler", "makeTableArrowHandler", "DEFAULTS", "_ref", "CodeBlock", "TAB", "lines", "getLines", "i", "insertAt", "domNode", "textContent", "startsWith", "deleteAt", "update", "where", "_defineProperty2", "_this$quill$getLeaf", "_this$quill$getLeaf2", "leaf", "EmbedBlot", "<PERSON><PERSON><PERSON>", "up", "targetRow", "parent", "targetCell", "children", "head", "Math", "min", "targetLine", "cloneDeep", "_ref2", "firstFormats", "lastFormats", "_table"], "sources": ["../../src/modules/keyboard.ts"], "sourcesContent": ["import { cloneDeep, isEqual } from 'lodash-es';\nimport Delta, { AttributeMap } from 'quill-delta';\nimport { EmbedBlot, Scope, TextBlot } from 'parchment';\nimport type { Blot, BlockBlot } from 'parchment';\nimport Quill from '../core/quill.js';\nimport logger from '../core/logger.js';\nimport Module from '../core/module.js';\nimport type { BlockEmbed } from '../blots/block.js';\nimport type { Range } from '../core/selection.js';\n\nconst debug = logger('quill:keyboard');\n\nconst SHORTKEY = /Mac/i.test(navigator.platform) ? 'metaKey' : 'ctrlKey';\n\nexport interface Context {\n  collapsed: boolean;\n  empty: boolean;\n  offset: number;\n  prefix: string;\n  suffix: string;\n  format: Record<string, unknown>;\n  event: KeyboardEvent;\n  line: BlockEmbed | BlockBlot;\n}\n\ninterface BindingObject\n  extends Partial<Omit<Context, 'prefix' | 'suffix' | 'format'>> {\n  key: number | string | string[];\n  shortKey?: boolean | null;\n  shiftKey?: boolean | null;\n  altKey?: boolean | null;\n  metaKey?: boolean | null;\n  ctrlKey?: boolean | null;\n  prefix?: RegExp;\n  suffix?: RegExp;\n  format?: Record<string, unknown> | string[];\n  handler?: (\n    this: { quill: Quill },\n    range: Range,\n    curContext: Context,\n    // eslint-disable-next-line no-use-before-define\n    binding: NormalizedBinding,\n  ) => boolean | void;\n}\n\ntype Binding = BindingObject | string | number;\n\ninterface NormalizedBinding extends Omit<BindingObject, 'key' | 'shortKey'> {\n  key: string | number;\n}\n\ninterface KeyboardOptions {\n  bindings: Record<string, Binding>;\n}\n\ninterface KeyboardOptions {\n  bindings: Record<string, Binding>;\n}\n\nclass Keyboard extends Module<KeyboardOptions> {\n  static DEFAULTS: KeyboardOptions;\n\n  static match(evt: KeyboardEvent, binding: BindingObject) {\n    if (\n      (['altKey', 'ctrlKey', 'metaKey', 'shiftKey'] as const).some((key) => {\n        return !!binding[key] !== evt[key] && binding[key] !== null;\n      })\n    ) {\n      return false;\n    }\n    return binding.key === evt.key || binding.key === evt.which;\n  }\n\n  bindings: Record<string, NormalizedBinding[]>;\n\n  constructor(quill: Quill, options: Partial<KeyboardOptions>) {\n    super(quill, options);\n    this.bindings = {};\n    // @ts-expect-error Fix me later\n    Object.keys(this.options.bindings).forEach((name) => {\n      // @ts-expect-error Fix me later\n      if (this.options.bindings[name]) {\n        // @ts-expect-error Fix me later\n        this.addBinding(this.options.bindings[name]);\n      }\n    });\n    this.addBinding({ key: 'Enter', shiftKey: null }, this.handleEnter);\n    this.addBinding(\n      { key: 'Enter', metaKey: null, ctrlKey: null, altKey: null },\n      () => {},\n    );\n    if (/Firefox/i.test(navigator.userAgent)) {\n      // Need to handle delete and backspace for Firefox in the general case #1171\n      this.addBinding(\n        { key: 'Backspace' },\n        { collapsed: true },\n        this.handleBackspace,\n      );\n      this.addBinding(\n        { key: 'Delete' },\n        { collapsed: true },\n        this.handleDelete,\n      );\n    } else {\n      this.addBinding(\n        { key: 'Backspace' },\n        { collapsed: true, prefix: /^.?$/ },\n        this.handleBackspace,\n      );\n      this.addBinding(\n        { key: 'Delete' },\n        { collapsed: true, suffix: /^.?$/ },\n        this.handleDelete,\n      );\n    }\n    this.addBinding(\n      { key: 'Backspace' },\n      { collapsed: false },\n      this.handleDeleteRange,\n    );\n    this.addBinding(\n      { key: 'Delete' },\n      { collapsed: false },\n      this.handleDeleteRange,\n    );\n    this.addBinding(\n      {\n        key: 'Backspace',\n        altKey: null,\n        ctrlKey: null,\n        metaKey: null,\n        shiftKey: null,\n      },\n      { collapsed: true, offset: 0 },\n      this.handleBackspace,\n    );\n    this.listen();\n  }\n\n  addBinding(\n    keyBinding: Binding,\n    context:\n      | Required<BindingObject['handler']>\n      | Partial<Omit<BindingObject, 'key' | 'handler'>> = {},\n    handler:\n      | Required<BindingObject['handler']>\n      | Partial<Omit<BindingObject, 'key' | 'handler'>> = {},\n  ) {\n    const binding = normalize(keyBinding);\n    if (binding == null) {\n      debug.warn('Attempted to add invalid keyboard binding', binding);\n      return;\n    }\n    if (typeof context === 'function') {\n      context = { handler: context };\n    }\n    if (typeof handler === 'function') {\n      handler = { handler };\n    }\n    const keys = Array.isArray(binding.key) ? binding.key : [binding.key];\n    keys.forEach((key) => {\n      const singleBinding = {\n        ...binding,\n        key,\n        ...context,\n        ...handler,\n      };\n      this.bindings[singleBinding.key] = this.bindings[singleBinding.key] || [];\n      this.bindings[singleBinding.key].push(singleBinding);\n    });\n  }\n\n  listen() {\n    this.quill.root.addEventListener('keydown', (evt) => {\n      if (evt.defaultPrevented || evt.isComposing) return;\n\n      // evt.isComposing is false when pressing Enter/Backspace when composing in Safari\n      // https://bugs.webkit.org/show_bug.cgi?id=165004\n      const isComposing =\n        evt.keyCode === 229 && (evt.key === 'Enter' || evt.key === 'Backspace');\n      if (isComposing) return;\n\n      const bindings = (this.bindings[evt.key] || []).concat(\n        this.bindings[evt.which] || [],\n      );\n      const matches = bindings.filter((binding) =>\n        Keyboard.match(evt, binding),\n      );\n      if (matches.length === 0) return;\n      // @ts-expect-error\n      const blot = Quill.find(evt.target, true);\n      if (blot && blot.scroll !== this.quill.scroll) return;\n      const range = this.quill.getSelection();\n      if (range == null || !this.quill.hasFocus()) return;\n      const [line, offset] = this.quill.getLine(range.index);\n      const [leafStart, offsetStart] = this.quill.getLeaf(range.index);\n      const [leafEnd, offsetEnd] =\n        range.length === 0\n          ? [leafStart, offsetStart]\n          : this.quill.getLeaf(range.index + range.length);\n      const prefixText =\n        leafStart instanceof TextBlot\n          ? leafStart.value().slice(0, offsetStart)\n          : '';\n      const suffixText =\n        leafEnd instanceof TextBlot ? leafEnd.value().slice(offsetEnd) : '';\n      const curContext = {\n        collapsed: range.length === 0,\n        // @ts-expect-error Fix me later\n        empty: range.length === 0 && line.length() <= 1,\n        format: this.quill.getFormat(range),\n        line,\n        offset,\n        prefix: prefixText,\n        suffix: suffixText,\n        event: evt,\n      };\n      const prevented = matches.some((binding) => {\n        if (\n          binding.collapsed != null &&\n          binding.collapsed !== curContext.collapsed\n        ) {\n          return false;\n        }\n        if (binding.empty != null && binding.empty !== curContext.empty) {\n          return false;\n        }\n        if (binding.offset != null && binding.offset !== curContext.offset) {\n          return false;\n        }\n        if (Array.isArray(binding.format)) {\n          // any format is present\n          if (binding.format.every((name) => curContext.format[name] == null)) {\n            return false;\n          }\n        } else if (typeof binding.format === 'object') {\n          // all formats must match\n          if (\n            !Object.keys(binding.format).every((name) => {\n              // @ts-expect-error Fix me later\n              if (binding.format[name] === true)\n                return curContext.format[name] != null;\n              // @ts-expect-error Fix me later\n              if (binding.format[name] === false)\n                return curContext.format[name] == null;\n              // @ts-expect-error Fix me later\n              return isEqual(binding.format[name], curContext.format[name]);\n            })\n          ) {\n            return false;\n          }\n        }\n        if (binding.prefix != null && !binding.prefix.test(curContext.prefix)) {\n          return false;\n        }\n        if (binding.suffix != null && !binding.suffix.test(curContext.suffix)) {\n          return false;\n        }\n        // @ts-expect-error Fix me later\n        return binding.handler.call(this, range, curContext, binding) !== true;\n      });\n      if (prevented) {\n        evt.preventDefault();\n      }\n    });\n  }\n\n  handleBackspace(range: Range, context: Context) {\n    // Check for astral symbols\n    const length = /[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]$/.test(context.prefix)\n      ? 2\n      : 1;\n    if (range.index === 0 || this.quill.getLength() <= 1) return;\n    let formats = {};\n    const [line] = this.quill.getLine(range.index);\n    let delta = new Delta().retain(range.index - length).delete(length);\n    if (context.offset === 0) {\n      // Always deleting newline here, length always 1\n      const [prev] = this.quill.getLine(range.index - 1);\n      if (prev) {\n        const isPrevLineEmpty =\n          prev.statics.blotName === 'block' && prev.length() <= 1;\n        if (!isPrevLineEmpty) {\n          // @ts-expect-error Fix me later\n          const curFormats = line.formats();\n          const prevFormats = this.quill.getFormat(range.index - 1, 1);\n          formats = AttributeMap.diff(curFormats, prevFormats) || {};\n          if (Object.keys(formats).length > 0) {\n            // line.length() - 1 targets \\n in line, another -1 for newline being deleted\n            const formatDelta = new Delta()\n              // @ts-expect-error Fix me later\n              .retain(range.index + line.length() - 2)\n              .retain(1, formats);\n            delta = delta.compose(formatDelta);\n          }\n        }\n      }\n    }\n    this.quill.updateContents(delta, Quill.sources.USER);\n    this.quill.focus();\n  }\n\n  handleDelete(range: Range, context: Context) {\n    // Check for astral symbols\n    const length = /^[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]/.test(context.suffix)\n      ? 2\n      : 1;\n    if (range.index >= this.quill.getLength() - length) return;\n    let formats = {};\n    const [line] = this.quill.getLine(range.index);\n    let delta = new Delta().retain(range.index).delete(length);\n    // @ts-expect-error Fix me later\n    if (context.offset >= line.length() - 1) {\n      const [next] = this.quill.getLine(range.index + 1);\n      if (next) {\n        // @ts-expect-error Fix me later\n        const curFormats = line.formats();\n        const nextFormats = this.quill.getFormat(range.index, 1);\n        formats = AttributeMap.diff(curFormats, nextFormats) || {};\n        if (Object.keys(formats).length > 0) {\n          delta = delta.retain(next.length() - 1).retain(1, formats);\n        }\n      }\n    }\n    this.quill.updateContents(delta, Quill.sources.USER);\n    this.quill.focus();\n  }\n\n  handleDeleteRange(range: Range) {\n    deleteRange({ range, quill: this.quill });\n    this.quill.focus();\n  }\n\n  handleEnter(range: Range, context: Context) {\n    const lineFormats = Object.keys(context.format).reduce(\n      (formats: Record<string, unknown>, format) => {\n        if (\n          this.quill.scroll.query(format, Scope.BLOCK) &&\n          !Array.isArray(context.format[format])\n        ) {\n          formats[format] = context.format[format];\n        }\n        return formats;\n      },\n      {},\n    );\n    const delta = new Delta()\n      .retain(range.index)\n      .delete(range.length)\n      .insert('\\n', lineFormats);\n    this.quill.updateContents(delta, Quill.sources.USER);\n    this.quill.setSelection(range.index + 1, Quill.sources.SILENT);\n    this.quill.focus();\n  }\n}\n\nconst defaultOptions: KeyboardOptions = {\n  bindings: {\n    bold: makeFormatHandler('bold'),\n    italic: makeFormatHandler('italic'),\n    underline: makeFormatHandler('underline'),\n    indent: {\n      // highlight tab or tab at beginning of list, indent or blockquote\n      key: 'Tab',\n      format: ['blockquote', 'indent', 'list'],\n      handler(range, context) {\n        if (context.collapsed && context.offset !== 0) return true;\n        this.quill.format('indent', '+1', Quill.sources.USER);\n        return false;\n      },\n    },\n    outdent: {\n      key: 'Tab',\n      shiftKey: true,\n      format: ['blockquote', 'indent', 'list'],\n      // highlight tab or tab at beginning of list, indent or blockquote\n      handler(range, context) {\n        if (context.collapsed && context.offset !== 0) return true;\n        this.quill.format('indent', '-1', Quill.sources.USER);\n        return false;\n      },\n    },\n    'outdent backspace': {\n      key: 'Backspace',\n      collapsed: true,\n      shiftKey: null,\n      metaKey: null,\n      ctrlKey: null,\n      altKey: null,\n      format: ['indent', 'list'],\n      offset: 0,\n      handler(range, context) {\n        if (context.format.indent != null) {\n          this.quill.format('indent', '-1', Quill.sources.USER);\n        } else if (context.format.list != null) {\n          this.quill.format('list', false, Quill.sources.USER);\n        }\n      },\n    },\n    'indent code-block': makeCodeBlockHandler(true),\n    'outdent code-block': makeCodeBlockHandler(false),\n    'remove tab': {\n      key: 'Tab',\n      shiftKey: true,\n      collapsed: true,\n      prefix: /\\t$/,\n      handler(range) {\n        this.quill.deleteText(range.index - 1, 1, Quill.sources.USER);\n      },\n    },\n    tab: {\n      key: 'Tab',\n      handler(range, context) {\n        if (context.format.table) return true;\n        this.quill.history.cutoff();\n        const delta = new Delta()\n          .retain(range.index)\n          .delete(range.length)\n          .insert('\\t');\n        this.quill.updateContents(delta, Quill.sources.USER);\n        this.quill.history.cutoff();\n        this.quill.setSelection(range.index + 1, Quill.sources.SILENT);\n        return false;\n      },\n    },\n    'blockquote empty enter': {\n      key: 'Enter',\n      collapsed: true,\n      format: ['blockquote'],\n      empty: true,\n      handler() {\n        this.quill.format('blockquote', false, Quill.sources.USER);\n      },\n    },\n    'list empty enter': {\n      key: 'Enter',\n      collapsed: true,\n      format: ['list'],\n      empty: true,\n      handler(range, context) {\n        const formats: Record<string, unknown> = { list: false };\n        if (context.format.indent) {\n          formats.indent = false;\n        }\n        this.quill.formatLine(\n          range.index,\n          range.length,\n          formats,\n          Quill.sources.USER,\n        );\n      },\n    },\n    'checklist enter': {\n      key: 'Enter',\n      collapsed: true,\n      format: { list: 'checked' },\n      handler(range) {\n        const [line, offset] = this.quill.getLine(range.index);\n        const formats = {\n          // @ts-expect-error Fix me later\n          ...line.formats(),\n          list: 'checked',\n        };\n        const delta = new Delta()\n          .retain(range.index)\n          .insert('\\n', formats)\n          // @ts-expect-error Fix me later\n          .retain(line.length() - offset - 1)\n          .retain(1, { list: 'unchecked' });\n        this.quill.updateContents(delta, Quill.sources.USER);\n        this.quill.setSelection(range.index + 1, Quill.sources.SILENT);\n        this.quill.scrollSelectionIntoView();\n      },\n    },\n    'header enter': {\n      key: 'Enter',\n      collapsed: true,\n      format: ['header'],\n      suffix: /^$/,\n      handler(range, context) {\n        const [line, offset] = this.quill.getLine(range.index);\n        const delta = new Delta()\n          .retain(range.index)\n          .insert('\\n', context.format)\n          // @ts-expect-error Fix me later\n          .retain(line.length() - offset - 1)\n          .retain(1, { header: null });\n        this.quill.updateContents(delta, Quill.sources.USER);\n        this.quill.setSelection(range.index + 1, Quill.sources.SILENT);\n        this.quill.scrollSelectionIntoView();\n      },\n    },\n    'table backspace': {\n      key: 'Backspace',\n      format: ['table'],\n      collapsed: true,\n      offset: 0,\n      handler() {},\n    },\n    'table delete': {\n      key: 'Delete',\n      format: ['table'],\n      collapsed: true,\n      suffix: /^$/,\n      handler() {},\n    },\n    'table enter': {\n      key: 'Enter',\n      shiftKey: null,\n      format: ['table'],\n      handler(range) {\n        const module = this.quill.getModule('table');\n        if (module) {\n          // @ts-expect-error\n          const [table, row, cell, offset] = module.getTable(range);\n          const shift = tableSide(table, row, cell, offset);\n          if (shift == null) return;\n          let index = table.offset();\n          if (shift < 0) {\n            const delta = new Delta().retain(index).insert('\\n');\n            this.quill.updateContents(delta, Quill.sources.USER);\n            this.quill.setSelection(\n              range.index + 1,\n              range.length,\n              Quill.sources.SILENT,\n            );\n          } else if (shift > 0) {\n            index += table.length();\n            const delta = new Delta().retain(index).insert('\\n');\n            this.quill.updateContents(delta, Quill.sources.USER);\n            this.quill.setSelection(index, Quill.sources.USER);\n          }\n        }\n      },\n    },\n    'table tab': {\n      key: 'Tab',\n      shiftKey: null,\n      format: ['table'],\n      handler(range, context) {\n        const { event, line: cell } = context;\n        const offset = cell.offset(this.quill.scroll);\n        if (event.shiftKey) {\n          this.quill.setSelection(offset - 1, Quill.sources.USER);\n        } else {\n          this.quill.setSelection(offset + cell.length(), Quill.sources.USER);\n        }\n      },\n    },\n    'list autofill': {\n      key: ' ',\n      shiftKey: null,\n      collapsed: true,\n      format: {\n        'code-block': false,\n        blockquote: false,\n        table: false,\n      },\n      prefix: /^\\s*?(\\d+\\.|-|\\*|\\[ ?\\]|\\[x\\])$/,\n      handler(range, context) {\n        if (this.quill.scroll.query('list') == null) return true;\n        const { length } = context.prefix;\n        const [line, offset] = this.quill.getLine(range.index);\n        if (offset > length) return true;\n        let value;\n        switch (context.prefix.trim()) {\n          case '[]':\n          case '[ ]':\n            value = 'unchecked';\n            break;\n          case '[x]':\n            value = 'checked';\n            break;\n          case '-':\n          case '*':\n            value = 'bullet';\n            break;\n          default:\n            value = 'ordered';\n        }\n        this.quill.insertText(range.index, ' ', Quill.sources.USER);\n        this.quill.history.cutoff();\n        const delta = new Delta()\n          .retain(range.index - offset)\n          .delete(length + 1)\n          // @ts-expect-error Fix me later\n          .retain(line.length() - 2 - offset)\n          .retain(1, { list: value });\n        this.quill.updateContents(delta, Quill.sources.USER);\n        this.quill.history.cutoff();\n        this.quill.setSelection(range.index - length, Quill.sources.SILENT);\n        return false;\n      },\n    },\n    'code exit': {\n      key: 'Enter',\n      collapsed: true,\n      format: ['code-block'],\n      prefix: /^$/,\n      suffix: /^\\s*$/,\n      handler(range) {\n        const [line, offset] = this.quill.getLine(range.index);\n        let numLines = 2;\n        let cur = line;\n        while (\n          cur != null &&\n          cur.length() <= 1 &&\n          cur.formats()['code-block']\n        ) {\n          // @ts-expect-error\n          cur = cur.prev;\n          numLines -= 1;\n          // Requisite prev lines are empty\n          if (numLines <= 0) {\n            const delta = new Delta()\n              // @ts-expect-error Fix me later\n              .retain(range.index + line.length() - offset - 2)\n              .retain(1, { 'code-block': null })\n              .delete(1);\n            this.quill.updateContents(delta, Quill.sources.USER);\n            this.quill.setSelection(range.index - 1, Quill.sources.SILENT);\n            return false;\n          }\n        }\n        return true;\n      },\n    },\n    'embed left': makeEmbedArrowHandler('ArrowLeft', false),\n    'embed left shift': makeEmbedArrowHandler('ArrowLeft', true),\n    'embed right': makeEmbedArrowHandler('ArrowRight', false),\n    'embed right shift': makeEmbedArrowHandler('ArrowRight', true),\n    'table down': makeTableArrowHandler(false),\n    'table up': makeTableArrowHandler(true),\n  },\n};\n\nKeyboard.DEFAULTS = defaultOptions;\n\nfunction makeCodeBlockHandler(indent: boolean): BindingObject {\n  return {\n    key: 'Tab',\n    shiftKey: !indent,\n    format: { 'code-block': true },\n    handler(range, { event }) {\n      const CodeBlock = this.quill.scroll.query('code-block');\n      // @ts-expect-error\n      const { TAB } = CodeBlock;\n      if (range.length === 0 && !event.shiftKey) {\n        this.quill.insertText(range.index, TAB, Quill.sources.USER);\n        this.quill.setSelection(range.index + TAB.length, Quill.sources.SILENT);\n        return;\n      }\n\n      const lines =\n        range.length === 0\n          ? this.quill.getLines(range.index, 1)\n          : this.quill.getLines(range);\n      let { index, length } = range;\n      lines.forEach((line, i) => {\n        if (indent) {\n          line.insertAt(0, TAB);\n          if (i === 0) {\n            index += TAB.length;\n          } else {\n            length += TAB.length;\n          }\n          // @ts-expect-error Fix me later\n        } else if (line.domNode.textContent.startsWith(TAB)) {\n          line.deleteAt(0, TAB.length);\n          if (i === 0) {\n            index -= TAB.length;\n          } else {\n            length -= TAB.length;\n          }\n        }\n      });\n      this.quill.update(Quill.sources.USER);\n      this.quill.setSelection(index, length, Quill.sources.SILENT);\n    },\n  };\n}\n\nfunction makeEmbedArrowHandler(\n  key: string,\n  shiftKey: boolean | null,\n): BindingObject {\n  const where = key === 'ArrowLeft' ? 'prefix' : 'suffix';\n  return {\n    key,\n    shiftKey,\n    altKey: null,\n    [where]: /^$/,\n    handler(range) {\n      let { index } = range;\n      if (key === 'ArrowRight') {\n        index += range.length + 1;\n      }\n      const [leaf] = this.quill.getLeaf(index);\n      if (!(leaf instanceof EmbedBlot)) return true;\n      if (key === 'ArrowLeft') {\n        if (shiftKey) {\n          this.quill.setSelection(\n            range.index - 1,\n            range.length + 1,\n            Quill.sources.USER,\n          );\n        } else {\n          this.quill.setSelection(range.index - 1, Quill.sources.USER);\n        }\n      } else if (shiftKey) {\n        this.quill.setSelection(\n          range.index,\n          range.length + 1,\n          Quill.sources.USER,\n        );\n      } else {\n        this.quill.setSelection(\n          range.index + range.length + 1,\n          Quill.sources.USER,\n        );\n      }\n      return false;\n    },\n  };\n}\n\nfunction makeFormatHandler(format: string): BindingObject {\n  return {\n    key: format[0],\n    shortKey: true,\n    handler(range, context) {\n      this.quill.format(format, !context.format[format], Quill.sources.USER);\n    },\n  };\n}\n\nfunction makeTableArrowHandler(up: boolean): BindingObject {\n  return {\n    key: up ? 'ArrowUp' : 'ArrowDown',\n    collapsed: true,\n    format: ['table'],\n    handler(range, context) {\n      // TODO move to table module\n      const key = up ? 'prev' : 'next';\n      const cell = context.line;\n      const targetRow = cell.parent[key];\n      if (targetRow != null) {\n        if (targetRow.statics.blotName === 'table-row') {\n          // @ts-expect-error\n          let targetCell = targetRow.children.head;\n          let cur = cell;\n          while (cur.prev != null) {\n            // @ts-expect-error\n            cur = cur.prev;\n            targetCell = targetCell.next;\n          }\n          const index =\n            targetCell.offset(this.quill.scroll) +\n            Math.min(context.offset, targetCell.length() - 1);\n          this.quill.setSelection(index, 0, Quill.sources.USER);\n        }\n      } else {\n        // @ts-expect-error\n        const targetLine = cell.table()[key];\n        if (targetLine != null) {\n          if (up) {\n            this.quill.setSelection(\n              targetLine.offset(this.quill.scroll) + targetLine.length() - 1,\n              0,\n              Quill.sources.USER,\n            );\n          } else {\n            this.quill.setSelection(\n              targetLine.offset(this.quill.scroll),\n              0,\n              Quill.sources.USER,\n            );\n          }\n        }\n      }\n      return false;\n    },\n  };\n}\n\nfunction normalize(binding: Binding): BindingObject | null {\n  if (typeof binding === 'string' || typeof binding === 'number') {\n    binding = { key: binding };\n  } else if (typeof binding === 'object') {\n    binding = cloneDeep(binding);\n  } else {\n    return null;\n  }\n  if (binding.shortKey) {\n    binding[SHORTKEY] = binding.shortKey;\n    delete binding.shortKey;\n  }\n  return binding;\n}\n\n// TODO: Move into quill.ts or editor.ts\nfunction deleteRange({ quill, range }: { quill: Quill; range: Range }) {\n  const lines = quill.getLines(range);\n  let formats = {};\n  if (lines.length > 1) {\n    const firstFormats = lines[0].formats();\n    const lastFormats = lines[lines.length - 1].formats();\n    formats = AttributeMap.diff(lastFormats, firstFormats) || {};\n  }\n  quill.deleteText(range, Quill.sources.USER);\n  if (Object.keys(formats).length > 0) {\n    quill.formatLine(range.index, 1, formats, Quill.sources.USER);\n  }\n  quill.setSelection(range.index, Quill.sources.SILENT);\n}\n\nfunction tableSide(_table: unknown, row: Blot, cell: Blot, offset: number) {\n  if (row.prev == null && row.next == null) {\n    if (cell.prev == null && cell.next == null) {\n      return offset === 0 ? -1 : 1;\n    }\n    return cell.prev == null ? -1 : 1;\n  }\n  if (row.prev == null) {\n    return -1;\n  }\n  if (row.next == null) {\n    return 1;\n  }\n  return null;\n}\n\nexport { Keyboard as default, SHORTKEY, normalize, deleteRange };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,WAAA,GAAAC,uBAAA,CAAAF,OAAA;AACA,IAAAG,UAAA,GAAAH,OAAA;AAEA,IAAAI,MAAA,GAAAC,sBAAA,CAAAL,OAAA;AACA,IAAAM,OAAA,GAAAD,sBAAA,CAAAL,OAAA;AACA,IAAAO,OAAA,GAAAF,sBAAA,CAAAL,OAAA;AAIA,IAAMQ,KAAK,GAAG,IAAAC,eAAM,EAAC,gBAAgB,CAAC;AAEtC,IAAMC,QAAQ,GAAAC,OAAA,CAAAD,QAAA,GAAG,MAAM,CAACE,IAAI,CAACC,SAAS,CAACC,QAAQ,CAAC,GAAG,SAAS,GAAG,SAAS;AAAA,IA+ClEC,QAAQ,GAAAJ,OAAA,CAAAK,OAAA,0BAAAC,OAAA;EAgBZ,SAAAF,SAAYG,KAAY,EAAEC,OAAiC,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAAL,OAAA,QAAAD,QAAA;IAC3DK,KAAA,OAAAE,WAAA,CAAAN,OAAA,QAAAD,QAAA,GAAMG,KAAK,EAAEC,OAAO;IACpBC,KAAA,CAAKG,QAAQ,GAAG,CAAC,CAAC;IAClB;IACAC,MAAM,CAACC,IAAI,CAACL,KAAA,CAAKD,OAAO,CAACI,QAAQ,CAAC,CAACG,OAAO,CAAE,UAAAC,IAAI,EAAK;MACnD;MACA,IAAIP,KAAA,CAAKD,OAAO,CAACI,QAAQ,CAACI,IAAI,CAAC,EAAE;QAC/B;QACAP,KAAA,CAAKQ,UAAU,CAACR,KAAA,CAAKD,OAAO,CAACI,QAAQ,CAACI,IAAI,CAAC,CAAC;MAC9C;IACF,CAAC,CAAC;IACFP,KAAA,CAAKQ,UAAU,CAAC;MAAEC,GAAG,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAK,CAAC,EAAEV,KAAA,CAAKW,WAAW,CAAC;IACnEX,KAAA,CAAKQ,UAAU,CACb;MAAEC,GAAG,EAAE,OAAO;MAAEG,OAAO,EAAE,IAAI;MAAEC,OAAO,EAAE,IAAI;MAAEC,MAAM,EAAE;IAAK,CAAC,EAC5D,YAAM,CAAC,CACT,CAAC;IACD,IAAI,UAAU,CAACtB,IAAI,CAACC,SAAS,CAACsB,SAAS,CAAC,EAAE;MACxC;MACAf,KAAA,CAAKQ,UAAU,CACb;QAAEC,GAAG,EAAE;MAAY,CAAC,EACpB;QAAEO,SAAS,EAAE;MAAK,CAAC,EACnBhB,KAAA,CAAKiB,eACP,CAAC;MACDjB,KAAA,CAAKQ,UAAU,CACb;QAAEC,GAAG,EAAE;MAAS,CAAC,EACjB;QAAEO,SAAS,EAAE;MAAK,CAAC,EACnBhB,KAAA,CAAKkB,YACP,CAAC;IACH,CAAC,MAAM;MACLlB,KAAA,CAAKQ,UAAU,CACb;QAAEC,GAAG,EAAE;MAAY,CAAC,EACpB;QAAEO,SAAS,EAAE,IAAI;QAAEG,MAAM,EAAE;MAAO,CAAC,EACnCnB,KAAA,CAAKiB,eACP,CAAC;MACDjB,KAAA,CAAKQ,UAAU,CACb;QAAEC,GAAG,EAAE;MAAS,CAAC,EACjB;QAAEO,SAAS,EAAE,IAAI;QAAEI,MAAM,EAAE;MAAO,CAAC,EACnCpB,KAAA,CAAKkB,YACP,CAAC;IACH;IACAlB,KAAA,CAAKQ,UAAU,CACb;MAAEC,GAAG,EAAE;IAAY,CAAC,EACpB;MAAEO,SAAS,EAAE;IAAM,CAAC,EACpBhB,KAAA,CAAKqB,iBACP,CAAC;IACDrB,KAAA,CAAKQ,UAAU,CACb;MAAEC,GAAG,EAAE;IAAS,CAAC,EACjB;MAAEO,SAAS,EAAE;IAAM,CAAC,EACpBhB,KAAA,CAAKqB,iBACP,CAAC;IACDrB,KAAA,CAAKQ,UAAU,CACb;MACEC,GAAG,EAAE,WAAW;MAChBK,MAAM,EAAE,IAAI;MACZD,OAAO,EAAE,IAAI;MACbD,OAAO,EAAE,IAAI;MACbF,QAAQ,EAAE;IACZ,CAAC,EACD;MAAEM,SAAS,EAAE,IAAI;MAAEM,MAAM,EAAE;IAAE,CAAC,EAC9BtB,KAAA,CAAKiB,eACP,CAAC;IACDjB,KAAA,CAAKuB,MAAM,CAAC,CAAC;IAAA,OAAAvB,KAAA;EACf;EAAA,IAAAwB,UAAA,CAAA5B,OAAA,EAAAD,QAAA,EAAAE,OAAA;EAAA,WAAA4B,aAAA,CAAA7B,OAAA,EAAAD,QAAA;IAAAc,GAAA;IAAAiB,KAAA,EAEA,SAAAlB,UAAUA,CACRmB,UAAmB,EAOnB;MAAA,IAAAC,MAAA;MAAA,IANAC,OAEmD,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAAA,IACxDG,OAEmD,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAExD,IAAMI,OAAO,GAAGC,SAAS,CAACR,UAAU,CAAC;MACrC,IAAIO,OAAO,IAAI,IAAI,EAAE;QACnB9C,KAAK,CAACgD,IAAI,CAAC,2CAA2C,EAAEF,OAAO,CAAC;QAChE;MACF;MACA,IAAI,OAAOL,OAAO,KAAK,UAAU,EAAE;QACjCA,OAAO,GAAG;UAAEI,OAAO,EAAEJ;QAAQ,CAAC;MAChC;MACA,IAAI,OAAOI,OAAO,KAAK,UAAU,EAAE;QACjCA,OAAO,GAAG;UAAEA,OAAA,EAAAA;QAAQ,CAAC;MACvB;MACA,IAAM5B,IAAI,GAAGgC,KAAK,CAACC,OAAO,CAACJ,OAAO,CAACzB,GAAG,CAAC,GAAGyB,OAAO,CAACzB,GAAG,GAAG,CAACyB,OAAO,CAACzB,GAAG,CAAC;MACrEJ,IAAI,CAACC,OAAO,CAAE,UAAAG,GAAG,EAAK;QACpB,IAAM8B,aAAa,OAAAC,cAAA,CAAA5C,OAAA,MAAA4C,cAAA,CAAA5C,OAAA,MAAA4C,cAAA,CAAA5C,OAAA,MACdsC,OAAO;UACVzB,GAAG,EAAHA;QAAG,GACAoB,OAAO,GACPI,OAAA,CACJ;QACDL,MAAI,CAACzB,QAAQ,CAACoC,aAAa,CAAC9B,GAAG,CAAC,GAAGmB,MAAI,CAACzB,QAAQ,CAACoC,aAAa,CAAC9B,GAAG,CAAC,IAAI,EAAE;QACzEmB,MAAI,CAACzB,QAAQ,CAACoC,aAAa,CAAC9B,GAAG,CAAC,CAACgC,IAAI,CAACF,aAAa,CAAC;MACtD,CAAC,CAAC;IACJ;EAAA;IAAA9B,GAAA;IAAAiB,KAAA,EAEA,SAAAH,MAAMA,CAAA,EAAG;MAAA,IAAAmB,MAAA;MACP,IAAI,CAAC5C,KAAK,CAAC6C,IAAI,CAACC,gBAAgB,CAAC,SAAS,EAAG,UAAAC,GAAG,EAAK;QACnD,IAAIA,GAAG,CAACC,gBAAgB,IAAID,GAAG,CAACE,WAAW,EAAE;;QAE7C;QACA;QACA,IAAMA,WAAW,GACfF,GAAG,CAACG,OAAO,KAAK,GAAG,KAAKH,GAAG,CAACpC,GAAG,KAAK,OAAO,IAAIoC,GAAG,CAACpC,GAAG,KAAK,WAAW,CAAC;QACzE,IAAIsC,WAAW,EAAE;QAEjB,IAAM5C,QAAQ,GAAG,CAACuC,MAAI,CAACvC,QAAQ,CAAC0C,GAAG,CAACpC,GAAG,CAAC,IAAI,EAAE,EAAEwC,MAAM,CACpDP,MAAI,CAACvC,QAAQ,CAAC0C,GAAG,CAACK,KAAK,CAAC,IAAI,EAC9B,CAAC;QACD,IAAMC,OAAO,GAAGhD,QAAQ,CAACiD,MAAM,CAAE,UAAAlB,OAAO;UAAA,OACtCvC,QAAQ,CAAC0D,KAAK,CAACR,GAAG,EAAEX,OAAO,CAC7B;QAAA,EAAC;QACD,IAAIiB,OAAO,CAACpB,MAAM,KAAK,CAAC,EAAE;QAC1B;QACA,IAAMuB,IAAI,GAAGC,cAAK,CAACC,IAAI,CAACX,GAAG,CAACY,MAAM,EAAE,IAAI,CAAC;QACzC,IAAIH,IAAI,IAAIA,IAAI,CAACI,MAAM,KAAKhB,MAAI,CAAC5C,KAAK,CAAC4D,MAAM,EAAE;QAC/C,IAAMC,KAAK,GAAGjB,MAAI,CAAC5C,KAAK,CAAC8D,YAAY,CAAC,CAAC;QACvC,IAAID,KAAK,IAAI,IAAI,IAAI,CAACjB,MAAI,CAAC5C,KAAK,CAAC+D,QAAQ,CAAC,CAAC,EAAE;QAC7C,IAAAC,oBAAA,GAAuBpB,MAAI,CAAC5C,KAAK,CAACiE,OAAO,CAACJ,KAAK,CAACK,KAAK,CAAC;UAAAC,qBAAA,OAAAC,eAAA,CAAAtE,OAAA,EAAAkE,oBAAA;UAA/CK,IAAI,GAAAF,qBAAA;UAAE3C,MAAM,GAAA2C,qBAAA;QACnB,IAAAG,oBAAA,GAAiC1B,MAAI,CAAC5C,KAAK,CAACuE,OAAO,CAACV,KAAK,CAACK,KAAK,CAAC;UAAAM,qBAAA,OAAAJ,eAAA,CAAAtE,OAAA,EAAAwE,oBAAA;UAAzDG,SAAS,GAAAD,qBAAA;UAAEE,WAAW,GAAAF,qBAAA;QAC7B,IAAAG,KAAA,GACEd,KAAK,CAAC5B,MAAM,KAAK,CAAC,GACd,CAACwC,SAAS,EAAEC,WAAW,CAAC,GACxB9B,MAAI,CAAC5C,KAAK,CAACuE,OAAO,CAACV,KAAK,CAACK,KAAK,GAAGL,KAAK,CAAC5B,MAAM,CAAC;UAAA2C,KAAA,OAAAR,eAAA,CAAAtE,OAAA,EAAA6E,KAAA;UAH7CE,OAAO,GAAAD,KAAA;UAAEE,SAAS,GAAAF,KAAA;QAIzB,IAAMG,UAAU,GACdN,SAAS,YAAYO,mBAAQ,GACzBP,SAAS,CAAC7C,KAAK,CAAC,CAAC,CAACqD,KAAK,CAAC,CAAC,EAAEP,WAAW,CAAC,GACvC,EAAE;QACR,IAAMQ,UAAU,GACdL,OAAO,YAAYG,mBAAQ,GAAGH,OAAO,CAACjD,KAAK,CAAC,CAAC,CAACqD,KAAK,CAACH,SAAS,CAAC,GAAG,EAAE;QACrE,IAAMK,UAAU,GAAG;UACjBjE,SAAS,EAAE2C,KAAK,CAAC5B,MAAM,KAAK,CAAC;UAC7B;UACAmD,KAAK,EAAEvB,KAAK,CAAC5B,MAAM,KAAK,CAAC,IAAIoC,IAAI,CAACpC,MAAM,CAAC,CAAC,IAAI,CAAC;UAC/CoD,MAAM,EAAEzC,MAAI,CAAC5C,KAAK,CAACsF,SAAS,CAACzB,KAAK,CAAC;UACnCQ,IAAI,EAAJA,IAAI;UACJ7C,MAAM,EAANA,MAAM;UACNH,MAAM,EAAE0D,UAAU;UAClBzD,MAAM,EAAE4D,UAAU;UAClBK,KAAK,EAAExC;QACT,CAAC;QACD,IAAMyC,SAAS,GAAGnC,OAAO,CAACoC,IAAI,CAAE,UAAArD,OAAO,EAAK;UAC1C,IACEA,OAAO,CAAClB,SAAS,IAAI,IAAI,IACzBkB,OAAO,CAAClB,SAAS,KAAKiE,UAAU,CAACjE,SAAS,EAC1C;YACA,OAAO,KAAK;UACd;UACA,IAAIkB,OAAO,CAACgD,KAAK,IAAI,IAAI,IAAIhD,OAAO,CAACgD,KAAK,KAAKD,UAAU,CAACC,KAAK,EAAE;YAC/D,OAAO,KAAK;UACd;UACA,IAAIhD,OAAO,CAACZ,MAAM,IAAI,IAAI,IAAIY,OAAO,CAACZ,MAAM,KAAK2D,UAAU,CAAC3D,MAAM,EAAE;YAClE,OAAO,KAAK;UACd;UACA,IAAIe,KAAK,CAACC,OAAO,CAACJ,OAAO,CAACiD,MAAM,CAAC,EAAE;YACjC;YACA,IAAIjD,OAAO,CAACiD,MAAM,CAACK,KAAK,CAAE,UAAAjF,IAAI;cAAA,OAAK0E,UAAU,CAACE,MAAM,CAAC5E,IAAI,CAAC,IAAI,IAAI;YAAA,EAAC,EAAE;cACnE,OAAO,KAAK;YACd;UACF,CAAC,MAAM,IAAI,IAAAkF,QAAA,CAAA7F,OAAA,EAAOsC,OAAO,CAACiD,MAAM,MAAK,QAAQ,EAAE;YAC7C;YACA,IACE,CAAC/E,MAAM,CAACC,IAAI,CAAC6B,OAAO,CAACiD,MAAM,CAAC,CAACK,KAAK,CAAE,UAAAjF,IAAI,EAAK;cAC3C;cACA,IAAI2B,OAAO,CAACiD,MAAM,CAAC5E,IAAI,CAAC,KAAK,IAAI,EAC/B,OAAO0E,UAAU,CAACE,MAAM,CAAC5E,IAAI,CAAC,IAAI,IAAI;cACxC;cACA,IAAI2B,OAAO,CAACiD,MAAM,CAAC5E,IAAI,CAAC,KAAK,KAAK,EAChC,OAAO0E,UAAU,CAACE,MAAM,CAAC5E,IAAI,CAAC,IAAI,IAAI;cACxC;cACA,OAAO,IAAAmF,iBAAO,EAACxD,OAAO,CAACiD,MAAM,CAAC5E,IAAI,CAAC,EAAE0E,UAAU,CAACE,MAAM,CAAC5E,IAAI,CAAC,CAAC;YAC/D,CAAC,CAAC,EACF;cACA,OAAO,KAAK;YACd;UACF;UACA,IAAI2B,OAAO,CAACf,MAAM,IAAI,IAAI,IAAI,CAACe,OAAO,CAACf,MAAM,CAAC3B,IAAI,CAACyF,UAAU,CAAC9D,MAAM,CAAC,EAAE;YACrE,OAAO,KAAK;UACd;UACA,IAAIe,OAAO,CAACd,MAAM,IAAI,IAAI,IAAI,CAACc,OAAO,CAACd,MAAM,CAAC5B,IAAI,CAACyF,UAAU,CAAC7D,MAAM,CAAC,EAAE;YACrE,OAAO,KAAK;UACd;UACA;UACA,OAAOc,OAAO,CAACD,OAAO,CAAC0D,IAAI,CAACjD,MAAI,EAAEiB,KAAK,EAAEsB,UAAU,EAAE/C,OAAO,CAAC,KAAK,IAAI;QACxE,CAAC,CAAC;QACF,IAAIoD,SAAS,EAAE;UACbzC,GAAG,CAAC+C,cAAc,CAAC,CAAC;QACtB;MACF,CAAC,CAAC;IACJ;EAAA;IAAAnF,GAAA;IAAAiB,KAAA,EAEA,SAAAT,eAAeA,CAAC0C,KAAY,EAAE9B,OAAgB,EAAE;MAC9C;MACA,IAAME,MAAM,GAAG,iCAAiC,CAACvC,IAAI,CAACqC,OAAO,CAACV,MAAM,CAAC,GACjE,CAAC,GACD,CAAC;MACL,IAAIwC,KAAK,CAACK,KAAK,KAAK,CAAC,IAAI,IAAI,CAAClE,KAAK,CAAC+F,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE;MACtD,IAAIC,OAAO,GAAG,CAAC,CAAC;MAChB,IAAAC,mBAAA,GAAe,IAAI,CAACjG,KAAK,CAACiE,OAAO,CAACJ,KAAK,CAACK,KAAK,CAAC;QAAAgC,oBAAA,OAAA9B,eAAA,CAAAtE,OAAA,EAAAmG,mBAAA;QAAvC5B,IAAI,GAAA6B,oBAAA;MACX,IAAIC,KAAK,GAAG,IAAIC,mBAAK,CAAC,CAAC,CAACC,MAAM,CAACxC,KAAK,CAACK,KAAK,GAAGjC,MAAM,CAAC,CAACqE,MAAM,CAACrE,MAAM,CAAC;MACnE,IAAIF,OAAO,CAACP,MAAM,KAAK,CAAC,EAAE;QACxB;QACA,IAAA+E,oBAAA,GAAe,IAAI,CAACvG,KAAK,CAACiE,OAAO,CAACJ,KAAK,CAACK,KAAK,GAAG,CAAC,CAAC;UAAAsC,oBAAA,OAAApC,eAAA,CAAAtE,OAAA,EAAAyG,oBAAA;UAA3CE,IAAI,GAAAD,oBAAA;QACX,IAAIC,IAAI,EAAE;UACR,IAAMC,eAAe,GACnBD,IAAI,CAACE,OAAO,CAACC,QAAQ,KAAK,OAAO,IAAIH,IAAI,CAACxE,MAAM,CAAC,CAAC,IAAI,CAAC;UACzD,IAAI,CAACyE,eAAe,EAAE;YACpB;YACA,IAAMG,UAAU,GAAGxC,IAAI,CAAC2B,OAAO,CAAC,CAAC;YACjC,IAAMc,WAAW,GAAG,IAAI,CAAC9G,KAAK,CAACsF,SAAS,CAACzB,KAAK,CAACK,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;YAC5D8B,OAAO,GAAGe,wBAAY,CAACC,IAAI,CAACH,UAAU,EAAEC,WAAW,CAAC,IAAI,CAAC,CAAC;YAC1D,IAAIxG,MAAM,CAACC,IAAI,CAACyF,OAAO,CAAC,CAAC/D,MAAM,GAAG,CAAC,EAAE;cACnC;cACA,IAAMgF,WAAW,GAAG,IAAIb,mBAAK,CAAC;cAC5B;cAAA,CACCC,MAAM,CAACxC,KAAK,CAACK,KAAK,GAAGG,IAAI,CAACpC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CACvCoE,MAAM,CAAC,CAAC,EAAEL,OAAO,CAAC;cACrBG,KAAK,GAAGA,KAAK,CAACe,OAAO,CAACD,WAAW,CAAC;YACpC;UACF;QACF;MACF;MACA,IAAI,CAACjH,KAAK,CAACmH,cAAc,CAAChB,KAAK,EAAE1C,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;MACpD,IAAI,CAACrH,KAAK,CAACsH,KAAK,CAAC,CAAC;IACpB;EAAA;IAAA3G,GAAA;IAAAiB,KAAA,EAEA,SAAAR,YAAYA,CAACyC,KAAY,EAAE9B,OAAgB,EAAE;MAC3C;MACA,IAAME,MAAM,GAAG,iCAAiC,CAACvC,IAAI,CAACqC,OAAO,CAACT,MAAM,CAAC,GACjE,CAAC,GACD,CAAC;MACL,IAAIuC,KAAK,CAACK,KAAK,IAAI,IAAI,CAAClE,KAAK,CAAC+F,SAAS,CAAC,CAAC,GAAG9D,MAAM,EAAE;MACpD,IAAI+D,OAAO,GAAG,CAAC,CAAC;MAChB,IAAAuB,oBAAA,GAAe,IAAI,CAACvH,KAAK,CAACiE,OAAO,CAACJ,KAAK,CAACK,KAAK,CAAC;QAAAsD,oBAAA,OAAApD,eAAA,CAAAtE,OAAA,EAAAyH,oBAAA;QAAvClD,IAAI,GAAAmD,oBAAA;MACX,IAAIrB,KAAK,GAAG,IAAIC,mBAAK,CAAC,CAAC,CAACC,MAAM,CAACxC,KAAK,CAACK,KAAK,CAAC,CAACoC,MAAM,CAACrE,MAAM,CAAC;MAC1D;MACA,IAAIF,OAAO,CAACP,MAAM,IAAI6C,IAAI,CAACpC,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE;QACvC,IAAAwF,oBAAA,GAAe,IAAI,CAACzH,KAAK,CAACiE,OAAO,CAACJ,KAAK,CAACK,KAAK,GAAG,CAAC,CAAC;UAAAwD,oBAAA,OAAAtD,eAAA,CAAAtE,OAAA,EAAA2H,oBAAA;UAA3CE,IAAI,GAAAD,oBAAA;QACX,IAAIC,IAAI,EAAE;UACR;UACA,IAAMd,UAAU,GAAGxC,IAAI,CAAC2B,OAAO,CAAC,CAAC;UACjC,IAAM4B,WAAW,GAAG,IAAI,CAAC5H,KAAK,CAACsF,SAAS,CAACzB,KAAK,CAACK,KAAK,EAAE,CAAC,CAAC;UACxD8B,OAAO,GAAGe,wBAAY,CAACC,IAAI,CAACH,UAAU,EAAEe,WAAW,CAAC,IAAI,CAAC,CAAC;UAC1D,IAAItH,MAAM,CAACC,IAAI,CAACyF,OAAO,CAAC,CAAC/D,MAAM,GAAG,CAAC,EAAE;YACnCkE,KAAK,GAAGA,KAAK,CAACE,MAAM,CAACsB,IAAI,CAAC1F,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAACoE,MAAM,CAAC,CAAC,EAAEL,OAAO,CAAC;UAC5D;QACF;MACF;MACA,IAAI,CAAChG,KAAK,CAACmH,cAAc,CAAChB,KAAK,EAAE1C,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;MACpD,IAAI,CAACrH,KAAK,CAACsH,KAAK,CAAC,CAAC;IACpB;EAAA;IAAA3G,GAAA;IAAAiB,KAAA,EAEA,SAAAL,iBAAiBA,CAACsC,KAAY,EAAE;MAC9BgE,WAAW,CAAC;QAAEhE,KAAK,EAALA,KAAK;QAAE7D,KAAK,EAAE,IAAI,CAACA;MAAM,CAAC,CAAC;MACzC,IAAI,CAACA,KAAK,CAACsH,KAAK,CAAC,CAAC;IACpB;EAAA;IAAA3G,GAAA;IAAAiB,KAAA,EAEA,SAAAf,WAAWA,CAACgD,KAAY,EAAE9B,OAAgB,EAAE;MAAA,IAAA+F,MAAA;MAC1C,IAAMC,WAAW,GAAGzH,MAAM,CAACC,IAAI,CAACwB,OAAO,CAACsD,MAAM,CAAC,CAAC2C,MAAM,CACpD,UAAChC,OAAgC,EAAEX,MAAM,EAAK;QAC5C,IACEyC,MAAI,CAAC9H,KAAK,CAAC4D,MAAM,CAACqE,KAAK,CAAC5C,MAAM,EAAE6C,gBAAK,CAACC,KAAK,CAAC,IAC5C,CAAC5F,KAAK,CAACC,OAAO,CAACT,OAAO,CAACsD,MAAM,CAACA,MAAM,CAAC,CAAC,EACtC;UACAW,OAAO,CAACX,MAAM,CAAC,GAAGtD,OAAO,CAACsD,MAAM,CAACA,MAAM,CAAC;QAC1C;QACA,OAAOW,OAAO;MAChB,CAAC,EACD,CAAC,CACH,CAAC;MACD,IAAMG,KAAK,GAAG,IAAIC,mBAAK,CAAC,CAAC,CACtBC,MAAM,CAACxC,KAAK,CAACK,KAAK,CAAC,CACnBoC,MAAM,CAACzC,KAAK,CAAC5B,MAAM,CAAC,CACpBmG,MAAM,CAAC,IAAI,EAAEL,WAAW,CAAC;MAC5B,IAAI,CAAC/H,KAAK,CAACmH,cAAc,CAAChB,KAAK,EAAE1C,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;MACpD,IAAI,CAACrH,KAAK,CAACqI,YAAY,CAACxE,KAAK,CAACK,KAAK,GAAG,CAAC,EAAET,cAAK,CAAC2D,OAAO,CAACkB,MAAM,CAAC;MAC9D,IAAI,CAACtI,KAAK,CAACsH,KAAK,CAAC,CAAC;IACpB;EAAA;IAAA3G,GAAA;IAAAiB,KAAA,EAnSA,SAAO2B,KAAKA,CAACR,GAAkB,EAAEX,OAAsB,EAAE;MACvD,IACG,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC,CAAWqD,IAAI,CAAE,UAAA9E,GAAG,EAAK;QACpE,OAAO,CAAC,CAACyB,OAAO,CAACzB,GAAG,CAAC,KAAKoC,GAAG,CAACpC,GAAG,CAAC,IAAIyB,OAAO,CAACzB,GAAG,CAAC,KAAK,IAAI;MAC7D,CAAC,CAAC,EACF;QACA,OAAO,KAAK;MACd;MACA,OAAOyB,OAAO,CAACzB,GAAG,KAAKoC,GAAG,CAACpC,GAAG,IAAIyB,OAAO,CAACzB,GAAG,KAAKoC,GAAG,CAACK,KAAK;IAC7D;EAAA;AAAA,EAZqBmF,eAAM;AAyS7B,IAAMC,cAA+B,GAAG;EACtCnI,QAAQ,EAAE;IACRoI,IAAI,EAAEC,iBAAiB,CAAC,MAAM,CAAC;IAC/BC,MAAM,EAAED,iBAAiB,CAAC,QAAQ,CAAC;IACnCE,SAAS,EAAEF,iBAAiB,CAAC,WAAW,CAAC;IACzCG,MAAM,EAAE;MACN;MACAlI,GAAG,EAAE,KAAK;MACV0E,MAAM,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,MAAM,CAAC;MACxClD,OAAO,WAAPA,OAAOA,CAAC0B,KAAK,EAAE9B,OAAO,EAAE;QACtB,IAAIA,OAAO,CAACb,SAAS,IAAIa,OAAO,CAACP,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;QAC1D,IAAI,CAACxB,KAAK,CAACqF,MAAM,CAAC,QAAQ,EAAE,IAAI,EAAE5B,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;QACrD,OAAO,KAAK;MACd;IACF,CAAC;IACDyB,OAAO,EAAE;MACPnI,GAAG,EAAE,KAAK;MACVC,QAAQ,EAAE,IAAI;MACdyE,MAAM,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,MAAM,CAAC;MACxC;MACAlD,OAAO,WAAPA,OAAOA,CAAC0B,KAAK,EAAE9B,OAAO,EAAE;QACtB,IAAIA,OAAO,CAACb,SAAS,IAAIa,OAAO,CAACP,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;QAC1D,IAAI,CAACxB,KAAK,CAACqF,MAAM,CAAC,QAAQ,EAAE,IAAI,EAAE5B,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;QACrD,OAAO,KAAK;MACd;IACF,CAAC;IACD,mBAAmB,EAAE;MACnB1G,GAAG,EAAE,WAAW;MAChBO,SAAS,EAAE,IAAI;MACfN,QAAQ,EAAE,IAAI;MACdE,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE,IAAI;MACbC,MAAM,EAAE,IAAI;MACZqE,MAAM,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC;MAC1B7D,MAAM,EAAE,CAAC;MACTW,OAAO,WAAPA,OAAOA,CAAC0B,KAAK,EAAE9B,OAAO,EAAE;QACtB,IAAIA,OAAO,CAACsD,MAAM,CAACwD,MAAM,IAAI,IAAI,EAAE;UACjC,IAAI,CAAC7I,KAAK,CAACqF,MAAM,CAAC,QAAQ,EAAE,IAAI,EAAE5B,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;QACvD,CAAC,MAAM,IAAItF,OAAO,CAACsD,MAAM,CAAC0D,IAAI,IAAI,IAAI,EAAE;UACtC,IAAI,CAAC/I,KAAK,CAACqF,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE5B,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;QACtD;MACF;IACF,CAAC;IACD,mBAAmB,EAAE2B,oBAAoB,CAAC,IAAI,CAAC;IAC/C,oBAAoB,EAAEA,oBAAoB,CAAC,KAAK,CAAC;IACjD,YAAY,EAAE;MACZrI,GAAG,EAAE,KAAK;MACVC,QAAQ,EAAE,IAAI;MACdM,SAAS,EAAE,IAAI;MACfG,MAAM,EAAE,KAAK;MACbc,OAAO,WAAPA,OAAOA,CAAC0B,KAAK,EAAE;QACb,IAAI,CAAC7D,KAAK,CAACiJ,UAAU,CAACpF,KAAK,CAACK,KAAK,GAAG,CAAC,EAAE,CAAC,EAAET,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;MAC/D;IACF,CAAC;IACD6B,GAAG,EAAE;MACHvI,GAAG,EAAE,KAAK;MACVwB,OAAO,WAAPA,OAAOA,CAAC0B,KAAK,EAAE9B,OAAO,EAAE;QACtB,IAAIA,OAAO,CAACsD,MAAM,CAAC8D,KAAK,EAAE,OAAO,IAAI;QACrC,IAAI,CAACnJ,KAAK,CAACoJ,OAAO,CAACC,MAAM,CAAC,CAAC;QAC3B,IAAMlD,KAAK,GAAG,IAAIC,mBAAK,CAAC,CAAC,CACtBC,MAAM,CAACxC,KAAK,CAACK,KAAK,CAAC,CACnBoC,MAAM,CAACzC,KAAK,CAAC5B,MAAM,CAAC,CACpBmG,MAAM,CAAC,IAAI,CAAC;QACf,IAAI,CAACpI,KAAK,CAACmH,cAAc,CAAChB,KAAK,EAAE1C,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;QACpD,IAAI,CAACrH,KAAK,CAACoJ,OAAO,CAACC,MAAM,CAAC,CAAC;QAC3B,IAAI,CAACrJ,KAAK,CAACqI,YAAY,CAACxE,KAAK,CAACK,KAAK,GAAG,CAAC,EAAET,cAAK,CAAC2D,OAAO,CAACkB,MAAM,CAAC;QAC9D,OAAO,KAAK;MACd;IACF,CAAC;IACD,wBAAwB,EAAE;MACxB3H,GAAG,EAAE,OAAO;MACZO,SAAS,EAAE,IAAI;MACfmE,MAAM,EAAE,CAAC,YAAY,CAAC;MACtBD,KAAK,EAAE,IAAI;MACXjD,OAAO,WAAPA,OAAOA,CAAA,EAAG;QACR,IAAI,CAACnC,KAAK,CAACqF,MAAM,CAAC,YAAY,EAAE,KAAK,EAAE5B,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;MAC5D;IACF,CAAC;IACD,kBAAkB,EAAE;MAClB1G,GAAG,EAAE,OAAO;MACZO,SAAS,EAAE,IAAI;MACfmE,MAAM,EAAE,CAAC,MAAM,CAAC;MAChBD,KAAK,EAAE,IAAI;MACXjD,OAAO,WAAPA,OAAOA,CAAC0B,KAAK,EAAE9B,OAAO,EAAE;QACtB,IAAMiE,OAAgC,GAAG;UAAE+C,IAAI,EAAE;QAAM,CAAC;QACxD,IAAIhH,OAAO,CAACsD,MAAM,CAACwD,MAAM,EAAE;UACzB7C,OAAO,CAAC6C,MAAM,GAAG,KAAK;QACxB;QACA,IAAI,CAAC7I,KAAK,CAACsJ,UAAU,CACnBzF,KAAK,CAACK,KAAK,EACXL,KAAK,CAAC5B,MAAM,EACZ+D,OAAO,EACPvC,cAAK,CAAC2D,OAAO,CAACC,IAChB,CAAC;MACH;IACF,CAAC;IACD,iBAAiB,EAAE;MACjB1G,GAAG,EAAE,OAAO;MACZO,SAAS,EAAE,IAAI;MACfmE,MAAM,EAAE;QAAE0D,IAAI,EAAE;MAAU,CAAC;MAC3B5G,OAAO,WAAPA,OAAOA,CAAC0B,KAAK,EAAE;QACb,IAAA0F,oBAAA,GAAuB,IAAI,CAACvJ,KAAK,CAACiE,OAAO,CAACJ,KAAK,CAACK,KAAK,CAAC;UAAAsF,oBAAA,OAAApF,eAAA,CAAAtE,OAAA,EAAAyJ,oBAAA;UAA/ClF,IAAI,GAAAmF,oBAAA;UAAEhI,MAAM,GAAAgI,oBAAA;QACnB,IAAMxD,OAAO,OAAAtD,cAAA,CAAA5C,OAAA,MAAA4C,cAAA,CAAA5C,OAAA,MAERuE,IAAI,CAAC2B,OAAO,CAAC,CAAC;UACjB+C,IAAI,EAAE;QAAA,EACP;QACD,IAAM5C,KAAK,GAAG,IAAIC,mBAAK,CAAC,CAAC,CACtBC,MAAM,CAACxC,KAAK,CAACK,KAAK,CAAC,CACnBkE,MAAM,CAAC,IAAI,EAAEpC,OAAO;QACrB;QAAA,CACCK,MAAM,CAAChC,IAAI,CAACpC,MAAM,CAAC,CAAC,GAAGT,MAAM,GAAG,CAAC,CAAC,CAClC6E,MAAM,CAAC,CAAC,EAAE;UAAE0C,IAAI,EAAE;QAAY,CAAC,CAAC;QACnC,IAAI,CAAC/I,KAAK,CAACmH,cAAc,CAAChB,KAAK,EAAE1C,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;QACpD,IAAI,CAACrH,KAAK,CAACqI,YAAY,CAACxE,KAAK,CAACK,KAAK,GAAG,CAAC,EAAET,cAAK,CAAC2D,OAAO,CAACkB,MAAM,CAAC;QAC9D,IAAI,CAACtI,KAAK,CAACyJ,uBAAuB,CAAC,CAAC;MACtC;IACF,CAAC;IACD,cAAc,EAAE;MACd9I,GAAG,EAAE,OAAO;MACZO,SAAS,EAAE,IAAI;MACfmE,MAAM,EAAE,CAAC,QAAQ,CAAC;MAClB/D,MAAM,EAAE,IAAI;MACZa,OAAO,WAAPA,OAAOA,CAAC0B,KAAK,EAAE9B,OAAO,EAAE;QACtB,IAAA2H,oBAAA,GAAuB,IAAI,CAAC1J,KAAK,CAACiE,OAAO,CAACJ,KAAK,CAACK,KAAK,CAAC;UAAAyF,qBAAA,OAAAvF,eAAA,CAAAtE,OAAA,EAAA4J,oBAAA;UAA/CrF,IAAI,GAAAsF,qBAAA;UAAEnI,MAAM,GAAAmI,qBAAA;QACnB,IAAMxD,KAAK,GAAG,IAAIC,mBAAK,CAAC,CAAC,CACtBC,MAAM,CAACxC,KAAK,CAACK,KAAK,CAAC,CACnBkE,MAAM,CAAC,IAAI,EAAErG,OAAO,CAACsD,MAAM;QAC5B;QAAA,CACCgB,MAAM,CAAChC,IAAI,CAACpC,MAAM,CAAC,CAAC,GAAGT,MAAM,GAAG,CAAC,CAAC,CAClC6E,MAAM,CAAC,CAAC,EAAE;UAAEuD,MAAM,EAAE;QAAK,CAAC,CAAC;QAC9B,IAAI,CAAC5J,KAAK,CAACmH,cAAc,CAAChB,KAAK,EAAE1C,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;QACpD,IAAI,CAACrH,KAAK,CAACqI,YAAY,CAACxE,KAAK,CAACK,KAAK,GAAG,CAAC,EAAET,cAAK,CAAC2D,OAAO,CAACkB,MAAM,CAAC;QAC9D,IAAI,CAACtI,KAAK,CAACyJ,uBAAuB,CAAC,CAAC;MACtC;IACF,CAAC;IACD,iBAAiB,EAAE;MACjB9I,GAAG,EAAE,WAAW;MAChB0E,MAAM,EAAE,CAAC,OAAO,CAAC;MACjBnE,SAAS,EAAE,IAAI;MACfM,MAAM,EAAE,CAAC;MACTW,OAAO,WAAPA,OAAOA,CAAA,EAAG,CAAC;IACb,CAAC;IACD,cAAc,EAAE;MACdxB,GAAG,EAAE,QAAQ;MACb0E,MAAM,EAAE,CAAC,OAAO,CAAC;MACjBnE,SAAS,EAAE,IAAI;MACfI,MAAM,EAAE,IAAI;MACZa,OAAO,WAAPA,OAAOA,CAAA,EAAG,CAAC;IACb,CAAC;IACD,aAAa,EAAE;MACbxB,GAAG,EAAE,OAAO;MACZC,QAAQ,EAAE,IAAI;MACdyE,MAAM,EAAE,CAAC,OAAO,CAAC;MACjBlD,OAAO,WAAPA,OAAOA,CAAC0B,KAAK,EAAE;QACb,IAAMgG,MAAM,GAAG,IAAI,CAAC7J,KAAK,CAAC8J,SAAS,CAAC,OAAO,CAAC;QAC5C,IAAID,MAAM,EAAE;UACV;UACA,IAAAE,gBAAA,GAAmCF,MAAM,CAACG,QAAQ,CAACnG,KAAK,CAAC;YAAAoG,iBAAA,OAAA7F,eAAA,CAAAtE,OAAA,EAAAiK,gBAAA;YAAlDZ,KAAK,GAAAc,iBAAA;YAAEC,GAAG,GAAAD,iBAAA;YAAEE,IAAI,GAAAF,iBAAA;YAAEzI,MAAM,GAAAyI,iBAAA;UAC/B,IAAMG,KAAK,GAAGC,SAAS,CAAClB,KAAK,EAAEe,GAAG,EAAEC,IAAI,EAAE3I,MAAM,CAAC;UACjD,IAAI4I,KAAK,IAAI,IAAI,EAAE;UACnB,IAAIlG,KAAK,GAAGiF,KAAK,CAAC3H,MAAM,CAAC,CAAC;UAC1B,IAAI4I,KAAK,GAAG,CAAC,EAAE;YACb,IAAMjE,KAAK,GAAG,IAAIC,mBAAK,CAAC,CAAC,CAACC,MAAM,CAACnC,KAAK,CAAC,CAACkE,MAAM,CAAC,IAAI,CAAC;YACpD,IAAI,CAACpI,KAAK,CAACmH,cAAc,CAAChB,KAAK,EAAE1C,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;YACpD,IAAI,CAACrH,KAAK,CAACqI,YAAY,CACrBxE,KAAK,CAACK,KAAK,GAAG,CAAC,EACfL,KAAK,CAAC5B,MAAM,EACZwB,cAAK,CAAC2D,OAAO,CAACkB,MAChB,CAAC;UACH,CAAC,MAAM,IAAI8B,KAAK,GAAG,CAAC,EAAE;YACpBlG,KAAK,IAAIiF,KAAK,CAAClH,MAAM,CAAC,CAAC;YACvB,IAAMkE,MAAK,GAAG,IAAIC,mBAAK,CAAC,CAAC,CAACC,MAAM,CAACnC,KAAK,CAAC,CAACkE,MAAM,CAAC,IAAI,CAAC;YACpD,IAAI,CAACpI,KAAK,CAACmH,cAAc,CAAChB,MAAK,EAAE1C,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;YACpD,IAAI,CAACrH,KAAK,CAACqI,YAAY,CAACnE,KAAK,EAAET,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;UACpD;QACF;MACF;IACF,CAAC;IACD,WAAW,EAAE;MACX1G,GAAG,EAAE,KAAK;MACVC,QAAQ,EAAE,IAAI;MACdyE,MAAM,EAAE,CAAC,OAAO,CAAC;MACjBlD,OAAO,WAAPA,OAAOA,CAAC0B,KAAK,EAAE9B,OAAO,EAAE;QACtB,IAAQwD,KAAK,GAAiBxD,OAAO,CAA7BwD,KAAK;UAAQ4E,IAAA,GAASpI,OAAO,CAAtBsC,IAAI;QACnB,IAAM7C,MAAM,GAAG2I,IAAI,CAAC3I,MAAM,CAAC,IAAI,CAACxB,KAAK,CAAC4D,MAAM,CAAC;QAC7C,IAAI2B,KAAK,CAAC3E,QAAQ,EAAE;UAClB,IAAI,CAACZ,KAAK,CAACqI,YAAY,CAAC7G,MAAM,GAAG,CAAC,EAAEiC,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;QACzD,CAAC,MAAM;UACL,IAAI,CAACrH,KAAK,CAACqI,YAAY,CAAC7G,MAAM,GAAG2I,IAAI,CAAClI,MAAM,CAAC,CAAC,EAAEwB,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;QACrE;MACF;IACF,CAAC;IACD,eAAe,EAAE;MACf1G,GAAG,EAAE,GAAG;MACRC,QAAQ,EAAE,IAAI;MACdM,SAAS,EAAE,IAAI;MACfmE,MAAM,EAAE;QACN,YAAY,EAAE,KAAK;QACnBiF,UAAU,EAAE,KAAK;QACjBnB,KAAK,EAAE;MACT,CAAC;MACD9H,MAAM,EAAE,iCAAiC;MACzCc,OAAO,WAAPA,OAAOA,CAAC0B,KAAK,EAAE9B,OAAO,EAAE;QACtB,IAAI,IAAI,CAAC/B,KAAK,CAAC4D,MAAM,CAACqE,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,OAAO,IAAI;QACxD,IAAQhG,MAAA,GAAWF,OAAO,CAACV,MAAM,CAAzBY,MAAA;QACR,IAAAsI,qBAAA,GAAuB,IAAI,CAACvK,KAAK,CAACiE,OAAO,CAACJ,KAAK,CAACK,KAAK,CAAC;UAAAsG,qBAAA,OAAApG,eAAA,CAAAtE,OAAA,EAAAyK,qBAAA;UAA/ClG,IAAI,GAAAmG,qBAAA;UAAEhJ,MAAM,GAAAgJ,qBAAA;QACnB,IAAIhJ,MAAM,GAAGS,MAAM,EAAE,OAAO,IAAI;QAChC,IAAIL,KAAK;QACT,QAAQG,OAAO,CAACV,MAAM,CAACoJ,IAAI,CAAC,CAAC;UAC3B,KAAK,IAAI;UACT,KAAK,KAAK;YACR7I,KAAK,GAAG,WAAW;YACnB;UACF,KAAK,KAAK;YACRA,KAAK,GAAG,SAAS;YACjB;UACF,KAAK,GAAG;UACR,KAAK,GAAG;YACNA,KAAK,GAAG,QAAQ;YAChB;UACF;YACEA,KAAK,GAAG,SAAS;QACrB;QACA,IAAI,CAAC5B,KAAK,CAAC0K,UAAU,CAAC7G,KAAK,CAACK,KAAK,EAAE,GAAG,EAAET,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;QAC3D,IAAI,CAACrH,KAAK,CAACoJ,OAAO,CAACC,MAAM,CAAC,CAAC;QAC3B,IAAMlD,KAAK,GAAG,IAAIC,mBAAK,CAAC,CAAC,CACtBC,MAAM,CAACxC,KAAK,CAACK,KAAK,GAAG1C,MAAM,CAAC,CAC5B8E,MAAM,CAACrE,MAAM,GAAG,CAAC;QAClB;QAAA,CACCoE,MAAM,CAAChC,IAAI,CAACpC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAGT,MAAM,CAAC,CAClC6E,MAAM,CAAC,CAAC,EAAE;UAAE0C,IAAI,EAAEnH;QAAM,CAAC,CAAC;QAC7B,IAAI,CAAC5B,KAAK,CAACmH,cAAc,CAAChB,KAAK,EAAE1C,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;QACpD,IAAI,CAACrH,KAAK,CAACoJ,OAAO,CAACC,MAAM,CAAC,CAAC;QAC3B,IAAI,CAACrJ,KAAK,CAACqI,YAAY,CAACxE,KAAK,CAACK,KAAK,GAAGjC,MAAM,EAAEwB,cAAK,CAAC2D,OAAO,CAACkB,MAAM,CAAC;QACnE,OAAO,KAAK;MACd;IACF,CAAC;IACD,WAAW,EAAE;MACX3H,GAAG,EAAE,OAAO;MACZO,SAAS,EAAE,IAAI;MACfmE,MAAM,EAAE,CAAC,YAAY,CAAC;MACtBhE,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,OAAO;MACfa,OAAO,WAAPA,OAAOA,CAAC0B,KAAK,EAAE;QACb,IAAA8G,qBAAA,GAAuB,IAAI,CAAC3K,KAAK,CAACiE,OAAO,CAACJ,KAAK,CAACK,KAAK,CAAC;UAAA0G,qBAAA,OAAAxG,eAAA,CAAAtE,OAAA,EAAA6K,qBAAA;UAA/CtG,IAAI,GAAAuG,qBAAA;UAAEpJ,MAAM,GAAAoJ,qBAAA;QACnB,IAAIC,QAAQ,GAAG,CAAC;QAChB,IAAIC,GAAG,GAAGzG,IAAI;QACd,OACEyG,GAAG,IAAI,IAAI,IACXA,GAAG,CAAC7I,MAAM,CAAC,CAAC,IAAI,CAAC,IACjB6I,GAAG,CAAC9E,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,EAC3B;UACA;UACA8E,GAAG,GAAGA,GAAG,CAACrE,IAAI;UACdoE,QAAQ,IAAI,CAAC;UACb;UACA,IAAIA,QAAQ,IAAI,CAAC,EAAE;YACjB,IAAM1E,KAAK,GAAG,IAAIC,mBAAK,CAAC;YACtB;YAAA,CACCC,MAAM,CAACxC,KAAK,CAACK,KAAK,GAAGG,IAAI,CAACpC,MAAM,CAAC,CAAC,GAAGT,MAAM,GAAG,CAAC,CAAC,CAChD6E,MAAM,CAAC,CAAC,EAAE;cAAE,YAAY,EAAE;YAAK,CAAC,CAAC,CACjCC,MAAM,CAAC,CAAC,CAAC;YACZ,IAAI,CAACtG,KAAK,CAACmH,cAAc,CAAChB,KAAK,EAAE1C,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;YACpD,IAAI,CAACrH,KAAK,CAACqI,YAAY,CAACxE,KAAK,CAACK,KAAK,GAAG,CAAC,EAAET,cAAK,CAAC2D,OAAO,CAACkB,MAAM,CAAC;YAC9D,OAAO,KAAK;UACd;QACF;QACA,OAAO,IAAI;MACb;IACF,CAAC;IACD,YAAY,EAAEyC,qBAAqB,CAAC,WAAW,EAAE,KAAK,CAAC;IACvD,kBAAkB,EAAEA,qBAAqB,CAAC,WAAW,EAAE,IAAI,CAAC;IAC5D,aAAa,EAAEA,qBAAqB,CAAC,YAAY,EAAE,KAAK,CAAC;IACzD,mBAAmB,EAAEA,qBAAqB,CAAC,YAAY,EAAE,IAAI,CAAC;IAC9D,YAAY,EAAEC,qBAAqB,CAAC,KAAK,CAAC;IAC1C,UAAU,EAAEA,qBAAqB,CAAC,IAAI;EACxC;AACF,CAAC;AAEDnL,QAAQ,CAACoL,QAAQ,GAAGzC,cAAc;AAElC,SAASQ,oBAAoBA,CAACH,MAAe,EAAiB;EAC5D,OAAO;IACLlI,GAAG,EAAE,KAAK;IACVC,QAAQ,EAAE,CAACiI,MAAM;IACjBxD,MAAM,EAAE;MAAE,YAAY,EAAE;IAAK,CAAC;IAC9BlD,OAAO,WAAPA,OAAOA,CAAC0B,KAAK,EAAAqH,IAAA,EAAa;MAAA,IAAT3F,KAAA,GAAO2F,IAAA,CAAP3F,KAAA;MACf,IAAM4F,SAAS,GAAG,IAAI,CAACnL,KAAK,CAAC4D,MAAM,CAACqE,KAAK,CAAC,YAAY,CAAC;MACvD;MACA,IAAQmD,GAAA,GAAQD,SAAS,CAAjBC,GAAA;MACR,IAAIvH,KAAK,CAAC5B,MAAM,KAAK,CAAC,IAAI,CAACsD,KAAK,CAAC3E,QAAQ,EAAE;QACzC,IAAI,CAACZ,KAAK,CAAC0K,UAAU,CAAC7G,KAAK,CAACK,KAAK,EAAEkH,GAAG,EAAE3H,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;QAC3D,IAAI,CAACrH,KAAK,CAACqI,YAAY,CAACxE,KAAK,CAACK,KAAK,GAAGkH,GAAG,CAACnJ,MAAM,EAAEwB,cAAK,CAAC2D,OAAO,CAACkB,MAAM,CAAC;QACvE;MACF;MAEA,IAAM+C,KAAK,GACTxH,KAAK,CAAC5B,MAAM,KAAK,CAAC,GACd,IAAI,CAACjC,KAAK,CAACsL,QAAQ,CAACzH,KAAK,CAACK,KAAK,EAAE,CAAC,CAAC,GACnC,IAAI,CAAClE,KAAK,CAACsL,QAAQ,CAACzH,KAAK,CAAC;MAChC,IAAMK,KAAK,GAAaL,KAAK,CAAvBK,KAAK;QAAEjC,MAAA,GAAW4B,KAAK,CAAhB5B,MAAA;MACboJ,KAAK,CAAC7K,OAAO,CAAC,UAAC6D,IAAI,EAAEkH,CAAC,EAAK;QACzB,IAAI1C,MAAM,EAAE;UACVxE,IAAI,CAACmH,QAAQ,CAAC,CAAC,EAAEJ,GAAG,CAAC;UACrB,IAAIG,CAAC,KAAK,CAAC,EAAE;YACXrH,KAAK,IAAIkH,GAAG,CAACnJ,MAAM;UACrB,CAAC,MAAM;YACLA,MAAM,IAAImJ,GAAG,CAACnJ,MAAM;UACtB;UACA;QACF,CAAC,MAAM,IAAIoC,IAAI,CAACoH,OAAO,CAACC,WAAW,CAACC,UAAU,CAACP,GAAG,CAAC,EAAE;UACnD/G,IAAI,CAACuH,QAAQ,CAAC,CAAC,EAAER,GAAG,CAACnJ,MAAM,CAAC;UAC5B,IAAIsJ,CAAC,KAAK,CAAC,EAAE;YACXrH,KAAK,IAAIkH,GAAG,CAACnJ,MAAM;UACrB,CAAC,MAAM;YACLA,MAAM,IAAImJ,GAAG,CAACnJ,MAAM;UACtB;QACF;MACF,CAAC,CAAC;MACF,IAAI,CAACjC,KAAK,CAAC6L,MAAM,CAACpI,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;MACrC,IAAI,CAACrH,KAAK,CAACqI,YAAY,CAACnE,KAAK,EAAEjC,MAAM,EAAEwB,cAAK,CAAC2D,OAAO,CAACkB,MAAM,CAAC;IAC9D;EACF,CAAC;AACH;AAEA,SAASyC,qBAAqBA,CAC5BpK,GAAW,EACXC,QAAwB,EACT;EACf,IAAMkL,KAAK,GAAGnL,GAAG,KAAK,WAAW,GAAG,QAAQ,GAAG,QAAQ;EACvD,WAAAoL,gBAAA,CAAAjM,OAAA,MAAAiM,gBAAA,CAAAjM,OAAA;IACEa,GAAG,EAAHA,GAAG;IACHC,QAAQ,EAARA,QAAQ;IACRI,MAAM,EAAE;EAAI,GACX8K,KAAK,EAAG,IAAI,uBACb3J,OAAOA,CAAC0B,KAAK,EAAE;IACb,IAAMK,KAAA,GAAUL,KAAK,CAAfK,KAAA;IACN,IAAIvD,GAAG,KAAK,YAAY,EAAE;MACxBuD,KAAK,IAAIL,KAAK,CAAC5B,MAAM,GAAG,CAAC;IAC3B;IACA,IAAA+J,mBAAA,GAAe,IAAI,CAAChM,KAAK,CAACuE,OAAO,CAACL,KAAK,CAAC;MAAA+H,oBAAA,OAAA7H,eAAA,CAAAtE,OAAA,EAAAkM,mBAAA;MAAjCE,IAAI,GAAAD,oBAAA;IACX,IAAI,EAAEC,IAAI,YAAYC,oBAAS,CAAC,EAAE,OAAO,IAAI;IAC7C,IAAIxL,GAAG,KAAK,WAAW,EAAE;MACvB,IAAIC,QAAQ,EAAE;QACZ,IAAI,CAACZ,KAAK,CAACqI,YAAY,CACrBxE,KAAK,CAACK,KAAK,GAAG,CAAC,EACfL,KAAK,CAAC5B,MAAM,GAAG,CAAC,EAChBwB,cAAK,CAAC2D,OAAO,CAACC,IAChB,CAAC;MACH,CAAC,MAAM;QACL,IAAI,CAACrH,KAAK,CAACqI,YAAY,CAACxE,KAAK,CAACK,KAAK,GAAG,CAAC,EAAET,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;MAC9D;IACF,CAAC,MAAM,IAAIzG,QAAQ,EAAE;MACnB,IAAI,CAACZ,KAAK,CAACqI,YAAY,CACrBxE,KAAK,CAACK,KAAK,EACXL,KAAK,CAAC5B,MAAM,GAAG,CAAC,EAChBwB,cAAK,CAAC2D,OAAO,CAACC,IAChB,CAAC;IACH,CAAC,MAAM;MACL,IAAI,CAACrH,KAAK,CAACqI,YAAY,CACrBxE,KAAK,CAACK,KAAK,GAAGL,KAAK,CAAC5B,MAAM,GAAG,CAAC,EAC9BwB,cAAK,CAAC2D,OAAO,CAACC,IAChB,CAAC;IACH;IACA,OAAO,KAAK;EACd;AAEJ;AAEA,SAASqB,iBAAiBA,CAACrD,MAAc,EAAiB;EACxD,OAAO;IACL1E,GAAG,EAAE0E,MAAM,CAAC,CAAC,CAAC;IACd+G,QAAQ,EAAE,IAAI;IACdjK,OAAO,WAAPA,OAAOA,CAAC0B,KAAK,EAAE9B,OAAO,EAAE;MACtB,IAAI,CAAC/B,KAAK,CAACqF,MAAM,CAACA,MAAM,EAAE,CAACtD,OAAO,CAACsD,MAAM,CAACA,MAAM,CAAC,EAAE5B,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;IACxE;EACF,CAAC;AACH;AAEA,SAAS2D,qBAAqBA,CAACqB,EAAW,EAAiB;EACzD,OAAO;IACL1L,GAAG,EAAE0L,EAAE,GAAG,SAAS,GAAG,WAAW;IACjCnL,SAAS,EAAE,IAAI;IACfmE,MAAM,EAAE,CAAC,OAAO,CAAC;IACjBlD,OAAO,WAAPA,OAAOA,CAAC0B,KAAK,EAAE9B,OAAO,EAAE;MACtB;MACA,IAAMpB,GAAG,GAAG0L,EAAE,GAAG,MAAM,GAAG,MAAM;MAChC,IAAMlC,IAAI,GAAGpI,OAAO,CAACsC,IAAI;MACzB,IAAMiI,SAAS,GAAGnC,IAAI,CAACoC,MAAM,CAAC5L,GAAG,CAAC;MAClC,IAAI2L,SAAS,IAAI,IAAI,EAAE;QACrB,IAAIA,SAAS,CAAC3F,OAAO,CAACC,QAAQ,KAAK,WAAW,EAAE;UAC9C;UACA,IAAI4F,UAAU,GAAGF,SAAS,CAACG,QAAQ,CAACC,IAAI;UACxC,IAAI5B,GAAG,GAAGX,IAAI;UACd,OAAOW,GAAG,CAACrE,IAAI,IAAI,IAAI,EAAE;YACvB;YACAqE,GAAG,GAAGA,GAAG,CAACrE,IAAI;YACd+F,UAAU,GAAGA,UAAU,CAAC7E,IAAI;UAC9B;UACA,IAAMzD,KAAK,GACTsI,UAAU,CAAChL,MAAM,CAAC,IAAI,CAACxB,KAAK,CAAC4D,MAAM,CAAC,GACpC+I,IAAI,CAACC,GAAG,CAAC7K,OAAO,CAACP,MAAM,EAAEgL,UAAU,CAACvK,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;UACnD,IAAI,CAACjC,KAAK,CAACqI,YAAY,CAACnE,KAAK,EAAE,CAAC,EAAET,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;QACvD;MACF,CAAC,MAAM;QACL;QACA,IAAMwF,UAAU,GAAG1C,IAAI,CAAChB,KAAK,CAAC,CAAC,CAACxI,GAAG,CAAC;QACpC,IAAIkM,UAAU,IAAI,IAAI,EAAE;UACtB,IAAIR,EAAE,EAAE;YACN,IAAI,CAACrM,KAAK,CAACqI,YAAY,CACrBwE,UAAU,CAACrL,MAAM,CAAC,IAAI,CAACxB,KAAK,CAAC4D,MAAM,CAAC,GAAGiJ,UAAU,CAAC5K,MAAM,CAAC,CAAC,GAAG,CAAC,EAC9D,CAAC,EACDwB,cAAK,CAAC2D,OAAO,CAACC,IAChB,CAAC;UACH,CAAC,MAAM;YACL,IAAI,CAACrH,KAAK,CAACqI,YAAY,CACrBwE,UAAU,CAACrL,MAAM,CAAC,IAAI,CAACxB,KAAK,CAAC4D,MAAM,CAAC,EACpC,CAAC,EACDH,cAAK,CAAC2D,OAAO,CAACC,IAChB,CAAC;UACH;QACF;MACF;MACA,OAAO,KAAK;IACd;EACF,CAAC;AACH;AAEA,SAAShF,SAASA,CAACD,OAAgB,EAAwB;EACzD,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;IAC9DA,OAAO,GAAG;MAAEzB,GAAG,EAAEyB;IAAQ,CAAC;EAC5B,CAAC,MAAM,IAAI,IAAAuD,QAAA,CAAA7F,OAAA,EAAOsC,OAAO,MAAK,QAAQ,EAAE;IACtCA,OAAO,GAAG,IAAA0K,mBAAS,EAAC1K,OAAO,CAAC;EAC9B,CAAC,MAAM;IACL,OAAO,IAAI;EACb;EACA,IAAIA,OAAO,CAACgK,QAAQ,EAAE;IACpBhK,OAAO,CAAC5C,QAAQ,CAAC,GAAG4C,OAAO,CAACgK,QAAQ;IACpC,OAAOhK,OAAO,CAACgK,QAAQ;EACzB;EACA,OAAOhK,OAAO;AAChB;;AAEA;AACA,SAASyF,WAAWA,CAAAkF,KAAA,EAAmD;EAAA,IAAhD/M,KAAK,GAAyC+M,KAAA,CAA9C/M,KAAK;IAAE6D,KAAA,GAAuCkJ,KAAA,CAAvClJ,KAAA;EAC5B,IAAMwH,KAAK,GAAGrL,KAAK,CAACsL,QAAQ,CAACzH,KAAK,CAAC;EACnC,IAAImC,OAAO,GAAG,CAAC,CAAC;EAChB,IAAIqF,KAAK,CAACpJ,MAAM,GAAG,CAAC,EAAE;IACpB,IAAM+K,YAAY,GAAG3B,KAAK,CAAC,CAAC,CAAC,CAACrF,OAAO,CAAC,CAAC;IACvC,IAAMiH,WAAW,GAAG5B,KAAK,CAACA,KAAK,CAACpJ,MAAM,GAAG,CAAC,CAAC,CAAC+D,OAAO,CAAC,CAAC;IACrDA,OAAO,GAAGe,wBAAY,CAACC,IAAI,CAACiG,WAAW,EAAED,YAAY,CAAC,IAAI,CAAC,CAAC;EAC9D;EACAhN,KAAK,CAACiJ,UAAU,CAACpF,KAAK,EAAEJ,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;EAC3C,IAAI/G,MAAM,CAACC,IAAI,CAACyF,OAAO,CAAC,CAAC/D,MAAM,GAAG,CAAC,EAAE;IACnCjC,KAAK,CAACsJ,UAAU,CAACzF,KAAK,CAACK,KAAK,EAAE,CAAC,EAAE8B,OAAO,EAAEvC,cAAK,CAAC2D,OAAO,CAACC,IAAI,CAAC;EAC/D;EACArH,KAAK,CAACqI,YAAY,CAACxE,KAAK,CAACK,KAAK,EAAET,cAAK,CAAC2D,OAAO,CAACkB,MAAM,CAAC;AACvD;AAEA,SAAS+B,SAASA,CAAC6C,MAAe,EAAEhD,GAAS,EAAEC,IAAU,EAAE3I,MAAc,EAAE;EACzE,IAAI0I,GAAG,CAACzD,IAAI,IAAI,IAAI,IAAIyD,GAAG,CAACvC,IAAI,IAAI,IAAI,EAAE;IACxC,IAAIwC,IAAI,CAAC1D,IAAI,IAAI,IAAI,IAAI0D,IAAI,CAACxC,IAAI,IAAI,IAAI,EAAE;MAC1C,OAAOnG,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IAC9B;IACA,OAAO2I,IAAI,CAAC1D,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC;EACnC;EACA,IAAIyD,GAAG,CAACzD,IAAI,IAAI,IAAI,EAAE;IACpB,OAAO,CAAC,CAAC;EACX;EACA,IAAIyD,GAAG,CAACvC,IAAI,IAAI,IAAI,EAAE;IACpB,OAAO,CAAC;EACV;EACA,OAAO,IAAI;AACb", "ignoreList": []}]}