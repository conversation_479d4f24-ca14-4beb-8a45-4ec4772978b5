{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/formats/image.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/formats/image.js", "mtime": 1753510684093}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_parchment", "require", "_link", "ATTRIBUTES", "Image", "_EmbedBlot", "_classCallCheck2", "default", "_callSuper2", "arguments", "_inherits2", "_createClass2", "key", "value", "format", "name", "indexOf", "domNode", "setAttribute", "removeAttribute", "_superPropGet2", "create", "node", "sanitize", "formats", "reduce", "attribute", "hasAttribute", "getAttribute", "match", "url", "test", "EmbedBlot", "_defineProperty2", "_default", "exports"], "sources": ["../../src/formats/image.ts"], "sourcesContent": ["import { EmbedBlot } from 'parchment';\nimport { sanitize } from './link.js';\n\nconst ATTRIBUTES = ['alt', 'height', 'width'];\n\nclass Image extends EmbedBlot {\n  static blotName = 'image';\n  static tagName = 'IMG';\n\n  static create(value: string) {\n    const node = super.create(value) as Element;\n    if (typeof value === 'string') {\n      node.setAttribute('src', this.sanitize(value));\n    }\n    return node;\n  }\n\n  static formats(domNode: Element) {\n    return ATTRIBUTES.reduce(\n      (formats: Record<string, string | null>, attribute) => {\n        if (domNode.hasAttribute(attribute)) {\n          formats[attribute] = domNode.getAttribute(attribute);\n        }\n        return formats;\n      },\n      {},\n    );\n  }\n\n  static match(url: string) {\n    return /\\.(jpe?g|gif|png)$/.test(url) || /^data:image\\/.+;base64/.test(url);\n  }\n\n  static sanitize(url: string) {\n    return sanitize(url, ['http', 'https', 'data']) ? url : '//:0';\n  }\n\n  static value(domNode: Element) {\n    return domNode.getAttribute('src');\n  }\n\n  domNode: HTMLImageElement;\n\n  format(name: string, value: string) {\n    if (ATTRIBUTES.indexOf(name) > -1) {\n      if (value) {\n        this.domNode.setAttribute(name, value);\n      } else {\n        this.domNode.removeAttribute(name);\n      }\n    } else {\n      super.format(name, value);\n    }\n  }\n}\n\nexport default Image;\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAEA,IAAME,UAAU,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC;AAAA,IAEvCC,KAAK,0BAAAC,UAAA;EAAA,SAAAD,MAAA;IAAA,IAAAE,gBAAA,CAAAC,OAAA,QAAAH,KAAA;IAAA,WAAAI,WAAA,CAAAD,OAAA,QAAAH,KAAA,EAAAK,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAH,KAAA,EAAAC,UAAA;EAAA,WAAAM,aAAA,CAAAJ,OAAA,EAAAH,KAAA;IAAAQ,GAAA;IAAAC,KAAA,EAsCT,SAAAC,MAAMA,CAACC,IAAY,EAAEF,KAAa,EAAE;MAClC,IAAIV,UAAU,CAACa,OAAO,CAACD,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;QACjC,IAAIF,KAAK,EAAE;UACT,IAAI,CAACI,OAAO,CAACC,YAAY,CAACH,IAAI,EAAEF,KAAK,CAAC;QACxC,CAAC,MAAM;UACL,IAAI,CAACI,OAAO,CAACE,eAAe,CAACJ,IAAI,CAAC;QACpC;MACF,CAAC,MAAM;QACL,IAAAK,cAAA,CAAAb,OAAA,EAAAH,KAAA,sBAAaW,IAAI,EAAEF,KAAK;MAC1B;IACF;EAAA;IAAAD,GAAA;IAAAC,KAAA,EA5CA,SAAOQ,MAAMA,CAACR,KAAa,EAAE;MAC3B,IAAMS,IAAI,OAAAF,cAAA,CAAAb,OAAA,EAAAH,KAAA,sBAAgBS,KAAK,EAAY;MAC3C,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7BS,IAAI,CAACJ,YAAY,CAAC,KAAK,EAAE,IAAI,CAACK,QAAQ,CAACV,KAAK,CAAC,CAAC;MAChD;MACA,OAAOS,IAAI;IACb;EAAA;IAAAV,GAAA;IAAAC,KAAA,EAEA,SAAOW,OAAOA,CAACP,OAAgB,EAAE;MAC/B,OAAOd,UAAU,CAACsB,MAAM,CACtB,UAACD,OAAsC,EAAEE,SAAS,EAAK;QACrD,IAAIT,OAAO,CAACU,YAAY,CAACD,SAAS,CAAC,EAAE;UACnCF,OAAO,CAACE,SAAS,CAAC,GAAGT,OAAO,CAACW,YAAY,CAACF,SAAS,CAAC;QACtD;QACA,OAAOF,OAAO;MAChB,CAAC,EACD,CAAC,CACH,CAAC;IACH;EAAA;IAAAZ,GAAA;IAAAC,KAAA,EAEA,SAAOgB,KAAKA,CAACC,GAAW,EAAE;MACxB,OAAO,oBAAoB,CAACC,IAAI,CAACD,GAAG,CAAC,IAAI,wBAAwB,CAACC,IAAI,CAACD,GAAG,CAAC;IAC7E;EAAA;IAAAlB,GAAA;IAAAC,KAAA,EAEA,SAAOU,QAAQA,CAACO,GAAW,EAAE;MAC3B,OAAO,IAAAP,cAAQ,EAACO,GAAG,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,GAAGA,GAAG,GAAG,MAAM;IAChE;EAAA;IAAAlB,GAAA;IAAAC,KAAA,EAEA,SAAOA,KAAKA,CAACI,OAAgB,EAAE;MAC7B,OAAOA,OAAO,CAACW,YAAY,CAAC,KAAK,CAAC;IACpC;EAAA;AAAA,EAlCkBI,oBAAS;AAAA,IAAAC,gBAAA,CAAA1B,OAAA,EAAvBH,KAAK,cACS,OAAO;AAAA,IAAA6B,gBAAA,CAAA1B,OAAA,EADrBH,KAAK,aAEQ,KAAK;AAAA,IAAA8B,QAAA,GAAAC,OAAA,CAAA5B,OAAA,GAiDTH,KAAK", "ignoreList": []}]}