{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/register.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/register.vue", "mtime": 1753510684535}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_login", "require", "name", "data", "_this", "equalToPassword", "rule", "value", "callback", "registerForm", "password", "Error", "title", "process", "env", "VUE_APP_TITLE", "codeUrl", "username", "confirmPassword", "code", "uuid", "registerRules", "required", "trigger", "message", "min", "max", "pattern", "validator", "loading", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "created", "getCode", "methods", "_this2", "getCodeImg", "then", "res", "undefined", "img", "handleRegister", "_this3", "$refs", "validate", "valid", "register", "$alert", "dangerouslyUseHTMLString", "type", "$router", "push", "catch"], "sources": ["src/views/register.vue"], "sourcesContent": ["<template>\n  <div class=\"register\">\n    <el-form ref=\"registerForm\" :model=\"registerForm\" :rules=\"registerRules\" class=\"register-form\">\n      <h3 class=\"title\">{{title}}</h3>\n      <el-form-item prop=\"username\">\n        <el-input v-model=\"registerForm.username\" type=\"text\" auto-complete=\"off\" placeholder=\"账号\">\n          <svg-icon slot=\"prefix\" icon-class=\"user\" class=\"el-input__icon input-icon\" />\n        </el-input>\n      </el-form-item>\n      <el-form-item prop=\"password\">\n        <el-input\n          v-model=\"registerForm.password\"\n          type=\"password\"\n          auto-complete=\"off\"\n          placeholder=\"密码\"\n          @keyup.enter.native=\"handleRegister\"\n        >\n          <svg-icon slot=\"prefix\" icon-class=\"password\" class=\"el-input__icon input-icon\" />\n        </el-input>\n      </el-form-item>\n      <el-form-item prop=\"confirmPassword\">\n        <el-input\n          v-model=\"registerForm.confirmPassword\"\n          type=\"password\"\n          auto-complete=\"off\"\n          placeholder=\"确认密码\"\n          @keyup.enter.native=\"handleRegister\"\n        >\n          <svg-icon slot=\"prefix\" icon-class=\"password\" class=\"el-input__icon input-icon\" />\n        </el-input>\n      </el-form-item>\n      <el-form-item prop=\"code\" v-if=\"captchaEnabled\">\n        <el-input\n          v-model=\"registerForm.code\"\n          auto-complete=\"off\"\n          placeholder=\"验证码\"\n          style=\"width: 63%\"\n          @keyup.enter.native=\"handleRegister\"\n        >\n          <svg-icon slot=\"prefix\" icon-class=\"validCode\" class=\"el-input__icon input-icon\" />\n        </el-input>\n        <div class=\"register-code\">\n          <img :src=\"codeUrl\" @click=\"getCode\" class=\"register-code-img\"/>\n        </div>\n      </el-form-item>\n      <el-form-item style=\"width:100%;\">\n        <el-button\n          :loading=\"loading\"\n          size=\"medium\"\n          type=\"primary\"\n          style=\"width:100%;\"\n          @click.native.prevent=\"handleRegister\"\n        >\n          <span v-if=\"!loading\">注 册</span>\n          <span v-else>注 册 中...</span>\n        </el-button>\n        <div style=\"float: right;\">\n          <router-link class=\"link-type\" :to=\"'/login'\">使用已有账户登录</router-link>\n        </div>\n      </el-form-item>\n    </el-form>\n    <!--  底部  -->\n    <div class=\"el-register-footer\">\n      <span>Copyright © 2018-2025 ruoyi.vip All Rights Reserved.</span>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getCodeImg, register } from \"@/api/login\"\n\nexport default {\n  name: \"Register\",\n  data() {\n    const equalToPassword = (rule, value, callback) => {\n      if (this.registerForm.password !== value) {\n        callback(new Error(\"两次输入的密码不一致\"))\n      } else {\n        callback()\n      }\n    }\n    return {\n      title: process.env.VUE_APP_TITLE,\n      codeUrl: \"\",\n      registerForm: {\n        username: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        code: \"\",\n        uuid: \"\"\n      },\n      registerRules: {\n        username: [\n          { required: true, trigger: \"blur\", message: \"请输入您的账号\" },\n          { min: 2, max: 20, message: '用户账号长度必须介于 2 和 20 之间', trigger: 'blur' }\n        ],\n        password: [\n          { required: true, trigger: \"blur\", message: \"请输入您的密码\" },\n          { min: 5, max: 20, message: \"用户密码长度必须介于 5 和 20 之间\", trigger: \"blur\" },\n          { pattern: /^[^<>\"'|\\\\]+$/, message: \"不能包含非法字符：< > \\\" ' \\\\\\ |\", trigger: \"blur\" }\n        ],\n        confirmPassword: [\n          { required: true, trigger: \"blur\", message: \"请再次输入您的密码\" },\n          { required: true, validator: equalToPassword, trigger: \"blur\" }\n        ],\n        code: [{ required: true, trigger: \"change\", message: \"请输入验证码\" }]\n      },\n      loading: false,\n      captchaEnabled: true\n    }\n  },\n  created() {\n    this.getCode()\n  },\n  methods: {\n    getCode() {\n      getCodeImg().then(res => {\n        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled\n        if (this.captchaEnabled) {\n          this.codeUrl = \"data:image/jpeg;base64,\" + res.img\n          this.registerForm.uuid = res.uuid\n        }\n      })\n    },\n    handleRegister() {\n      this.$refs.registerForm.validate(valid => {\n        if (valid) {\n          this.loading = true\n          register(this.registerForm).then(res => {\n            const username = this.registerForm.username\n            this.$alert(\"<font color='red'>恭喜你，您的账号 \" + username + \" 注册成功！</font>\", '系统提示', {\n              dangerouslyUseHTMLString: true,\n              type: 'success'\n            }).then(() => {\n              this.$router.push(\"/login\")\n            }).catch(() => {})\n          }).catch(() => {\n            this.loading = false\n            if (this.captchaEnabled) {\n              this.getCode()\n            }\n          })\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style rel=\"stylesheet/scss\" lang=\"scss\">\n.register {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n  background-image: url(\"../assets/images/login-background.jpg\");\n  background-size: cover;\n}\n.title {\n  margin: 0px auto 30px auto;\n  text-align: center;\n  color: #707070;\n}\n\n.register-form {\n  border-radius: 6px;\n  background: #ffffff;\n  width: 400px;\n  padding: 25px 25px 5px 25px;\n  .el-input {\n    height: 38px;\n    input {\n      height: 38px;\n    }\n  }\n  .input-icon {\n    height: 39px;\n    width: 14px;\n    margin-left: 2px;\n  }\n}\n.register-tip {\n  font-size: 13px;\n  text-align: center;\n  color: #bfbfbf;\n}\n.register-code {\n  width: 33%;\n  height: 38px;\n  float: right;\n  img {\n    cursor: pointer;\n    vertical-align: middle;\n  }\n}\n.el-register-footer {\n  height: 40px;\n  line-height: 40px;\n  position: fixed;\n  bottom: 0;\n  width: 100%;\n  text-align: center;\n  color: #fff;\n  font-family: Arial;\n  font-size: 12px;\n  letter-spacing: 1px;\n}\n.register-code-img {\n  height: 38px;\n}\n</style>\n"], "mappings": ";;;;;;;;AAqEA,IAAAA,MAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA,IAAAC,eAAA,YAAAA,gBAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAJ,KAAA,CAAAK,YAAA,CAAAC,QAAA,KAAAH,KAAA;QACAC,QAAA,KAAAG,KAAA;MACA;QACAH,QAAA;MACA;IACA;IACA;MACAI,KAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,aAAA;MACAC,OAAA;MACAP,YAAA;QACAQ,QAAA;QACAP,QAAA;QACAQ,eAAA;QACAC,IAAA;QACAC,IAAA;MACA;MACAC,aAAA;QACAJ,QAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAF,OAAA;UAAAD,OAAA;QAAA,EACA;QACAb,QAAA,GACA;UAAAY,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAF,OAAA;UAAAD,OAAA;QAAA,GACA;UAAAI,OAAA;UAAAH,OAAA;UAAAD,OAAA;QAAA,EACA;QACAL,eAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,QAAA;UAAAM,SAAA,EAAAvB,eAAA;UAAAkB,OAAA;QAAA,EACA;QACAJ,IAAA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACAK,OAAA;MACAC,cAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,MAAA;MACA,IAAAC,iBAAA,IAAAC,IAAA,WAAAC,GAAA;QACAH,MAAA,CAAAJ,cAAA,GAAAO,GAAA,CAAAP,cAAA,KAAAQ,SAAA,UAAAD,GAAA,CAAAP,cAAA;QACA,IAAAI,MAAA,CAAAJ,cAAA;UACAI,MAAA,CAAAlB,OAAA,+BAAAqB,GAAA,CAAAE,GAAA;UACAL,MAAA,CAAAzB,YAAA,CAAAW,IAAA,GAAAiB,GAAA,CAAAjB,IAAA;QACA;MACA;IACA;IACAoB,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAAjC,YAAA,CAAAkC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,MAAA,CAAAZ,OAAA;UACA,IAAAgB,eAAA,EAAAJ,MAAA,CAAAhC,YAAA,EAAA2B,IAAA,WAAAC,GAAA;YACA,IAAApB,QAAA,GAAAwB,MAAA,CAAAhC,YAAA,CAAAQ,QAAA;YACAwB,MAAA,CAAAK,MAAA,iCAAA7B,QAAA;cACA8B,wBAAA;cACAC,IAAA;YACA,GAAAZ,IAAA;cACAK,MAAA,CAAAQ,OAAA,CAAAC,IAAA;YACA,GAAAC,KAAA;UACA,GAAAA,KAAA;YACAV,MAAA,CAAAZ,OAAA;YACA,IAAAY,MAAA,CAAAX,cAAA;cACAW,MAAA,CAAAT,OAAA;YACA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}