{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/blots/scroll.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/blots/scroll.js", "mtime": 1753510684086}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_parchment", "require", "_quill<PERSON><PERSON><PERSON>", "_interopRequireWildcard", "_emitter", "_interopRequireDefault", "_block", "_break", "_container", "isLine", "blot", "Block", "BlockEmbed", "isUpdatable", "updateContent", "<PERSON><PERSON>", "_ScrollBlot", "registry", "domNode", "_ref", "_this", "_classCallCheck2", "default", "emitter", "_callSuper2", "batch", "optimize", "enable", "addEventListener", "e", "handleDragStart", "_inherits2", "_createClass2", "key", "value", "batchStart", "Array", "isArray", "batchEnd", "mutations", "update", "emitMount", "emit", "Emitter", "events", "SCROLL_BLOT_MOUNT", "emitUnmount", "SCROLL_BLOT_UNMOUNT", "emitEmbedUpdate", "change", "SCROLL_EMBED_UPDATE", "deleteAt", "index", "length", "_this$line", "line", "_this$line2", "_slicedToArray2", "first", "offset", "_this$line3", "_this$line4", "last", "_superPropGet2", "ref", "children", "head", "Break", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "remove", "enabled", "arguments", "undefined", "setAttribute", "formatAt", "format", "insertAt", "def", "scroll", "query", "<PERSON><PERSON>", "BLOCK", "create", "statics", "defaultChild", "blotName", "append<PERSON><PERSON><PERSON>", "endsWith", "slice", "embed", "insertBefore", "scope", "INLINE_BLOT", "wrapper", "insertContents", "delta", "_this2", "renderBlocks", "deltaToRenderBlocks", "concat", "Delta", "insert", "pop", "shift", "shouldInsertNewlineChar", "type", "descendant", "_defineProperty2", "insertInlineContents", "newlineCharLength", "lineEndIndex", "formats", "bubbleFormats", "attributes", "AttributeMap", "diff", "Object", "keys", "for<PERSON>ach", "name", "_this$children$find", "find", "_this$children$find2", "refBlot", "refBlotOffset", "split", "renderBlock", "block", "createBlock", "blockEmbed", "isEnabled", "getAttribute", "leaf", "path", "_last", "LeafBlot", "lines", "Number", "MAX_VALUE", "getLines", "blotIndex", "blotLength", "lengthLeft", "forEachAt", "child", "childIndex", "<PERSON><PERSON><PERSON><PERSON>", "push", "ContainerBlot", "context", "SCROLL_OPTIMIZE", "_this3", "source", "sources", "USER", "observer", "takeRecords", "filter", "_ref2", "target", "SCROLL_BEFORE_UPDATE", "SCROLL_UPDATE", "updateEmbedAt", "_this$descendant", "b", "_this$descendant2", "event", "preventDefault", "_this4", "currentBlockDelta", "op", "splitted", "text", "_op$attributes", "INLINE", "_op$attributes2", "_this5", "entries", "_ref3", "_ref5", "isBlockBlot", "BLOT", "_ref4", "_ref6", "ScrollBlot", "Container", "parent", "inlineContents", "reduce", "Op", "_parent$descendant", "_parent$descendant2", "_typeof2", "isInlineEmbed", "_parent$descendant3", "_parent$descendant4", "_default", "exports"], "sources": ["../../src/blots/scroll.ts"], "sourcesContent": ["import { Container<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, ScrollBlot } from 'parchment';\nimport type { Blot, Parent, EmbedBlot, ParentBlot, Registry } from 'parchment';\nimport Delta, { AttributeMap, Op } from 'quill-delta';\nimport Emitter from '../core/emitter.js';\nimport type { EmitterSource } from '../core/emitter.js';\nimport Block, { BlockEmbed, bubbleFormats } from './block.js';\nimport Break from './break.js';\nimport Container from './container.js';\n\ntype RenderBlock =\n  | {\n      type: 'blockEmbed';\n      attributes: AttributeMap;\n      key: string;\n      value: unknown;\n    }\n  | { type: 'block'; attributes: AttributeMap; delta: Delta };\n\nfunction isLine(blot: unknown): blot is Block | BlockEmbed {\n  return blot instanceof Block || blot instanceof BlockEmbed;\n}\n\ninterface UpdatableEmbed {\n  updateContent(change: unknown): void;\n}\n\nfunction isUpdatable(blot: Blot): blot is Blot & UpdatableEmbed {\n  return typeof (blot as unknown as any).updateContent === 'function';\n}\n\nclass Scroll extends ScrollBlot {\n  static blotName = 'scroll';\n  static className = 'ql-editor';\n  static tagName = 'DIV';\n  static defaultChild = Block;\n  static allowedChildren = [Block, BlockEmbed, Container];\n\n  emitter: Emitter;\n  batch: false | MutationRecord[];\n\n  constructor(\n    registry: Registry,\n    domNode: HTMLDivElement,\n    { emitter }: { emitter: Emitter },\n  ) {\n    super(registry, domNode);\n    this.emitter = emitter;\n    this.batch = false;\n    this.optimize();\n    this.enable();\n    this.domNode.addEventListener('dragstart', (e) => this.handleDragStart(e));\n  }\n\n  batchStart() {\n    if (!Array.isArray(this.batch)) {\n      this.batch = [];\n    }\n  }\n\n  batchEnd() {\n    if (!this.batch) return;\n    const mutations = this.batch;\n    this.batch = false;\n    this.update(mutations);\n  }\n\n  emitMount(blot: Blot) {\n    this.emitter.emit(Emitter.events.SCROLL_BLOT_MOUNT, blot);\n  }\n\n  emitUnmount(blot: Blot) {\n    this.emitter.emit(Emitter.events.SCROLL_BLOT_UNMOUNT, blot);\n  }\n\n  emitEmbedUpdate(blot: Blot, change: unknown) {\n    this.emitter.emit(Emitter.events.SCROLL_EMBED_UPDATE, blot, change);\n  }\n\n  deleteAt(index: number, length: number) {\n    const [first, offset] = this.line(index);\n    const [last] = this.line(index + length);\n    super.deleteAt(index, length);\n    if (last != null && first !== last && offset > 0) {\n      if (first instanceof BlockEmbed || last instanceof BlockEmbed) {\n        this.optimize();\n        return;\n      }\n      const ref =\n        last.children.head instanceof Break ? null : last.children.head;\n      // @ts-expect-error\n      first.moveChildren(last, ref);\n      // @ts-expect-error\n      first.remove();\n    }\n    this.optimize();\n  }\n\n  enable(enabled = true) {\n    this.domNode.setAttribute('contenteditable', enabled ? 'true' : 'false');\n  }\n\n  formatAt(index: number, length: number, format: string, value: unknown) {\n    super.formatAt(index, length, format, value);\n    this.optimize();\n  }\n\n  insertAt(index: number, value: string, def?: unknown) {\n    if (index >= this.length()) {\n      if (def == null || this.scroll.query(value, Scope.BLOCK) == null) {\n        const blot = this.scroll.create(this.statics.defaultChild.blotName);\n        this.appendChild(blot);\n        if (def == null && value.endsWith('\\n')) {\n          blot.insertAt(0, value.slice(0, -1), def);\n        } else {\n          blot.insertAt(0, value, def);\n        }\n      } else {\n        const embed = this.scroll.create(value, def);\n        this.appendChild(embed);\n      }\n    } else {\n      super.insertAt(index, value, def);\n    }\n    this.optimize();\n  }\n\n  insertBefore(blot: Blot, ref?: Blot | null) {\n    if (blot.statics.scope === Scope.INLINE_BLOT) {\n      const wrapper = this.scroll.create(\n        this.statics.defaultChild.blotName,\n      ) as Parent;\n      wrapper.appendChild(blot);\n      super.insertBefore(wrapper, ref);\n    } else {\n      super.insertBefore(blot, ref);\n    }\n  }\n\n  insertContents(index: number, delta: Delta) {\n    const renderBlocks = this.deltaToRenderBlocks(\n      delta.concat(new Delta().insert('\\n')),\n    );\n    const last = renderBlocks.pop();\n    if (last == null) return;\n\n    this.batchStart();\n\n    const first = renderBlocks.shift();\n    if (first) {\n      const shouldInsertNewlineChar =\n        first.type === 'block' &&\n        (first.delta.length() === 0 ||\n          (!this.descendant(BlockEmbed, index)[0] && index < this.length()));\n      const delta =\n        first.type === 'block'\n          ? first.delta\n          : new Delta().insert({ [first.key]: first.value });\n      insertInlineContents(this, index, delta);\n      const newlineCharLength = first.type === 'block' ? 1 : 0;\n      const lineEndIndex = index + delta.length() + newlineCharLength;\n      if (shouldInsertNewlineChar) {\n        this.insertAt(lineEndIndex - 1, '\\n');\n      }\n\n      const formats = bubbleFormats(this.line(index)[0]);\n      const attributes = AttributeMap.diff(formats, first.attributes) || {};\n      Object.keys(attributes).forEach((name) => {\n        this.formatAt(lineEndIndex - 1, 1, name, attributes[name]);\n      });\n\n      index = lineEndIndex;\n    }\n\n    let [refBlot, refBlotOffset] = this.children.find(index);\n    if (renderBlocks.length) {\n      if (refBlot) {\n        refBlot = refBlot.split(refBlotOffset);\n        refBlotOffset = 0;\n      }\n\n      renderBlocks.forEach((renderBlock) => {\n        if (renderBlock.type === 'block') {\n          const block = this.createBlock(\n            renderBlock.attributes,\n            refBlot || undefined,\n          );\n          insertInlineContents(block, 0, renderBlock.delta);\n        } else {\n          const blockEmbed = this.create(\n            renderBlock.key,\n            renderBlock.value,\n          ) as EmbedBlot;\n          this.insertBefore(blockEmbed, refBlot || undefined);\n          Object.keys(renderBlock.attributes).forEach((name) => {\n            blockEmbed.format(name, renderBlock.attributes[name]);\n          });\n        }\n      });\n    }\n\n    if (last.type === 'block' && last.delta.length()) {\n      const offset = refBlot\n        ? refBlot.offset(refBlot.scroll) + refBlotOffset\n        : this.length();\n      insertInlineContents(this, offset, last.delta);\n    }\n\n    this.batchEnd();\n    this.optimize();\n  }\n\n  isEnabled() {\n    return this.domNode.getAttribute('contenteditable') === 'true';\n  }\n\n  leaf(index: number): [LeafBlot | null, number] {\n    const last = this.path(index).pop();\n    if (!last) {\n      return [null, -1];\n    }\n\n    const [blot, offset] = last;\n    return blot instanceof LeafBlot ? [blot, offset] : [null, -1];\n  }\n\n  line(index: number): [Block | BlockEmbed | null, number] {\n    if (index === this.length()) {\n      return this.line(index - 1);\n    }\n    // @ts-expect-error TODO: make descendant() generic\n    return this.descendant(isLine, index);\n  }\n\n  lines(index = 0, length = Number.MAX_VALUE): (Block | BlockEmbed)[] {\n    const getLines = (\n      blot: ParentBlot,\n      blotIndex: number,\n      blotLength: number,\n    ) => {\n      let lines: (Block | BlockEmbed)[] = [];\n      let lengthLeft = blotLength;\n      blot.children.forEachAt(\n        blotIndex,\n        blotLength,\n        (child, childIndex, childLength) => {\n          if (isLine(child)) {\n            lines.push(child);\n          } else if (child instanceof ContainerBlot) {\n            lines = lines.concat(getLines(child, childIndex, lengthLeft));\n          }\n          lengthLeft -= childLength;\n        },\n      );\n      return lines;\n    };\n    return getLines(this, index, length);\n  }\n\n  optimize(context?: { [key: string]: any }): void;\n  optimize(\n    mutations?: MutationRecord[],\n    context?: { [key: string]: any },\n  ): void;\n  optimize(mutations = [], context = {}) {\n    if (this.batch) return;\n    super.optimize(mutations, context);\n    if (mutations.length > 0) {\n      this.emitter.emit(Emitter.events.SCROLL_OPTIMIZE, mutations, context);\n    }\n  }\n\n  path(index: number) {\n    return super.path(index).slice(1); // Exclude self\n  }\n\n  remove() {\n    // Never remove self\n  }\n\n  update(source?: EmitterSource): void;\n  update(mutations?: MutationRecord[]): void;\n  update(mutations?: MutationRecord[] | EmitterSource): void {\n    if (this.batch) {\n      if (Array.isArray(mutations)) {\n        this.batch = this.batch.concat(mutations);\n      }\n      return;\n    }\n    let source: EmitterSource = Emitter.sources.USER;\n    if (typeof mutations === 'string') {\n      source = mutations;\n    }\n    if (!Array.isArray(mutations)) {\n      mutations = this.observer.takeRecords();\n    }\n    mutations = mutations.filter(({ target }) => {\n      const blot = this.find(target, true);\n      return blot && !isUpdatable(blot);\n    });\n    if (mutations.length > 0) {\n      this.emitter.emit(Emitter.events.SCROLL_BEFORE_UPDATE, source, mutations);\n    }\n    super.update(mutations.concat([])); // pass copy\n    if (mutations.length > 0) {\n      this.emitter.emit(Emitter.events.SCROLL_UPDATE, source, mutations);\n    }\n  }\n\n  updateEmbedAt(index: number, key: string, change: unknown) {\n    // Currently it only supports top-level embeds (BlockEmbed).\n    // We can update `ParentBlot` in parchment to support inline embeds.\n    const [blot] = this.descendant((b: Blot) => b instanceof BlockEmbed, index);\n    if (blot && blot.statics.blotName === key && isUpdatable(blot)) {\n      blot.updateContent(change);\n    }\n  }\n\n  protected handleDragStart(event: DragEvent) {\n    event.preventDefault();\n  }\n\n  private deltaToRenderBlocks(delta: Delta) {\n    const renderBlocks: RenderBlock[] = [];\n\n    let currentBlockDelta = new Delta();\n    delta.forEach((op) => {\n      const insert = op?.insert;\n      if (!insert) return;\n      if (typeof insert === 'string') {\n        const splitted = insert.split('\\n');\n        splitted.slice(0, -1).forEach((text) => {\n          currentBlockDelta.insert(text, op.attributes);\n          renderBlocks.push({\n            type: 'block',\n            delta: currentBlockDelta,\n            attributes: op.attributes ?? {},\n          });\n          currentBlockDelta = new Delta();\n        });\n        const last = splitted[splitted.length - 1];\n        if (last) {\n          currentBlockDelta.insert(last, op.attributes);\n        }\n      } else {\n        const key = Object.keys(insert)[0];\n        if (!key) return;\n        if (this.query(key, Scope.INLINE)) {\n          currentBlockDelta.push(op);\n        } else {\n          if (currentBlockDelta.length()) {\n            renderBlocks.push({\n              type: 'block',\n              delta: currentBlockDelta,\n              attributes: {},\n            });\n          }\n          currentBlockDelta = new Delta();\n          renderBlocks.push({\n            type: 'blockEmbed',\n            key,\n            value: insert[key],\n            attributes: op.attributes ?? {},\n          });\n        }\n      }\n    });\n\n    if (currentBlockDelta.length()) {\n      renderBlocks.push({\n        type: 'block',\n        delta: currentBlockDelta,\n        attributes: {},\n      });\n    }\n\n    return renderBlocks;\n  }\n\n  private createBlock(attributes: AttributeMap, refBlot?: Blot) {\n    let blotName: string | undefined;\n    const formats: AttributeMap = {};\n\n    Object.entries(attributes).forEach(([key, value]) => {\n      const isBlockBlot = this.query(key, Scope.BLOCK & Scope.BLOT) != null;\n      if (isBlockBlot) {\n        blotName = key;\n      } else {\n        formats[key] = value;\n      }\n    });\n\n    const block = this.create(\n      blotName || this.statics.defaultChild.blotName,\n      blotName ? attributes[blotName] : undefined,\n    ) as ParentBlot;\n\n    this.insertBefore(block, refBlot || undefined);\n\n    const length = block.length();\n    Object.entries(formats).forEach(([key, value]) => {\n      block.formatAt(0, length, key, value);\n    });\n\n    return block;\n  }\n}\n\nfunction insertInlineContents(\n  parent: ParentBlot,\n  index: number,\n  inlineContents: Delta,\n) {\n  inlineContents.reduce((index, op) => {\n    const length = Op.length(op);\n    let attributes = op.attributes || {};\n    if (op.insert != null) {\n      if (typeof op.insert === 'string') {\n        const text = op.insert;\n        parent.insertAt(index, text);\n        const [leaf] = parent.descendant(LeafBlot, index);\n        const formats = bubbleFormats(leaf);\n        attributes = AttributeMap.diff(formats, attributes) || {};\n      } else if (typeof op.insert === 'object') {\n        const key = Object.keys(op.insert)[0]; // There should only be one key\n        if (key == null) return index;\n        parent.insertAt(index, key, op.insert[key]);\n        const isInlineEmbed = parent.scroll.query(key, Scope.INLINE) != null;\n        if (isInlineEmbed) {\n          const [leaf] = parent.descendant(LeafBlot, index);\n          const formats = bubbleFormats(leaf);\n          attributes = AttributeMap.diff(formats, attributes) || {};\n        }\n      }\n    }\n    Object.keys(attributes).forEach((key) => {\n      parent.formatAt(index, length, key, attributes[key]);\n    });\n    return index + length;\n  }, index);\n}\n\nexport default Scroll;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAEA,IAAAC,WAAA,GAAAC,uBAAA,CAAAF,OAAA;AACA,IAAAG,QAAA,GAAAC,sBAAA,CAAAJ,OAAA;AAEA,IAAAK,MAAA,GAAAH,uBAAA,CAAAF,OAAA;AACA,IAAAM,MAAA,GAAAF,sBAAA,CAAAJ,OAAA;AACA,IAAAO,UAAA,GAAAH,sBAAA,CAAAJ,OAAA;AAWA,SAASQ,MAAMA,CAACC,IAAa,EAA8B;EACzD,OAAOA,IAAI,YAAYC,cAAK,IAAID,IAAI,YAAYE,iBAAU;AAC5D;AAMA,SAASC,WAAWA,CAACH,IAAU,EAAiC;EAC9D,OAAO,OAAQA,IAAI,CAAoBI,aAAa,KAAK,UAAU;AACrE;AAAA,IAEMC,MAAM,0BAAAC,WAAA;EAUV,SAAAD,OACEE,QAAkB,EAClBC,OAAuB,EAAAC,IAAA,EAEvB;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAP,MAAA;IAAA,IADEQ,OAAA,GAA+BJ,IAAA,CAA/BI,OAAA;IAEFH,KAAA,OAAAI,WAAA,CAAAF,OAAA,QAAAP,MAAA,GAAME,QAAQ,EAAEC,OAAO;IACvBE,KAAA,CAAKG,OAAO,GAAGA,OAAO;IACtBH,KAAA,CAAKK,KAAK,GAAG,KAAK;IAClBL,KAAA,CAAKM,QAAQ,CAAC,CAAC;IACfN,KAAA,CAAKO,MAAM,CAAC,CAAC;IACbP,KAAA,CAAKF,OAAO,CAACU,gBAAgB,CAAC,WAAW,EAAG,UAAAC,CAAC;MAAA,OAAKT,KAAA,CAAKU,eAAe,CAACD,CAAC,CAAC;IAAA,EAAC;IAAA,OAAAT,KAAA;EAC5E;EAAA,IAAAW,UAAA,CAAAT,OAAA,EAAAP,MAAA,EAAAC,WAAA;EAAA,WAAAgB,aAAA,CAAAV,OAAA,EAAAP,MAAA;IAAAkB,GAAA;IAAAC,KAAA,EAEA,SAAAC,UAAUA,CAAA,EAAG;MACX,IAAI,CAACC,KAAK,CAACC,OAAO,CAAC,IAAI,CAACZ,KAAK,CAAC,EAAE;QAC9B,IAAI,CAACA,KAAK,GAAG,EAAE;MACjB;IACF;EAAA;IAAAQ,GAAA;IAAAC,KAAA,EAEA,SAAAI,QAAQA,CAAA,EAAG;MACT,IAAI,CAAC,IAAI,CAACb,KAAK,EAAE;MACjB,IAAMc,SAAS,GAAG,IAAI,CAACd,KAAK;MAC5B,IAAI,CAACA,KAAK,GAAG,KAAK;MAClB,IAAI,CAACe,MAAM,CAACD,SAAS,CAAC;IACxB;EAAA;IAAAN,GAAA;IAAAC,KAAA,EAEA,SAAAO,SAASA,CAAC/B,IAAU,EAAE;MACpB,IAAI,CAACa,OAAO,CAACmB,IAAI,CAACC,gBAAO,CAACC,MAAM,CAACC,iBAAiB,EAAEnC,IAAI,CAAC;IAC3D;EAAA;IAAAuB,GAAA;IAAAC,KAAA,EAEA,SAAAY,WAAWA,CAACpC,IAAU,EAAE;MACtB,IAAI,CAACa,OAAO,CAACmB,IAAI,CAACC,gBAAO,CAACC,MAAM,CAACG,mBAAmB,EAAErC,IAAI,CAAC;IAC7D;EAAA;IAAAuB,GAAA;IAAAC,KAAA,EAEA,SAAAc,eAAeA,CAACtC,IAAU,EAAEuC,MAAe,EAAE;MAC3C,IAAI,CAAC1B,OAAO,CAACmB,IAAI,CAACC,gBAAO,CAACC,MAAM,CAACM,mBAAmB,EAAExC,IAAI,EAAEuC,MAAM,CAAC;IACrE;EAAA;IAAAhB,GAAA;IAAAC,KAAA,EAEA,SAAAiB,QAAQA,CAACC,KAAa,EAAEC,MAAc,EAAE;MACtC,IAAAC,UAAA,GAAwB,IAAI,CAACC,IAAI,CAACH,KAAK,CAAC;QAAAI,WAAA,OAAAC,eAAA,CAAAnC,OAAA,EAAAgC,UAAA;QAAjCI,KAAK,GAAAF,WAAA;QAAEG,MAAM,GAAAH,WAAA;MACpB,IAAAI,WAAA,GAAe,IAAI,CAACL,IAAI,CAACH,KAAK,GAAGC,MAAM,CAAC;QAAAQ,WAAA,OAAAJ,eAAA,CAAAnC,OAAA,EAAAsC,WAAA;QAAjCE,IAAI,GAAAD,WAAA;MACX,IAAAE,cAAA,CAAAzC,OAAA,EAAAP,MAAA,wBAAeqC,KAAK,EAAEC,MAAM;MAC5B,IAAIS,IAAI,IAAI,IAAI,IAAIJ,KAAK,KAAKI,IAAI,IAAIH,MAAM,GAAG,CAAC,EAAE;QAChD,IAAID,KAAK,YAAY9C,iBAAU,IAAIkD,IAAI,YAAYlD,iBAAU,EAAE;UAC7D,IAAI,CAACc,QAAQ,CAAC,CAAC;UACf;QACF;QACA,IAAMsC,GAAG,GACPF,IAAI,CAACG,QAAQ,CAACC,IAAI,YAAYC,cAAK,GAAG,IAAI,GAAGL,IAAI,CAACG,QAAQ,CAACC,IAAI;QACjE;QACAR,KAAK,CAACU,YAAY,CAACN,IAAI,EAAEE,GAAG,CAAC;QAC7B;QACAN,KAAK,CAACW,MAAM,CAAC,CAAC;MAChB;MACA,IAAI,CAAC3C,QAAQ,CAAC,CAAC;IACjB;EAAA;IAAAO,GAAA;IAAAC,KAAA,EAEA,SAAAP,MAAMA,CAAA,EAAiB;MAAA,IAAhB2C,OAAO,GAAAC,SAAA,CAAAlB,MAAA,QAAAkB,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,IAAI;MACnB,IAAI,CAACrD,OAAO,CAACuD,YAAY,CAAC,iBAAiB,EAAEH,OAAO,GAAG,MAAM,GAAG,OAAO,CAAC;IAC1E;EAAA;IAAArC,GAAA;IAAAC,KAAA,EAEA,SAAAwC,QAAQA,CAACtB,KAAa,EAAEC,MAAc,EAAEsB,MAAc,EAAEzC,KAAc,EAAE;MACtE,IAAA6B,cAAA,CAAAzC,OAAA,EAAAP,MAAA,wBAAeqC,KAAK,EAAEC,MAAM,EAAEsB,MAAM,EAAEzC,KAAK;MAC3C,IAAI,CAACR,QAAQ,CAAC,CAAC;IACjB;EAAA;IAAAO,GAAA;IAAAC,KAAA,EAEA,SAAA0C,QAAQA,CAACxB,KAAa,EAAElB,KAAa,EAAE2C,GAAa,EAAE;MACpD,IAAIzB,KAAK,IAAI,IAAI,CAACC,MAAM,CAAC,CAAC,EAAE;QAC1B,IAAIwB,GAAG,IAAI,IAAI,IAAI,IAAI,CAACC,MAAM,CAACC,KAAK,CAAC7C,KAAK,EAAE8C,gBAAK,CAACC,KAAK,CAAC,IAAI,IAAI,EAAE;UAChE,IAAMvE,IAAI,GAAG,IAAI,CAACoE,MAAM,CAACI,MAAM,CAAC,IAAI,CAACC,OAAO,CAACC,YAAY,CAACC,QAAQ,CAAC;UACnE,IAAI,CAACC,WAAW,CAAC5E,IAAI,CAAC;UACtB,IAAImE,GAAG,IAAI,IAAI,IAAI3C,KAAK,CAACqD,QAAQ,CAAC,IAAI,CAAC,EAAE;YACvC7E,IAAI,CAACkE,QAAQ,CAAC,CAAC,EAAE1C,KAAK,CAACsD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEX,GAAG,CAAC;UAC3C,CAAC,MAAM;YACLnE,IAAI,CAACkE,QAAQ,CAAC,CAAC,EAAE1C,KAAK,EAAE2C,GAAG,CAAC;UAC9B;QACF,CAAC,MAAM;UACL,IAAMY,KAAK,GAAG,IAAI,CAACX,MAAM,CAACI,MAAM,CAAChD,KAAK,EAAE2C,GAAG,CAAC;UAC5C,IAAI,CAACS,WAAW,CAACG,KAAK,CAAC;QACzB;MACF,CAAC,MAAM;QACL,IAAA1B,cAAA,CAAAzC,OAAA,EAAAP,MAAA,wBAAeqC,KAAK,EAAElB,KAAK,EAAE2C,GAAG;MAClC;MACA,IAAI,CAACnD,QAAQ,CAAC,CAAC;IACjB;EAAA;IAAAO,GAAA;IAAAC,KAAA,EAEA,SAAAwD,YAAYA,CAAChF,IAAU,EAAEsD,GAAiB,EAAE;MAC1C,IAAItD,IAAI,CAACyE,OAAO,CAACQ,KAAK,KAAKX,gBAAK,CAACY,WAAW,EAAE;QAC5C,IAAMC,OAAO,GAAG,IAAI,CAACf,MAAM,CAACI,MAAM,CAChC,IAAI,CAACC,OAAO,CAACC,YAAY,CAACC,QAC5B,CAAW;QACXQ,OAAO,CAACP,WAAW,CAAC5E,IAAI,CAAC;QACzB,IAAAqD,cAAA,CAAAzC,OAAA,EAAAP,MAAA,4BAAmB8E,OAAO,EAAE7B,GAAG;MACjC,CAAC,MAAM;QACL,IAAAD,cAAA,CAAAzC,OAAA,EAAAP,MAAA,4BAAmBL,IAAI,EAAEsD,GAAG;MAC9B;IACF;EAAA;IAAA/B,GAAA;IAAAC,KAAA,EAEA,SAAA4D,cAAcA,CAAC1C,KAAa,EAAE2C,KAAY,EAAE;MAAA,IAAAC,MAAA;MAC1C,IAAMC,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAC3CH,KAAK,CAACI,MAAM,CAAC,IAAIC,mBAAK,CAAC,CAAC,CAACC,MAAM,CAAC,IAAI,CAAC,CACvC,CAAC;MACD,IAAMvC,IAAI,GAAGmC,YAAY,CAACK,GAAG,CAAC,CAAC;MAC/B,IAAIxC,IAAI,IAAI,IAAI,EAAE;MAElB,IAAI,CAAC3B,UAAU,CAAC,CAAC;MAEjB,IAAMuB,KAAK,GAAGuC,YAAY,CAACM,KAAK,CAAC,CAAC;MAClC,IAAI7C,KAAK,EAAE;QACT,IAAM8C,uBAAuB,GAC3B9C,KAAK,CAAC+C,IAAI,KAAK,OAAO,KACrB/C,KAAK,CAACqC,KAAK,CAAC1C,MAAM,CAAC,CAAC,KAAK,CAAC,IACxB,CAAC,IAAI,CAACqD,UAAU,CAAC9F,iBAAU,EAAEwC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAIA,KAAK,GAAG,IAAI,CAACC,MAAM,CAAC,CAAE,CAAC;QACtE,IAAM0C,MAAK,GACTrC,KAAK,CAAC+C,IAAI,KAAK,OAAO,GAClB/C,KAAK,CAACqC,KAAK,GACX,IAAIK,mBAAK,CAAC,CAAC,CAACC,MAAM,KAAAM,gBAAA,CAAArF,OAAA,MAAIoC,KAAK,CAACzB,GAAG,EAAGyB,KAAK,CAACxB,KAAA,CAAO,CAAC;QACtD0E,oBAAoB,CAAC,IAAI,EAAExD,KAAK,EAAE2C,MAAK,CAAC;QACxC,IAAMc,iBAAiB,GAAGnD,KAAK,CAAC+C,IAAI,KAAK,OAAO,GAAG,CAAC,GAAG,CAAC;QACxD,IAAMK,YAAY,GAAG1D,KAAK,GAAG2C,MAAK,CAAC1C,MAAM,CAAC,CAAC,GAAGwD,iBAAiB;QAC/D,IAAIL,uBAAuB,EAAE;UAC3B,IAAI,CAAC5B,QAAQ,CAACkC,YAAY,GAAG,CAAC,EAAE,IAAI,CAAC;QACvC;QAEA,IAAMC,OAAO,GAAG,IAAAC,oBAAa,EAAC,IAAI,CAACzD,IAAI,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAClD,IAAM6D,UAAU,GAAGC,wBAAY,CAACC,IAAI,CAACJ,OAAO,EAAErD,KAAK,CAACuD,UAAU,CAAC,IAAI,CAAC,CAAC;QACrEG,MAAM,CAACC,IAAI,CAACJ,UAAU,CAAC,CAACK,OAAO,CAAE,UAAAC,IAAI,EAAK;UACxCvB,MAAI,CAACtB,QAAQ,CAACoC,YAAY,GAAG,CAAC,EAAE,CAAC,EAAES,IAAI,EAAEN,UAAU,CAACM,IAAI,CAAC,CAAC;QAC5D,CAAC,CAAC;QAEFnE,KAAK,GAAG0D,YAAY;MACtB;MAEA,IAAAU,mBAAA,GAA+B,IAAI,CAACvD,QAAQ,CAACwD,IAAI,CAACrE,KAAK,CAAC;QAAAsE,oBAAA,OAAAjE,eAAA,CAAAnC,OAAA,EAAAkG,mBAAA;QAAnDG,OAAO,GAAAD,oBAAA;QAAEE,aAAa,GAAAF,oBAAA;MAC3B,IAAIzB,YAAY,CAAC5C,MAAM,EAAE;QACvB,IAAIsE,OAAO,EAAE;UACXA,OAAO,GAAGA,OAAO,CAACE,KAAK,CAACD,aAAa,CAAC;UACtCA,aAAa,GAAG,CAAC;QACnB;QAEA3B,YAAY,CAACqB,OAAO,CAAE,UAAAQ,WAAW,EAAK;UACpC,IAAIA,WAAW,CAACrB,IAAI,KAAK,OAAO,EAAE;YAChC,IAAMsB,KAAK,GAAG/B,MAAI,CAACgC,WAAW,CAC5BF,WAAW,CAACb,UAAU,EACtBU,OAAO,IAAInD,SACb,CAAC;YACDoC,oBAAoB,CAACmB,KAAK,EAAE,CAAC,EAAED,WAAW,CAAC/B,KAAK,CAAC;UACnD,CAAC,MAAM;YACL,IAAMkC,UAAU,GAAGjC,MAAI,CAACd,MAAM,CAC5B4C,WAAW,CAAC7F,GAAG,EACf6F,WAAW,CAAC5F,KACd,CAAc;YACd8D,MAAI,CAACN,YAAY,CAACuC,UAAU,EAAEN,OAAO,IAAInD,SAAS,CAAC;YACnD4C,MAAM,CAACC,IAAI,CAACS,WAAW,CAACb,UAAU,CAAC,CAACK,OAAO,CAAE,UAAAC,IAAI,EAAK;cACpDU,UAAU,CAACtD,MAAM,CAAC4C,IAAI,EAAEO,WAAW,CAACb,UAAU,CAACM,IAAI,CAAC,CAAC;YACvD,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ;MAEA,IAAIzD,IAAI,CAAC2C,IAAI,KAAK,OAAO,IAAI3C,IAAI,CAACiC,KAAK,CAAC1C,MAAM,CAAC,CAAC,EAAE;QAChD,IAAMM,MAAM,GAAGgE,OAAO,GAClBA,OAAO,CAAChE,MAAM,CAACgE,OAAO,CAAC7C,MAAM,CAAC,GAAG8C,aAAa,GAC9C,IAAI,CAACvE,MAAM,CAAC,CAAC;QACjBuD,oBAAoB,CAAC,IAAI,EAAEjD,MAAM,EAAEG,IAAI,CAACiC,KAAK,CAAC;MAChD;MAEA,IAAI,CAACzD,QAAQ,CAAC,CAAC;MACf,IAAI,CAACZ,QAAQ,CAAC,CAAC;IACjB;EAAA;IAAAO,GAAA;IAAAC,KAAA,EAEA,SAAAgG,SAASA,CAAA,EAAG;MACV,OAAO,IAAI,CAAChH,OAAO,CAACiH,YAAY,CAAC,iBAAiB,CAAC,KAAK,MAAM;IAChE;EAAA;IAAAlG,GAAA;IAAAC,KAAA,EAEA,SAAAkG,IAAIA,CAAChF,KAAa,EAA6B;MAC7C,IAAMU,IAAI,GAAG,IAAI,CAACuE,IAAI,CAACjF,KAAK,CAAC,CAACkD,GAAG,CAAC,CAAC;MACnC,IAAI,CAACxC,IAAI,EAAE;QACT,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;MACnB;MAEA,IAAAwE,KAAA,OAAA7E,eAAA,CAAAnC,OAAA,EAAuBwC,IAAI;QAApBpD,IAAI,GAAA4H,KAAA;QAAE3E,MAAM,GAAA2E,KAAA;MACnB,OAAO5H,IAAI,YAAY6H,mBAAQ,GAAG,CAAC7H,IAAI,EAAEiD,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IAC/D;EAAA;IAAA1B,GAAA;IAAAC,KAAA,EAEA,SAAAqB,IAAIA,CAACH,KAAa,EAAuC;MACvD,IAAIA,KAAK,KAAK,IAAI,CAACC,MAAM,CAAC,CAAC,EAAE;QAC3B,OAAO,IAAI,CAACE,IAAI,CAACH,KAAK,GAAG,CAAC,CAAC;MAC7B;MACA;MACA,OAAO,IAAI,CAACsD,UAAU,CAACjG,MAAM,EAAE2C,KAAK,CAAC;IACvC;EAAA;IAAAnB,GAAA;IAAAC,KAAA,EAEA,SAAAsG,KAAKA,CAAA,EAA+D;MAAA,IAA9DpF,KAAK,GAAAmB,SAAA,CAAAlB,MAAA,QAAAkB,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC;MAAA,IAAElB,MAAM,GAAAkB,SAAA,CAAAlB,MAAA,QAAAkB,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGkE,MAAM,CAACC,SAAS;MACxC,IAAMC,SAAQ,GAAG,SAAXA,QAAQA,CACZjI,IAAgB,EAChBkI,SAAiB,EACjBC,UAAkB,EACf;QACH,IAAIL,KAA6B,GAAG,EAAE;QACtC,IAAIM,UAAU,GAAGD,UAAU;QAC3BnI,IAAI,CAACuD,QAAQ,CAAC8E,SAAS,CACrBH,SAAS,EACTC,UAAU,EACV,UAACG,KAAK,EAAEC,UAAU,EAAEC,WAAW,EAAK;UAClC,IAAIzI,MAAM,CAACuI,KAAK,CAAC,EAAE;YACjBR,KAAK,CAACW,IAAI,CAACH,KAAK,CAAC;UACnB,CAAC,MAAM,IAAIA,KAAK,YAAYI,wBAAa,EAAE;YACzCZ,KAAK,GAAGA,KAAK,CAACrC,MAAM,CAACwC,SAAQ,CAACK,KAAK,EAAEC,UAAU,EAAEH,UAAU,CAAC,CAAC;UAC/D;UACAA,UAAU,IAAII,WAAW;QAC3B,CACF,CAAC;QACD,OAAOV,KAAK;MACd,CAAC;MACD,OAAOG,SAAQ,CAAC,IAAI,EAAEvF,KAAK,EAAEC,MAAM,CAAC;IACtC;EAAA;IAAApB,GAAA;IAAAC,KAAA,EAOA,SAAAR,QAAQA,CAAA,EAA+B;MAAA,IAA9Ba,SAAS,GAAAgC,SAAA,CAAAlB,MAAA,QAAAkB,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;MAAA,IAAE8E,OAAO,GAAA9E,SAAA,CAAAlB,MAAA,QAAAkB,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;MACnC,IAAI,IAAI,CAAC9C,KAAK,EAAE;MAChB,IAAAsC,cAAA,CAAAzC,OAAA,EAAAP,MAAA,wBAAewB,SAAS,EAAE8G,OAAO;MACjC,IAAI9G,SAAS,CAACc,MAAM,GAAG,CAAC,EAAE;QACxB,IAAI,CAAC9B,OAAO,CAACmB,IAAI,CAACC,gBAAO,CAACC,MAAM,CAAC0G,eAAe,EAAE/G,SAAS,EAAE8G,OAAO,CAAC;MACvE;IACF;EAAA;IAAApH,GAAA;IAAAC,KAAA,EAEA,SAAAmG,IAAIA,CAACjF,KAAa,EAAE;MAClB,OAAO,IAAAW,cAAA,CAAAzC,OAAA,EAAAP,MAAA,oBAAWqC,KAAK,GAAEoC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC;EAAA;IAAAvD,GAAA;IAAAC,KAAA,EAEA,SAAAmC,MAAMA,CAAA,EAAG;MACP;IAAA;EAAA;IAAApC,GAAA;IAAAC,KAAA,EAKF,SAAAM,MAAMA,CAACD,SAA4C,EAAQ;MAAA,IAAAgH,MAAA;MACzD,IAAI,IAAI,CAAC9H,KAAK,EAAE;QACd,IAAIW,KAAK,CAACC,OAAO,CAACE,SAAS,CAAC,EAAE;UAC5B,IAAI,CAACd,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC0E,MAAM,CAAC5D,SAAS,CAAC;QAC3C;QACA;MACF;MACA,IAAIiH,MAAqB,GAAG7G,gBAAO,CAAC8G,OAAO,CAACC,IAAI;MAChD,IAAI,OAAOnH,SAAS,KAAK,QAAQ,EAAE;QACjCiH,MAAM,GAAGjH,SAAS;MACpB;MACA,IAAI,CAACH,KAAK,CAACC,OAAO,CAACE,SAAS,CAAC,EAAE;QAC7BA,SAAS,GAAG,IAAI,CAACoH,QAAQ,CAACC,WAAW,CAAC,CAAC;MACzC;MACArH,SAAS,GAAGA,SAAS,CAACsH,MAAM,CAAC,UAAAC,KAAA,EAAgB;QAAA,IAAbC,MAAA,GAAQD,KAAA,CAARC,MAAA;QAC9B,IAAMrJ,IAAI,GAAG6I,MAAI,CAAC9B,IAAI,CAACsC,MAAM,EAAE,IAAI,CAAC;QACpC,OAAOrJ,IAAI,IAAI,CAACG,WAAW,CAACH,IAAI,CAAC;MACnC,CAAC,CAAC;MACF,IAAI6B,SAAS,CAACc,MAAM,GAAG,CAAC,EAAE;QACxB,IAAI,CAAC9B,OAAO,CAACmB,IAAI,CAACC,gBAAO,CAACC,MAAM,CAACoH,oBAAoB,EAAER,MAAM,EAAEjH,SAAS,CAAC;MAC3E;MACA,IAAAwB,cAAA,CAAAzC,OAAA,EAAAP,MAAA,sBAAawB,SAAS,CAAC4D,MAAM,CAAC,EAAE,CAAC,GAAE,CAAC;MACpC,IAAI5D,SAAS,CAACc,MAAM,GAAG,CAAC,EAAE;QACxB,IAAI,CAAC9B,OAAO,CAACmB,IAAI,CAACC,gBAAO,CAACC,MAAM,CAACqH,aAAa,EAAET,MAAM,EAAEjH,SAAS,CAAC;MACpE;IACF;EAAA;IAAAN,GAAA;IAAAC,KAAA,EAEA,SAAAgI,aAAaA,CAAC9G,KAAa,EAAEnB,GAAW,EAAEgB,MAAe,EAAE;MACzD;MACA;MACA,IAAAkH,gBAAA,GAAe,IAAI,CAACzD,UAAU,CAAE,UAAA0D,CAAO;UAAA,OAAKA,CAAC,YAAYxJ,iBAAU;QAAA,GAAEwC,KAAK,CAAC;QAAAiH,iBAAA,OAAA5G,eAAA,CAAAnC,OAAA,EAAA6I,gBAAA;QAApEzJ,IAAI,GAAA2J,iBAAA;MACX,IAAI3J,IAAI,IAAIA,IAAI,CAACyE,OAAO,CAACE,QAAQ,KAAKpD,GAAG,IAAIpB,WAAW,CAACH,IAAI,CAAC,EAAE;QAC9DA,IAAI,CAACI,aAAa,CAACmC,MAAM,CAAC;MAC5B;IACF;EAAA;IAAAhB,GAAA;IAAAC,KAAA,EAEU,SAAAJ,eAAeA,CAACwI,KAAgB,EAAE;MAC1CA,KAAK,CAACC,cAAc,CAAC,CAAC;IACxB;EAAA;IAAAtI,GAAA;IAAAC,KAAA,EAEQ,SAAAgE,mBAAmBA,CAACH,KAAY,EAAE;MAAA,IAAAyE,MAAA;MACxC,IAAMvE,YAA2B,GAAG,EAAE;MAEtC,IAAIwE,iBAAiB,GAAG,IAAIrE,mBAAK,CAAC,CAAC;MACnCL,KAAK,CAACuB,OAAO,CAAE,UAAAoD,EAAE,EAAK;QACpB,IAAMrE,MAAM,GAAGqE,EAAE,aAAFA,EAAE,uBAAFA,EAAE,CAAErE,MAAM;QACzB,IAAI,CAACA,MAAM,EAAE;QACb,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;UAC9B,IAAMsE,QAAQ,GAAGtE,MAAM,CAACwB,KAAK,CAAC,IAAI,CAAC;UACnC8C,QAAQ,CAACnF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC8B,OAAO,CAAE,UAAAsD,IAAI,EAAK;YAAA,IAAAC,cAAA;YACtCJ,iBAAiB,CAACpE,MAAM,CAACuE,IAAI,EAAEF,EAAE,CAACzD,UAAU,CAAC;YAC7ChB,YAAY,CAACkD,IAAI,CAAC;cAChB1C,IAAI,EAAE,OAAO;cACbV,KAAK,EAAE0E,iBAAiB;cACxBxD,UAAU,GAAA4D,cAAA,GAAEH,EAAE,CAACzD,UAAU,cAAA4D,cAAA,cAAAA,cAAA,GAAI,CAAC;YAChC,CAAC,CAAC;YACFJ,iBAAiB,GAAG,IAAIrE,mBAAK,CAAC,CAAC;UACjC,CAAC,CAAC;UACF,IAAMtC,IAAI,GAAG6G,QAAQ,CAACA,QAAQ,CAACtH,MAAM,GAAG,CAAC,CAAC;UAC1C,IAAIS,IAAI,EAAE;YACR2G,iBAAiB,CAACpE,MAAM,CAACvC,IAAI,EAAE4G,EAAE,CAACzD,UAAU,CAAC;UAC/C;QACF,CAAC,MAAM;UACL,IAAMhF,GAAG,GAAGmF,MAAM,CAACC,IAAI,CAAChB,MAAM,CAAC,CAAC,CAAC,CAAC;UAClC,IAAI,CAACpE,GAAG,EAAE;UACV,IAAIuI,MAAI,CAACzF,KAAK,CAAC9C,GAAG,EAAE+C,gBAAK,CAAC8F,MAAM,CAAC,EAAE;YACjCL,iBAAiB,CAACtB,IAAI,CAACuB,EAAE,CAAC;UAC5B,CAAC,MAAM;YAAA,IAAAK,eAAA;YACL,IAAIN,iBAAiB,CAACpH,MAAM,CAAC,CAAC,EAAE;cAC9B4C,YAAY,CAACkD,IAAI,CAAC;gBAChB1C,IAAI,EAAE,OAAO;gBACbV,KAAK,EAAE0E,iBAAiB;gBACxBxD,UAAU,EAAE,CAAC;cACf,CAAC,CAAC;YACJ;YACAwD,iBAAiB,GAAG,IAAIrE,mBAAK,CAAC,CAAC;YAC/BH,YAAY,CAACkD,IAAI,CAAC;cAChB1C,IAAI,EAAE,YAAY;cAClBxE,GAAG,EAAHA,GAAG;cACHC,KAAK,EAAEmE,MAAM,CAACpE,GAAG,CAAC;cAClBgF,UAAU,GAAA8D,eAAA,GAAEL,EAAE,CAACzD,UAAU,cAAA8D,eAAA,cAAAA,eAAA,GAAI,CAAC;YAChC,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;MAEF,IAAIN,iBAAiB,CAACpH,MAAM,CAAC,CAAC,EAAE;QAC9B4C,YAAY,CAACkD,IAAI,CAAC;UAChB1C,IAAI,EAAE,OAAO;UACbV,KAAK,EAAE0E,iBAAiB;UACxBxD,UAAU,EAAE,CAAC;QACf,CAAC,CAAC;MACJ;MAEA,OAAOhB,YAAY;IACrB;EAAA;IAAAhE,GAAA;IAAAC,KAAA,EAEQ,SAAA8F,WAAWA,CAACf,UAAwB,EAAEU,OAAc,EAAE;MAAA,IAAAqD,MAAA;MAC5D,IAAI3F,QAA4B;MAChC,IAAM0B,OAAqB,GAAG,CAAC,CAAC;MAEhCK,MAAM,CAAC6D,OAAO,CAAChE,UAAU,CAAC,CAACK,OAAO,CAAC,UAAA4D,KAAA,EAAkB;QAAA,IAAAC,KAAA,OAAA1H,eAAA,CAAAnC,OAAA,EAAL4J,KAAA;UAAXjJ,GAAG,GAAAkJ,KAAA;UAAEjJ,KAAK,GAAAiJ,KAAA;QAC7C,IAAMC,WAAW,GAAGJ,MAAI,CAACjG,KAAK,CAAC9C,GAAG,EAAE+C,gBAAK,CAACC,KAAK,GAAGD,gBAAK,CAACqG,IAAI,CAAC,IAAI,IAAI;QACrE,IAAID,WAAW,EAAE;UACf/F,QAAQ,GAAGpD,GAAG;QAChB,CAAC,MAAM;UACL8E,OAAO,CAAC9E,GAAG,CAAC,GAAGC,KAAK;QACtB;MACF,CAAC,CAAC;MAEF,IAAM6F,KAAK,GAAG,IAAI,CAAC7C,MAAM,CACvBG,QAAQ,IAAI,IAAI,CAACF,OAAO,CAACC,YAAY,CAACC,QAAQ,EAC9CA,QAAQ,GAAG4B,UAAU,CAAC5B,QAAQ,CAAC,GAAGb,SACpC,CAAe;MAEf,IAAI,CAACkB,YAAY,CAACqC,KAAK,EAAEJ,OAAO,IAAInD,SAAS,CAAC;MAE9C,IAAMnB,MAAM,GAAG0E,KAAK,CAAC1E,MAAM,CAAC,CAAC;MAC7B+D,MAAM,CAAC6D,OAAO,CAAClE,OAAO,CAAC,CAACO,OAAO,CAAC,UAAAgE,KAAA,EAAkB;QAAA,IAAAC,KAAA,OAAA9H,eAAA,CAAAnC,OAAA,EAALgK,KAAA;UAAXrJ,GAAG,GAAAsJ,KAAA;UAAErJ,KAAK,GAAAqJ,KAAA;QAC1CxD,KAAK,CAACrD,QAAQ,CAAC,CAAC,EAAErB,MAAM,EAAEpB,GAAG,EAAEC,KAAK,CAAC;MACvC,CAAC,CAAC;MAEF,OAAO6F,KAAK;IACd;EAAA;AAAA,EAtXmByD,qBAAU;AAAA,IAAA7E,gBAAA,CAAArF,OAAA,EAAzBP,MAAM,cACQ,QAAQ;AAAA,IAAA4F,gBAAA,CAAArF,OAAA,EADtBP,MAAM,eAES,WAAW;AAAA,IAAA4F,gBAAA,CAAArF,OAAA,EAF1BP,MAAM,aAGO,KAAK;AAAA,IAAA4F,gBAAA,CAAArF,OAAA,EAHlBP,MAAM,kBAIYJ,cAAK;AAAA,IAAAgG,gBAAA,CAAArF,OAAA,EAJvBP,MAAM,qBAKe,CAACJ,cAAK,EAAEC,iBAAU,EAAE6K,kBAAS,CAAC;AAoXzD,SAAS7E,oBAAoBA,CAC3B8E,MAAkB,EAClBtI,KAAa,EACbuI,cAAqB,EACrB;EACAA,cAAc,CAACC,MAAM,CAAC,UAACxI,KAAK,EAAEsH,EAAE,EAAK;IACnC,IAAMrH,MAAM,GAAGwI,cAAE,CAACxI,MAAM,CAACqH,EAAE,CAAC;IAC5B,IAAIzD,UAAU,GAAGyD,EAAE,CAACzD,UAAU,IAAI,CAAC,CAAC;IACpC,IAAIyD,EAAE,CAACrE,MAAM,IAAI,IAAI,EAAE;MACrB,IAAI,OAAOqE,EAAE,CAACrE,MAAM,KAAK,QAAQ,EAAE;QACjC,IAAMuE,IAAI,GAAGF,EAAE,CAACrE,MAAM;QACtBqF,MAAM,CAAC9G,QAAQ,CAACxB,KAAK,EAAEwH,IAAI,CAAC;QAC5B,IAAAkB,kBAAA,GAAeJ,MAAM,CAAChF,UAAU,CAAC6B,mBAAQ,EAAEnF,KAAK,CAAC;UAAA2I,mBAAA,OAAAtI,eAAA,CAAAnC,OAAA,EAAAwK,kBAAA;UAA1C1D,IAAI,GAAA2D,mBAAA;QACX,IAAMhF,OAAO,GAAG,IAAAC,oBAAa,EAACoB,IAAI,CAAC;QACnCnB,UAAU,GAAGC,wBAAY,CAACC,IAAI,CAACJ,OAAO,EAAEE,UAAU,CAAC,IAAI,CAAC,CAAC;MAC3D,CAAC,MAAM,IAAI,IAAA+E,QAAA,CAAA1K,OAAA,EAAOoJ,EAAE,CAACrE,MAAM,MAAK,QAAQ,EAAE;QACxC,IAAMpE,GAAG,GAAGmF,MAAM,CAACC,IAAI,CAACqD,EAAE,CAACrE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,IAAIpE,GAAG,IAAI,IAAI,EAAE,OAAOmB,KAAK;QAC7BsI,MAAM,CAAC9G,QAAQ,CAACxB,KAAK,EAAEnB,GAAG,EAAEyI,EAAE,CAACrE,MAAM,CAACpE,GAAG,CAAC,CAAC;QAC3C,IAAMgK,aAAa,GAAGP,MAAM,CAAC5G,MAAM,CAACC,KAAK,CAAC9C,GAAG,EAAE+C,gBAAK,CAAC8F,MAAM,CAAC,IAAI,IAAI;QACpE,IAAImB,aAAa,EAAE;UACjB,IAAAC,mBAAA,GAAeR,MAAM,CAAChF,UAAU,CAAC6B,mBAAQ,EAAEnF,KAAK,CAAC;YAAA+I,mBAAA,OAAA1I,eAAA,CAAAnC,OAAA,EAAA4K,mBAAA;YAA1C9D,KAAI,GAAA+D,mBAAA;UACX,IAAMpF,QAAO,GAAG,IAAAC,oBAAa,EAACoB,KAAI,CAAC;UACnCnB,UAAU,GAAGC,wBAAY,CAACC,IAAI,CAACJ,QAAO,EAAEE,UAAU,CAAC,IAAI,CAAC,CAAC;QAC3D;MACF;IACF;IACAG,MAAM,CAACC,IAAI,CAACJ,UAAU,CAAC,CAACK,OAAO,CAAE,UAAArF,GAAG,EAAK;MACvCyJ,MAAM,CAAChH,QAAQ,CAACtB,KAAK,EAAEC,MAAM,EAAEpB,GAAG,EAAEgF,UAAU,CAAChF,GAAG,CAAC,CAAC;IACtD,CAAC,CAAC;IACF,OAAOmB,KAAK,GAAGC,MAAM;EACvB,CAAC,EAAED,KAAK,CAAC;AACX;AAAA,IAAAgJ,QAAA,GAAAC,OAAA,CAAA/K,OAAA,GAEeP,MAAM", "ignoreList": []}]}