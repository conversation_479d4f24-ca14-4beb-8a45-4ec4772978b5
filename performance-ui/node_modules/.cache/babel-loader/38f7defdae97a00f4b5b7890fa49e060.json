{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/settings.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/settings.js", "mtime": 1753510684530}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:bW9kdWxlLmV4cG9ydHMgPSB7CiAgLyoqCiAgICog572R6aG15qCH6aKYCiAgICovCiAgdGl0bGU6ICfnu6nmlYjnrqHnkIbns7vnu58nLAogIC8qKgogICAqIOS+p+i+ueagj+S4u+mimCDmt7HoibLkuLvpoph0aGVtZS1kYXJr77yM5rWF6Imy5Li76aKYdGhlbWUtbGlnaHQKICAgKi8KICBzaWRlVGhlbWU6ICd0aGVtZS1kYXJrJywKICAvKioKICAgKiDns7vnu5/luIPlsYDphY3nva4KICAgKi8KICBzaG93U2V0dGluZ3M6IHRydWUsCiAgLyoqCiAgICog5piv5ZCm5pi+56S66aG26YOo5a+86IiqCiAgICovCiAgdG9wTmF2OiBmYWxzZSwKICAvKioKICAgKiDmmK/lkKbmmL7npLogdGFnc1ZpZXcKICAgKi8KICB0YWdzVmlldzogdHJ1ZSwKICAvKioKICAgKiDmmL7npLrpobXnrb7lm77moIcKICAgKi8KICB0YWdzSWNvbjogZmFsc2UsCiAgLyoqCiAgICog5piv5ZCm5Zu65a6a5aS06YOoCiAgICovCiAgZml4ZWRIZWFkZXI6IGZhbHNlLAogIC8qKgogICAqIOaYr+WQpuaYvuekumxvZ28KICAgKi8KICBzaWRlYmFyTG9nbzogdHJ1ZSwKICAvKioKICAgKiDmmK/lkKbmmL7npLrliqjmgIHmoIfpopgKICAgKi8KICBkeW5hbWljVGl0bGU6IGZhbHNlLAogIC8qKgogICAqIOaYr+WQpuaYvuekuuW6lemDqOeJiOadgwogICAqLwogIGZvb3RlclZpc2libGU6IGZhbHNlLAogIC8qKgogICAqIOW6lemDqOeJiOadg+aWh+acrOWGheWuuQogICAqLwogIGZvb3RlckNvbnRlbnQ6ICdDb3B5cmlnaHQgwqkgMjAxOC0yMDI1IOe7qeaViOeuoeeQhuezu+e7ny4gQWxsIFJpZ2h0cyBSZXNlcnZlZC4nCn07"}, {"version": 3, "names": ["module", "exports", "title", "sideTheme", "showSettings", "topNav", "tagsView", "tagsIcon", "fixedHeader", "sidebarLogo", "dynamicTitle", "footerVisible", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/settings.js"], "sourcesContent": ["module.exports = {\n  /**\n   * 网页标题\n   */\n  title: '绩效管理系统',\n\n  /**\n   * 侧边栏主题 深色主题theme-dark，浅色主题theme-light\n   */\n  sideTheme: 'theme-dark',\n\n  /**\n   * 系统布局配置\n   */\n  showSettings: true,\n\n  /**\n   * 是否显示顶部导航\n   */\n  topNav: false,\n\n  /**\n   * 是否显示 tagsView\n   */\n  tagsView: true,\n  \n  /**\n   * 显示页签图标\n   */\n  tagsIcon: false,\n\n  /**\n   * 是否固定头部\n   */\n  fixedHeader: false,\n\n  /**\n   * 是否显示logo\n   */\n  sidebarLogo: true,\n\n  /**\n   * 是否显示动态标题\n   */\n  dynamicTitle: false,\n\n  /**\n   * 是否显示底部版权\n   */\n  footerVisible: false,\n\n  /**\n   * 底部版权文本内容\n   */\n  footerContent: 'Copyright © 2018-2025 绩效管理系统. All Rights Reserved.'\n}\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAG;EACf;AACF;AACA;EACEC,KAAK,EAAE,QAAQ;EAEf;AACF;AACA;EACEC,SAAS,EAAE,YAAY;EAEvB;AACF;AACA;EACEC,YAAY,EAAE,IAAI;EAElB;AACF;AACA;EACEC,MAAM,EAAE,KAAK;EAEb;AACF;AACA;EACEC,QAAQ,EAAE,IAAI;EAEd;AACF;AACA;EACEC,QAAQ,EAAE,KAAK;EAEf;AACF;AACA;EACEC,WAAW,EAAE,KAAK;EAElB;AACF;AACA;EACEC,WAAW,EAAE,IAAI;EAEjB;AACF;AACA;EACEC,YAAY,EAAE,KAAK;EAEnB;AACF;AACA;EACEC,aAAa,EAAE,KAAK;EAEpB;AACF;AACA;EACEC,aAAa,EAAE;AACjB,CAAC", "ignoreList": []}]}