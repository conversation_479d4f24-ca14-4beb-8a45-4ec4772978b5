{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/modules/normalizeExternalHTML/index.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/modules/normalizeExternalHTML/index.js", "mtime": 1753510684096}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCIvVXNlcnMvbWF6aWhhby9EZXNrdG9wL2Rldi9wZXJmb3JtYW5jZS9wZXJmb3JtYW5jZS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiKTsKdmFyIF9nb29nbGVEb2NzID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIuL25vcm1hbGl6ZXJzL2dvb2dsZURvY3MuanMiKSk7CnZhciBfbXNXb3JkID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIuL25vcm1hbGl6ZXJzL21zV29yZC5qcyIpKTsKdmFyIE5PUk1BTElaRVJTID0gW19tc1dvcmQuZGVmYXVsdCwgX2dvb2dsZURvY3MuZGVmYXVsdF07CnZhciBub3JtYWxpemVFeHRlcm5hbEhUTUwgPSBmdW5jdGlvbiBub3JtYWxpemVFeHRlcm5hbEhUTUwoZG9jKSB7CiAgaWYgKGRvYy5kb2N1bWVudEVsZW1lbnQpIHsKICAgIE5PUk1BTElaRVJTLmZvckVhY2goZnVuY3Rpb24gKG5vcm1hbGl6ZSkgewogICAgICBub3JtYWxpemUoZG9jKTsKICAgIH0pOwogIH0KfTsKdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gbm9ybWFsaXplRXh0ZXJuYWxIVE1MOw=="}, {"version": 3, "names": ["_googleDocs", "_interopRequireDefault", "require", "_msWord", "NORMALIZERS", "msWord", "googleDocs", "normalizeExternalHTML", "doc", "documentElement", "for<PERSON>ach", "normalize", "_default", "exports", "default"], "sources": ["../../../src/modules/normalizeExternalHTML/index.ts"], "sourcesContent": ["import googleDocs from './normalizers/googleDocs.js';\nimport msWord from './normalizers/msWord.js';\n\nconst NORMALIZERS = [msWord, googleDocs];\n\nconst normalizeExternalHTML = (doc: Document) => {\n  if (doc.documentElement) {\n    NORMALIZERS.forEach((normalize) => {\n      normalize(doc);\n    });\n  }\n};\n\nexport default normalizeExternalHTML;\n"], "mappings": ";;;;;;;;AAAA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAF,sBAAA,CAAAC,OAAA;AAEA,IAAME,WAAW,GAAG,CAACC,eAAM,EAAEC,mBAAU,CAAC;AAExC,IAAMC,qBAAqB,GAAI,SAAzBA,qBAAqBA,CAAIC,GAAa,EAAK;EAC/C,IAAIA,GAAG,CAACC,eAAe,EAAE;IACvBL,WAAW,CAACM,OAAO,CAAE,UAAAC,SAAS,EAAK;MACjCA,SAAS,CAACH,GAAG,CAAC;IAChB,CAAC,CAAC;EACJ;AACF,CAAC;AAAA,IAAAI,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEcP,qBAAqB", "ignoreList": []}]}