{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/evaluation/member/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/evaluation/member/index.vue", "mtime": 1753510684533}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_memberOrganization", "require", "_default", "exports", "default", "name", "data", "loading", "ids", "single", "multiple", "total", "tableData", "showSearch", "queryParams", "memberName", "testTime", "form", "open", "title", "rules", "required", "message", "trigger", "upload", "isUploading", "file", "created", "getList", "methods", "_this", "listMemberOrganization", "then", "res", "rows", "handleSelectionChange", "selection", "map", "item", "id", "length", "handleAdd", "reset", "handleUpdate", "row", "Object", "assign", "submitForm", "_this2", "$refs", "validate", "valid", "updateMemberOrganization", "$modal", "msgSuccess", "addMemberOrganization", "handleDelete", "_this3", "confirm", "delMemberOrganization", "catch", "handleExport", "trim", "$message", "error", "exportMemberWord", "blob", "Blob", "type", "link", "document", "createElement", "href", "window", "URL", "createObjectURL", "download", "click", "revokeObjectURL", "handleDownloadTemplate", "downloadMemberTemplate", "handleImport", "handleFileChange", "raw", "submitFileForm", "_this4", "importMemberWord", "$alert", "msg", "dangerouslyUseHTMLString", "_this5", "serialNumber", "taskType", "performanceTask", "targetMeasures", "evaluationStandard", "responsibilityCategory", "<PERSON><PERSON><PERSON><PERSON>", "completionDeadline", "evaluationScore", "$nextTick", "resetFields", "computed", "totalScore", "reduce", "sum", "parseFloat", "toFixed"], "sources": ["src/views/evaluation/member/index.vue"], "sourcesContent": ["<script>\nimport { listMemberOrganization, addMemberOrganization, updateMemberOrganization, delMemberOrganization, exportMemberWord, downloadMemberTemplate, importMemberWord } from '@/api/performance/memberOrganization'\n\nexport default {\n  name: 'MemberOrganizationEvaluation',\n  data() {\n    return {\n      loading: false,\n      ids: [],\n      single: true,\n      multiple: true,\n      total: 0,\n      tableData: [],\n      showSearch: true,\n      queryParams: {\n        memberName: '',\n        testTime: ''\n      },\n      form: {},\n      open: false,\n      title: '',\n      rules: {\n        memberName: [\n          { required: true, message: '姓名不能为空', trigger: 'blur' }\n        ],\n        testTime: [\n          { required: true, message: '测评时间不能为空', trigger: 'blur' }\n        ]\n      },\n      upload: {\n        open: false,\n        title: '',\n        isUploading: false,\n        file: null\n      }\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    getList() {\n      this.loading = true\n      listMemberOrganization(this.queryParams).then(res => {\n        this.tableData = res.rows\n        this.total = res.total\n        this.loading = false\n      })\n    },\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length !== 1\n      this.multiple = !selection.length\n    },\n    handleAdd() {\n      this.reset()\n      this.open = true\n      this.title = '新增绩效任务'\n    },\n    handleUpdate(row) {\n      this.reset()\n      this.form = Object.assign({}, row)\n      this.open = true\n      this.title = '修改绩效任务'\n    },\n    submitForm() {\n      this.$refs['form'].validate(valid => {\n        if (valid) {\n          if (this.form.id) {\n            updateMemberOrganization(this.form).then(() => {\n              this.$modal.msgSuccess('修改成功')\n              this.open = false\n              this.getList()\n            })\n          } else {\n            addMemberOrganization(this.form).then(() => {\n              this.$modal.msgSuccess('新增成功')\n              this.open = false\n              this.getList()\n            })\n          }\n        }\n      })\n    },\n    handleDelete(row) {\n      const ids = row.id || this.ids\n      this.$modal.confirm('是否确认删除选中数据项？').then(() => {\n        return delMemberOrganization(ids)\n      }).then(() => {\n        this.getList()\n        this.$modal.msgSuccess('删除成功')\n      }).catch(() => {})\n    },\n    handleExport() {\n      // 校验姓名和测评时间是否有值\n      if (!this.queryParams.memberName || !this.queryParams.memberName.trim()) {\n        this.$message.error('请先输入姓名再导出')\n        return\n      }\n      if (!this.queryParams.testTime || !this.queryParams.testTime.trim()) {\n        this.$message.error('请先输入测评时间再导出')\n        return\n      }\n\n      // 校验是否选择了数据\n      if (!this.ids || this.ids.length === 0) {\n        this.$message.error('请先选择要导出的数据（勾选表格左侧的复选框）')\n        return\n      }\n\n      exportMemberWord(this.queryParams, this.ids).then(res => {\n        const blob = new Blob([res], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })\n        const link = document.createElement('a')\n        link.href = window.URL.createObjectURL(blob)\n        link.download = '班子成员组织绩效考核测评表.docx'\n        link.click()\n        window.URL.revokeObjectURL(link.href)\n      })\n    },\n    handleDownloadTemplate() {\n      downloadMemberTemplate().then(res => {\n        const blob = new Blob([res], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })\n        const link = document.createElement('a')\n        link.href = window.URL.createObjectURL(blob)\n        link.download = '班子成员组织绩效考核测评表模板.docx'\n        link.click()\n        window.URL.revokeObjectURL(link.href)\n      })\n    },\n    handleImport() {\n      this.upload.open = true\n      this.upload.title = '导入班子成员组织绩效考核测评表数据'\n      this.upload.file = null\n    },\n    handleFileChange(file) {\n      this.upload.file = file.raw\n    },\n    submitFileForm() {\n      if (!this.upload.file) {\n        this.$message.error('请先选择文件')\n        return\n      }\n      this.upload.isUploading = true\n      importMemberWord(this.upload.file).then(res => {\n        this.upload.open = false\n        this.upload.isUploading = false\n        this.$alert(res.msg, '导入结果', { dangerouslyUseHTMLString: true })\n        this.getList()\n      }).catch(() => {\n        this.upload.isUploading = false\n      })\n    },\n    reset() {\n      this.form = {\n        id: null,\n        memberName: '',\n        testTime: '',\n        serialNumber: null,\n        taskType: '',\n        performanceTask: '',\n        targetMeasures: '',\n        evaluationStandard: '',\n        responsibilityCategory: '',\n        responsibleLeader: '',\n        completionDeadline: '',\n        evaluationScore: null,\n      }\n      this.$nextTick(() => {\n        if (this.$refs['form']) this.$refs['form'].resetFields()\n      })\n    }\n  },\n  computed: {\n    // 自动计算合计分值\n    totalScore() {\n      return this.tableData.reduce((sum, row) => sum + (parseFloat(row.evaluationScore) || 0), 0).toFixed(2)\n    }\n  }\n}\n</script>\n\n<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" class=\"search-form\">\n      <el-form-item label=\"姓名\" prop=\"memberName\">\n        <el-input v-model=\"queryParams.memberName\" placeholder=\"请输入姓名\" clearable />\n      </el-form-item>\n      <el-form-item label=\"测评时间\" prop=\"testTime\">\n        <el-input v-model=\"queryParams.testTime\" placeholder=\"请输入测评时间\" clearable />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"getList\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" @click=\"reset\">重置</el-button>\n      </el-form-item>\n    </el-form>\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" @click=\"handleAdd\">新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"success\" plain icon=\"el-icon-edit\" :disabled=\"single\" @click=\"handleUpdate\">修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"danger\" plain icon=\"el-icon-delete\" :disabled=\"multiple\" @click=\"handleDelete\">删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"info\" plain icon=\"el-icon-download\" @click=\"handleExport\">导出Word</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"info\" plain icon=\"el-icon-download\" @click=\"handleDownloadTemplate\">下载模板</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"warning\" plain icon=\"el-icon-upload\" @click=\"handleImport\">导入</el-button>\n      </el-col>\n    </el-row>\n    <el-table v-loading=\"loading\" :data=\"tableData\" @selection-change=\"handleSelectionChange\" border>\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"序号\" align=\"center\" prop=\"serialNumber\" width=\"60\" />\n      <el-table-column label=\"任务类型\" align=\"center\" prop=\"taskType\" />\n      <el-table-column label=\"绩效任务\" align=\"center\" prop=\"performanceTask\" />\n      <el-table-column label=\"目标及措施\" align=\"center\" prop=\"targetMeasures\" />\n      <el-table-column label=\"评价标准\" align=\"center\" prop=\"evaluationStandard\" />\n      <el-table-column label=\"责任分类\" align=\"center\" prop=\"responsibilityCategory\" />\n      <el-table-column label=\"责任领导\" align=\"center\" prop=\"responsibleLeader\" />\n      <el-table-column label=\"完成时限\" align=\"center\" prop=\"completionDeadline\" />\n      <el-table-column label=\"评价分值\" align=\"center\" prop=\"evaluationScore\" />\n    </el-table>\n    <pagination v-show=\"total>0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\" @pagination=\"getList\" />\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"姓名\" prop=\"memberName\">\n              <el-input v-model=\"form.memberName\" placeholder=\"请输入姓名\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"测评时间\" prop=\"testTime\">\n              <el-input v-model=\"form.testTime\" placeholder=\"请输入测评时间\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"序号\" prop=\"serialNumber\">\n              <el-input-number v-model=\"form.serialNumber\" :min=\"1\" :max=\"999\" controls-position=\"right\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"任务类型\" prop=\"taskType\">\n              <el-input v-model=\"form.taskType\" placeholder=\"请输入任务类型\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-form-item label=\"绩效任务\" prop=\"performanceTask\">\n          <el-input v-model=\"form.performanceTask\" placeholder=\"请输入绩效任务\" />\n        </el-form-item>\n        <el-form-item label=\"目标及措施\" prop=\"targetMeasures\">\n          <el-input v-model=\"form.targetMeasures\" placeholder=\"请输入目标及措施\" />\n        </el-form-item>\n        <el-form-item label=\"评价标准\" prop=\"evaluationStandard\">\n          <el-input v-model=\"form.evaluationStandard\" placeholder=\"请输入评价标准\" />\n        </el-form-item>\n        <el-form-item label=\"责任分类\" prop=\"responsibilityCategory\">\n          <el-input v-model=\"form.responsibilityCategory\" placeholder=\"请输入责任分类\" />\n        </el-form-item>\n        <el-form-item label=\"责任领导\" prop=\"responsibleLeader\">\n          <el-input v-model=\"form.responsibleLeader\" placeholder=\"请输入责任领导\" />\n        </el-form-item>\n        <el-form-item label=\"完成时限\" prop=\"completionDeadline\">\n          <el-input v-model=\"form.completionDeadline\" placeholder=\"请输入完成时限\" />\n        </el-form-item>\n        <el-form-item label=\"评价分值\" prop=\"evaluationScore\">\n          <el-input-number v-model=\"form.evaluationScore\" :precision=\"2\" :step=\"0.1\" :min=\"0\" :max=\"100\" controls-position=\"right\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"open = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n    <el-dialog :title=\"upload.title\" :visible.sync=\"upload.open\" width=\"400px\" append-to-body>\n      <el-upload\n        :limit=\"1\"\n        accept=\".docx\"\n        :auto-upload=\"false\"\n        :disabled=\"upload.isUploading\"\n        :on-change=\"handleFileChange\"\n        drag\n      >\n        <i class=\"el-icon-upload\"></i>\n        <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\n        <div class=\"el-upload__tip\" slot=\"tip\">只能上传docx文件，且不超过10MB</div>\n      </el-upload>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" :loading=\"upload.isUploading\" @click=\"submitFileForm\">确 定</el-button>\n        <el-button @click=\"upload.open = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<style scoped lang=\"scss\">\n.search-form {\n  margin-bottom: 20px;\n}\n.mb8 {\n  margin-bottom: 8px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AACA,IAAAA,mBAAA,GAAAC,OAAA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,GAAA;MACAC,MAAA;MACAC,QAAA;MACAC,KAAA;MACAC,SAAA;MACAC,UAAA;MACAC,WAAA;QACAC,UAAA;QACAC,QAAA;MACA;MACAC,IAAA;MACAC,IAAA;MACAC,KAAA;MACAC,KAAA;QACAL,UAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAP,QAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,MAAA;QACAN,IAAA;QACAC,KAAA;QACAM,WAAA;QACAC,IAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAvB,OAAA;MACA,IAAAwB,0CAAA,OAAAjB,WAAA,EAAAkB,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAAlB,SAAA,GAAAqB,GAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAnB,KAAA,GAAAsB,GAAA,CAAAtB,KAAA;QACAmB,KAAA,CAAAvB,OAAA;MACA;IACA;IACA4B,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA5B,GAAA,GAAA4B,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,EAAA;MAAA;MACA,KAAA9B,MAAA,GAAA2B,SAAA,CAAAI,MAAA;MACA,KAAA9B,QAAA,IAAA0B,SAAA,CAAAI,MAAA;IACA;IACAC,SAAA,WAAAA,UAAA;MACA,KAAAC,KAAA;MACA,KAAAxB,IAAA;MACA,KAAAC,KAAA;IACA;IACAwB,YAAA,WAAAA,aAAAC,GAAA;MACA,KAAAF,KAAA;MACA,KAAAzB,IAAA,GAAA4B,MAAA,CAAAC,MAAA,KAAAF,GAAA;MACA,KAAA1B,IAAA;MACA,KAAAC,KAAA;IACA;IACA4B,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAA/B,IAAA,CAAAsB,EAAA;YACA,IAAAa,4CAAA,EAAAJ,MAAA,CAAA/B,IAAA,EAAAe,IAAA;cACAgB,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAA9B,IAAA;cACA8B,MAAA,CAAApB,OAAA;YACA;UACA;YACA,IAAA2B,yCAAA,EAAAP,MAAA,CAAA/B,IAAA,EAAAe,IAAA;cACAgB,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAA9B,IAAA;cACA8B,MAAA,CAAApB,OAAA;YACA;UACA;QACA;MACA;IACA;IACA4B,YAAA,WAAAA,aAAAZ,GAAA;MAAA,IAAAa,MAAA;MACA,IAAAjD,GAAA,GAAAoC,GAAA,CAAAL,EAAA,SAAA/B,GAAA;MACA,KAAA6C,MAAA,CAAAK,OAAA,iBAAA1B,IAAA;QACA,WAAA2B,yCAAA,EAAAnD,GAAA;MACA,GAAAwB,IAAA;QACAyB,MAAA,CAAA7B,OAAA;QACA6B,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAM,KAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA;MACA,UAAA/C,WAAA,CAAAC,UAAA,UAAAD,WAAA,CAAAC,UAAA,CAAA+C,IAAA;QACA,KAAAC,QAAA,CAAAC,KAAA;QACA;MACA;MACA,UAAAlD,WAAA,CAAAE,QAAA,UAAAF,WAAA,CAAAE,QAAA,CAAA8C,IAAA;QACA,KAAAC,QAAA,CAAAC,KAAA;QACA;MACA;;MAEA;MACA,UAAAxD,GAAA,SAAAA,GAAA,CAAAgC,MAAA;QACA,KAAAuB,QAAA,CAAAC,KAAA;QACA;MACA;MAEA,IAAAC,oCAAA,OAAAnD,WAAA,OAAAN,GAAA,EAAAwB,IAAA,WAAAC,GAAA;QACA,IAAAiC,IAAA,OAAAC,IAAA,EAAAlC,GAAA;UAAAmC,IAAA;QAAA;QACA,IAAAC,IAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,IAAA,CAAAG,IAAA,GAAAC,MAAA,CAAAC,GAAA,CAAAC,eAAA,CAAAT,IAAA;QACAG,IAAA,CAAAO,QAAA;QACAP,IAAA,CAAAQ,KAAA;QACAJ,MAAA,CAAAC,GAAA,CAAAI,eAAA,CAAAT,IAAA,CAAAG,IAAA;MACA;IACA;IACAO,sBAAA,WAAAA,uBAAA;MACA,IAAAC,0CAAA,IAAAhD,IAAA,WAAAC,GAAA;QACA,IAAAiC,IAAA,OAAAC,IAAA,EAAAlC,GAAA;UAAAmC,IAAA;QAAA;QACA,IAAAC,IAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,IAAA,CAAAG,IAAA,GAAAC,MAAA,CAAAC,GAAA,CAAAC,eAAA,CAAAT,IAAA;QACAG,IAAA,CAAAO,QAAA;QACAP,IAAA,CAAAQ,KAAA;QACAJ,MAAA,CAAAC,GAAA,CAAAI,eAAA,CAAAT,IAAA,CAAAG,IAAA;MACA;IACA;IACAS,YAAA,WAAAA,aAAA;MACA,KAAAzD,MAAA,CAAAN,IAAA;MACA,KAAAM,MAAA,CAAAL,KAAA;MACA,KAAAK,MAAA,CAAAE,IAAA;IACA;IACAwD,gBAAA,WAAAA,iBAAAxD,IAAA;MACA,KAAAF,MAAA,CAAAE,IAAA,GAAAA,IAAA,CAAAyD,GAAA;IACA;IACAC,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,UAAA7D,MAAA,CAAAE,IAAA;QACA,KAAAqC,QAAA,CAAAC,KAAA;QACA;MACA;MACA,KAAAxC,MAAA,CAAAC,WAAA;MACA,IAAA6D,oCAAA,OAAA9D,MAAA,CAAAE,IAAA,EAAAM,IAAA,WAAAC,GAAA;QACAoD,MAAA,CAAA7D,MAAA,CAAAN,IAAA;QACAmE,MAAA,CAAA7D,MAAA,CAAAC,WAAA;QACA4D,MAAA,CAAAE,MAAA,CAAAtD,GAAA,CAAAuD,GAAA;UAAAC,wBAAA;QAAA;QACAJ,MAAA,CAAAzD,OAAA;MACA,GAAAgC,KAAA;QACAyB,MAAA,CAAA7D,MAAA,CAAAC,WAAA;MACA;IACA;IACAiB,KAAA,WAAAA,MAAA;MAAA,IAAAgD,MAAA;MACA,KAAAzE,IAAA;QACAsB,EAAA;QACAxB,UAAA;QACAC,QAAA;QACA2E,YAAA;QACAC,QAAA;QACAC,eAAA;QACAC,cAAA;QACAC,kBAAA;QACAC,sBAAA;QACAC,iBAAA;QACAC,kBAAA;QACAC,eAAA;MACA;MACA,KAAAC,SAAA;QACA,IAAAV,MAAA,CAAAzC,KAAA,UAAAyC,MAAA,CAAAzC,KAAA,SAAAoD,WAAA;MACA;IACA;EACA;EACAC,QAAA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,YAAA3F,SAAA,CAAA4F,MAAA,WAAAC,GAAA,EAAA7D,GAAA;QAAA,OAAA6D,GAAA,IAAAC,UAAA,CAAA9D,GAAA,CAAAuD,eAAA;MAAA,MAAAQ,OAAA;IACA;EACA;AACA", "ignoreList": []}]}