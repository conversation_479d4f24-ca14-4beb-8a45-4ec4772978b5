{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/system/role.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/system/role.js", "mtime": 1753510684517}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listRole", "query", "request", "url", "method", "params", "getRole", "roleId", "addRole", "data", "updateRole", "dataScope", "changeRoleStatus", "status", "delRole", "allocatedUserList", "unallocatedUserList", "authUserCancel", "authUserCancelAll", "authUserSelectAll", "deptTreeSelect"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/system/role.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询角色列表\nexport function listRole(query) {\n  return request({\n    url: '/system/role/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询角色详细\nexport function getRole(roleId) {\n  return request({\n    url: '/system/role/' + roleId,\n    method: 'get'\n  })\n}\n\n// 新增角色\nexport function addRole(data) {\n  return request({\n    url: '/system/role',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改角色\nexport function updateRole(data) {\n  return request({\n    url: '/system/role',\n    method: 'put',\n    data: data\n  })\n}\n\n// 角色数据权限\nexport function dataScope(data) {\n  return request({\n    url: '/system/role/dataScope',\n    method: 'put',\n    data: data\n  })\n}\n\n// 角色状态修改\nexport function changeRoleStatus(roleId, status) {\n  const data = {\n    roleId,\n    status\n  }\n  return request({\n    url: '/system/role/changeStatus',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除角色\nexport function delRole(roleId) {\n  return request({\n    url: '/system/role/' + roleId,\n    method: 'delete'\n  })\n}\n\n// 查询角色已授权用户列表\nexport function allocatedUserList(query) {\n  return request({\n    url: '/system/role/authUser/allocatedList',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询角色未授权用户列表\nexport function unallocatedUserList(query) {\n  return request({\n    url: '/system/role/authUser/unallocatedList',\n    method: 'get',\n    params: query\n  })\n}\n\n// 取消用户授权角色\nexport function authUserCancel(data) {\n  return request({\n    url: '/system/role/authUser/cancel',\n    method: 'put',\n    data: data\n  })\n}\n\n// 批量取消用户授权角色\nexport function authUserCancelAll(data) {\n  return request({\n    url: '/system/role/authUser/cancelAll',\n    method: 'put',\n    params: data\n  })\n}\n\n// 授权用户选择\nexport function authUserSelectAll(data) {\n  return request({\n    url: '/system/role/authUser/selectAll',\n    method: 'put',\n    params: data\n  })\n}\n\n// 根据角色ID查询部门树结构\nexport function deptTreeSelect(roleId) {\n  return request({\n    url: '/system/role/deptTree/' + roleId,\n    method: 'get'\n  })\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,OAAOA,CAACC,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,SAASA,CAACF,IAAI,EAAE;EAC9B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASG,gBAAgBA,CAACL,MAAM,EAAEM,MAAM,EAAE;EAC/C,IAAMJ,IAAI,GAAG;IACXF,MAAM,EAANA,MAAM;IACNM,MAAM,EAANA;EACF,CAAC;EACD,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,OAAOA,CAACP,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASW,iBAAiBA,CAACd,KAAK,EAAE;EACvC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,qCAAqC;IAC1CC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASe,mBAAmBA,CAACf,KAAK,EAAE;EACzC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,uCAAuC;IAC5CC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASgB,cAAcA,CAACR,IAAI,EAAE;EACnC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,iBAAiBA,CAACT,IAAI,EAAE;EACtC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEI;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,iBAAiBA,CAACV,IAAI,EAAE;EACtC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEI;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASW,cAAcA,CAACb,MAAM,EAAE;EACrC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB,GAAGI,MAAM;IACtCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}