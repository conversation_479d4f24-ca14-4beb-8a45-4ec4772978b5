{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/utils/dict/DictData.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/utils/dict/DictData.js", "mtime": 1753510684531}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCIvVXNlcnMvbWF6aWhhby9EZXNrdG9wL2Rldi9wZXJmb3JtYW5jZS9wZXJmb3JtYW5jZS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKdmFyIF9jcmVhdGVDbGFzczIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi9Vc2Vycy9tYXppaGFvL0Rlc2t0b3AvZGV2L3BlcmZvcm1hbmNlL3BlcmZvcm1hbmNlLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2NyZWF0ZUNsYXNzLmpzIikpOwp2YXIgX2NsYXNzQ2FsbENoZWNrMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiL1VzZXJzL21hemloYW8vRGVza3RvcC9kZXYvcGVyZm9ybWFuY2UvcGVyZm9ybWFuY2UtdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvY2xhc3NDYWxsQ2hlY2suanMiKSk7Ci8qKgogKiBAY2xhc3NkZXNjIOWtl+WFuOaVsOaNrgogKiBAcHJvcGVydHkge1N0cmluZ30gbGFiZWwg5qCH562+CiAqIEBwcm9wZXJ0eSB7Kn0gdmFsdWUg5qCH562+CiAqIEBwcm9wZXJ0eSB7T2JqZWN0fSByYXcg5Y6f5aeL5pWw5o2uCiAqLwp2YXIgRGljdERhdGEgPSBleHBvcnRzLmRlZmF1bHQgPSAvKiNfX1BVUkVfXyovKDAsIF9jcmVhdGVDbGFzczIuZGVmYXVsdCkoZnVuY3Rpb24gRGljdERhdGEobGFiZWwsIHZhbHVlLCByYXcpIHsKICAoMCwgX2NsYXNzQ2FsbENoZWNrMi5kZWZhdWx0KSh0aGlzLCBEaWN0RGF0YSk7CiAgdGhpcy5sYWJlbCA9IGxhYmVsOwogIHRoaXMudmFsdWUgPSB2YWx1ZTsKICB0aGlzLnJhdyA9IHJhdzsKfSk7"}, {"version": 3, "names": ["DictData", "exports", "default", "_createClass2", "label", "value", "raw", "_classCallCheck2"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/utils/dict/DictData.js"], "sourcesContent": ["/**\n * @classdesc 字典数据\n * @property {String} label 标签\n * @property {*} value 标签\n * @property {Object} raw 原始数据\n */\nexport default class DictData {\n  constructor(label, value, raw) {\n    this.label = label\n    this.value = value\n    this.raw = raw\n  }\n}\n"], "mappings": ";;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AALA,IAMqBA,QAAQ,GAAAC,OAAA,CAAAC,OAAA,oBAAAC,aAAA,CAAAD,OAAA,EAC3B,SAAAF,SAAYI,KAAK,EAAEC,KAAK,EAAEC,GAAG,EAAE;EAAA,IAAAC,gBAAA,CAAAL,OAAA,QAAAF,QAAA;EAC7B,IAAI,CAACI,KAAK,GAAGA,KAAK;EAClB,IAAI,CAACC,KAAK,GAAGA,KAAK;EAClB,IAAI,CAACC,GAAG,GAAGA,GAAG;AAChB,CAAC", "ignoreList": []}]}