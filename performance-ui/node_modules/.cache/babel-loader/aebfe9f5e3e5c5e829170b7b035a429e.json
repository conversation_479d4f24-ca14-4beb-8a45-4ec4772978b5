{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/router/index.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/router/index.js", "mtime": 1753510684530}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_vue", "_interopRequireDefault", "require", "_vueR<PERSON>er", "_layout", "<PERSON><PERSON>", "use", "Router", "constantRoutes", "exports", "path", "component", "Layout", "hidden", "children", "Promise", "resolve", "then", "_interopRequireWildcard2", "default", "redirect", "name", "meta", "title", "icon", "affix", "dynamicRoutes", "permissions", "activeMenu", "routerPush", "prototype", "push", "routerReplace", "replace", "location", "call", "catch", "err", "_default", "mode", "scroll<PERSON>eh<PERSON>or", "y", "routes"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/router/index.js"], "sourcesContent": ["import Vue from 'vue'\nimport Router from 'vue-router'\n\nVue.use(Router)\n\n/* Layout */\nimport Layout from '@/layout'\n\n/**\n * Note: 路由配置项\n *\n * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1\n * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面\n *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面\n *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由\n *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由\n * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击\n * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题\n * query: '{\"id\": 1, \"name\": \"ry\"}' // 访问路由的默认传递参数\n * roles: ['admin', 'common']       // 访问路由的角色权限\n * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限\n * meta : {\n    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)\n    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字\n    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg\n    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示\n    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。\n  }\n */\n\n// 公共路由\nexport const constantRoutes = [\n  {\n    path: '/redirect',\n    component: Layout,\n    hidden: true,\n    children: [\n      {\n        path: '/redirect/:path(.*)',\n        component: () => import('@/views/redirect')\n      }\n    ]\n  },\n  {\n    path: '/login',\n    component: () => import('@/views/login'),\n    hidden: true\n  },\n  {\n    path: '/register',\n    component: () => import('@/views/register'),\n    hidden: true\n  },\n  {\n    path: '/404',\n    component: () => import('@/views/error/404'),\n    hidden: true\n  },\n  {\n    path: '/401',\n    component: () => import('@/views/error/401'),\n    hidden: true\n  },\n  {\n    path: '/user',\n    component: Layout,\n    hidden: true,\n    redirect: 'noredirect',\n    children: [\n      {\n        path: 'profile',\n        component: () => import('@/views/system/user/profile/index'),\n        name: 'Profile',\n        meta: { title: '个人中心', icon: 'user' }\n      }\n    ]\n  },\n  {\n    path: '/',\n    component: Layout,\n    redirect: '/index',\n    children: [\n      {\n        path: 'index',\n        component: () => import('@/views/index'),\n        name: 'Index',\n        meta: { title: '首页', icon: 'dashboard', affix: true }\n      }\n    ]\n  },\n  {\n    path: '/system',\n    component: Layout,\n    meta: { title: '系统管理', icon: 'system' },\n    children: [\n      {\n        path: 'user',\n        component: () => import('@/views/system/user/index'),\n        name: 'User',\n        meta: { title: '用户管理', icon: 'user' }\n      },\n      {\n        path: 'role',\n        component: () => import('@/views/system/role/index'),\n        name: 'Role',\n        meta: { title: '角色管理', icon: 'peoples' }\n      },\n      {\n        path: 'menu',\n        component: () => import('@/views/system/menu/index'),\n        name: 'Menu',\n        meta: { title: '菜单管理', icon: 'tree-table' }\n      },\n      {\n        path: 'dept',\n        component: () => import('@/views/system/dept/index'),\n        name: 'Dept',\n        meta: { title: '部门管理', icon: 'tree' }\n      },\n      {\n        path: 'post',\n        component: () => import('@/views/system/post/index'),\n        name: 'Post',\n        meta: { title: '岗位管理', icon: 'post' }\n      }\n    ]\n  },\n  {\n    path: '/monitor',\n    component: Layout,\n    meta: { title: '系统监控', icon: 'monitor' },\n    children: [\n      {\n        path: 'online',\n        component: () => import('@/views/monitor/online/index'),\n        name: 'Online',\n        meta: { title: '在线用户', icon: 'online' }\n      }\n    ]\n  },\n  // 404 page must be placed at the end !!!\n  { path: '*', redirect: '/404', hidden: true }\n]\n\n// 动态路由，基于用户权限动态去加载\nexport const dynamicRoutes = [\n  {\n    path: '/system/user-auth',\n    component: Layout,\n    hidden: true,\n    permissions: ['system:user:edit'],\n    children: [\n      {\n        path: 'role/:userId(\\\\d+)',\n        component: () => import('@/views/system/user/authRole'),\n        name: 'AuthRole',\n        meta: { title: '分配角色', activeMenu: '/system/user' }\n      }\n    ]\n  },\n  {\n    path: '/system/role-auth',\n    component: Layout,\n    hidden: true,\n    permissions: ['system:role:edit'],\n    children: [\n      {\n        path: 'user/:roleId(\\\\d+)',\n        component: () => import('@/views/system/role/authUser'),\n        name: 'AuthUser',\n        meta: { title: '分配用户', activeMenu: '/system/role' }\n      }\n    ]\n  }\n]\n\n// 防止连续点击多次路由报错\nlet routerPush = Router.prototype.push\nlet routerReplace = Router.prototype.replace\n// push\nRouter.prototype.push = function push(location) {\n  return routerPush.call(this, location).catch(err => err)\n}\n// replace\nRouter.prototype.replace = function push(location) {\n  return routerReplace.call(this, location).catch(err => err)\n}\n\nexport default new Router({\n  mode: 'history', // 去掉url中的#\n  scrollBehavior: () => ({ y: 0 }),\n  routes: constantRoutes\n})\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,UAAA,GAAAF,sBAAA,CAAAC,OAAA;AAKA,IAAAE,OAAA,GAAAH,sBAAA,CAAAC,OAAA;AAHAG,YAAG,CAACC,GAAG,CAACC,kBAAM,CAAC;;AAEf;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACO,IAAMC,cAAc,GAAAC,OAAA,CAAAD,cAAA,GAAG,CAC5B;EACEE,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZC,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,qBAAqB;IAC3BC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,kBAAkB;MAAA;IAAA;EAC5C,CAAC;AAEL,CAAC,EACD;EACEQ,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,eAAe;IAAA;EAAA,CAAC;EACxCW,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,kBAAkB;IAAA;EAAA,CAAC;EAC3CW,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,mBAAmB;IAAA;EAAA,CAAC;EAC5CW,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,mBAAmB;IAAA;EAAA,CAAC;EAC5CW,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZO,QAAQ,EAAE,YAAY;EACtBN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,mCAAmC;MAAA;IAAA,CAAC;IAC5DmB,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAO;EACtC,CAAC;AAEL,CAAC,EACD;EACEd,IAAI,EAAE,GAAG;EACTC,SAAS,EAAEC,eAAM;EACjBQ,QAAQ,EAAE,QAAQ;EAClBN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,eAAe;MAAA;IAAA,CAAC;IACxCmB,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAK;EACtD,CAAC;AAEL,CAAC,EACD;EACEf,IAAI,EAAE,SAAS;EACfC,SAAS,EAAEC,eAAM;EACjBU,IAAI,EAAE;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE;EAAS,CAAC;EACvCV,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,2BAA2B;MAAA;IAAA,CAAC;IACpDmB,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAO;EACtC,CAAC,EACD;IACEd,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,2BAA2B;MAAA;IAAA,CAAC;IACpDmB,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAU;EACzC,CAAC,EACD;IACEd,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,2BAA2B;MAAA;IAAA,CAAC;IACpDmB,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAa;EAC5C,CAAC,EACD;IACEd,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,2BAA2B;MAAA;IAAA,CAAC;IACpDmB,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAO;EACtC,CAAC,EACD;IACEd,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,2BAA2B;MAAA;IAAA,CAAC;IACpDmB,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAO;EACtC,CAAC;AAEL,CAAC,EACD;EACEd,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEC,eAAM;EACjBU,IAAI,EAAE;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE;EAAU,CAAC;EACxCV,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,8BAA8B;MAAA;IAAA,CAAC;IACvDmB,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAS;EACxC,CAAC;AAEL,CAAC;AACD;AACA;EAAEd,IAAI,EAAE,GAAG;EAAEU,QAAQ,EAAE,MAAM;EAAEP,MAAM,EAAE;AAAK,CAAC,CAC9C;;AAED;AACO,IAAMa,aAAa,GAAAjB,OAAA,CAAAiB,aAAA,GAAG,CAC3B;EACEhB,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZc,WAAW,EAAE,CAAC,kBAAkB,CAAC;EACjCb,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,oBAAoB;IAC1BC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,8BAA8B;MAAA;IAAA,CAAC;IACvDmB,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEK,UAAU,EAAE;IAAe;EACpD,CAAC;AAEL,CAAC,EACD;EACElB,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZc,WAAW,EAAE,CAAC,kBAAkB,CAAC;EACjCb,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,oBAAoB;IAC1BC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,8BAA8B;MAAA;IAAA,CAAC;IACvDmB,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEK,UAAU,EAAE;IAAe;EACpD,CAAC;AAEL,CAAC,CACF;;AAED;AACA,IAAIC,UAAU,GAAGtB,kBAAM,CAACuB,SAAS,CAACC,IAAI;AACtC,IAAIC,aAAa,GAAGzB,kBAAM,CAACuB,SAAS,CAACG,OAAO;AAC5C;AACA1B,kBAAM,CAACuB,SAAS,CAACC,IAAI,GAAG,SAASA,IAAIA,CAACG,QAAQ,EAAE;EAC9C,OAAOL,UAAU,CAACM,IAAI,CAAC,IAAI,EAAED,QAAQ,CAAC,CAACE,KAAK,CAAC,UAAAC,GAAG;IAAA,OAAIA,GAAG;EAAA,EAAC;AAC1D,CAAC;AACD;AACA9B,kBAAM,CAACuB,SAAS,CAACG,OAAO,GAAG,SAASF,IAAIA,CAACG,QAAQ,EAAE;EACjD,OAAOF,aAAa,CAACG,IAAI,CAAC,IAAI,EAAED,QAAQ,CAAC,CAACE,KAAK,CAAC,UAAAC,GAAG;IAAA,OAAIA,GAAG;EAAA,EAAC;AAC7D,CAAC;AAAA,IAAAC,QAAA,GAAA7B,OAAA,CAAAU,OAAA,GAEc,IAAIZ,kBAAM,CAAC;EACxBgC,IAAI,EAAE,SAAS;EAAE;EACjBC,cAAc,EAAE,SAAhBA,cAAcA,CAAA;IAAA,OAAS;MAAEC,CAAC,EAAE;IAAE,CAAC;EAAA,CAAC;EAChCC,MAAM,EAAElC;AACV,CAAC,CAAC", "ignoreList": []}]}