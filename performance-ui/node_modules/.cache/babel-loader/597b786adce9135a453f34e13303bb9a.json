{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/TopNav/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/TopNav/index.vue", "mtime": 1753510684528}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_router", "require", "_validate", "hideList", "_default", "exports", "default", "data", "visibleNumber", "currentIndex", "undefined", "computed", "theme", "$store", "state", "settings", "topMenus", "routers", "map", "menu", "hidden", "path", "children", "push", "permission", "topbarRouters", "childrenMenus", "router", "item", "parentPath", "isHttp", "constantRoutes", "concat", "activeMenu", "$route", "activePath", "lastIndexOf", "indexOf", "tmpPath", "substring", "length", "meta", "link", "dispatch", "activeRoutes", "beforeMount", "window", "addEventListener", "setVisibleNumber", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "mounted", "methods", "width", "document", "body", "getBoundingClientRect", "parseInt", "handleSelect", "key", "keyP<PERSON>", "route", "find", "open", "routeMenu", "query", "JSON", "parse", "$router", "routes", "commit"], "sources": ["src/components/TopNav/index.vue"], "sourcesContent": ["<template>\n  <el-menu\n    :default-active=\"activeMenu\"\n    mode=\"horizontal\"\n    @select=\"handleSelect\"\n  >\n    <template v-for=\"(item, index) in topMenus\">\n      <el-menu-item :style=\"{'--theme': theme}\" :index=\"item.path\" :key=\"index\" v-if=\"index < visibleNumber\">\n        <svg-icon\n        v-if=\"item.meta && item.meta.icon && item.meta.icon !== '#'\"\n        :icon-class=\"item.meta.icon\"/>\n        {{ item.meta.title }}\n      </el-menu-item>\n    </template>\n\n    <!-- 顶部菜单超出数量折叠 -->\n    <el-submenu :style=\"{'--theme': theme}\" index=\"more\" :key=\"visibleNumber\" v-if=\"topMenus.length > visibleNumber\">\n      <template slot=\"title\">更多菜单</template>\n      <template v-for=\"(item, index) in topMenus\">\n        <el-menu-item\n          :index=\"item.path\"\n          :key=\"index\"\n          v-if=\"index >= visibleNumber\">\n          <svg-icon\n            v-if=\"item.meta && item.meta.icon && item.meta.icon !== '#'\"\n            :icon-class=\"item.meta.icon\"/>\n          {{ item.meta.title }}\n        </el-menu-item>\n      </template>\n    </el-submenu>\n  </el-menu>\n</template>\n\n<script>\nimport { constantRoutes } from \"@/router\"\nimport { isHttp } from \"@/utils/validate\"\n\n// 隐藏侧边栏路由\nconst hideList = ['/index', '/user/profile']\n\nexport default {\n  data() {\n    return {\n      // 顶部栏初始数\n      visibleNumber: 5,\n      // 当前激活菜单的 index\n      currentIndex: undefined\n    }\n  },\n  computed: {\n    theme() {\n      return this.$store.state.settings.theme\n    },\n    // 顶部显示菜单\n    topMenus() {\n      let topMenus = []\n      this.routers.map((menu) => {\n        if (menu.hidden !== true) {\n          // 兼容顶部栏一级菜单内部跳转\n          if (menu.path === '/' && menu.children) {\n            topMenus.push(menu.children[0])\n          } else {\n            topMenus.push(menu)\n          }\n        }\n      })\n      return topMenus\n    },\n    // 所有的路由信息\n    routers() {\n      return this.$store.state.permission.topbarRouters\n    },\n    // 设置子路由\n    childrenMenus() {\n      var childrenMenus = []\n      this.routers.map((router) => {\n        for (var item in router.children) {\n          if (router.children[item].parentPath === undefined) {\n            if(router.path === \"/\") {\n              router.children[item].path = \"/\" + router.children[item].path\n            } else {\n              if(!isHttp(router.children[item].path)) {\n                router.children[item].path = router.path + \"/\" + router.children[item].path\n              }\n            }\n            router.children[item].parentPath = router.path\n          }\n          childrenMenus.push(router.children[item])\n        }\n      })\n      return constantRoutes.concat(childrenMenus)\n    },\n    // 默认激活的菜单\n    activeMenu() {\n      const path = this.$route.path\n      let activePath = path\n      if (path !== undefined && path.lastIndexOf(\"/\") > 0 && hideList.indexOf(path) === -1) {\n        const tmpPath = path.substring(1, path.length)\n        if (!this.$route.meta.link) {\n          activePath = \"/\" + tmpPath.substring(0, tmpPath.indexOf(\"/\"))\n          this.$store.dispatch('app/toggleSideBarHide', false)\n        }\n      } else if(!this.$route.children) {\n        activePath = path\n        this.$store.dispatch('app/toggleSideBarHide', true)\n      }\n      this.activeRoutes(activePath)\n      return activePath\n    },\n  },\n  beforeMount() {\n    window.addEventListener('resize', this.setVisibleNumber)\n  },\n  beforeDestroy() {\n    window.removeEventListener('resize', this.setVisibleNumber)\n  },\n  mounted() {\n    this.setVisibleNumber()\n  },\n  methods: {\n    // 根据宽度计算设置显示栏数\n    setVisibleNumber() {\n      const width = document.body.getBoundingClientRect().width / 3\n      this.visibleNumber = parseInt(width / 85)\n    },\n    // 菜单选择事件\n    handleSelect(key, keyPath) {\n      this.currentIndex = key\n      const route = this.routers.find(item => item.path === key)\n      if (isHttp(key)) {\n        // http(s):// 路径新窗口打开\n        window.open(key, \"_blank\")\n      } else if (!route || !route.children) {\n        // 没有子路由路径内部打开\n        const routeMenu = this.childrenMenus.find(item => item.path === key)\n        if (routeMenu && routeMenu.query) {\n          let query = JSON.parse(routeMenu.query)\n          this.$router.push({ path: key, query: query })\n        } else {\n          this.$router.push({ path: key })\n        }\n        this.$store.dispatch('app/toggleSideBarHide', true)\n      } else {\n        // 显示左侧联动菜单\n        this.activeRoutes(key)\n        this.$store.dispatch('app/toggleSideBarHide', false)\n      }\n    },\n    // 当前激活的路由\n    activeRoutes(key) {\n      var routes = []\n      if (this.childrenMenus && this.childrenMenus.length > 0) {\n        this.childrenMenus.map((item) => {\n          if (key == item.parentPath || (key == \"index\" && \"\" == item.path)) {\n            routes.push(item)\n          }\n        })\n      }\n      if(routes.length > 0) {\n        this.$store.commit(\"SET_SIDEBAR_ROUTERS\", routes)\n      } else {\n        this.$store.dispatch('app/toggleSideBarHide', true)\n      }\n    }\n  },\n}\n</script>\n\n<style lang=\"scss\">\n.topmenu-container.el-menu--horizontal > .el-menu-item {\n  float: left;\n  height: 50px !important;\n  line-height: 50px !important;\n  color: #999093 !important;\n  padding: 0 5px !important;\n  margin: 0 10px !important;\n}\n\n.topmenu-container.el-menu--horizontal > .el-menu-item.is-active, .el-menu--horizontal > .el-submenu.is-active .el-submenu__title {\n  border-bottom: 2px solid #{'var(--theme)'} !important;\n  color: #303133;\n}\n\n/* submenu item */\n.topmenu-container.el-menu--horizontal > .el-submenu .el-submenu__title {\n  float: left;\n  height: 50px !important;\n  line-height: 50px !important;\n  color: #999093 !important;\n  padding: 0 5px !important;\n  margin: 0 10px !important;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;AAkCA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA,IAAAE,QAAA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,aAAA;MACA;MACAC,YAAA,EAAAC;IACA;EACA;EACAC,QAAA;IACAC,KAAA,WAAAA,MAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAH,KAAA;IACA;IACA;IACAI,QAAA,WAAAA,SAAA;MACA,IAAAA,QAAA;MACA,KAAAC,OAAA,CAAAC,GAAA,WAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,MAAA;UACA;UACA,IAAAD,IAAA,CAAAE,IAAA,YAAAF,IAAA,CAAAG,QAAA;YACAN,QAAA,CAAAO,IAAA,CAAAJ,IAAA,CAAAG,QAAA;UACA;YACAN,QAAA,CAAAO,IAAA,CAAAJ,IAAA;UACA;QACA;MACA;MACA,OAAAH,QAAA;IACA;IACA;IACAC,OAAA,WAAAA,QAAA;MACA,YAAAJ,MAAA,CAAAC,KAAA,CAAAU,UAAA,CAAAC,aAAA;IACA;IACA;IACAC,aAAA,WAAAA,cAAA;MACA,IAAAA,aAAA;MACA,KAAAT,OAAA,CAAAC,GAAA,WAAAS,MAAA;QACA,SAAAC,IAAA,IAAAD,MAAA,CAAAL,QAAA;UACA,IAAAK,MAAA,CAAAL,QAAA,CAAAM,IAAA,EAAAC,UAAA,KAAAnB,SAAA;YACA,IAAAiB,MAAA,CAAAN,IAAA;cACAM,MAAA,CAAAL,QAAA,CAAAM,IAAA,EAAAP,IAAA,SAAAM,MAAA,CAAAL,QAAA,CAAAM,IAAA,EAAAP,IAAA;YACA;cACA,SAAAS,gBAAA,EAAAH,MAAA,CAAAL,QAAA,CAAAM,IAAA,EAAAP,IAAA;gBACAM,MAAA,CAAAL,QAAA,CAAAM,IAAA,EAAAP,IAAA,GAAAM,MAAA,CAAAN,IAAA,SAAAM,MAAA,CAAAL,QAAA,CAAAM,IAAA,EAAAP,IAAA;cACA;YACA;YACAM,MAAA,CAAAL,QAAA,CAAAM,IAAA,EAAAC,UAAA,GAAAF,MAAA,CAAAN,IAAA;UACA;UACAK,aAAA,CAAAH,IAAA,CAAAI,MAAA,CAAAL,QAAA,CAAAM,IAAA;QACA;MACA;MACA,OAAAG,sBAAA,CAAAC,MAAA,CAAAN,aAAA;IACA;IACA;IACAO,UAAA,WAAAA,WAAA;MACA,IAAAZ,IAAA,QAAAa,MAAA,CAAAb,IAAA;MACA,IAAAc,UAAA,GAAAd,IAAA;MACA,IAAAA,IAAA,KAAAX,SAAA,IAAAW,IAAA,CAAAe,WAAA,aAAAjC,QAAA,CAAAkC,OAAA,CAAAhB,IAAA;QACA,IAAAiB,OAAA,GAAAjB,IAAA,CAAAkB,SAAA,IAAAlB,IAAA,CAAAmB,MAAA;QACA,UAAAN,MAAA,CAAAO,IAAA,CAAAC,IAAA;UACAP,UAAA,SAAAG,OAAA,CAAAC,SAAA,IAAAD,OAAA,CAAAD,OAAA;UACA,KAAAxB,MAAA,CAAA8B,QAAA;QACA;MACA,iBAAAT,MAAA,CAAAZ,QAAA;QACAa,UAAA,GAAAd,IAAA;QACA,KAAAR,MAAA,CAAA8B,QAAA;MACA;MACA,KAAAC,YAAA,CAAAT,UAAA;MACA,OAAAA,UAAA;IACA;EACA;EACAU,WAAA,WAAAA,YAAA;IACAC,MAAA,CAAAC,gBAAA,gBAAAC,gBAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACAH,MAAA,CAAAI,mBAAA,gBAAAF,gBAAA;EACA;EACAG,OAAA,WAAAA,QAAA;IACA,KAAAH,gBAAA;EACA;EACAI,OAAA;IACA;IACAJ,gBAAA,WAAAA,iBAAA;MACA,IAAAK,KAAA,GAAAC,QAAA,CAAAC,IAAA,CAAAC,qBAAA,GAAAH,KAAA;MACA,KAAA7C,aAAA,GAAAiD,QAAA,CAAAJ,KAAA;IACA;IACA;IACAK,YAAA,WAAAA,aAAAC,GAAA,EAAAC,OAAA;MACA,KAAAnD,YAAA,GAAAkD,GAAA;MACA,IAAAE,KAAA,QAAA5C,OAAA,CAAA6C,IAAA,WAAAlC,IAAA;QAAA,OAAAA,IAAA,CAAAP,IAAA,KAAAsC,GAAA;MAAA;MACA,QAAA7B,gBAAA,EAAA6B,GAAA;QACA;QACAb,MAAA,CAAAiB,IAAA,CAAAJ,GAAA;MACA,YAAAE,KAAA,KAAAA,KAAA,CAAAvC,QAAA;QACA;QACA,IAAA0C,SAAA,QAAAtC,aAAA,CAAAoC,IAAA,WAAAlC,IAAA;UAAA,OAAAA,IAAA,CAAAP,IAAA,KAAAsC,GAAA;QAAA;QACA,IAAAK,SAAA,IAAAA,SAAA,CAAAC,KAAA;UACA,IAAAA,KAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAH,SAAA,CAAAC,KAAA;UACA,KAAAG,OAAA,CAAA7C,IAAA;YAAAF,IAAA,EAAAsC,GAAA;YAAAM,KAAA,EAAAA;UAAA;QACA;UACA,KAAAG,OAAA,CAAA7C,IAAA;YAAAF,IAAA,EAAAsC;UAAA;QACA;QACA,KAAA9C,MAAA,CAAA8B,QAAA;MACA;QACA;QACA,KAAAC,YAAA,CAAAe,GAAA;QACA,KAAA9C,MAAA,CAAA8B,QAAA;MACA;IACA;IACA;IACAC,YAAA,WAAAA,aAAAe,GAAA;MACA,IAAAU,MAAA;MACA,SAAA3C,aAAA,SAAAA,aAAA,CAAAc,MAAA;QACA,KAAAd,aAAA,CAAAR,GAAA,WAAAU,IAAA;UACA,IAAA+B,GAAA,IAAA/B,IAAA,CAAAC,UAAA,IAAA8B,GAAA,qBAAA/B,IAAA,CAAAP,IAAA;YACAgD,MAAA,CAAA9C,IAAA,CAAAK,IAAA;UACA;QACA;MACA;MACA,IAAAyC,MAAA,CAAA7B,MAAA;QACA,KAAA3B,MAAA,CAAAyD,MAAA,wBAAAD,MAAA;MACA;QACA,KAAAxD,MAAA,CAAA8B,QAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}