{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/layout/components/IframeToggle/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/layout/components/IframeToggle/index.vue", "mtime": 1753510684529}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCIvVXNlcnMvbWF6aWhhby9EZXNrdG9wL2Rldi9wZXJmb3JtYW5jZS9wZXJmb3JtYW5jZS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmpvaW4uanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5Lm1hcC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LmtleXMuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5jb25zdHJ1Y3Rvci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLm1hcC5qcyIpOwp2YXIgX2luZGV4ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIuLi9Jbm5lckxpbmsvaW5kZXgiKSk7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBjb21wb25lbnRzOiB7CiAgICBJbm5lckxpbms6IF9pbmRleC5kZWZhdWx0CiAgfSwKICBjb21wdXRlZDogewogICAgaWZyYW1lVmlld3M6IGZ1bmN0aW9uIGlmcmFtZVZpZXdzKCkgewogICAgICByZXR1cm4gdGhpcy4kc3RvcmUuc3RhdGUudGFnc1ZpZXcuaWZyYW1lVmlld3M7CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBpZnJhbWVVcmw6IGZ1bmN0aW9uIGlmcmFtZVVybCh1cmwsIHF1ZXJ5KSB7CiAgICAgIGlmIChPYmplY3Qua2V5cyhxdWVyeSkubGVuZ3RoID4gMCkgewogICAgICAgIHZhciBwYXJhbXMgPSBPYmplY3Qua2V5cyhxdWVyeSkubWFwKGZ1bmN0aW9uIChrZXkpIHsKICAgICAgICAgIHJldHVybiBrZXkgKyAiPSIgKyBxdWVyeVtrZXldOwogICAgICAgIH0pLmpvaW4oIiYiKTsKICAgICAgICByZXR1cm4gdXJsICsgIj8iICsgcGFyYW1zOwogICAgICB9CiAgICAgIHJldHVybiB1cmw7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["_index", "_interopRequireDefault", "require", "components", "InnerLink", "computed", "iframeViews", "$store", "state", "tagsView", "methods", "iframeUrl", "url", "query", "Object", "keys", "length", "params", "map", "key", "join"], "sources": ["src/layout/components/IframeToggle/index.vue"], "sourcesContent": ["<template>\n  <transition-group name=\"fade-transform\" mode=\"out-in\">\n    <inner-link\n      v-for=\"(item, index) in iframeViews\"\n      :key=\"item.path\"\n      :iframeId=\"'iframe' + index\"\n      v-show=\"$route.path === item.path\"\n      :src=\"iframeUrl(item.meta.link, item.query)\"\n    ></inner-link>\n  </transition-group>\n</template>\n\n<script>\nimport InnerLink from \"../InnerLink/index\"\n\nexport default {\n  components: { InnerLink },\n  computed: {\n    iframeViews() {\n      return this.$store.state.tagsView.iframeViews\n    }\n  },\n  methods: {\n    iframeUrl(url, query) {\n      if (Object.keys(query).length > 0) {\n        let params = Object.keys(query).map((key) => key + \"=\" + query[key]).join(\"&\")\n        return url + \"?\" + params\n      }\n      return url\n    }\n  }\n}\n</script>\n"], "mappings": ";;;;;;;;;;;;;AAaA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;iCAEA;EACAC,UAAA;IAAAC,SAAA,EAAAA;EAAA;EACAC,QAAA;IACAC,WAAA,WAAAA,YAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAH,WAAA;IACA;EACA;EACAI,OAAA;IACAC,SAAA,WAAAA,UAAAC,GAAA,EAAAC,KAAA;MACA,IAAAC,MAAA,CAAAC,IAAA,CAAAF,KAAA,EAAAG,MAAA;QACA,IAAAC,MAAA,GAAAH,MAAA,CAAAC,IAAA,CAAAF,KAAA,EAAAK,GAAA,WAAAC,GAAA;UAAA,OAAAA,GAAA,SAAAN,KAAA,CAAAM,GAAA;QAAA,GAAAC,IAAA;QACA,OAAAR,GAAA,SAAAK,MAAA;MACA;MACA,OAAAL,GAAA;IACA;EACA;AACA", "ignoreList": []}]}