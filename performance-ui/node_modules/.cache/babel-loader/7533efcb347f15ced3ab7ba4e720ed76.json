{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/tool/build/CodeTypeDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/tool/build/CodeTypeDialog.vue", "mtime": 1753510684536}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["inheritAttrs", "props", "data", "formData", "fileName", "undefined", "type", "rules", "required", "message", "trigger", "typeOptions", "label", "value", "computed", "watch", "mounted", "methods", "onOpen", "showFileName", "concat", "Date", "onClose", "close", "e", "$emit", "handleConfirm", "_this", "$refs", "elForm", "validate", "valid", "_objectSpread2", "default"], "sources": ["src/views/tool/build/CodeTypeDialog.vue"], "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      v-bind=\"$attrs\"\n      width=\"500px\"\n      :close-on-click-modal=\"false\"\n      :modal-append-to-body=\"false\"\n      v-on=\"$listeners\"\n      @open=\"onOpen\"\n      @close=\"onClose\"\n    >\n      <el-row :gutter=\"15\">\n        <el-form\n          ref=\"elForm\"\n          :model=\"formData\"\n          :rules=\"rules\"\n          size=\"medium\"\n          label-width=\"100px\"\n        >\n          <el-col :span=\"24\">\n            <el-form-item label=\"生成类型\" prop=\"type\">\n              <el-radio-group v-model=\"formData.type\">\n                <el-radio-button\n                  v-for=\"(item, index) in typeOptions\"\n                  :key=\"index\"\n                  :label=\"item.value\"\n                  :disabled=\"item.disabled\"\n                >\n                  {{ item.label }}\n                </el-radio-button>\n              </el-radio-group>\n            </el-form-item>\n            <el-form-item v-if=\"showFileName\" label=\"文件名\" prop=\"fileName\">\n              <el-input v-model=\"formData.fileName\" placeholder=\"请输入文件名\" clearable />\n            </el-form-item>\n          </el-col>\n        </el-form>\n      </el-row>\n\n      <div slot=\"footer\">\n        <el-button @click=\"close\">\n          取消\n        </el-button>\n        <el-button type=\"primary\" @click=\"handleConfirm\">\n          确定\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n<script>\nexport default {\n  inheritAttrs: false,\n  props: ['showFileName'],\n  data() {\n    return {\n      formData: {\n        fileName: undefined,\n        type: 'file'\n      },\n      rules: {\n        fileName: [{\n          required: true,\n          message: '请输入文件名',\n          trigger: 'blur'\n        }],\n        type: [{\n          required: true,\n          message: '生成类型不能为空',\n          trigger: 'change'\n        }]\n      },\n      typeOptions: [{\n        label: '页面',\n        value: 'file'\n      }, {\n        label: '弹窗',\n        value: 'dialog'\n      }]\n    }\n  },\n  computed: {\n  },\n  watch: {},\n  mounted() {},\n  methods: {\n    onOpen() {\n      if (this.showFileName) {\n        this.formData.fileName = `${+new Date()}.vue`\n      }\n    },\n    onClose() {\n    },\n    close(e) {\n      this.$emit('update:visible', false)\n    },\n    handleConfirm() {\n      this.$refs.elForm.validate(valid => {\n        if (!valid) return\n        this.$emit('confirm', { ...this.formData })\n        this.close()\n      })\n    }\n  }\n}\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAmDA;EACAA,YAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;QACAC,QAAA,EAAAC,SAAA;QACAC,IAAA;MACA;MACAC,KAAA;QACAH,QAAA;UACAI,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;QACAJ,IAAA;UACAE,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MACA;MACAC,WAAA;QACAC,KAAA;QACAC,KAAA;MACA;QACAD,KAAA;QACAC,KAAA;MACA;IACA;EACA;EACAC,QAAA,GACA;EACAC,KAAA;EACAC,OAAA,WAAAA,QAAA;EACAC,OAAA;IACAC,MAAA,WAAAA,OAAA;MACA,SAAAC,YAAA;QACA,KAAAhB,QAAA,CAAAC,QAAA,MAAAgB,MAAA,MAAAC,IAAA;MACA;IACA;IACAC,OAAA,WAAAA,QAAA,GACA;IACAC,KAAA,WAAAA,MAAAC,CAAA;MACA,KAAAC,KAAA;IACA;IACAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA,CAAAC,MAAA,CAAAC,QAAA,WAAAC,KAAA;QACA,KAAAA,KAAA;QACAJ,KAAA,CAAAF,KAAA,gBAAAO,cAAA,CAAAC,OAAA,MAAAN,KAAA,CAAAxB,QAAA;QACAwB,KAAA,CAAAJ,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}