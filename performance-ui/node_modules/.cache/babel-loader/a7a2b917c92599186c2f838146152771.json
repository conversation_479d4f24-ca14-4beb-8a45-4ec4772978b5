{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/tool/gen/editTable.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/tool/gen/editTable.vue", "mtime": 1753510684537}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_gen", "require", "_type", "_menu", "_basicInfoForm", "_interopRequireDefault", "_genInfoForm", "_sortablejs", "name", "components", "basicInfoForm", "genInfoForm", "data", "activeName", "tableHeight", "document", "documentElement", "scrollHeight", "tables", "columns", "dictOptions", "menus", "info", "created", "_this", "tableId", "$route", "params", "getGenTable", "then", "res", "rows", "getDictOptionselect", "response", "getMenuTreeselect", "handleTree", "methods", "submitForm", "_this2", "basicForm", "$refs", "basicInfo", "genForm", "genInfo", "Promise", "all", "map", "getFormPromise", "validateResult", "every", "item", "genTable", "Object", "assign", "model", "treeCode", "treeName", "treeParentCode", "parentMenuId", "updateGenTable", "$modal", "msgSuccess", "msg", "code", "close", "msgError", "form", "resolve", "validate", "obj", "path", "query", "t", "Date", "now", "pageNum", "$tab", "closeOpenPage", "mounted", "_this3", "el", "dragTable", "$el", "querySelectorAll", "sortable", "Sortable", "create", "handle", "onEnd", "evt", "targetRow", "splice", "oldIndex", "newIndex", "index", "sort", "parseInt"], "sources": ["src/views/tool/gen/editTable.vue"], "sourcesContent": ["<template>\n  <el-card>\n    <el-tabs v-model=\"activeName\">\n      <el-tab-pane label=\"基本信息\" name=\"basic\">\n        <basic-info-form ref=\"basicInfo\" :info=\"info\" />\n      </el-tab-pane>\n      <el-tab-pane label=\"字段信息\" name=\"columnInfo\">\n        <el-table ref=\"dragTable\" :data=\"columns\" row-key=\"columnId\" :max-height=\"tableHeight\">\n          <el-table-column label=\"序号\" type=\"index\" min-width=\"5%\" class-name=\"allowDrag\"/>\n          <el-table-column label=\"字段列名\" prop=\"columnName\" min-width=\"10%\" :show-overflow-tooltip=\"true\" class-name=\"allowDrag\"/>\n          <el-table-column label=\"字段描述\" min-width=\"10%\">\n            <template slot-scope=\"scope\">\n              <el-input v-model=\"scope.row.columnComment\"></el-input>\n            </template>\n          </el-table-column>\n          <el-table-column\n            label=\"物理类型\"\n            prop=\"columnType\"\n            min-width=\"10%\"\n            :show-overflow-tooltip=\"true\"\n          />\n          <el-table-column label=\"Java类型\" min-width=\"11%\">\n            <template slot-scope=\"scope\">\n              <el-select v-model=\"scope.row.javaType\">\n                <el-option label=\"Long\" value=\"Long\" />\n                <el-option label=\"String\" value=\"String\" />\n                <el-option label=\"Integer\" value=\"Integer\" />\n                <el-option label=\"Double\" value=\"Double\" />\n                <el-option label=\"BigDecimal\" value=\"BigDecimal\" />\n                <el-option label=\"Date\" value=\"Date\" />\n                <el-option label=\"Boolean\" value=\"Boolean\" />\n              </el-select>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"java属性\" min-width=\"10%\">\n            <template slot-scope=\"scope\">\n              <el-input v-model=\"scope.row.javaField\"></el-input>\n            </template>\n          </el-table-column>\n\n          <el-table-column label=\"插入\" min-width=\"5%\">\n            <template slot-scope=\"scope\">\n              <el-checkbox true-label=\"1\" false-label=\"0\" v-model=\"scope.row.isInsert\"></el-checkbox>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"编辑\" min-width=\"5%\">\n            <template slot-scope=\"scope\">\n              <el-checkbox true-label=\"1\" false-label=\"0\" v-model=\"scope.row.isEdit\"></el-checkbox>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"列表\" min-width=\"5%\">\n            <template slot-scope=\"scope\">\n              <el-checkbox true-label=\"1\" false-label=\"0\" v-model=\"scope.row.isList\"></el-checkbox>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"查询\" min-width=\"5%\">\n            <template slot-scope=\"scope\">\n              <el-checkbox true-label=\"1\" false-label=\"0\" v-model=\"scope.row.isQuery\"></el-checkbox>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"查询方式\" min-width=\"10%\">\n            <template slot-scope=\"scope\">\n              <el-select v-model=\"scope.row.queryType\">\n                <el-option label=\"=\" value=\"EQ\" />\n                <el-option label=\"!=\" value=\"NE\" />\n                <el-option label=\">\" value=\"GT\" />\n                <el-option label=\">=\" value=\"GTE\" />\n                <el-option label=\"<\" value=\"LT\" />\n                <el-option label=\"<=\" value=\"LTE\" />\n                <el-option label=\"LIKE\" value=\"LIKE\" />\n                <el-option label=\"BETWEEN\" value=\"BETWEEN\" />\n              </el-select>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"必填\" min-width=\"5%\">\n            <template slot-scope=\"scope\">\n              <el-checkbox true-label=\"1\" false-label=\"0\" v-model=\"scope.row.isRequired\"></el-checkbox>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"显示类型\" min-width=\"12%\">\n            <template slot-scope=\"scope\">\n              <el-select v-model=\"scope.row.htmlType\">\n                <el-option label=\"文本框\" value=\"input\" />\n                <el-option label=\"文本域\" value=\"textarea\" />\n                <el-option label=\"下拉框\" value=\"select\" />\n                <el-option label=\"单选框\" value=\"radio\" />\n                <el-option label=\"复选框\" value=\"checkbox\" />\n                <el-option label=\"日期控件\" value=\"datetime\" />\n                <el-option label=\"图片上传\" value=\"imageUpload\" />\n                <el-option label=\"文件上传\" value=\"fileUpload\" />\n                <el-option label=\"富文本控件\" value=\"editor\" />\n              </el-select>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"字典类型\" min-width=\"12%\">\n            <template slot-scope=\"scope\">\n              <el-select v-model=\"scope.row.dictType\" clearable filterable placeholder=\"请选择\">\n                <el-option\n                  v-for=\"dict in dictOptions\"\n                  :key=\"dict.dictType\"\n                  :label=\"dict.dictName\"\n                  :value=\"dict.dictType\">\n                  <span style=\"float: left\">{{ dict.dictName }}</span>\n                  <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ dict.dictType }}</span>\n              </el-option>\n              </el-select>\n            </template>\n          </el-table-column>\n        </el-table>\n      </el-tab-pane>\n      <el-tab-pane label=\"生成信息\" name=\"genInfo\">\n        <gen-info-form ref=\"genInfo\" :info=\"info\" :tables=\"tables\" :menus=\"menus\"/>\n      </el-tab-pane>\n    </el-tabs>\n    <el-form label-width=\"100px\">\n      <el-form-item style=\"text-align: center;margin-left:-100px;margin-top:10px;\">\n        <el-button type=\"primary\" @click=\"submitForm()\">提交</el-button>\n        <el-button @click=\"close()\">返回</el-button>\n      </el-form-item>\n    </el-form>\n  </el-card>\n</template>\n\n<script>\nimport { getGenTable, updateGenTable } from \"@/api/tool/gen\"\nimport { optionselect as getDictOptionselect } from \"@/api/system/dict/type\"\nimport { listMenu as getMenuTreeselect } from \"@/api/system/menu\"\nimport basicInfoForm from \"./basicInfoForm\"\nimport genInfoForm from \"./genInfoForm\"\nimport Sortable from 'sortablejs'\n\nexport default {\n  name: \"GenEdit\",\n  components: {\n    basicInfoForm,\n    genInfoForm\n  },\n  data() {\n    return {\n      // 选中选项卡的 name\n      activeName: \"columnInfo\",\n      // 表格的高度\n      tableHeight: document.documentElement.scrollHeight - 245 + \"px\",\n      // 表信息\n      tables: [],\n      // 表列信息\n      columns: [],\n      // 字典信息\n      dictOptions: [],\n      // 菜单信息\n      menus: [],\n      // 表详细信息\n      info: {}\n    }\n  },\n  created() {\n    const tableId = this.$route.params && this.$route.params.tableId\n    if (tableId) {\n      // 获取表详细信息\n      getGenTable(tableId).then(res => {\n        this.columns = res.data.rows\n        this.info = res.data.info\n        this.tables = res.data.tables\n      })\n      /** 查询字典下拉列表 */\n      getDictOptionselect().then(response => {\n        this.dictOptions = response.data\n      })\n      /** 查询菜单下拉列表 */\n      getMenuTreeselect().then(response => {\n        this.menus = this.handleTree(response.data, \"menuId\")\n      })\n    }\n  },\n  methods: {\n    /** 提交按钮 */\n    submitForm() {\n      const basicForm = this.$refs.basicInfo.$refs.basicInfoForm\n      const genForm = this.$refs.genInfo.$refs.genInfoForm\n      Promise.all([basicForm, genForm].map(this.getFormPromise)).then(res => {\n        const validateResult = res.every(item => !!item)\n        if (validateResult) {\n          const genTable = Object.assign({}, basicForm.model, genForm.model)\n          genTable.columns = this.columns\n          genTable.params = {\n            treeCode: genTable.treeCode,\n            treeName: genTable.treeName,\n            treeParentCode: genTable.treeParentCode,\n            parentMenuId: genTable.parentMenuId\n          }\n          updateGenTable(genTable).then(res => {\n            this.$modal.msgSuccess(res.msg)\n            if (res.code === 200) {\n              this.close()\n            }\n          })\n        } else {\n          this.$modal.msgError(\"表单校验未通过，请重新检查提交内容\")\n        }\n      })\n    },\n    getFormPromise(form) {\n      return new Promise(resolve => {\n        form.validate(res => {\n          resolve(res)\n        })\n      })\n    },\n    /** 关闭按钮 */\n    close() {\n      const obj = { path: \"/tool/gen\", query: { t: Date.now(), pageNum: this.$route.query.pageNum } }\n      this.$tab.closeOpenPage(obj)\n    }\n  },\n  mounted() {\n    const el = this.$refs.dragTable.$el.querySelectorAll(\".el-table__body-wrapper > table > tbody\")[0]\n    const sortable = Sortable.create(el, {\n      handle: \".allowDrag\",\n      onEnd: evt => {\n        const targetRow = this.columns.splice(evt.oldIndex, 1)[0]\n        this.columns.splice(evt.newIndex, 0, targetRow)\n        for (let index in this.columns) {\n          this.columns[index].sort = parseInt(index) + 1\n        }\n      }\n    })\n  }\n}\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;AA4HA,IAAAA,IAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,cAAA,GAAAC,sBAAA,CAAAJ,OAAA;AACA,IAAAK,YAAA,GAAAD,sBAAA,CAAAJ,OAAA;AACA,IAAAM,WAAA,GAAAF,sBAAA,CAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAO,IAAA;EACAC,UAAA;IACAC,aAAA,EAAAA,sBAAA;IACAC,WAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,UAAA;MACA;MACAC,WAAA,EAAAC,QAAA,CAAAC,eAAA,CAAAC,YAAA;MACA;MACAC,MAAA;MACA;MACAC,OAAA;MACA;MACAC,WAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,IAAAC,OAAA,QAAAC,MAAA,CAAAC,MAAA,SAAAD,MAAA,CAAAC,MAAA,CAAAF,OAAA;IACA,IAAAA,OAAA;MACA;MACA,IAAAG,gBAAA,EAAAH,OAAA,EAAAI,IAAA,WAAAC,GAAA;QACAN,KAAA,CAAAL,OAAA,GAAAW,GAAA,CAAAlB,IAAA,CAAAmB,IAAA;QACAP,KAAA,CAAAF,IAAA,GAAAQ,GAAA,CAAAlB,IAAA,CAAAU,IAAA;QACAE,KAAA,CAAAN,MAAA,GAAAY,GAAA,CAAAlB,IAAA,CAAAM,MAAA;MACA;MACA;MACA,IAAAc,kBAAA,IAAAH,IAAA,WAAAI,QAAA;QACAT,KAAA,CAAAJ,WAAA,GAAAa,QAAA,CAAArB,IAAA;MACA;MACA;MACA,IAAAsB,cAAA,IAAAL,IAAA,WAAAI,QAAA;QACAT,KAAA,CAAAH,KAAA,GAAAG,KAAA,CAAAW,UAAA,CAAAF,QAAA,CAAArB,IAAA;MACA;IACA;EACA;EACAwB,OAAA;IACA,WACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,SAAA,QAAAC,KAAA,CAAAC,SAAA,CAAAD,KAAA,CAAA9B,aAAA;MACA,IAAAgC,OAAA,QAAAF,KAAA,CAAAG,OAAA,CAAAH,KAAA,CAAA7B,WAAA;MACAiC,OAAA,CAAAC,GAAA,EAAAN,SAAA,EAAAG,OAAA,EAAAI,GAAA,MAAAC,cAAA,GAAAlB,IAAA,WAAAC,GAAA;QACA,IAAAkB,cAAA,GAAAlB,GAAA,CAAAmB,KAAA,WAAAC,IAAA;UAAA,SAAAA,IAAA;QAAA;QACA,IAAAF,cAAA;UACA,IAAAG,QAAA,GAAAC,MAAA,CAAAC,MAAA,KAAAd,SAAA,CAAAe,KAAA,EAAAZ,OAAA,CAAAY,KAAA;UACAH,QAAA,CAAAhC,OAAA,GAAAmB,MAAA,CAAAnB,OAAA;UACAgC,QAAA,CAAAxB,MAAA;YACA4B,QAAA,EAAAJ,QAAA,CAAAI,QAAA;YACAC,QAAA,EAAAL,QAAA,CAAAK,QAAA;YACAC,cAAA,EAAAN,QAAA,CAAAM,cAAA;YACAC,YAAA,EAAAP,QAAA,CAAAO;UACA;UACA,IAAAC,mBAAA,EAAAR,QAAA,EAAAtB,IAAA,WAAAC,GAAA;YACAQ,MAAA,CAAAsB,MAAA,CAAAC,UAAA,CAAA/B,GAAA,CAAAgC,GAAA;YACA,IAAAhC,GAAA,CAAAiC,IAAA;cACAzB,MAAA,CAAA0B,KAAA;YACA;UACA;QACA;UACA1B,MAAA,CAAAsB,MAAA,CAAAK,QAAA;QACA;MACA;IACA;IACAlB,cAAA,WAAAA,eAAAmB,IAAA;MACA,WAAAtB,OAAA,WAAAuB,OAAA;QACAD,IAAA,CAAAE,QAAA,WAAAtC,GAAA;UACAqC,OAAA,CAAArC,GAAA;QACA;MACA;IACA;IACA,WACAkC,KAAA,WAAAA,MAAA;MACA,IAAAK,GAAA;QAAAC,IAAA;QAAAC,KAAA;UAAAC,CAAA,EAAAC,IAAA,CAAAC,GAAA;UAAAC,OAAA,OAAAjD,MAAA,CAAA6C,KAAA,CAAAI;QAAA;MAAA;MACA,KAAAC,IAAA,CAAAC,aAAA,CAAAR,GAAA;IACA;EACA;EACAS,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IACA,IAAAC,EAAA,QAAAxC,KAAA,CAAAyC,SAAA,CAAAC,GAAA,CAAAC,gBAAA;IACA,IAAAC,QAAA,GAAAC,mBAAA,CAAAC,MAAA,CAAAN,EAAA;MACAO,MAAA;MACAC,KAAA,WAAAA,MAAAC,GAAA;QACA,IAAAC,SAAA,GAAAX,MAAA,CAAA5D,OAAA,CAAAwE,MAAA,CAAAF,GAAA,CAAAG,QAAA;QACAb,MAAA,CAAA5D,OAAA,CAAAwE,MAAA,CAAAF,GAAA,CAAAI,QAAA,KAAAH,SAAA;QACA,SAAAI,KAAA,IAAAf,MAAA,CAAA5D,OAAA;UACA4D,MAAA,CAAA5D,OAAA,CAAA2E,KAAA,EAAAC,IAAA,GAAAC,QAAA,CAAAF,KAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}