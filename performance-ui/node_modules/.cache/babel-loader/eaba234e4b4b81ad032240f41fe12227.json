{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/utils/auth.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/utils/auth.js", "mtime": 1753510684531}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCIvVXNlcnMvbWF6aWhhby9EZXNrdG9wL2Rldi9wZXJmb3JtYW5jZS9wZXJmb3JtYW5jZS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZ2V0VG9rZW4gPSBnZXRUb2tlbjsKZXhwb3J0cy5yZW1vdmVUb2tlbiA9IHJlbW92ZVRva2VuOwpleHBvcnRzLnNldFRva2VuID0gc2V0VG9rZW47CnZhciBfanNDb29raWUgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoImpzLWNvb2tpZSIpKTsKdmFyIFRva2VuS2V5ID0gJ0FkbWluLVRva2VuJzsKZnVuY3Rpb24gZ2V0VG9rZW4oKSB7CiAgcmV0dXJuIF9qc0Nvb2tpZS5kZWZhdWx0LmdldChUb2tlbktleSk7Cn0KZnVuY3Rpb24gc2V0VG9rZW4odG9rZW4pIHsKICByZXR1cm4gX2pzQ29va2llLmRlZmF1bHQuc2V0KFRva2VuS2V5LCB0b2tlbik7Cn0KZnVuY3Rpb24gcmVtb3ZlVG9rZW4oKSB7CiAgcmV0dXJuIF9qc0Nvb2tpZS5kZWZhdWx0LnJlbW92ZShUb2tlbktleSk7Cn0="}, {"version": 3, "names": ["_js<PERSON><PERSON>ie", "_interopRequireDefault", "require", "TokenKey", "getToken", "Cookies", "get", "setToken", "token", "set", "removeToken", "remove"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/utils/auth.js"], "sourcesContent": ["import Cookies from 'js-cookie'\n\nconst TokenKey = 'Admin-Token'\n\nexport function getToken() {\n  return Cookies.get(TokenKey)\n}\n\nexport function setToken(token) {\n  return Cookies.set(To<PERSON><PERSON><PERSON>, token)\n}\n\nexport function removeToken() {\n  return Cookies.remove(TokenKey)\n}\n"], "mappings": ";;;;;;;;;AAAA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAMC,QAAQ,GAAG,aAAa;AAEvB,SAASC,QAAQA,CAAA,EAAG;EACzB,OAAOC,iBAAO,CAACC,GAAG,CAACH,QAAQ,CAAC;AAC9B;AAEO,SAASI,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAOH,iBAAO,CAACI,GAAG,CAACN,QAAQ,EAAEK,KAAK,CAAC;AACrC;AAEO,SAASE,WAAWA,CAAA,EAAG;EAC5B,OAAOL,iBAAO,CAACM,MAAM,CAACR,QAAQ,CAAC;AACjC", "ignoreList": []}]}