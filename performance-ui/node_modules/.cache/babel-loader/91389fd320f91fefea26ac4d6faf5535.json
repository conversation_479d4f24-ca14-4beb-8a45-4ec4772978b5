{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/formats/list.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/formats/list.js", "mtime": 1753510684094}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_block", "_interopRequireDefault", "require", "_container", "_quill", "ListContainer", "exports", "_Container", "_classCallCheck2", "default", "_callSuper2", "arguments", "_inherits2", "_createClass2", "Container", "blotName", "tagName", "ListItem", "_Block", "scroll", "domNode", "_this", "ui", "ownerDocument", "createElement", "listEventHandler", "e", "isEnabled", "format", "statics", "formats", "preventDefault", "addEventListener", "attachUI", "key", "value", "name", "setAttribute", "_superPropGet2", "create", "node", "getAttribute", "undefined", "register", "<PERSON><PERSON><PERSON>", "Block", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requiredC<PERSON><PERSON>"], "sources": ["../../src/formats/list.ts"], "sourcesContent": ["import Block from '../blots/block.js';\nimport Container from '../blots/container.js';\nimport type Scroll from '../blots/scroll.js';\nimport Quill from '../core/quill.js';\n\nclass ListContainer extends Container {}\nListContainer.blotName = 'list-container';\nListContainer.tagName = 'OL';\n\nclass ListItem extends Block {\n  static create(value: string) {\n    const node = super.create() as HTMLElement;\n    node.setAttribute('data-list', value);\n    return node;\n  }\n\n  static formats(domNode: HTMLElement) {\n    return domNode.getAttribute('data-list') || undefined;\n  }\n\n  static register() {\n    Quill.register(ListContainer);\n  }\n\n  constructor(scroll: Scroll, domNode: HTMLElement) {\n    super(scroll, domNode);\n    const ui = domNode.ownerDocument.createElement('span');\n    const listEventHandler = (e: Event) => {\n      if (!scroll.isEnabled()) return;\n      const format = this.statics.formats(domNode, scroll);\n      if (format === 'checked') {\n        this.format('list', 'unchecked');\n        e.preventDefault();\n      } else if (format === 'unchecked') {\n        this.format('list', 'checked');\n        e.preventDefault();\n      }\n    };\n    ui.addEventListener('mousedown', listEventHandler);\n    ui.addEventListener('touchstart', listEventHandler);\n    this.attachUI(ui);\n  }\n\n  format(name: string, value: string) {\n    if (name === this.statics.blotName && value) {\n      this.domNode.setAttribute('data-list', value);\n    } else {\n      super.format(name, value);\n    }\n  }\n}\nListItem.blotName = 'list';\nListItem.tagName = 'LI';\n\nListContainer.allowedChildren = [ListItem];\nListItem.requiredContainer = ListContainer;\n\nexport { ListContainer, ListItem as default };\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,UAAA,GAAAF,sBAAA,CAAAC,OAAA;AAEA,IAAAE,MAAA,GAAAH,sBAAA,CAAAC,OAAA;AAAoC,IAE9BG,aAAa,GAAAC,OAAA,CAAAD,aAAA,0BAAAE,UAAA;EAAA,SAAAF,cAAA;IAAA,IAAAG,gBAAA,CAAAC,OAAA,QAAAJ,aAAA;IAAA,WAAAK,WAAA,CAAAD,OAAA,QAAAJ,aAAA,EAAAM,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAJ,aAAA,EAAAE,UAAA;EAAA,WAAAM,aAAA,CAAAJ,OAAA,EAAAJ,aAAA;AAAA,EAASS,kBAAS;AACrCT,aAAa,CAACU,QAAQ,GAAG,gBAAgB;AACzCV,aAAa,CAACW,OAAO,GAAG,IAAI;AAAA,IAEtBC,QAAQ,GAAAX,OAAA,CAAAG,OAAA,0BAAAS,MAAA;EAeZ,SAAAD,SAAYE,MAAc,EAAEC,OAAoB,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAb,gBAAA,CAAAC,OAAA,QAAAQ,QAAA;IAChDI,KAAA,OAAAX,WAAA,CAAAD,OAAA,QAAAQ,QAAA,GAAME,MAAM,EAAEC,OAAO;IACrB,IAAME,EAAE,GAAGF,OAAO,CAACG,aAAa,CAACC,aAAa,CAAC,MAAM,CAAC;IACtD,IAAMC,gBAAgB,GAAI,SAApBA,gBAAgBA,CAAIC,CAAQ,EAAK;MACrC,IAAI,CAACP,MAAM,CAACQ,SAAS,CAAC,CAAC,EAAE;MACzB,IAAMC,MAAM,GAAGP,KAAA,CAAKQ,OAAO,CAACC,OAAO,CAACV,OAAO,EAAED,MAAM,CAAC;MACpD,IAAIS,MAAM,KAAK,SAAS,EAAE;QACxBP,KAAA,CAAKO,MAAM,CAAC,MAAM,EAAE,WAAW,CAAC;QAChCF,CAAC,CAACK,cAAc,CAAC,CAAC;MACpB,CAAC,MAAM,IAAIH,MAAM,KAAK,WAAW,EAAE;QACjCP,KAAA,CAAKO,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC;QAC9BF,CAAC,CAACK,cAAc,CAAC,CAAC;MACpB;IACF,CAAC;IACDT,EAAE,CAACU,gBAAgB,CAAC,WAAW,EAAEP,gBAAgB,CAAC;IAClDH,EAAE,CAACU,gBAAgB,CAAC,YAAY,EAAEP,gBAAgB,CAAC;IACnDJ,KAAA,CAAKY,QAAQ,CAACX,EAAE,CAAC;IAAA,OAAAD,KAAA;EACnB;EAAA,IAAAT,UAAA,CAAAH,OAAA,EAAAQ,QAAA,EAAAC,MAAA;EAAA,WAAAL,aAAA,CAAAJ,OAAA,EAAAQ,QAAA;IAAAiB,GAAA;IAAAC,KAAA,EAEA,SAAAP,MAAMA,CAACQ,IAAY,EAAED,KAAa,EAAE;MAClC,IAAIC,IAAI,KAAK,IAAI,CAACP,OAAO,CAACd,QAAQ,IAAIoB,KAAK,EAAE;QAC3C,IAAI,CAACf,OAAO,CAACiB,YAAY,CAAC,WAAW,EAAEF,KAAK,CAAC;MAC/C,CAAC,MAAM;QACL,IAAAG,cAAA,CAAA7B,OAAA,EAAAQ,QAAA,sBAAamB,IAAI,EAAED,KAAK;MAC1B;IACF;EAAA;IAAAD,GAAA;IAAAC,KAAA,EAvCA,SAAOI,MAAMA,CAACJ,KAAa,EAAE;MAC3B,IAAMK,IAAI,OAAAF,cAAA,CAAA7B,OAAA,EAAAQ,QAAA,wBAAgC;MAC1CuB,IAAI,CAACH,YAAY,CAAC,WAAW,EAAEF,KAAK,CAAC;MACrC,OAAOK,IAAI;IACb;EAAA;IAAAN,GAAA;IAAAC,KAAA,EAEA,SAAOL,OAAOA,CAACV,OAAoB,EAAE;MACnC,OAAOA,OAAO,CAACqB,YAAY,CAAC,WAAW,CAAC,IAAIC,SAAS;IACvD;EAAA;IAAAR,GAAA;IAAAC,KAAA,EAEA,SAAOQ,QAAQA,CAAA,EAAG;MAChBC,cAAK,CAACD,QAAQ,CAACtC,aAAa,CAAC;IAC/B;EAAA;AAAA,EAbqBwC,cAAK;AA0C5B5B,QAAQ,CAACF,QAAQ,GAAG,MAAM;AAC1BE,QAAQ,CAACD,OAAO,GAAG,IAAI;AAEvBX,aAAa,CAACyC,eAAe,GAAG,CAAC7B,QAAQ,CAAC;AAC1CA,QAAQ,CAAC8B,iBAAiB,GAAG1C,aAAa", "ignoreList": []}]}