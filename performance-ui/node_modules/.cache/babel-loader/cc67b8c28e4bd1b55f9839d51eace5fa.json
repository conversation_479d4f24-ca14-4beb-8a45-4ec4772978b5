{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/plugins/modal.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/plugins/modal.js", "mtime": 1753510684530}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_elementUi", "require", "loadingInstance", "_default", "exports", "default", "msg", "content", "Message", "info", "msgError", "error", "msgSuccess", "success", "msgWarning", "warning", "alert", "MessageBox", "alertError", "type", "alertSuccess", "alertWarning", "notify", "Notification", "notifyError", "notifySuccess", "notify<PERSON><PERSON><PERSON>", "confirm", "confirmButtonText", "cancelButtonText", "prompt", "loading", "Loading", "service", "lock", "text", "spinner", "background", "closeLoading", "close"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/plugins/modal.js"], "sourcesContent": ["import { Message, MessageBox, Notification, Loading } from 'element-ui'\n\nlet loadingInstance\n\nexport default {\n  // 消息提示\n  msg(content) {\n    Message.info(content)\n  },\n  // 错误消息\n  msgError(content) {\n    Message.error(content)\n  },\n  // 成功消息\n  msgSuccess(content) {\n    Message.success(content)\n  },\n  // 警告消息\n  msgWarning(content) {\n    Message.warning(content)\n  },\n  // 弹出提示\n  alert(content) {\n    MessageBox.alert(content, \"系统提示\")\n  },\n  // 错误提示\n  alertError(content) {\n    MessageBox.alert(content, \"系统提示\", { type: 'error' })\n  },\n  // 成功提示\n  alertSuccess(content) {\n    MessageBox.alert(content, \"系统提示\", { type: 'success' })\n  },\n  // 警告提示\n  alertWarning(content) {\n    MessageBox.alert(content, \"系统提示\", { type: 'warning' })\n  },\n  // 通知提示\n  notify(content) {\n    Notification.info(content)\n  },\n  // 错误通知\n  notifyError(content) {\n    Notification.error(content)\n  },\n  // 成功通知\n  notifySuccess(content) {\n    Notification.success(content)\n  },\n  // 警告通知\n  notifyWarning(content) {\n    Notification.warning(content)\n  },\n  // 确认窗体\n  confirm(content) {\n    return MessageBox.confirm(content, \"系统提示\", {\n      confirmButtonText: '确定',\n      cancelButtonText: '取消',\n      type: \"warning\",\n    })\n  },\n  // 提交内容\n  prompt(content) {\n    return MessageBox.prompt(content, \"系统提示\", {\n      confirmButtonText: '确定',\n      cancelButtonText: '取消',\n      type: \"warning\",\n    })\n  },\n  // 打开遮罩层\n  loading(content) {\n    loadingInstance = Loading.service({\n      lock: true,\n      text: content,\n      spinner: \"el-icon-loading\",\n      background: \"rgba(0, 0, 0, 0.7)\",\n    })\n  },\n  // 关闭遮罩层\n  closeLoading() {\n    loadingInstance.close()\n  }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAEA,IAAIC,eAAe;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEJ;EACb;EACAC,GAAG,WAAHA,GAAGA,CAACC,OAAO,EAAE;IACXC,kBAAO,CAACC,IAAI,CAACF,OAAO,CAAC;EACvB,CAAC;EACD;EACAG,QAAQ,WAARA,QAAQA,CAACH,OAAO,EAAE;IAChBC,kBAAO,CAACG,KAAK,CAACJ,OAAO,CAAC;EACxB,CAAC;EACD;EACAK,UAAU,WAAVA,UAAUA,CAACL,OAAO,EAAE;IAClBC,kBAAO,CAACK,OAAO,CAACN,OAAO,CAAC;EAC1B,CAAC;EACD;EACAO,UAAU,WAAVA,UAAUA,CAACP,OAAO,EAAE;IAClBC,kBAAO,CAACO,OAAO,CAACR,OAAO,CAAC;EAC1B,CAAC;EACD;EACAS,KAAK,WAALA,KAAKA,CAACT,OAAO,EAAE;IACbU,qBAAU,CAACD,KAAK,CAACT,OAAO,EAAE,MAAM,CAAC;EACnC,CAAC;EACD;EACAW,UAAU,WAAVA,UAAUA,CAACX,OAAO,EAAE;IAClBU,qBAAU,CAACD,KAAK,CAACT,OAAO,EAAE,MAAM,EAAE;MAAEY,IAAI,EAAE;IAAQ,CAAC,CAAC;EACtD,CAAC;EACD;EACAC,YAAY,WAAZA,YAAYA,CAACb,OAAO,EAAE;IACpBU,qBAAU,CAACD,KAAK,CAACT,OAAO,EAAE,MAAM,EAAE;MAAEY,IAAI,EAAE;IAAU,CAAC,CAAC;EACxD,CAAC;EACD;EACAE,YAAY,WAAZA,YAAYA,CAACd,OAAO,EAAE;IACpBU,qBAAU,CAACD,KAAK,CAACT,OAAO,EAAE,MAAM,EAAE;MAAEY,IAAI,EAAE;IAAU,CAAC,CAAC;EACxD,CAAC;EACD;EACAG,MAAM,WAANA,MAAMA,CAACf,OAAO,EAAE;IACdgB,uBAAY,CAACd,IAAI,CAACF,OAAO,CAAC;EAC5B,CAAC;EACD;EACAiB,WAAW,WAAXA,WAAWA,CAACjB,OAAO,EAAE;IACnBgB,uBAAY,CAACZ,KAAK,CAACJ,OAAO,CAAC;EAC7B,CAAC;EACD;EACAkB,aAAa,WAAbA,aAAaA,CAAClB,OAAO,EAAE;IACrBgB,uBAAY,CAACV,OAAO,CAACN,OAAO,CAAC;EAC/B,CAAC;EACD;EACAmB,aAAa,WAAbA,aAAaA,CAACnB,OAAO,EAAE;IACrBgB,uBAAY,CAACR,OAAO,CAACR,OAAO,CAAC;EAC/B,CAAC;EACD;EACAoB,OAAO,WAAPA,OAAOA,CAACpB,OAAO,EAAE;IACf,OAAOU,qBAAU,CAACU,OAAO,CAACpB,OAAO,EAAE,MAAM,EAAE;MACzCqB,iBAAiB,EAAE,IAAI;MACvBC,gBAAgB,EAAE,IAAI;MACtBV,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;EACD;EACAW,MAAM,WAANA,MAAMA,CAACvB,OAAO,EAAE;IACd,OAAOU,qBAAU,CAACa,MAAM,CAACvB,OAAO,EAAE,MAAM,EAAE;MACxCqB,iBAAiB,EAAE,IAAI;MACvBC,gBAAgB,EAAE,IAAI;MACtBV,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;EACD;EACAY,OAAO,WAAPA,OAAOA,CAACxB,OAAO,EAAE;IACfL,eAAe,GAAG8B,kBAAO,CAACC,OAAO,CAAC;MAChCC,IAAI,EAAE,IAAI;MACVC,IAAI,EAAE5B,OAAO;MACb6B,OAAO,EAAE,iBAAiB;MAC1BC,UAAU,EAAE;IACd,CAAC,CAAC;EACJ,CAAC;EACD;EACAC,YAAY,WAAZA,YAAYA,CAAA,EAAG;IACbpC,eAAe,CAACqC,KAAK,CAAC,CAAC;EACzB;AACF,CAAC", "ignoreList": []}]}