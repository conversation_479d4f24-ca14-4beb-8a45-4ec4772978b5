{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/system/menu.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/system/menu.js", "mtime": 1753510684517}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listMenu", "query", "request", "url", "method", "params", "getMenu", "menuId", "treeselect", "roleMenuTreeselect", "roleId", "addMenu", "data", "updateMenu", "delMenu"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/system/menu.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询菜单列表\nexport function listMenu(query) {\n  return request({\n    url: '/system/menu/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询菜单详细\nexport function getMenu(menuId) {\n  return request({\n    url: '/system/menu/' + menuId,\n    method: 'get'\n  })\n}\n\n// 查询菜单下拉树结构\nexport function treeselect() {\n  return request({\n    url: '/system/menu/treeselect',\n    method: 'get'\n  })\n}\n\n// 根据角色ID查询菜单下拉树结构\nexport function roleMenuTreeselect(roleId) {\n  return request({\n    url: '/system/menu/roleMenuTreeselect/' + roleId,\n    method: 'get'\n  })\n}\n\n// 新增菜单\nexport function addMenu(data) {\n  return request({\n    url: '/system/menu',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改菜单\nexport function updateMenu(data) {\n  return request({\n    url: '/system/menu',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除菜单\nexport function delMenu(menuId) {\n  return request({\n    url: '/system/menu/' + menuId,\n    method: 'delete'\n  })\n}"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,OAAOA,CAACC,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,UAAUA,CAAA,EAAG;EAC3B,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,kBAAkBA,CAACC,MAAM,EAAE;EACzC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC,GAAGO,MAAM;IAChDN,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASO,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,MAAM;IACdQ,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbQ,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,OAAOA,CAACP,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}