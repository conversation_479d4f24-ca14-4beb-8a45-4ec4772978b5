{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/tool/build/IconsDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/tool/build/IconsDialog.vue", "mtime": 1753510684536}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_icon", "_interopRequireDefault", "require", "originList", "iconList", "map", "name", "concat", "_default", "exports", "default", "inheritAttrs", "props", "data", "active", "key", "watch", "val", "filter", "indexOf", "methods", "onOpen", "current", "onClose", "onSelect", "icon", "$emit"], "sources": ["src/views/tool/build/IconsDialog.vue"], "sourcesContent": ["<template>\n  <div class=\"icon-dialog\">\n    <el-dialog\n      v-bind=\"$attrs\"\n      width=\"980px\"\n      :modal-append-to-body=\"false\"\n      v-on=\"$listeners\"\n      @open=\"onOpen\"\n      @close=\"onClose\"\n    >\n      <div slot=\"title\">\n        选择图标\n        <el-input\n          v-model=\"key\"\n          size=\"mini\"\n          :style=\"{width: '260px'}\"\n          placeholder=\"请输入图标名称\"\n          prefix-icon=\"el-icon-search\"\n          clearable\n        />\n      </div>\n      <ul class=\"icon-ul\">\n        <li\n          v-for=\"icon in iconList\"\n          :key=\"icon\"\n          :class=\"active===icon?'active-item':''\"\n          @click=\"onSelect(icon)\"\n        >\n          <i :class=\"icon\" />\n          <div>{{ icon }}</div>\n        </li>\n      </ul>\n    </el-dialog>\n  </div>\n</template>\n<script>\nimport iconList from '@/utils/generator/icon.json'\n\nconst originList = iconList.map(name => `el-icon-${name}`)\n\nexport default {\n  inheritAttrs: false,\n  props: ['current'],\n  data() {\n    return {\n      iconList: originList,\n      active: null,\n      key: ''\n    }\n  },\n  watch: {\n    key(val) {\n      if (val) {\n        this.iconList = originList.filter(name => name.indexOf(val) > -1)\n      } else {\n        this.iconList = originList\n      }\n    }\n  },\n  methods: {\n    onOpen() {\n      this.active = this.current\n      this.key = ''\n    },\n    onClose() {},\n    onSelect(icon) {\n      this.active = icon\n      this.$emit('select', icon)\n      this.$emit('update:visible', false)\n    }\n  }\n}\n</script>\n<style lang=\"scss\" scoped>\n.icon-ul {\n  margin: 0;\n  padding: 0;\n  font-size: 0;\n  li {\n    list-style-type: none;\n    text-align: center;\n    font-size: 14px;\n    display: inline-block;\n    width: 16.66%;\n    box-sizing: border-box;\n    height: 108px;\n    padding: 15px 6px 6px 6px;\n    cursor: pointer;\n    overflow: hidden;\n    &:hover {\n      background: #f2f2f2;\n    }\n    &.active-item{\n      background: #e1f3fb;\n      color: #7a6df0\n    }\n    > i {\n      font-size: 30px;\n      line-height: 50px;\n    }\n  }\n}\n.icon-dialog {\n  ::v-deep .el-dialog {\n    border-radius: 8px;\n    margin-bottom: 0;\n    margin-top: 4vh !important;\n    display: flex;\n    flex-direction: column;\n    max-height: 92vh;\n    overflow: hidden;\n    box-sizing: border-box;\n    .el-dialog__header {\n      padding-top: 14px;\n    }\n    .el-dialog__body {\n      margin: 0 20px 20px 20px;\n      padding: 0;\n      overflow: auto;\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;AAoCA,IAAAA,KAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAAC,UAAA,GAAAC,aAAA,CAAAC,GAAA,WAAAC,IAAA;EAAA,kBAAAC,MAAA,CAAAD,IAAA;AAAA;AAAA,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,YAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAT,QAAA,EAAAD,UAAA;MACAW,MAAA;MACAC,GAAA;IACA;EACA;EACAC,KAAA;IACAD,GAAA,WAAAA,IAAAE,GAAA;MACA,IAAAA,GAAA;QACA,KAAAb,QAAA,GAAAD,UAAA,CAAAe,MAAA,WAAAZ,IAAA;UAAA,OAAAA,IAAA,CAAAa,OAAA,CAAAF,GAAA;QAAA;MACA;QACA,KAAAb,QAAA,GAAAD,UAAA;MACA;IACA;EACA;EACAiB,OAAA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAP,MAAA,QAAAQ,OAAA;MACA,KAAAP,GAAA;IACA;IACAQ,OAAA,WAAAA,QAAA;IACAC,QAAA,WAAAA,SAAAC,IAAA;MACA,KAAAX,MAAA,GAAAW,IAAA;MACA,KAAAC,KAAA,WAAAD,IAAA;MACA,KAAAC,KAAA;IACA;EACA;AACA", "ignoreList": []}]}