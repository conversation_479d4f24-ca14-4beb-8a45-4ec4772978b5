{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/modules/normalizeExternalHTML/normalizers/googleDocs.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/modules/normalizeExternalHTML/normalizers/googleDocs.js", "mtime": 1753510684096}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["normalWeightRegexp", "blockTagNames", "isBlockElement", "element", "includes", "tagName", "normalizeEmptyLines", "doc", "Array", "from", "querySelectorAll", "filter", "br", "previousElementSibling", "nextElement<PERSON><PERSON>ling", "for<PERSON>ach", "_br$parentNode", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "normalizeFontWeight", "node", "_node$getAttribute", "getAttribute", "match", "_node$parentNode", "fragment", "createDocumentFragment", "append", "apply", "_toConsumableArray2", "default", "childNodes", "<PERSON><PERSON><PERSON><PERSON>", "normalize", "querySelector"], "sources": ["../../../../src/modules/normalizeExternalHTML/normalizers/googleDocs.ts"], "sourcesContent": ["const normalWeightRegexp = /font-weight:\\s*normal/;\nconst blockTagNames = ['P', 'OL', 'UL'];\n\nconst isBlockElement = (element: Element | null) => {\n  return element && blockTagNames.includes(element.tagName);\n};\n\nconst normalizeEmptyLines = (doc: Document) => {\n  Array.from(doc.querySelectorAll('br'))\n    .filter(\n      (br) =>\n        isBlockElement(br.previousElementSibling) &&\n        isBlockElement(br.nextElementSibling),\n    )\n    .forEach((br) => {\n      br.parentNode?.removeChild(br);\n    });\n};\n\nconst normalizeFontWeight = (doc: Document) => {\n  Array.from(doc.querySelectorAll('b[style*=\"font-weight\"]'))\n    .filter((node) => node.getAttribute('style')?.match(normalWeightRegexp))\n    .forEach((node) => {\n      const fragment = doc.createDocumentFragment();\n      fragment.append(...node.childNodes);\n      node.parentNode?.replaceChild(fragment, node);\n    });\n};\n\nexport default function normalize(doc: Document) {\n  if (doc.querySelector('[id^=\"docs-internal-guid-\"]')) {\n    normalizeFontWeight(doc);\n    normalizeEmptyLines(doc);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA,IAAMA,kBAAkB,GAAG,uBAAuB;AAClD,IAAMC,aAAa,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;AAEvC,IAAMC,cAAc,GAAI,SAAlBA,cAAcA,CAAIC,OAAuB,EAAK;EAClD,OAAOA,OAAO,IAAIF,aAAa,CAACG,QAAQ,CAACD,OAAO,CAACE,OAAO,CAAC;AAC3D,CAAC;AAED,IAAMC,mBAAmB,GAAI,SAAvBA,mBAAmBA,CAAIC,GAAa,EAAK;EAC7CC,KAAK,CAACC,IAAI,CAACF,GAAG,CAACG,gBAAgB,CAAC,IAAI,CAAC,CAAC,CACnCC,MAAM,CACJ,UAAAC,EAAE;IAAA,OACDV,cAAc,CAACU,EAAE,CAACC,sBAAsB,CAAC,IACzCX,cAAc,CAACU,EAAE,CAACE,kBAAkB,CACxC;EAAA,EAAC,CACAC,OAAO,CAAE,UAAAH,EAAE,EAAK;IAAA,IAAAI,cAAA;IACf,CAAAA,cAAA,GAAAJ,EAAE,CAACK,UAAU,cAAAD,cAAA,eAAbA,cAAA,CAAeE,WAAW,CAACN,EAAE,CAAC;EAChC,CAAC,CAAC;AACN,CAAC;AAED,IAAMO,mBAAmB,GAAI,SAAvBA,mBAAmBA,CAAIZ,GAAa,EAAK;EAC7CC,KAAK,CAACC,IAAI,CAACF,GAAG,CAACG,gBAAgB,CAAC,yBAAyB,CAAC,CAAC,CACxDC,MAAM,CAAE,UAAAS,IAAI;IAAA,IAAAC,kBAAA;IAAA,QAAAA,kBAAA,GAAKD,IAAI,CAACE,YAAY,CAAC,OAAO,CAAC,cAAAD,kBAAA,uBAA1BA,kBAAA,CAA4BE,KAAK,CAACvB,kBAAkB,CAAC;EAAA,EAAC,CACvEe,OAAO,CAAE,UAAAK,IAAI,EAAK;IAAA,IAAAI,gBAAA;IACjB,IAAMC,QAAQ,GAAGlB,GAAG,CAACmB,sBAAsB,CAAC,CAAC;IAC7CD,QAAQ,CAACE,MAAM,CAAAC,KAAA,CAAfH,QAAQ,MAAAI,mBAAA,CAAAC,OAAA,EAAWV,IAAI,CAACW,UAAU,EAAC;IACnC,CAAAP,gBAAA,GAAAJ,IAAI,CAACH,UAAU,cAAAO,gBAAA,eAAfA,gBAAA,CAAiBQ,YAAY,CAACP,QAAQ,EAAEL,IAAI,CAAC;EAC/C,CAAC,CAAC;AACN,CAAC;AAEc,SAASa,SAASA,CAAC1B,GAAa,EAAE;EAC/C,IAAIA,GAAG,CAAC2B,aAAa,CAAC,6BAA6B,CAAC,EAAE;IACpDf,mBAAmB,CAACZ,GAAG,CAAC;IACxBD,mBAAmB,CAACC,GAAG,CAAC;EAC1B;AACF", "ignoreList": []}]}