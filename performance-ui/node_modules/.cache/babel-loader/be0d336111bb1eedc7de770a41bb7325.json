{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/core/emitter.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/core/emitter.js", "mtime": 1753510684087}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_eventemitter", "require", "_instances", "_interopRequireDefault", "_logger", "debug", "logger", "EVENTS", "for<PERSON>ach", "eventName", "document", "addEventListener", "_len", "arguments", "length", "args", "Array", "_key", "from", "querySelectorAll", "node", "quill", "instances", "get", "emitter", "_quill$emitter", "handleDOM", "apply", "Emitter", "_EventEmitter", "_this", "_classCallCheck2", "default", "_callSuper2", "domListeners", "on", "error", "_inherits2", "_createClass2", "key", "value", "emit", "_debug$log", "_len2", "_key2", "log", "call", "concat", "_superPropGet2", "event", "_len3", "_key3", "type", "_ref", "handler", "target", "contains", "listenDOM", "push", "EventEmitter", "_defineProperty2", "EDITOR_CHANGE", "SCROLL_BEFORE_UPDATE", "SCROLL_BLOT_MOUNT", "SCROLL_BLOT_UNMOUNT", "SCROLL_OPTIMIZE", "SCROLL_UPDATE", "SCROLL_EMBED_UPDATE", "SELECTION_CHANGE", "TEXT_CHANGE", "COMPOSITION_BEFORE_START", "COMPOSITION_START", "COMPOSITION_BEFORE_END", "COMPOSITION_END", "API", "SILENT", "USER", "_default", "exports"], "sources": ["../../src/core/emitter.ts"], "sourcesContent": ["import { EventEmitter } from 'eventemitter3';\nimport instances from './instances.js';\nimport logger from './logger.js';\n\nconst debug = logger('quill:events');\nconst EVENTS = ['selectionchange', 'mousedown', 'mouseup', 'click'];\n\nEVENTS.forEach((eventName) => {\n  document.addEventListener(eventName, (...args) => {\n    Array.from(document.querySelectorAll('.ql-container')).forEach((node) => {\n      const quill = instances.get(node);\n      if (quill && quill.emitter) {\n        quill.emitter.handleDOM(...args);\n      }\n    });\n  });\n});\n\nclass Emitter extends EventEmitter<string> {\n  static events = {\n    EDITOR_CHANGE: 'editor-change',\n    SCROLL_BEFORE_UPDATE: 'scroll-before-update',\n    SCROLL_BLOT_MOUNT: 'scroll-blot-mount',\n    SCROLL_BLOT_UNMOUNT: 'scroll-blot-unmount',\n    SCROLL_OPTIMIZE: 'scroll-optimize',\n    SCROLL_UPDATE: 'scroll-update',\n    SCROLL_EMBED_UPDATE: 'scroll-embed-update',\n    SELECTION_CHANGE: 'selection-change',\n    TEXT_CHANGE: 'text-change',\n    COMPOSITION_BEFORE_START: 'composition-before-start',\n    COMPOSITION_START: 'composition-start',\n    COMPOSITION_BEFORE_END: 'composition-before-end',\n    COMPOSITION_END: 'composition-end',\n  } as const;\n\n  static sources = {\n    API: 'api',\n    SILENT: 'silent',\n    USER: 'user',\n  } as const;\n\n  protected domListeners: Record<string, { node: Node; handler: Function }[]>;\n\n  constructor() {\n    super();\n    this.domListeners = {};\n    this.on('error', debug.error);\n  }\n\n  emit(...args: unknown[]): boolean {\n    debug.log.call(debug, ...args);\n    // @ts-expect-error\n    return super.emit(...args);\n  }\n\n  handleDOM(event: Event, ...args: unknown[]) {\n    (this.domListeners[event.type] || []).forEach(({ node, handler }) => {\n      if (event.target === node || node.contains(event.target as Node)) {\n        handler(event, ...args);\n      }\n    });\n  }\n\n  listenDOM(eventName: string, node: Node, handler: EventListener) {\n    if (!this.domListeners[eventName]) {\n      this.domListeners[eventName] = [];\n    }\n    this.domListeners[eventName].push({ node, handler });\n  }\n}\n\nexport type EmitterSource =\n  (typeof Emitter.sources)[keyof typeof Emitter.sources];\n\nexport default Emitter;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,aAAA,GAAAC,OAAA;AACA,IAAAC,UAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,OAAA,GAAAD,sBAAA,CAAAF,OAAA;AAEA,IAAMI,KAAK,GAAG,IAAAC,eAAM,EAAC,cAAc,CAAC;AACpC,IAAMC,MAAM,GAAG,CAAC,iBAAiB,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,CAAC;AAEnEA,MAAM,CAACC,OAAO,CAAE,UAAAC,SAAS,EAAK;EAC5BC,QAAQ,CAACC,gBAAgB,CAACF,SAAS,EAAE,YAAa;IAAA,SAAAG,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAATC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IAC3CD,KAAK,CAACE,IAAI,CAACR,QAAQ,CAACS,gBAAgB,CAAC,eAAe,CAAC,CAAC,CAACX,OAAO,CAAE,UAAAY,IAAI,EAAK;MACvE,IAAMC,KAAK,GAAGC,kBAAS,CAACC,GAAG,CAACH,IAAI,CAAC;MACjC,IAAIC,KAAK,IAAIA,KAAK,CAACG,OAAO,EAAE;QAAA,IAAAC,cAAA;QAC1B,CAAAA,cAAA,GAAAJ,KAAK,CAACG,OAAO,EAACE,SAAS,CAAAC,KAAA,CAAAF,cAAA,EAAIV,IAAI,CAAC;MAClC;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AAAA,IAEIa,OAAO,0BAAAC,aAAA;EAyBX,SAAAD,QAAA,EAAc;IAAA,IAAAE,KAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAJ,OAAA;IACZE,KAAA,OAAAG,WAAA,CAAAD,OAAA,QAAAJ,OAAA;IACAE,KAAA,CAAKI,YAAY,GAAG,CAAC,CAAC;IACtBJ,KAAA,CAAKK,EAAE,CAAC,OAAO,EAAE9B,KAAK,CAAC+B,KAAK,CAAC;IAAA,OAAAN,KAAA;EAC/B;EAAA,IAAAO,UAAA,CAAAL,OAAA,EAAAJ,OAAA,EAAAC,aAAA;EAAA,WAAAS,aAAA,CAAAN,OAAA,EAAAJ,OAAA;IAAAW,GAAA;IAAAC,KAAA,EAEA,SAAAC,IAAIA,CAAA,EAA8B;MAAA,IAAAC,UAAA;MAAA,SAAAC,KAAA,GAAA9B,SAAA,CAAAC,MAAA,EAA1BC,IAAI,OAAAC,KAAA,CAAA2B,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;QAAJ7B,IAAI,CAAA6B,KAAA,IAAA/B,SAAA,CAAA+B,KAAA;MAAA;MACV,CAAAF,UAAA,GAAArC,KAAK,CAACwC,GAAG,EAACC,IAAI,CAAAnB,KAAA,CAAAe,UAAA,GAACrC,KAAK,EAAA0C,MAAA,CAAKhC,IAAI,EAAC;MAC9B;MACA,WAAAiC,cAAA,CAAAhB,OAAA,EAAAJ,OAAA,mBAAqBb,IAAI;IAC3B;EAAA;IAAAwB,GAAA;IAAAC,KAAA,EAEA,SAAAd,SAASA,CAACuB,KAAY,EAAsB;MAAA,SAAAC,KAAA,GAAArC,SAAA,CAAAC,MAAA,EAAjBC,IAAI,OAAAC,KAAA,CAAAkC,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;QAAJpC,IAAI,CAAAoC,KAAA,QAAAtC,SAAA,CAAAsC,KAAA;MAAA;MAC7B,CAAC,IAAI,CAACjB,YAAY,CAACe,KAAK,CAACG,IAAI,CAAC,IAAI,EAAE,EAAE5C,OAAO,CAAC,UAAA6C,IAAA,EAAuB;QAAA,IAApBjC,IAAI,GAAWiC,IAAA,CAAfjC,IAAI;UAAEkC,OAAA,GAASD,IAAA,CAATC,OAAA;QACrD,IAAIL,KAAK,CAACM,MAAM,KAAKnC,IAAI,IAAIA,IAAI,CAACoC,QAAQ,CAACP,KAAK,CAACM,MAAc,CAAC,EAAE;UAChED,OAAO,CAAA3B,KAAA,UAACsB,KAAK,EAAAF,MAAA,CAAKhC,IAAI,EAAC;QACzB;MACF,CAAC,CAAC;IACJ;EAAA;IAAAwB,GAAA;IAAAC,KAAA,EAEA,SAAAiB,SAASA,CAAChD,SAAiB,EAAEW,IAAU,EAAEkC,OAAsB,EAAE;MAC/D,IAAI,CAAC,IAAI,CAACpB,YAAY,CAACzB,SAAS,CAAC,EAAE;QACjC,IAAI,CAACyB,YAAY,CAACzB,SAAS,CAAC,GAAG,EAAE;MACnC;MACA,IAAI,CAACyB,YAAY,CAACzB,SAAS,CAAC,CAACiD,IAAI,CAAC;QAAEtC,IAAI,EAAJA,IAAI;QAAEkC,OAAA,EAAAA;MAAQ,CAAC,CAAC;IACtD;EAAA;AAAA,EAlDoBK,0BAAY;AAAA,IAAAC,gBAAA,CAAA5B,OAAA,EAA5BJ,OAAO,YACK;EACdiC,aAAa,EAAE,eAAe;EAC9BC,oBAAoB,EAAE,sBAAsB;EAC5CC,iBAAiB,EAAE,mBAAmB;EACtCC,mBAAmB,EAAE,qBAAqB;EAC1CC,eAAe,EAAE,iBAAiB;EAClCC,aAAa,EAAE,eAAe;EAC9BC,mBAAmB,EAAE,qBAAqB;EAC1CC,gBAAgB,EAAE,kBAAkB;EACpCC,WAAW,EAAE,aAAa;EAC1BC,wBAAwB,EAAE,0BAA0B;EACpDC,iBAAiB,EAAE,mBAAmB;EACtCC,sBAAsB,EAAE,wBAAwB;EAChDC,eAAe,EAAE;AACnB,CAAC;AAAA,IAAAb,gBAAA,CAAA5B,OAAA,EAfGJ,OAAO,aAiBM;EACf8C,GAAG,EAAE,KAAK;EACVC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE;AACR,CAAC;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAA9C,OAAA,GAmCYJ,OAAO", "ignoreList": []}]}