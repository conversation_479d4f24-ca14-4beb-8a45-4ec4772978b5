{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/FileUpload/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/FileUpload/index.vue", "mtime": 1753510684527}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_auth", "require", "_sortablejs", "_interopRequireDefault", "name", "props", "value", "String", "Object", "Array", "action", "type", "default", "data", "limit", "Number", "fileSize", "fileType", "isShowTip", "Boolean", "disabled", "drag", "number", "uploadList", "baseUrl", "process", "env", "VUE_APP_BASE_API", "uploadFileUrl", "headers", "Authorization", "getToken", "fileList", "mounted", "_this", "$nextTick", "_this$$refs$uploadFil", "element", "$refs", "uploadFileList", "$el", "Sortable", "create", "ghostClass", "onEnd", "evt", "movedItem", "splice", "oldIndex", "newIndex", "$emit", "listToString", "watch", "handler", "val", "temp", "list", "isArray", "split", "map", "item", "url", "uid", "Date", "getTime", "deep", "immediate", "computed", "showTip", "methods", "handleBeforeUpload", "file", "fileName", "fileExt", "length", "isTypeOk", "indexOf", "$modal", "msgError", "concat", "join", "includes", "isLt", "size", "loading", "handleExceed", "handleUploadError", "err", "closeLoading", "handleUploadSuccess", "res", "code", "push", "uploadedSuccessfully", "msg", "fileUpload", "handleRemove", "handleDelete", "index", "getFileName", "lastIndexOf", "slice", "separator", "strs", "i", "substr"], "sources": ["src/components/FileUpload/index.vue"], "sourcesContent": ["<template>\n  <div class=\"upload-file\">\n    <el-upload\n      multiple\n      :action=\"uploadFileUrl\"\n      :before-upload=\"handleBeforeUpload\"\n      :file-list=\"fileList\"\n      :data=\"data\"\n      :limit=\"limit\"\n      :on-error=\"handleUploadError\"\n      :on-exceed=\"handleExceed\"\n      :on-success=\"handleUploadSuccess\"\n      :show-file-list=\"false\"\n      :headers=\"headers\"\n      class=\"upload-file-uploader\"\n      ref=\"fileUpload\"\n      v-if=\"!disabled\"\n    >\n      <!-- 上传按钮 -->\n      <el-button size=\"mini\" type=\"primary\">选取文件</el-button>\n      <!-- 上传提示 -->\n      <div class=\"el-upload__tip\" slot=\"tip\" v-if=\"showTip\">\n        请上传\n        <template v-if=\"fileSize\"> 大小不超过 <b style=\"color: #f56c6c\">{{ fileSize }}MB</b> </template>\n        <template v-if=\"fileType\"> 格式为 <b style=\"color: #f56c6c\">{{ fileType.join(\"/\") }}</b> </template>\n        的文件\n      </div>\n    </el-upload>\n\n    <!-- 文件列表 -->\n    <transition-group ref=\"uploadFileList\" class=\"upload-file-list el-upload-list el-upload-list--text\" name=\"el-fade-in-linear\" tag=\"ul\">\n      <li :key=\"file.url\" class=\"el-upload-list__item ele-upload-list__item-content\" v-for=\"(file, index) in fileList\">\n        <el-link :href=\"`${baseUrl}${file.url}`\" :underline=\"false\" target=\"_blank\">\n          <span class=\"el-icon-document\"> {{ getFileName(file.name) }} </span>\n        </el-link>\n        <div class=\"ele-upload-list__item-content-action\">\n          <el-link :underline=\"false\" @click=\"handleDelete(index)\" type=\"danger\" v-if=\"!disabled\">删除</el-link>\n        </div>\n      </li>\n    </transition-group>\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\"\nimport Sortable from 'sortablejs'\n\nexport default {\n  name: \"FileUpload\",\n  props: {\n    // 值\n    value: [String, Object, Array],\n    // 上传接口地址\n    action: {\n      type: String,\n      default: \"/common/upload\"\n    },\n    // 上传携带的参数\n    data: {\n      type: Object\n    },\n    // 数量限制\n    limit: {\n      type: Number,\n      default: 5\n    },\n    // 大小限制(MB)\n    fileSize: {\n      type: Number,\n      default: 5\n    },\n    // 文件类型, 例如['png', 'jpg', 'jpeg']\n    fileType: {\n      type: Array,\n      default: () => [\"doc\", \"docx\", \"xls\", \"xlsx\", \"ppt\", \"pptx\", \"txt\", \"pdf\"]\n    },\n    // 是否显示提示\n    isShowTip: {\n      type: Boolean,\n      default: true\n    },\n    // 禁用组件（仅查看文件）\n    disabled: {\n      type: Boolean,\n      default: false\n    },\n    // 拖动排序\n    drag: {\n      type: Boolean,\n      default: true\n    }\n  },\n  data() {\n    return {\n      number: 0,\n      uploadList: [],\n      baseUrl: process.env.VUE_APP_BASE_API,\n      uploadFileUrl: process.env.VUE_APP_BASE_API + this.action, // 上传文件服务器地址\n      headers: {\n        Authorization: \"Bearer \" + getToken(),\n      },\n      fileList: []\n    }\n  },\n  mounted() {\n    if (this.drag && !this.disabled) {\n      this.$nextTick(() => {\n        const element = this.$refs.uploadFileList?.$el || this.$refs.uploadFileList\n        Sortable.create(element, {\n          ghostClass: 'file-upload-darg',\n          onEnd: (evt) => {\n            const movedItem = this.fileList.splice(evt.oldIndex, 1)[0]\n            this.fileList.splice(evt.newIndex, 0, movedItem)\n            this.$emit(\"input\", this.listToString(this.fileList))\n          }\n        })\n      })\n    }\n  },\n  watch: {\n    value: {\n      handler(val) {\n        if (val) {\n          let temp = 1\n          // 首先将值转为数组\n          const list = Array.isArray(val) ? val : this.value.split(',')\n          // 然后将数组转为对象数组\n          this.fileList = list.map(item => {\n            if (typeof item === \"string\") {\n              item = { name: item, url: item }\n            }\n            item.uid = item.uid || new Date().getTime() + temp++\n            return item\n          })\n        } else {\n          this.fileList = []\n          return []\n        }\n      },\n      deep: true,\n      immediate: true\n    }\n  },\n  computed: {\n    // 是否显示提示\n    showTip() {\n      return this.isShowTip && (this.fileType || this.fileSize)\n    },\n  },\n  methods: {\n    // 上传前校检格式和大小\n    handleBeforeUpload(file) {\n      // 校检文件类型\n      if (this.fileType) {\n        const fileName = file.name.split('.')\n        const fileExt = fileName[fileName.length - 1]\n        const isTypeOk = this.fileType.indexOf(fileExt) >= 0\n        if (!isTypeOk) {\n          this.$modal.msgError(`文件格式不正确，请上传${this.fileType.join(\"/\")}格式文件!`)\n          return false\n        }\n      }\n      // 校检文件名是否包含特殊字符\n      if (file.name.includes(',')) {\n        this.$modal.msgError('文件名不正确，不能包含英文逗号!')\n        return false\n      }\n      // 校检文件大小\n      if (this.fileSize) {\n        const isLt = file.size / 1024 / 1024 < this.fileSize\n        if (!isLt) {\n          this.$modal.msgError(`上传文件大小不能超过 ${this.fileSize} MB!`)\n          return false\n        }\n      }\n      this.$modal.loading(\"正在上传文件，请稍候...\")\n      this.number++\n      return true\n    },\n    // 文件个数超出\n    handleExceed() {\n      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`)\n    },\n    // 上传失败\n    handleUploadError(err) {\n      this.$modal.msgError(\"上传文件失败，请重试\")\n      this.$modal.closeLoading()\n    },\n    // 上传成功回调\n    handleUploadSuccess(res, file) {\n      if (res.code === 200) {\n        this.uploadList.push({ name: res.fileName, url: res.fileName })\n        this.uploadedSuccessfully()\n      } else {\n        this.number--\n        this.$modal.closeLoading()\n        this.$modal.msgError(res.msg)\n        this.$refs.fileUpload.handleRemove(file)\n        this.uploadedSuccessfully()\n      }\n    },\n    // 删除文件\n    handleDelete(index) {\n      this.fileList.splice(index, 1)\n      this.$emit(\"input\", this.listToString(this.fileList))\n    },\n    // 上传结束处理\n    uploadedSuccessfully() {\n      if (this.number > 0 && this.uploadList.length === this.number) {\n        this.fileList = this.fileList.concat(this.uploadList)\n        this.uploadList = []\n        this.number = 0\n        this.$emit(\"input\", this.listToString(this.fileList))\n        this.$modal.closeLoading()\n      }\n    },\n    // 获取文件名称\n    getFileName(name) {\n      // 如果是url那么取最后的名字 如果不是直接返回\n      if (name.lastIndexOf(\"/\") > -1) {\n        return name.slice(name.lastIndexOf(\"/\") + 1)\n      } else {\n        return name\n      }\n    },\n    // 对象转成指定字符串分隔\n    listToString(list, separator) {\n      let strs = \"\"\n      separator = separator || \",\"\n      for (let i in list) {\n        strs += list[i].url + separator\n      }\n      return strs != '' ? strs.substr(0, strs.length - 1) : ''\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.file-upload-darg {\n  opacity: 0.5;\n  background: #c8ebfb;\n}\n.upload-file-uploader {\n  margin-bottom: 5px;\n}\n.upload-file-list .el-upload-list__item {\n  border: 1px solid #e4e7ed;\n  line-height: 2;\n  margin-bottom: 10px;\n  position: relative;\n}\n.upload-file-list .ele-upload-list__item-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  color: inherit;\n}\n.ele-upload-list__item-content-action .el-link {\n  margin-right: 10px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AA4CA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,WAAA,GAAAC,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAG,IAAA;EACAC,KAAA;IACA;IACAC,KAAA,GAAAC,MAAA,EAAAC,MAAA,EAAAC,KAAA;IACA;IACAC,MAAA;MACAC,IAAA,EAAAJ,MAAA;MACAK,OAAA;IACA;IACA;IACAC,IAAA;MACAF,IAAA,EAAAH;IACA;IACA;IACAM,KAAA;MACAH,IAAA,EAAAI,MAAA;MACAH,OAAA;IACA;IACA;IACAI,QAAA;MACAL,IAAA,EAAAI,MAAA;MACAH,OAAA;IACA;IACA;IACAK,QAAA;MACAN,IAAA,EAAAF,KAAA;MACAG,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACA;IACAM,SAAA;MACAP,IAAA,EAAAQ,OAAA;MACAP,OAAA;IACA;IACA;IACAQ,QAAA;MACAT,IAAA,EAAAQ,OAAA;MACAP,OAAA;IACA;IACA;IACAS,IAAA;MACAV,IAAA,EAAAQ,OAAA;MACAP,OAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAS,MAAA;MACAC,UAAA;MACAC,OAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACAC,aAAA,EAAAH,OAAA,CAAAC,GAAA,CAAAC,gBAAA,QAAAjB,MAAA;MAAA;MACAmB,OAAA;QACAC,aAAA,kBAAAC,cAAA;MACA;MACAC,QAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,SAAAb,IAAA,UAAAD,QAAA;MACA,KAAAe,SAAA;QAAA,IAAAC,qBAAA;QACA,IAAAC,OAAA,KAAAD,qBAAA,GAAAF,KAAA,CAAAI,KAAA,CAAAC,cAAA,cAAAH,qBAAA,uBAAAA,qBAAA,CAAAI,GAAA,KAAAN,KAAA,CAAAI,KAAA,CAAAC,cAAA;QACAE,mBAAA,CAAAC,MAAA,CAAAL,OAAA;UACAM,UAAA;UACAC,KAAA,WAAAA,MAAAC,GAAA;YACA,IAAAC,SAAA,GAAAZ,KAAA,CAAAF,QAAA,CAAAe,MAAA,CAAAF,GAAA,CAAAG,QAAA;YACAd,KAAA,CAAAF,QAAA,CAAAe,MAAA,CAAAF,GAAA,CAAAI,QAAA,KAAAH,SAAA;YACAZ,KAAA,CAAAgB,KAAA,UAAAhB,KAAA,CAAAiB,YAAA,CAAAjB,KAAA,CAAAF,QAAA;UACA;QACA;MACA;IACA;EACA;EACAoB,KAAA;IACA9C,KAAA;MACA+C,OAAA,WAAAA,QAAAC,GAAA;QACA,IAAAA,GAAA;UACA,IAAAC,IAAA;UACA;UACA,IAAAC,IAAA,GAAA/C,KAAA,CAAAgD,OAAA,CAAAH,GAAA,IAAAA,GAAA,QAAAhD,KAAA,CAAAoD,KAAA;UACA;UACA,KAAA1B,QAAA,GAAAwB,IAAA,CAAAG,GAAA,WAAAC,IAAA;YACA,WAAAA,IAAA;cACAA,IAAA;gBAAAxD,IAAA,EAAAwD,IAAA;gBAAAC,GAAA,EAAAD;cAAA;YACA;YACAA,IAAA,CAAAE,GAAA,GAAAF,IAAA,CAAAE,GAAA,QAAAC,IAAA,GAAAC,OAAA,KAAAT,IAAA;YACA,OAAAK,IAAA;UACA;QACA;UACA,KAAA5B,QAAA;UACA;QACA;MACA;MACAiC,IAAA;MACAC,SAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,OAAA,WAAAA,QAAA;MACA,YAAAlD,SAAA,UAAAD,QAAA,SAAAD,QAAA;IACA;EACA;EACAqD,OAAA;IACA;IACAC,kBAAA,WAAAA,mBAAAC,IAAA;MACA;MACA,SAAAtD,QAAA;QACA,IAAAuD,QAAA,GAAAD,IAAA,CAAAnE,IAAA,CAAAsD,KAAA;QACA,IAAAe,OAAA,GAAAD,QAAA,CAAAA,QAAA,CAAAE,MAAA;QACA,IAAAC,QAAA,QAAA1D,QAAA,CAAA2D,OAAA,CAAAH,OAAA;QACA,KAAAE,QAAA;UACA,KAAAE,MAAA,CAAAC,QAAA,sEAAAC,MAAA,MAAA9D,QAAA,CAAA+D,IAAA;UACA;QACA;MACA;MACA;MACA,IAAAT,IAAA,CAAAnE,IAAA,CAAA6E,QAAA;QACA,KAAAJ,MAAA,CAAAC,QAAA;QACA;MACA;MACA;MACA,SAAA9D,QAAA;QACA,IAAAkE,IAAA,GAAAX,IAAA,CAAAY,IAAA,sBAAAnE,QAAA;QACA,KAAAkE,IAAA;UACA,KAAAL,MAAA,CAAAC,QAAA,iEAAAC,MAAA,MAAA/D,QAAA;UACA;QACA;MACA;MACA,KAAA6D,MAAA,CAAAO,OAAA;MACA,KAAA9D,MAAA;MACA;IACA;IACA;IACA+D,YAAA,WAAAA,aAAA;MACA,KAAAR,MAAA,CAAAC,QAAA,iEAAAC,MAAA,MAAAjE,KAAA;IACA;IACA;IACAwE,iBAAA,WAAAA,kBAAAC,GAAA;MACA,KAAAV,MAAA,CAAAC,QAAA;MACA,KAAAD,MAAA,CAAAW,YAAA;IACA;IACA;IACAC,mBAAA,WAAAA,oBAAAC,GAAA,EAAAnB,IAAA;MACA,IAAAmB,GAAA,CAAAC,IAAA;QACA,KAAApE,UAAA,CAAAqE,IAAA;UAAAxF,IAAA,EAAAsF,GAAA,CAAAlB,QAAA;UAAAX,GAAA,EAAA6B,GAAA,CAAAlB;QAAA;QACA,KAAAqB,oBAAA;MACA;QACA,KAAAvE,MAAA;QACA,KAAAuD,MAAA,CAAAW,YAAA;QACA,KAAAX,MAAA,CAAAC,QAAA,CAAAY,GAAA,CAAAI,GAAA;QACA,KAAAxD,KAAA,CAAAyD,UAAA,CAAAC,YAAA,CAAAzB,IAAA;QACA,KAAAsB,oBAAA;MACA;IACA;IACA;IACAI,YAAA,WAAAA,aAAAC,KAAA;MACA,KAAAlE,QAAA,CAAAe,MAAA,CAAAmD,KAAA;MACA,KAAAhD,KAAA,eAAAC,YAAA,MAAAnB,QAAA;IACA;IACA;IACA6D,oBAAA,WAAAA,qBAAA;MACA,SAAAvE,MAAA,aAAAC,UAAA,CAAAmD,MAAA,UAAApD,MAAA;QACA,KAAAU,QAAA,QAAAA,QAAA,CAAA+C,MAAA,MAAAxD,UAAA;QACA,KAAAA,UAAA;QACA,KAAAD,MAAA;QACA,KAAA4B,KAAA,eAAAC,YAAA,MAAAnB,QAAA;QACA,KAAA6C,MAAA,CAAAW,YAAA;MACA;IACA;IACA;IACAW,WAAA,WAAAA,YAAA/F,IAAA;MACA;MACA,IAAAA,IAAA,CAAAgG,WAAA;QACA,OAAAhG,IAAA,CAAAiG,KAAA,CAAAjG,IAAA,CAAAgG,WAAA;MACA;QACA,OAAAhG,IAAA;MACA;IACA;IACA;IACA+C,YAAA,WAAAA,aAAAK,IAAA,EAAA8C,SAAA;MACA,IAAAC,IAAA;MACAD,SAAA,GAAAA,SAAA;MACA,SAAAE,CAAA,IAAAhD,IAAA;QACA+C,IAAA,IAAA/C,IAAA,CAAAgD,CAAA,EAAA3C,GAAA,GAAAyC,SAAA;MACA;MACA,OAAAC,IAAA,SAAAA,IAAA,CAAAE,MAAA,IAAAF,IAAA,CAAA7B,MAAA;IACA;EACA;AACA", "ignoreList": []}]}