{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/system/menu/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/system/menu/index.vue", "mtime": 1753510684535}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_menu", "require", "_vueTreeselect", "_interopRequireDefault", "_IconSelect", "name", "dicts", "components", "Treeselect", "IconSelect", "data", "loading", "showSearch", "menuList", "menuOptions", "title", "open", "isExpandAll", "refreshTable", "queryParams", "menuName", "undefined", "visible", "form", "rules", "required", "message", "trigger", "orderNum", "path", "created", "getList", "methods", "selected", "icon", "_this", "listMenu", "then", "response", "handleTree", "normalizer", "node", "children", "length", "id", "menuId", "label", "getTreeselect", "_this2", "menu", "push", "cancel", "reset", "parentId", "menuType", "isFrame", "isCache", "status", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleAdd", "row", "toggleExpandAll", "_this3", "$nextTick", "handleUpdate", "_this4", "getMenu", "submitForm", "_this5", "$refs", "validate", "valid", "updateMenu", "$modal", "msgSuccess", "addMenu", "handleDelete", "_this6", "confirm", "delMenu", "catch"], "sources": ["src/views/system/menu/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\">\n      <el-form-item label=\"菜单名称\" prop=\"menuName\">\n        <el-input\n          v-model=\"queryParams.menuName\"\n          placeholder=\"请输入菜单名称\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"状态\" prop=\"status\">\n        <el-select v-model=\"queryParams.status\" placeholder=\"菜单状态\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.sys_normal_disable\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['system:menu:add']\"\n        >新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"info\"\n          plain\n          icon=\"el-icon-sort\"\n          size=\"mini\"\n          @click=\"toggleExpandAll\"\n        >展开/折叠</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table\n      v-if=\"refreshTable\"\n      v-loading=\"loading\"\n      :data=\"menuList\"\n      row-key=\"menuId\"\n      :default-expand-all=\"isExpandAll\"\n      :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\n    >\n      <el-table-column prop=\"menuName\" label=\"菜单名称\" :show-overflow-tooltip=\"true\" width=\"160\"></el-table-column>\n      <el-table-column prop=\"icon\" label=\"图标\" align=\"center\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <svg-icon :icon-class=\"scope.row.icon\" />\n        </template>\n      </el-table-column>\n      <el-table-column prop=\"orderNum\" label=\"排序\" width=\"60\"></el-table-column>\n      <el-table-column prop=\"perms\" label=\"权限标识\" :show-overflow-tooltip=\"true\"></el-table-column>\n      <el-table-column prop=\"component\" label=\"组件路径\" :show-overflow-tooltip=\"true\"></el-table-column>\n      <el-table-column prop=\"status\" label=\"状态\" width=\"80\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['system:menu:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-plus\"\n            @click=\"handleAdd(scope.row)\"\n            v-hasPermi=\"['system:menu:add']\"\n          >新增</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['system:menu:remove']\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 添加或修改菜单对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"680px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"上级菜单\" prop=\"parentId\">\n              <treeselect\n                v-model=\"form.parentId\"\n                :options=\"menuOptions\"\n                :normalizer=\"normalizer\"\n                :show-count=\"true\"\n                placeholder=\"选择上级菜单\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"菜单类型\" prop=\"menuType\">\n              <el-radio-group v-model=\"form.menuType\">\n                <el-radio label=\"M\">目录</el-radio>\n                <el-radio label=\"C\">菜单</el-radio>\n                <el-radio label=\"F\">按钮</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\" v-if=\"form.menuType != 'F'\">\n            <el-form-item label=\"菜单图标\" prop=\"icon\">\n              <el-popover\n                placement=\"bottom-start\"\n                width=\"460\"\n                trigger=\"click\"\n                @show=\"$refs['iconSelect'].reset()\"\n              >\n                <IconSelect ref=\"iconSelect\" @selected=\"selected\" :active-icon=\"form.icon\" />\n                <el-input slot=\"reference\" v-model=\"form.icon\" placeholder=\"点击选择图标\" readonly>\n                  <svg-icon\n                    v-if=\"form.icon\"\n                    slot=\"prefix\"\n                    :icon-class=\"form.icon\"\n                    style=\"width: 25px;\"\n                  />\n                  <i v-else slot=\"prefix\" class=\"el-icon-search el-input__icon\" />\n                </el-input>\n              </el-popover>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"显示排序\" prop=\"orderNum\">\n              <el-input-number v-model=\"form.orderNum\" controls-position=\"right\" :min=\"0\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"菜单名称\" prop=\"menuName\">\n              <el-input v-model=\"form.menuName\" placeholder=\"请输入菜单名称\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\" v-if=\"form.menuType == 'C'\">\n            <el-form-item prop=\"routeName\">\n              <el-input v-model=\"form.routeName\" placeholder=\"请输入路由名称\" />\n              <span slot=\"label\">\n                <el-tooltip content=\"默认不填则和路由地址相同：如地址为：`user`，则名称为`User`（注意：为避免名字的冲突，特殊情况下请自定义，保证唯一性）\" placement=\"top\">\n                <i class=\"el-icon-question\"></i>\n                </el-tooltip>\n                路由名称\n              </span>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\" v-if=\"form.menuType != 'F'\">\n            <el-form-item prop=\"isFrame\">\n              <span slot=\"label\">\n                <el-tooltip content=\"选择是外链则路由地址需要以`http(s)://`开头\" placement=\"top\">\n                <i class=\"el-icon-question\"></i>\n                </el-tooltip>\n                是否外链\n              </span>\n              <el-radio-group v-model=\"form.isFrame\">\n                <el-radio label=\"0\">是</el-radio>\n                <el-radio label=\"1\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\" v-if=\"form.menuType != 'F'\">\n            <el-form-item prop=\"path\">\n              <span slot=\"label\">\n                <el-tooltip content=\"访问的路由地址，如：`user`，如外网地址需内链访问则以`http(s)://`开头\" placement=\"top\">\n                <i class=\"el-icon-question\"></i>\n                </el-tooltip>\n                路由地址\n              </span>\n              <el-input v-model=\"form.path\" placeholder=\"请输入路由地址\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\" v-if=\"form.menuType == 'C'\">\n            <el-form-item prop=\"component\">\n              <span slot=\"label\">\n                <el-tooltip content=\"访问的组件路径，如：`system/user/index`，默认在`views`目录下\" placement=\"top\">\n                <i class=\"el-icon-question\"></i>\n                </el-tooltip>\n                组件路径\n              </span>\n              <el-input v-model=\"form.component\" placeholder=\"请输入组件路径\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\" v-if=\"form.menuType != 'M'\">\n            <el-form-item prop=\"perms\">\n              <el-input v-model=\"form.perms\" placeholder=\"请输入权限标识\" maxlength=\"100\" />\n              <span slot=\"label\">\n                <el-tooltip content=\"控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasPermi('system:user:list')`)\" placement=\"top\">\n                <i class=\"el-icon-question\"></i>\n                </el-tooltip>\n                权限字符\n              </span>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\" v-if=\"form.menuType == 'C'\">\n            <el-form-item prop=\"query\">\n              <el-input v-model=\"form.query\" placeholder=\"请输入路由参数\" maxlength=\"255\" />\n              <span slot=\"label\">\n                <el-tooltip content='访问路由的默认传递参数，如：`{\"id\": 1, \"name\": \"ry\"}`' placement=\"top\">\n                <i class=\"el-icon-question\"></i>\n                </el-tooltip>\n                路由参数\n              </span>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\" v-if=\"form.menuType == 'C'\">\n            <el-form-item prop=\"isCache\">\n              <span slot=\"label\">\n                <el-tooltip content=\"选择是则会被`keep-alive`缓存，需要匹配组件的`name`和地址保持一致\" placement=\"top\">\n                <i class=\"el-icon-question\"></i>\n                </el-tooltip>\n                是否缓存\n              </span>\n              <el-radio-group v-model=\"form.isCache\">\n                <el-radio label=\"0\">缓存</el-radio>\n                <el-radio label=\"1\">不缓存</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\" v-if=\"form.menuType != 'F'\">\n            <el-form-item prop=\"visible\">\n              <span slot=\"label\">\n                <el-tooltip content=\"选择隐藏则路由将不会出现在侧边栏，但仍然可以访问\" placement=\"top\">\n                <i class=\"el-icon-question\"></i>\n                </el-tooltip>\n                显示状态\n              </span>\n              <el-radio-group v-model=\"form.visible\">\n                <el-radio\n                  v-for=\"dict in dict.type.sys_show_hide\"\n                  :key=\"dict.value\"\n                  :label=\"dict.value\"\n                >{{dict.label}}</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item prop=\"status\">\n              <span slot=\"label\">\n                <el-tooltip content=\"选择停用则路由将不会出现在侧边栏，也不能被访问\" placement=\"top\">\n                <i class=\"el-icon-question\"></i>\n                </el-tooltip>\n                菜单状态\n              </span>\n              <el-radio-group v-model=\"form.status\">\n                <el-radio\n                  v-for=\"dict in dict.type.sys_normal_disable\"\n                  :key=\"dict.value\"\n                  :label=\"dict.value\"\n                >{{dict.label}}</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listMenu, getMenu, delMenu, addMenu, updateMenu } from \"@/api/system/menu\"\nimport Treeselect from \"@riophae/vue-treeselect\"\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\"\nimport IconSelect from \"@/components/IconSelect\"\n\nexport default {\n  name: \"Menu\",\n  dicts: ['sys_show_hide', 'sys_normal_disable'],\n  components: { Treeselect, IconSelect },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 菜单表格树数据\n      menuList: [],\n      // 菜单树选项\n      menuOptions: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 是否展开，默认全部折叠\n      isExpandAll: false,\n      // 重新渲染表格状态\n      refreshTable: true,\n      // 查询参数\n      queryParams: {\n        menuName: undefined,\n        visible: undefined\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        menuName: [\n          { required: true, message: \"菜单名称不能为空\", trigger: \"blur\" }\n        ],\n        orderNum: [\n          { required: true, message: \"菜单顺序不能为空\", trigger: \"blur\" }\n        ],\n        path: [\n          { required: true, message: \"路由地址不能为空\", trigger: \"blur\" }\n        ]\n      }\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    // 选择图标\n    selected(name) {\n      this.form.icon = name\n    },\n    /** 查询菜单列表 */\n    getList() {\n      this.loading = true\n      listMenu(this.queryParams).then(response => {\n        this.menuList = this.handleTree(response.data, \"menuId\")\n        this.loading = false\n      })\n    },\n    /** 转换菜单数据结构 */\n    normalizer(node) {\n      if (node.children && !node.children.length) {\n        delete node.children\n      }\n      return {\n        id: node.menuId,\n        label: node.menuName,\n        children: node.children\n      }\n    },\n    /** 查询菜单下拉树结构 */\n    getTreeselect() {\n      listMenu().then(response => {\n        this.menuOptions = []\n        const menu = { menuId: 0, menuName: '主类目', children: [] }\n        menu.children = this.handleTree(response.data, \"menuId\")\n        this.menuOptions.push(menu)\n      })\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false\n      this.reset()\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        menuId: undefined,\n        parentId: 0,\n        menuName: undefined,\n        icon: undefined,\n        menuType: \"M\",\n        orderNum: undefined,\n        isFrame: \"1\",\n        isCache: \"0\",\n        visible: \"0\",\n        status: \"0\"\n      }\n      this.resetForm(\"form\")\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.getList()\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\")\n      this.handleQuery()\n    },\n    /** 新增按钮操作 */\n    handleAdd(row) {\n      this.reset()\n      this.getTreeselect()\n      if (row != null && row.menuId) {\n        this.form.parentId = row.menuId\n      } else {\n        this.form.parentId = 0\n      }\n      this.open = true\n      this.title = \"添加菜单\"\n    },\n    /** 展开/折叠操作 */\n    toggleExpandAll() {\n      this.refreshTable = false\n      this.isExpandAll = !this.isExpandAll\n      this.$nextTick(() => {\n        this.refreshTable = true\n      })\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset()\n      this.getTreeselect()\n      getMenu(row.menuId).then(response => {\n        this.form = response.data\n        this.open = true\n        this.title = \"修改菜单\"\n      })\n    },\n    /** 提交按钮 */\n    submitForm: function() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.menuId != undefined) {\n            updateMenu(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\")\n              this.open = false\n              this.getList()\n            })\n          } else {\n            addMenu(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\")\n              this.open = false\n              this.getList()\n            })\n          }\n        }\n      })\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      this.$modal.confirm('是否确认删除名称为\"' + row.menuName + '\"的数据项？').then(function() {\n        return delMenu(row.menuId)\n      }).then(() => {\n        this.getList()\n        this.$modal.msgSuccess(\"删除成功\")\n      }).catch(() => {})\n    }\n  }\n}\n</script>\n"], "mappings": ";;;;;;;;AA6SA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAC,sBAAA,CAAAF,OAAA;AACAA,OAAA;AACA,IAAAG,WAAA,GAAAD,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAI,IAAA;EACAC,KAAA;EACAC,UAAA;IAAAC,UAAA,EAAAA,sBAAA;IAAAC,UAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,UAAA;MACA;MACAC,QAAA;MACA;MACAC,WAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;MACA;MACAC,YAAA;MACA;MACAC,WAAA;QACAC,QAAA,EAAAC,SAAA;QACAC,OAAA,EAAAD;MACA;MACA;MACAE,IAAA;MACA;MACAC,KAAA;QACAJ,QAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,QAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,IAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA;IACAC,QAAA,WAAAA,SAAA5B,IAAA;MACA,KAAAkB,IAAA,CAAAW,IAAA,GAAA7B,IAAA;IACA;IACA,aACA0B,OAAA,WAAAA,QAAA;MAAA,IAAAI,KAAA;MACA,KAAAxB,OAAA;MACA,IAAAyB,cAAA,OAAAjB,WAAA,EAAAkB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAtB,QAAA,GAAAsB,KAAA,CAAAI,UAAA,CAAAD,QAAA,CAAA5B,IAAA;QACAyB,KAAA,CAAAxB,OAAA;MACA;IACA;IACA,eACA6B,UAAA,WAAAA,WAAAC,IAAA;MACA,IAAAA,IAAA,CAAAC,QAAA,KAAAD,IAAA,CAAAC,QAAA,CAAAC,MAAA;QACA,OAAAF,IAAA,CAAAC,QAAA;MACA;MACA;QACAE,EAAA,EAAAH,IAAA,CAAAI,MAAA;QACAC,KAAA,EAAAL,IAAA,CAAArB,QAAA;QACAsB,QAAA,EAAAD,IAAA,CAAAC;MACA;IACA;IACA,gBACAK,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,IAAAZ,cAAA,IAAAC,IAAA,WAAAC,QAAA;QACAU,MAAA,CAAAlC,WAAA;QACA,IAAAmC,IAAA;UAAAJ,MAAA;UAAAzB,QAAA;UAAAsB,QAAA;QAAA;QACAO,IAAA,CAAAP,QAAA,GAAAM,MAAA,CAAAT,UAAA,CAAAD,QAAA,CAAA5B,IAAA;QACAsC,MAAA,CAAAlC,WAAA,CAAAoC,IAAA,CAAAD,IAAA;MACA;IACA;IACA;IACAE,MAAA,WAAAA,OAAA;MACA,KAAAnC,IAAA;MACA,KAAAoC,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAA7B,IAAA;QACAsB,MAAA,EAAAxB,SAAA;QACAgC,QAAA;QACAjC,QAAA,EAAAC,SAAA;QACAa,IAAA,EAAAb,SAAA;QACAiC,QAAA;QACA1B,QAAA,EAAAP,SAAA;QACAkC,OAAA;QACAC,OAAA;QACAlC,OAAA;QACAmC,MAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAA5B,OAAA;IACA;IACA,aACA6B,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA,aACAE,SAAA,WAAAA,UAAAC,GAAA;MACA,KAAAV,KAAA;MACA,KAAAL,aAAA;MACA,IAAAe,GAAA,YAAAA,GAAA,CAAAjB,MAAA;QACA,KAAAtB,IAAA,CAAA8B,QAAA,GAAAS,GAAA,CAAAjB,MAAA;MACA;QACA,KAAAtB,IAAA,CAAA8B,QAAA;MACA;MACA,KAAArC,IAAA;MACA,KAAAD,KAAA;IACA;IACA,cACAgD,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAA9C,YAAA;MACA,KAAAD,WAAA,SAAAA,WAAA;MACA,KAAAgD,SAAA;QACAD,MAAA,CAAA9C,YAAA;MACA;IACA;IACA,aACAgD,YAAA,WAAAA,aAAAJ,GAAA;MAAA,IAAAK,MAAA;MACA,KAAAf,KAAA;MACA,KAAAL,aAAA;MACA,IAAAqB,aAAA,EAAAN,GAAA,CAAAjB,MAAA,EAAAR,IAAA,WAAAC,QAAA;QACA6B,MAAA,CAAA5C,IAAA,GAAAe,QAAA,CAAA5B,IAAA;QACAyD,MAAA,CAAAnD,IAAA;QACAmD,MAAA,CAAApD,KAAA;MACA;IACA;IACA;IACAsD,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAA/C,IAAA,CAAAsB,MAAA,IAAAxB,SAAA;YACA,IAAAqD,gBAAA,EAAAJ,MAAA,CAAA/C,IAAA,EAAAc,IAAA,WAAAC,QAAA;cACAgC,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAtD,IAAA;cACAsD,MAAA,CAAAvC,OAAA;YACA;UACA;YACA,IAAA8C,aAAA,EAAAP,MAAA,CAAA/C,IAAA,EAAAc,IAAA,WAAAC,QAAA;cACAgC,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAtD,IAAA;cACAsD,MAAA,CAAAvC,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACA+C,YAAA,WAAAA,aAAAhB,GAAA;MAAA,IAAAiB,MAAA;MACA,KAAAJ,MAAA,CAAAK,OAAA,gBAAAlB,GAAA,CAAA1C,QAAA,aAAAiB,IAAA;QACA,WAAA4C,aAAA,EAAAnB,GAAA,CAAAjB,MAAA;MACA,GAAAR,IAAA;QACA0C,MAAA,CAAAhD,OAAA;QACAgD,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAM,KAAA;IACA;EACA;AACA", "ignoreList": []}]}