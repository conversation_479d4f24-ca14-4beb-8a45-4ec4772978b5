{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/execute/month/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/execute/month/index.vue", "mtime": 1754401892477}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_monthly", "require", "_auth", "data", "tableData", "total", "pageNum", "pageSize", "dialogVisible", "dialogTitle", "form", "selectedIds", "uploadUrl", "process", "env", "VUE_APP_BASE_API", "uploadHeaders", "Authorization", "getToken", "created", "loadData", "methods", "_this", "listMonthly", "then", "res", "code", "rows", "handleAdd", "handleEdit", "row", "Object", "assign", "handleDelete", "_this2", "$confirm", "type", "delMonthly", "id", "$message", "success", "submitForm", "_this3", "updateMonthly", "addMonthly", "handleSelectionChange", "val", "map", "item", "handleImportSuccess", "msg", "error", "handleImportError", "handleExport", "length", "warning", "exportMonthly", "blob", "Blob", "url", "window", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleDownloadTemplate", "downloadTemplateMonthly"], "sources": ["src/views/execute/month/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"20\" style=\"margin-bottom: 20px;\">\n      <el-col :span=\"12\">\n        <el-button type=\"primary\" @click=\"handleAdd\">新增</el-button>\n        <el-upload\n          class=\"upload-demo\"\n          :action=\"uploadUrl\"\n          :headers=\"uploadHeaders\"\n          :show-file-list=\"false\"\n          :on-success=\"handleImportSuccess\"\n          :on-error=\"handleImportError\"\n          accept=\".doc,.docx\"\n          style=\"display:inline-block;margin-left:10px;\"\n        >\n          <el-button>导入Word</el-button>\n        </el-upload>\n        <el-button @click=\"handleExport\" style=\"margin-left:10px;\">导出Word</el-button>\n        <el-button @click=\"handleDownloadTemplate\" style=\"margin-left:10px;\">下载模板</el-button>\n      </el-col>\n    </el-row>\n    <el-table :data=\"tableData\" border style=\"width: 100%\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" />\n      <el-table-column prop=\"id\" label=\"序号\" width=\"60\" />\n      <el-table-column prop=\"deptName\" label=\"科室工作任务\" />\n      <el-table-column prop=\"category\" label=\"分类\" />\n      <el-table-column prop=\"workTarget\" label=\"工作目标\" />\n      <el-table-column prop=\"workMeasure\" label=\"工作措施\" />\n      <el-table-column prop=\"completeTime\" label=\"计划完成时间\" />\n      <el-table-column prop=\"firstWeek\" label=\"第一周\" />\n      <el-table-column prop=\"secondWeek\" label=\"第二周\" />\n      <el-table-column prop=\"thirdWeek\" label=\"第三周\" />\n      <el-table-column prop=\"fourthWeek\" label=\"第四周\" />\n      <el-table-column prop=\"responsibleLeader\" label=\"责任领导\" />\n      <el-table-column prop=\"responsibleDepartment\" label=\"责任科室\" />\n      <el-table-column prop=\"departmentHeader\" label=\"科室负责人\" />\n      <el-table-column prop=\"specificResponsiblePerson\" label=\"具体负责人\" />\n\n      <el-table-column label=\"操作\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <el-button size=\"mini\" @click=\"handleEdit(scope.row)\">编辑</el-button>\n          <el-button size=\"mini\" type=\"danger\" @click=\"handleDelete(scope.row)\">删除</el-button>\n        </template>\n      </el-table-column>\n\n    </el-table>\n    <el-pagination\n      style=\"margin-top: 20px;\"\n      background\n      layout=\"prev, pager, next, jumper\"\n      :total=\"total\"\n      :page-size=\"pageSize\"\n      :current-page.sync=\"pageNum\"\n      @current-change=\"loadData\"\n    />\n    <el-dialog :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\n      <el-form :model=\"form\" label-width=\"100px\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\"><el-form-item label=\"科室工作任务\"><el-input v-model=\"form.deptName\" /></el-form-item></el-col>\n          <!-- <el-col :span=\"8\"><el-form-item label=\"职务\"><el-input v-model=\"form.position\" /></el-form-item></el-col> -->\n          <el-col :span=\"8\"><el-form-item label=\"分类\"><el-input v-model=\"form.category\" /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\"><el-form-item label=\"工作目标\"><el-input v-model=\"form.workTarget\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"工作措施\"><el-input v-model=\"form.workMeasure\"  /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"计划完成时间\"><el-input v-model=\"form.completeTime\"  /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\"><el-form-item label=\"第一周\"><el-input v-model=\"form.firstWeek\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"第二周\"><el-input v-model=\"form.secondWeek\" /></el-form-item></el-col>\n\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\"><el-form-item label=\"第三周\"><el-input v-model=\"form.thirdWeek\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"第四周\"><el-input v-model=\"form.fourthWeek\" /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\"><el-form-item label=\"责任领导\"><el-input v-model=\"form.responsibleLeader\"  /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"责任科室\"><el-input v-model=\"form.responsibleDepartment\"  /></el-form-item></el-col>\n\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\"><el-form-item label=\"科室负责人\"><el-input v-model=\"form.departmentHeader\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"具体负责人\"><el-input v-model=\"form.specificResponsiblePerson\"  /></el-form-item></el-col>\n\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"submitForm\">确定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  listMonthly,\n  addMonthly,\n  updateMonthly,\n  delMonthly,\n  exportMonthly,\n  downloadTemplateMonthly\n} from '@/api/performance/monthly'\nimport { getToken } from '@/utils/auth'\n\nexport default {\n  data() {\n    return {\n      tableData: [],\n      total: 0,\n      pageNum: 1,\n      pageSize: 10,\n      dialogVisible: false,\n      dialogTitle: '新增',\n      form: {},\n      selectedIds: [],\n      uploadUrl: process.env.VUE_APP_BASE_API + '/performance/monthly/importData',\n      uploadHeaders: {\n        Authorization: 'Bearer ' + getToken()\n      }\n    }\n  },\n  created() {\n    this.loadData()\n  },\n  methods: {\n    loadData() {\n      listMonthly({ pageNum: this.pageNum, pageSize: this.pageSize }).then(res => {\n        if (res.code === 200) {\n          this.tableData = res.rows\n          this.total = res.total\n        }\n      })\n    },\n    handleAdd() {\n      this.dialogTitle = '新增'\n      this.form = {}\n      this.dialogVisible = true\n    },\n    handleEdit(row) {\n      this.dialogTitle = '编辑'\n      this.form = Object.assign({}, row)\n      this.dialogVisible = true\n    },\n    handleDelete(row) {\n      this.$confirm('确定删除该条记录吗？', '提示', { type: 'warning' }).then(() => {\n        delMonthly(row.id).then(() => {\n          this.$message.success('删除成功')\n          this.loadData()\n        })\n      })\n    },\n    submitForm() {\n      if (this.form.id) {\n        updateMonthly(this.form).then(() => {\n          this.$message.success('修改成功')\n          this.dialogVisible = false\n          this.loadData()\n        })\n      } else {\n        addMonthly(this.form).then(() => {\n          this.$message.success('新增成功')\n          this.dialogVisible = false\n          this.loadData()\n        })\n      }\n    },\n    handleSelectionChange(val) {\n      this.selectedIds = val.map(item => item.id)\n    },\n    handleImportSuccess(res) {\n      if (res.code === 200) {\n        this.$message.success(res.msg || '导入成功')\n        this.loadData()\n      } else {\n        this.$message.error(res.msg || '导入失败')\n      }\n    },\n    handleImportError() {\n      this.$message.error('导入失败，请检查文件或网络')\n    },\n    handleExport() {\n      if (this.selectedIds.length === 0) {\n        this.$message.warning('请先选择要导出的数据')\n        return\n      }\n      exportMonthly(this.selectedIds).then(data => {\n        const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })\n        const url = window.URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.href = url\n        a.download = '月总结导出.docx'\n        document.body.appendChild(a)\n        a.click()\n        document.body.removeChild(a)\n        window.URL.revokeObjectURL(url)\n      })\n    },\n    handleDownloadTemplate() {\n      downloadTemplateMonthly().then(data => {\n        const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })\n        const url = window.URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.href = url\n        a.download = '月总结导入模板.docx'\n        document.body.appendChild(a)\n        a.click()\n        document.body.removeChild(a)\n        window.URL.revokeObjectURL(url)\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.app-container {\n  padding: 20px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAgGA,IAAAA,QAAA,GAAAC,OAAA;AAQA,IAAAC,KAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACAC,KAAA;MACAC,OAAA;MACAC,QAAA;MACAC,aAAA;MACAC,WAAA;MACAC,IAAA;MACAC,WAAA;MACAC,SAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACAC,aAAA;QACAC,aAAA,kBAAAC,cAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,QAAA;EACA;EACAC,OAAA;IACAD,QAAA,WAAAA,SAAA;MAAA,IAAAE,KAAA;MACA,IAAAC,oBAAA;QAAAjB,OAAA,OAAAA,OAAA;QAAAC,QAAA,OAAAA;MAAA,GAAAiB,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAJ,KAAA,CAAAlB,SAAA,GAAAqB,GAAA,CAAAE,IAAA;UACAL,KAAA,CAAAjB,KAAA,GAAAoB,GAAA,CAAApB,KAAA;QACA;MACA;IACA;IACAuB,SAAA,WAAAA,UAAA;MACA,KAAAnB,WAAA;MACA,KAAAC,IAAA;MACA,KAAAF,aAAA;IACA;IACAqB,UAAA,WAAAA,WAAAC,GAAA;MACA,KAAArB,WAAA;MACA,KAAAC,IAAA,GAAAqB,MAAA,CAAAC,MAAA,KAAAF,GAAA;MACA,KAAAtB,aAAA;IACA;IACAyB,YAAA,WAAAA,aAAAH,GAAA;MAAA,IAAAI,MAAA;MACA,KAAAC,QAAA;QAAAC,IAAA;MAAA,GAAAZ,IAAA;QACA,IAAAa,mBAAA,EAAAP,GAAA,CAAAQ,EAAA,EAAAd,IAAA;UACAU,MAAA,CAAAK,QAAA,CAAAC,OAAA;UACAN,MAAA,CAAAd,QAAA;QACA;MACA;IACA;IACAqB,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,SAAAhC,IAAA,CAAA4B,EAAA;QACA,IAAAK,sBAAA,OAAAjC,IAAA,EAAAc,IAAA;UACAkB,MAAA,CAAAH,QAAA,CAAAC,OAAA;UACAE,MAAA,CAAAlC,aAAA;UACAkC,MAAA,CAAAtB,QAAA;QACA;MACA;QACA,IAAAwB,mBAAA,OAAAlC,IAAA,EAAAc,IAAA;UACAkB,MAAA,CAAAH,QAAA,CAAAC,OAAA;UACAE,MAAA,CAAAlC,aAAA;UACAkC,MAAA,CAAAtB,QAAA;QACA;MACA;IACA;IACAyB,qBAAA,WAAAA,sBAAAC,GAAA;MACA,KAAAnC,WAAA,GAAAmC,GAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAV,EAAA;MAAA;IACA;IACAW,mBAAA,WAAAA,oBAAAxB,GAAA;MACA,IAAAA,GAAA,CAAAC,IAAA;QACA,KAAAa,QAAA,CAAAC,OAAA,CAAAf,GAAA,CAAAyB,GAAA;QACA,KAAA9B,QAAA;MACA;QACA,KAAAmB,QAAA,CAAAY,KAAA,CAAA1B,GAAA,CAAAyB,GAAA;MACA;IACA;IACAE,iBAAA,WAAAA,kBAAA;MACA,KAAAb,QAAA,CAAAY,KAAA;IACA;IACAE,YAAA,WAAAA,aAAA;MACA,SAAA1C,WAAA,CAAA2C,MAAA;QACA,KAAAf,QAAA,CAAAgB,OAAA;QACA;MACA;MACA,IAAAC,sBAAA,OAAA7C,WAAA,EAAAa,IAAA,WAAArB,IAAA;QACA,IAAAsD,IAAA,OAAAC,IAAA,EAAAvD,IAAA;UAAAiC,IAAA;QAAA;QACA,IAAAuB,GAAA,GAAAC,MAAA,CAAAC,GAAA,CAAAC,eAAA,CAAAL,IAAA;QACA,IAAAM,CAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,CAAA,CAAAG,IAAA,GAAAP,GAAA;QACAI,CAAA,CAAAI,QAAA;QACAH,QAAA,CAAAI,IAAA,CAAAC,WAAA,CAAAN,CAAA;QACAA,CAAA,CAAAO,KAAA;QACAN,QAAA,CAAAI,IAAA,CAAAG,WAAA,CAAAR,CAAA;QACAH,MAAA,CAAAC,GAAA,CAAAW,eAAA,CAAAb,GAAA;MACA;IACA;IACAc,sBAAA,WAAAA,uBAAA;MACA,IAAAC,gCAAA,IAAAlD,IAAA,WAAArB,IAAA;QACA,IAAAsD,IAAA,OAAAC,IAAA,EAAAvD,IAAA;UAAAiC,IAAA;QAAA;QACA,IAAAuB,GAAA,GAAAC,MAAA,CAAAC,GAAA,CAAAC,eAAA,CAAAL,IAAA;QACA,IAAAM,CAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,CAAA,CAAAG,IAAA,GAAAP,GAAA;QACAI,CAAA,CAAAI,QAAA;QACAH,QAAA,CAAAI,IAAA,CAAAC,WAAA,CAAAN,CAAA;QACAA,CAAA,CAAAO,KAAA;QACAN,QAAA,CAAAI,IAAA,CAAAG,WAAA,CAAAR,CAAA;QACAH,MAAA,CAAAC,GAAA,CAAAW,eAAA,CAAAb,GAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}