{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/modules/uploader.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/modules/uploader.js", "mtime": 1753510684097}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_quill<PERSON><PERSON><PERSON>", "_interopRequireDefault", "require", "_emitter", "_module", "Uploader", "_Module", "quill", "options", "_this", "_classCallCheck2", "default", "_callSuper2", "root", "addEventListener", "e", "preventDefault", "native", "document", "caretRangeFromPoint", "clientX", "clientY", "caretPositionFromPoint", "position", "createRange", "setStart", "offsetNode", "offset", "setEnd", "normalized", "selection", "normalizeNative", "_e$dataTransfer", "range", "normalizedToRange", "dataTransfer", "files", "upload", "_inherits2", "_createClass2", "key", "value", "_this2", "uploads", "Array", "from", "for<PERSON>ach", "file", "_this2$options$mimety", "mimetypes", "includes", "type", "push", "length", "handler", "call", "<PERSON><PERSON><PERSON>", "DEFAULTS", "_this3", "scroll", "query", "promises", "map", "Promise", "resolve", "reader", "FileReader", "onload", "result", "readAsDataURL", "all", "then", "images", "update", "reduce", "delta", "image", "insert", "Delta", "retain", "index", "delete", "updateContents", "Emitter", "sources", "USER", "setSelection", "SILENT", "_default", "exports"], "sources": ["../../src/modules/uploader.ts"], "sourcesContent": ["import Delta from 'quill-delta';\nimport type Quill from '../core/quill.js';\nimport Emitter from '../core/emitter.js';\nimport Module from '../core/module.js';\nimport type { Range } from '../core/selection.js';\n\ninterface UploaderOptions {\n  mimetypes: string[];\n  handler: (this: { quill: Quill }, range: Range, files: File[]) => void;\n}\n\nclass Uploader extends Module<UploaderOptions> {\n  static DEFAULTS: UploaderOptions;\n\n  constructor(quill: Quill, options: Partial<UploaderOptions>) {\n    super(quill, options);\n    quill.root.addEventListener('drop', (e) => {\n      e.preventDefault();\n      let native: ReturnType<typeof document.createRange> | null = null;\n      if (document.caretRangeFromPoint) {\n        native = document.caretRangeFromPoint(e.clientX, e.clientY);\n        // @ts-expect-error\n      } else if (document.caretPositionFromPoint) {\n        // @ts-expect-error\n        const position = document.caretPositionFromPoint(e.clientX, e.clientY);\n        native = document.createRange();\n        native.setStart(position.offsetNode, position.offset);\n        native.setEnd(position.offsetNode, position.offset);\n      }\n\n      const normalized = native && quill.selection.normalizeNative(native);\n      if (normalized) {\n        const range = quill.selection.normalizedToRange(normalized);\n        if (e.dataTransfer?.files) {\n          this.upload(range, e.dataTransfer.files);\n        }\n      }\n    });\n  }\n\n  upload(range: Range, files: FileList | File[]) {\n    const uploads: File[] = [];\n    Array.from(files).forEach((file) => {\n      if (file && this.options.mimetypes?.includes(file.type)) {\n        uploads.push(file);\n      }\n    });\n    if (uploads.length > 0) {\n      // @ts-expect-error Fix me later\n      this.options.handler.call(this, range, uploads);\n    }\n  }\n}\n\nUploader.DEFAULTS = {\n  mimetypes: ['image/png', 'image/jpeg'],\n  handler(range: Range, files: File[]) {\n    if (!this.quill.scroll.query('image')) {\n      return;\n    }\n    const promises = files.map<Promise<string>>((file) => {\n      return new Promise((resolve) => {\n        const reader = new FileReader();\n        reader.onload = () => {\n          resolve(reader.result as string);\n        };\n        reader.readAsDataURL(file);\n      });\n    });\n    Promise.all(promises).then((images) => {\n      const update = images.reduce((delta: Delta, image) => {\n        return delta.insert({ image });\n      }, new Delta().retain(range.index).delete(range.length)) as Delta;\n      this.quill.updateContents(update, Emitter.sources.USER);\n      this.quill.setSelection(\n        range.index + images.length,\n        Emitter.sources.SILENT,\n      );\n    });\n  },\n};\n\nexport default Uploader;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,QAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,OAAA,GAAAH,sBAAA,CAAAC,OAAA;AAAsC,IAQhCG,QAAQ,0BAAAC,OAAA;EAGZ,SAAAD,SAAYE,KAAY,EAAEC,OAAiC,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAN,QAAA;IAC3DI,KAAA,OAAAG,WAAA,CAAAD,OAAA,QAAAN,QAAA,GAAME,KAAK,EAAEC,OAAO;IACpBD,KAAK,CAACM,IAAI,CAACC,gBAAgB,CAAC,MAAM,EAAG,UAAAC,CAAC,EAAK;MACzCA,CAAC,CAACC,cAAc,CAAC,CAAC;MAClB,IAAIC,MAAsD,GAAG,IAAI;MACjE,IAAIC,QAAQ,CAACC,mBAAmB,EAAE;QAChCF,MAAM,GAAGC,QAAQ,CAACC,mBAAmB,CAACJ,CAAC,CAACK,OAAO,EAAEL,CAAC,CAACM,OAAO,CAAC;QAC3D;MACF,CAAC,MAAM,IAAIH,QAAQ,CAACI,sBAAsB,EAAE;QAC1C;QACA,IAAMC,QAAQ,GAAGL,QAAQ,CAACI,sBAAsB,CAACP,CAAC,CAACK,OAAO,EAAEL,CAAC,CAACM,OAAO,CAAC;QACtEJ,MAAM,GAAGC,QAAQ,CAACM,WAAW,CAAC,CAAC;QAC/BP,MAAM,CAACQ,QAAQ,CAACF,QAAQ,CAACG,UAAU,EAAEH,QAAQ,CAACI,MAAM,CAAC;QACrDV,MAAM,CAACW,MAAM,CAACL,QAAQ,CAACG,UAAU,EAAEH,QAAQ,CAACI,MAAM,CAAC;MACrD;MAEA,IAAME,UAAU,GAAGZ,MAAM,IAAIV,KAAK,CAACuB,SAAS,CAACC,eAAe,CAACd,MAAM,CAAC;MACpE,IAAIY,UAAU,EAAE;QAAA,IAAAG,eAAA;QACd,IAAMC,KAAK,GAAG1B,KAAK,CAACuB,SAAS,CAACI,iBAAiB,CAACL,UAAU,CAAC;QAC3D,KAAAG,eAAA,GAAIjB,CAAC,CAACoB,YAAY,cAAAH,eAAA,eAAdA,eAAA,CAAgBI,KAAK,EAAE;UACzB3B,KAAA,CAAK4B,MAAM,CAACJ,KAAK,EAAElB,CAAC,CAACoB,YAAY,CAACC,KAAK,CAAC;QAC1C;MACF;IACF,CAAC,CAAC;IAAA,OAAA3B,KAAA;EACJ;EAAA,IAAA6B,UAAA,CAAA3B,OAAA,EAAAN,QAAA,EAAAC,OAAA;EAAA,WAAAiC,aAAA,CAAA5B,OAAA,EAAAN,QAAA;IAAAmC,GAAA;IAAAC,KAAA,EAEA,SAAAJ,MAAMA,CAACJ,KAAY,EAAEG,KAAwB,EAAE;MAAA,IAAAM,MAAA;MAC7C,IAAMC,OAAe,GAAG,EAAE;MAC1BC,KAAK,CAACC,IAAI,CAACT,KAAK,CAAC,CAACU,OAAO,CAAE,UAAAC,IAAI,EAAK;QAAA,IAAAC,qBAAA;QAClC,IAAID,IAAI,KAAAC,qBAAA,GAAIN,MAAI,CAAClC,OAAO,CAACyC,SAAS,cAAAD,qBAAA,eAAtBA,qBAAA,CAAwBE,QAAQ,CAACH,IAAI,CAACI,IAAI,CAAC,EAAE;UACvDR,OAAO,CAACS,IAAI,CAACL,IAAI,CAAC;QACpB;MACF,CAAC,CAAC;MACF,IAAIJ,OAAO,CAACU,MAAM,GAAG,CAAC,EAAE;QACtB;QACA,IAAI,CAAC7C,OAAO,CAAC8C,OAAO,CAACC,IAAI,CAAC,IAAI,EAAEtB,KAAK,EAAEU,OAAO,CAAC;MACjD;IACF;EAAA;AAAA,EAxCqBa,eAAM;AA2C7BnD,QAAQ,CAACoD,QAAQ,GAAG;EAClBR,SAAS,EAAE,CAAC,WAAW,EAAE,YAAY,CAAC;EACtCK,OAAO,WAAPA,OAAOA,CAACrB,KAAY,EAAEG,KAAa,EAAE;IAAA,IAAAsB,MAAA;IACnC,IAAI,CAAC,IAAI,CAACnD,KAAK,CAACoD,MAAM,CAACC,KAAK,CAAC,OAAO,CAAC,EAAE;MACrC;IACF;IACA,IAAMC,QAAQ,GAAGzB,KAAK,CAAC0B,GAAG,CAAmB,UAAAf,IAAI,EAAK;MACpD,OAAO,IAAIgB,OAAO,CAAE,UAAAC,OAAO,EAAK;QAC9B,IAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;QAC/BD,MAAM,CAACE,MAAM,GAAG,YAAM;UACpBH,OAAO,CAACC,MAAM,CAACG,MAAgB,CAAC;QAClC,CAAC;QACDH,MAAM,CAACI,aAAa,CAACtB,IAAI,CAAC;MAC5B,CAAC,CAAC;IACJ,CAAC,CAAC;IACFgB,OAAO,CAACO,GAAG,CAACT,QAAQ,CAAC,CAACU,IAAI,CAAE,UAAAC,MAAM,EAAK;MACrC,IAAMC,MAAM,GAAGD,MAAM,CAACE,MAAM,CAAC,UAACC,KAAY,EAAEC,KAAK,EAAK;QACpD,OAAOD,KAAK,CAACE,MAAM,CAAC;UAAED,KAAA,EAAAA;QAAM,CAAC,CAAC;MAChC,CAAC,EAAE,IAAIE,mBAAK,CAAC,CAAC,CAACC,MAAM,CAAC9C,KAAK,CAAC+C,KAAK,CAAC,CAACC,MAAM,CAAChD,KAAK,CAACoB,MAAM,CAAC,CAAU;MACjEK,MAAI,CAACnD,KAAK,CAAC2E,cAAc,CAACT,MAAM,EAAEU,gBAAO,CAACC,OAAO,CAACC,IAAI,CAAC;MACvD3B,MAAI,CAACnD,KAAK,CAAC+E,YAAY,CACrBrD,KAAK,CAAC+C,KAAK,GAAGR,MAAM,CAACnB,MAAM,EAC3B8B,gBAAO,CAACC,OAAO,CAACG,MAClB,CAAC;IACH,CAAC,CAAC;EACJ;AACF,CAAC;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAA9E,OAAA,GAEcN,QAAQ", "ignoreList": []}]}