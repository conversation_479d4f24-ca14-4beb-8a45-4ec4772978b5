{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/layout/components/Sidebar/SidebarItem.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/layout/components/Sidebar/SidebarItem.vue", "mtime": 1753510684529}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_path", "_interopRequireDefault", "require", "_validate", "_Item", "_Link", "_FixiOSBug", "name", "components", "<PERSON><PERSON>", "AppLink", "mixins", "FixiOSBug", "props", "item", "type", "Object", "required", "isNest", "Boolean", "default", "basePath", "String", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "methods", "hasOneShowingChild", "_this", "children", "arguments", "length", "undefined", "parent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "hidden", "_objectSpread2", "path", "noShowingChildren", "<PERSON><PERSON><PERSON>", "routePath", "routeQuery", "isExternal", "query", "JSON", "parse", "resolve", "created", "console", "log"], "sources": ["src/layout/components/Sidebar/SidebarItem.vue"], "sourcesContent": ["<template>\n  <div v-if=\"!item.hidden\">\n    <template v-if=\"hasOneShowingChild(item.children,item) && (!onlyOneChild.children||onlyOneChild.noShowingChildren)&&!item.alwaysShow\">\n      <app-link v-if=\"onlyOneChild.meta\" :to=\"resolvePath(onlyOneChild.path, onlyOneChild.query)\">\n        <el-menu-item :index=\"resolvePath(onlyOneChild.path)\" :class=\"{'submenu-title-noDropdown':!isNest}\">\n          <item :icon=\"onlyOneChild.meta.icon||(item.meta&&item.meta.icon)\" :title=\"onlyOneChild.meta.title\" />\n        </el-menu-item>\n      </app-link>\n    </template>\n\n    <el-submenu v-else ref=\"subMenu\" :index=\"resolvePath(item.path)\" popper-append-to-body>\n      <template slot=\"title\">\n        <item v-if=\"item.meta\" :icon=\"item.meta && item.meta.icon\" :title=\"item.meta.title\" />\n      </template>\n      <sidebar-item\n        v-for=\"(child, index) in item.children\"\n        :key=\"child.path + index\"\n        :is-nest=\"true\"\n        :item=\"child\"\n        :base-path=\"resolvePath(child.path)\"\n        class=\"nest-menu\"\n      />\n    </el-submenu>\n  </div>\n</template>\n\n<script>\nimport path from 'path'\nimport { isExternal } from '@/utils/validate'\nimport Item from './Item'\nimport AppLink from './Link'\nimport FixiOSBug from './FixiOSBug'\n\nexport default {\n  name: 'SidebarItem',\n  components: { Item, AppLink },\n  mixins: [FixiOSBug],\n  props: {\n    // route object\n    item: {\n      type: Object,\n      required: true\n    },\n    isNest: {\n      type: Boolean,\n      default: false\n    },\n    basePath: {\n      type: String,\n      default: ''\n    }\n  },\n  data() {\n    this.onlyOneChild = null\n    return {}\n  },\n  methods: {\n    hasOneShowingChild(children = [], parent) {\n      if (!children) {\n        children = []\n      }\n      const showingChildren = children.filter(item => {\n        if (item.hidden) {\n          return false\n        }\n        // Temp set(will be used if only has one showing child)\n        this.onlyOneChild = item\n        return true\n      })\n\n      // When there is only one child router, the child router is displayed by default\n      if (showingChildren.length === 1) {\n        return true\n      }\n\n      // Show parent if there are no child router to display\n      if (showingChildren.length === 0) {\n        this.onlyOneChild = { ... parent, path: '', noShowingChildren: true }\n        return true\n      }\n\n      return false\n    },\n    resolvePath(routePath, routeQuery) {\n      if (isExternal(routePath)) {\n        return routePath\n      }\n      if (isExternal(this.basePath)) {\n        return this.basePath\n      }\n      if (routeQuery) {\n        let query = JSON.parse(routeQuery)\n        return { path: path.resolve(this.basePath, routePath), query: query }\n      }\n      return path.resolve(this.basePath, routePath)\n    }\n  },\n  created() {\n    console.log('SidebarItem组件中的item数据:', this.item)\n  }\n}\n</script>\n"], "mappings": ";;;;;;;;;;;;;AA2BA,IAAAA,KAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,KAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,UAAA,GAAAL,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAK,IAAA;EACAC,UAAA;IAAAC,IAAA,EAAAA,aAAA;IAAAC,OAAA,EAAAA;EAAA;EACAC,MAAA,GAAAC,kBAAA;EACAC,KAAA;IACA;IACAC,IAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAC,MAAA;MACAH,IAAA,EAAAI,OAAA;MACAC,OAAA;IACA;IACAC,QAAA;MACAN,IAAA,EAAAO,MAAA;MACAF,OAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA,KAAAC,YAAA;IACA;EACA;EACAC,OAAA;IACAC,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,KAAA;MAAA,IAAAC,QAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;MAAA,IAAAG,MAAA,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;MACA,KAAAH,QAAA;QACAA,QAAA;MACA;MACA,IAAAK,eAAA,GAAAL,QAAA,CAAAM,MAAA,WAAApB,IAAA;QACA,IAAAA,IAAA,CAAAqB,MAAA;UACA;QACA;QACA;QACAR,KAAA,CAAAH,YAAA,GAAAV,IAAA;QACA;MACA;;MAEA;MACA,IAAAmB,eAAA,CAAAH,MAAA;QACA;MACA;;MAEA;MACA,IAAAG,eAAA,CAAAH,MAAA;QACA,KAAAN,YAAA,OAAAY,cAAA,CAAAhB,OAAA,MAAAgB,cAAA,CAAAhB,OAAA,MAAAY,MAAA;UAAAK,IAAA;UAAAC,iBAAA;QAAA;QACA;MACA;MAEA;IACA;IACAC,WAAA,WAAAA,YAAAC,SAAA,EAAAC,UAAA;MACA,QAAAC,oBAAA,EAAAF,SAAA;QACA,OAAAA,SAAA;MACA;MACA,QAAAE,oBAAA,OAAArB,QAAA;QACA,YAAAA,QAAA;MACA;MACA,IAAAoB,UAAA;QACA,IAAAE,KAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAJ,UAAA;QACA;UAAAJ,IAAA,EAAAA,aAAA,CAAAS,OAAA,MAAAzB,QAAA,EAAAmB,SAAA;UAAAG,KAAA,EAAAA;QAAA;MACA;MACA,OAAAN,aAAA,CAAAS,OAAA,MAAAzB,QAAA,EAAAmB,SAAA;IACA;EACA;EACAO,OAAA,WAAAA,QAAA;IACAC,OAAA,CAAAC,GAAA,gCAAAnC,IAAA;EACA;AACA", "ignoreList": []}]}