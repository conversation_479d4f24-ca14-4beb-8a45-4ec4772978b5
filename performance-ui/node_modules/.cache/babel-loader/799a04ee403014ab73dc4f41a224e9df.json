{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/utils/generator/render.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/utils/generator/render.js", "mtime": 1753510684532}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_index", "require", "isAttr", "makeMap", "vModel", "self", "dataObject", "defaultValue", "props", "value", "on", "input", "val", "$emit", "componentChild", "default", "h", "conf", "key", "prepend", "append", "options", "list", "for<PERSON>ach", "item", "push", "label", "disabled", "optionType", "border", "listType", "buttonText", "showTip", "fileSize", "sizeUnit", "accept", "_default2", "exports", "render", "_this", "attrs", "style", "confClone", "JSON", "parse", "stringify", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tag", "Object", "keys", "childFunc"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/utils/generator/render.js"], "sourcesContent": ["import { makeMap } from '@/utils/index'\n\n// 参考https://github.com/vuejs/vue/blob/v2.6.10/src/platforms/web/server/util.js\nconst isAttr = makeMap(\n  'accept,accept-charset,accesskey,action,align,alt,async,autocomplete,'\n  + 'autofocus,autoplay,autosave,bgcolor,border,buffered,challenge,charset,'\n  + 'checked,cite,class,code,codebase,color,cols,colspan,content,http-equiv,'\n  + 'name,contenteditable,contextmenu,controls,coords,data,datetime,default,'\n  + 'defer,dir,dirname,disabled,download,draggable,dropzone,enctype,method,for,'\n  + 'form,formaction,headers,height,hidden,high,href,hreflang,http-equiv,'\n  + 'icon,id,ismap,itemprop,keytype,kind,label,lang,language,list,loop,low,'\n  + 'manifest,max,maxlength,media,method,GET,POST,min,multiple,email,file,'\n  + 'muted,name,novalidate,open,optimum,pattern,ping,placeholder,poster,'\n  + 'preload,radiogroup,readonly,rel,required,reversed,rows,rowspan,sandbox,'\n  + 'scope,scoped,seamless,selected,shape,size,type,text,password,sizes,span,'\n  + 'spellcheck,src,srcdoc,srclang,srcset,start,step,style,summary,tabindex,'\n  + 'target,title,type,usemap,value,width,wrap'\n)\n\nfunction vModel(self, dataObject, defaultValue) {\n  dataObject.props.value = defaultValue\n\n  dataObject.on.input = val => {\n    self.$emit('input', val)\n  }\n}\n\nconst componentChild = {\n  'el-button': {\n    default(h, conf, key) {\n      return conf[key]\n    },\n  },\n  'el-input': {\n    prepend(h, conf, key) {\n      return <template slot=\"prepend\">{conf[key]}</template>\n    },\n    append(h, conf, key) {\n      return <template slot=\"append\">{conf[key]}</template>\n    }\n  },\n  'el-select': {\n    options(h, conf, key) {\n      const list = []\n      conf.options.forEach(item => {\n        list.push(<el-option label={item.label} value={item.value} disabled={item.disabled}></el-option>)\n      })\n      return list\n    }\n  },\n  'el-radio-group': {\n    options(h, conf, key) {\n      const list = []\n      conf.options.forEach(item => {\n        if (conf.optionType === 'button') list.push(<el-radio-button label={item.value}>{item.label}</el-radio-button>)\n        else list.push(<el-radio label={item.value} border={conf.border}>{item.label}</el-radio>)\n      })\n      return list\n    }\n  },\n  'el-checkbox-group': {\n    options(h, conf, key) {\n      const list = []\n      conf.options.forEach(item => {\n        if (conf.optionType === 'button') {\n          list.push(<el-checkbox-button label={item.value}>{item.label}</el-checkbox-button>)\n        } else {\n          list.push(<el-checkbox label={item.value} border={conf.border}>{item.label}</el-checkbox>)\n        }\n      })\n      return list\n    }\n  },\n  'el-upload': {\n    'list-type': (h, conf, key) => {\n      const list = []\n      if (conf['list-type'] === 'picture-card') {\n        list.push(<i class=\"el-icon-plus\"></i>)\n      } else {\n        list.push(<el-button size=\"small\" type=\"primary\" icon=\"el-icon-upload\">{conf.buttonText}</el-button>)\n      }\n      if (conf.showTip) {\n        list.push(<div slot=\"tip\" class=\"el-upload__tip\">只能上传不超过 {conf.fileSize}{conf.sizeUnit} 的{conf.accept}文件</div>)\n      }\n      return list\n    }\n  }\n}\n\nexport default {\n  render(h) {\n    const dataObject = {\n      attrs: {},\n      props: {},\n      on: {},\n      style: {}\n    }\n    const confClone = JSON.parse(JSON.stringify(this.conf))\n    const children = []\n\n    const childObjs = componentChild[confClone.tag]\n    if (childObjs) {\n      Object.keys(childObjs).forEach(key => {\n        const childFunc = childObjs[key]\n        if (confClone[key]) {\n          children.push(childFunc(h, confClone, key))\n        }\n      })\n    }\n\n    Object.keys(confClone).forEach(key => {\n      const val = confClone[key]\n      if (key === 'vModel') {\n        vModel(this, dataObject, confClone.defaultValue)\n      } else if (dataObject[key]) {\n        dataObject[key] = val\n      } else if (!isAttr(key)) {\n        dataObject.props[key] = val\n      } else {\n        dataObject.attrs[key] = val\n      }\n    })\n    return h(this.conf.tag, dataObject, children)\n  },\n  props: ['conf']\n}\n"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAEA;AACA,IAAMC,MAAM,GAAG,IAAAC,cAAO,EACpB,sEAAsE,GACpE,wEAAwE,GACxE,yEAAyE,GACzE,yEAAyE,GACzE,4EAA4E,GAC5E,sEAAsE,GACtE,wEAAwE,GACxE,uEAAuE,GACvE,qEAAqE,GACrE,yEAAyE,GACzE,0EAA0E,GAC1E,yEAAyE,GACzE,2CACJ,CAAC;AAED,SAASC,MAAMA,CAACC,IAAI,EAAEC,UAAU,EAAEC,YAAY,EAAE;EAC9CD,UAAU,CAACE,KAAK,CAACC,KAAK,GAAGF,YAAY;EAErCD,UAAU,CAACI,EAAE,CAACC,KAAK,GAAG,UAAAC,GAAG,EAAI;IAC3BP,IAAI,CAACQ,KAAK,CAAC,OAAO,EAAED,GAAG,CAAC;EAC1B,CAAC;AACH;AAEA,IAAME,cAAc,GAAG;EACrB,WAAW,EAAE;IACXC,OAAO,WAAPA,QAAOA,CAACC,CAAC,EAAEC,IAAI,EAAEC,GAAG,EAAE;MACpB,OAAOD,IAAI,CAACC,GAAG,CAAC;IAClB;EACF,CAAC;EACD,UAAU,EAAE;IACVC,OAAO,WAAPA,OAAOA,CAACH,CAAC,EAAEC,IAAI,EAAEC,GAAG,EAAE;MACpB,OAAAF,CAAA;QAAA,QAAsB;MAAS,IAAEC,IAAI,CAACC,GAAG,CAAC;IAC5C,CAAC;IACDE,MAAM,WAANA,MAAMA,CAACJ,CAAC,EAAEC,IAAI,EAAEC,GAAG,EAAE;MACnB,OAAAF,CAAA;QAAA,QAAsB;MAAQ,IAAEC,IAAI,CAACC,GAAG,CAAC;IAC3C;EACF,CAAC;EACD,WAAW,EAAE;IACXG,OAAO,WAAPA,OAAOA,CAACL,CAAC,EAAEC,IAAI,EAAEC,GAAG,EAAE;MACpB,IAAMI,IAAI,GAAG,EAAE;MACfL,IAAI,CAACI,OAAO,CAACE,OAAO,CAAC,UAAAC,IAAI,EAAI;QAC3BF,IAAI,CAACG,IAAI,CAAAT,CAAA;UAAA;YAAA,SAAmBQ,IAAI,CAACE,KAAK;YAAA,SAASF,IAAI,CAACf,KAAK;YAAA,YAAYe,IAAI,CAACG;UAAQ;QAAA,EAAc,CAAC;MACnG,CAAC,CAAC;MACF,OAAOL,IAAI;IACb;EACF,CAAC;EACD,gBAAgB,EAAE;IAChBD,OAAO,WAAPA,OAAOA,CAACL,CAAC,EAAEC,IAAI,EAAEC,GAAG,EAAE;MACpB,IAAMI,IAAI,GAAG,EAAE;MACfL,IAAI,CAACI,OAAO,CAACE,OAAO,CAAC,UAAAC,IAAI,EAAI;QAC3B,IAAIP,IAAI,CAACW,UAAU,KAAK,QAAQ,EAAEN,IAAI,CAACG,IAAI,CAAAT,CAAA;UAAA;YAAA,SAAyBQ,IAAI,CAACf;UAAK;QAAA,IAAGe,IAAI,CAACE,KAAK,EAAmB,CAAC,MAC1GJ,IAAI,CAACG,IAAI,CAAAT,CAAA;UAAA;YAAA,SAAkBQ,IAAI,CAACf,KAAK;YAAA,UAAUQ,IAAI,CAACY;UAAM;QAAA,IAAGL,IAAI,CAACE,KAAK,EAAY,CAAC;MAC3F,CAAC,CAAC;MACF,OAAOJ,IAAI;IACb;EACF,CAAC;EACD,mBAAmB,EAAE;IACnBD,OAAO,WAAPA,OAAOA,CAACL,CAAC,EAAEC,IAAI,EAAEC,GAAG,EAAE;MACpB,IAAMI,IAAI,GAAG,EAAE;MACfL,IAAI,CAACI,OAAO,CAACE,OAAO,CAAC,UAAAC,IAAI,EAAI;QAC3B,IAAIP,IAAI,CAACW,UAAU,KAAK,QAAQ,EAAE;UAChCN,IAAI,CAACG,IAAI,CAAAT,CAAA;YAAA;cAAA,SAA4BQ,IAAI,CAACf;YAAK;UAAA,IAAGe,IAAI,CAACE,KAAK,EAAsB,CAAC;QACrF,CAAC,MAAM;UACLJ,IAAI,CAACG,IAAI,CAAAT,CAAA;YAAA;cAAA,SAAqBQ,IAAI,CAACf,KAAK;cAAA,UAAUQ,IAAI,CAACY;YAAM;UAAA,IAAGL,IAAI,CAACE,KAAK,EAAe,CAAC;QAC5F;MACF,CAAC,CAAC;MACF,OAAOJ,IAAI;IACb;EACF,CAAC;EACD,WAAW,EAAE;IACX,WAAW,EAAE,SAAbQ,QAAWA,CAAGd,CAAC,EAAEC,IAAI,EAAEC,GAAG,EAAK;MAC7B,IAAMI,IAAI,GAAG,EAAE;MACf,IAAIL,IAAI,CAAC,WAAW,CAAC,KAAK,cAAc,EAAE;QACxCK,IAAI,CAACG,IAAI,CAAAT,CAAA;UAAA,SAAU;QAAc,EAAK,CAAC;MACzC,CAAC,MAAM;QACLM,IAAI,CAACG,IAAI,CAAAT,CAAA;UAAA;YAAA,QAAiB,OAAO;YAAA,QAAM,SAAS;YAAA,QAAM;UAAgB;QAAA,IAAEC,IAAI,CAACc,UAAU,EAAa,CAAC;MACvG;MACA,IAAId,IAAI,CAACe,OAAO,EAAE;QAChBV,IAAI,CAACG,IAAI,CAAAT,CAAA;UAAA,QAAW,KAAK;UAAA,SAAO;QAAgB,mDAAUC,IAAI,CAACgB,QAAQ,EAAEhB,IAAI,CAACiB,QAAQ,aAAIjB,IAAI,CAACkB,MAAM,kBAAS,CAAC;MACjH;MACA,OAAOb,IAAI;IACb;EACF;AACF,CAAC;AAAA,IAAAc,SAAA,GAAAC,OAAA,CAAAtB,OAAA,GAEc;EACbuB,MAAM,WAANA,MAAMA,CAACtB,CAAC,EAAE;IAAA,IAAAuB,KAAA;IACR,IAAMjC,UAAU,GAAG;MACjBkC,KAAK,EAAE,CAAC,CAAC;MACThC,KAAK,EAAE,CAAC,CAAC;MACTE,EAAE,EAAE,CAAC,CAAC;MACN+B,KAAK,EAAE,CAAC;IACV,CAAC;IACD,IAAMC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAAC,IAAI,CAAC5B,IAAI,CAAC,CAAC;IACvD,IAAM6B,QAAQ,GAAG,EAAE;IAEnB,IAAMC,SAAS,GAAGjC,cAAc,CAAC4B,SAAS,CAACM,GAAG,CAAC;IAC/C,IAAID,SAAS,EAAE;MACbE,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACxB,OAAO,CAAC,UAAAL,GAAG,EAAI;QACpC,IAAMiC,SAAS,GAAGJ,SAAS,CAAC7B,GAAG,CAAC;QAChC,IAAIwB,SAAS,CAACxB,GAAG,CAAC,EAAE;UAClB4B,QAAQ,CAACrB,IAAI,CAAC0B,SAAS,CAACnC,CAAC,EAAE0B,SAAS,EAAExB,GAAG,CAAC,CAAC;QAC7C;MACF,CAAC,CAAC;IACJ;IAEA+B,MAAM,CAACC,IAAI,CAACR,SAAS,CAAC,CAACnB,OAAO,CAAC,UAAAL,GAAG,EAAI;MACpC,IAAMN,GAAG,GAAG8B,SAAS,CAACxB,GAAG,CAAC;MAC1B,IAAIA,GAAG,KAAK,QAAQ,EAAE;QACpBd,MAAM,CAACmC,KAAI,EAAEjC,UAAU,EAAEoC,SAAS,CAACnC,YAAY,CAAC;MAClD,CAAC,MAAM,IAAID,UAAU,CAACY,GAAG,CAAC,EAAE;QAC1BZ,UAAU,CAACY,GAAG,CAAC,GAAGN,GAAG;MACvB,CAAC,MAAM,IAAI,CAACV,MAAM,CAACgB,GAAG,CAAC,EAAE;QACvBZ,UAAU,CAACE,KAAK,CAACU,GAAG,CAAC,GAAGN,GAAG;MAC7B,CAAC,MAAM;QACLN,UAAU,CAACkC,KAAK,CAACtB,GAAG,CAAC,GAAGN,GAAG;MAC7B;IACF,CAAC,CAAC;IACF,OAAOI,CAAC,CAAC,IAAI,CAACC,IAAI,CAAC+B,GAAG,EAAE1C,UAAU,EAAEwC,QAAQ,CAAC;EAC/C,CAAC;EACDtC,KAAK,EAAE,CAAC,MAAM;AAChB,CAAC", "ignoreList": []}]}