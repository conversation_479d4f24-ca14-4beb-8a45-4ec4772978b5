{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/system/role/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/system/role/index.vue", "mtime": 1753510684536}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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<PERSON>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"}, {"version": 3, "names": ["_role", "require", "_menu", "name", "dicts", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "roleList", "title", "open", "openDataScope", "menuExpand", "menuNodeAll", "deptExpand", "deptNodeAll", "date<PERSON><PERSON><PERSON>", "dataScopeOptions", "value", "label", "menuOptions", "deptOptions", "queryParams", "pageNum", "pageSize", "<PERSON><PERSON><PERSON>", "undefined", "<PERSON><PERSON><PERSON>", "status", "form", "defaultProps", "children", "rules", "required", "message", "trigger", "roleSort", "created", "getList", "methods", "_this", "listRole", "addDateRange", "then", "response", "rows", "getMenuTreeselect", "_this2", "menuTreeselect", "getMenuAllCheckedKeys", "checked<PERSON>eys", "$refs", "menu", "getChe<PERSON><PERSON>eys", "halfC<PERSON>cked<PERSON>eys", "getHalfCheckedKeys", "unshift", "apply", "getDeptAllCheckedKeys", "dept", "getRoleMenuTreeselect", "roleId", "_this3", "roleMenuTreeselect", "menus", "getDeptTree", "_this4", "deptTreeSelect", "depts", "handleStatusChange", "row", "_this5", "text", "$modal", "confirm", "changeRoleStatus", "msgSuccess", "catch", "cancel", "reset", "cancelDataScope", "set<PERSON><PERSON><PERSON><PERSON>eys", "menuIds", "deptIds", "menu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "remark", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleCommand", "command", "handleDataScope", "handleAuthUser", "handleCheckedTreeExpand", "type", "treeList", "i", "store", "nodesMap", "id", "expanded", "handleCheckedTreeNodeAll", "setCheckedNodes", "handleCheckedTreeConnect", "handleAdd", "handleUpdate", "_this6", "roleMenu", "getRole", "$nextTick", "res", "for<PERSON>ach", "v", "setChecked", "dataScopeSelectChange", "_this7", "$router", "push", "submitForm", "_this8", "validate", "valid", "updateRole", "addRole", "submitDataScope", "_this9", "dataScope", "handleDelete", "_this0", "roleIds", "delRole", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime"], "sources": ["src/views/system/role/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\">\n      <el-form-item label=\"角色名称\" prop=\"roleName\">\n        <el-input\n          v-model=\"queryParams.roleName\"\n          placeholder=\"请输入角色名称\"\n          clearable\n          style=\"width: 240px\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"权限字符\" prop=\"roleKey\">\n        <el-input\n          v-model=\"queryParams.roleKey\"\n          placeholder=\"请输入权限字符\"\n          clearable\n          style=\"width: 240px\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"状态\" prop=\"status\">\n        <el-select\n          v-model=\"queryParams.status\"\n          placeholder=\"角色状态\"\n          clearable\n          style=\"width: 240px\"\n        >\n          <el-option\n            v-for=\"dict in dict.type.sys_normal_disable\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"创建时间\">\n        <el-date-picker\n          v-model=\"dateRange\"\n          style=\"width: 240px\"\n          value-format=\"yyyy-MM-dd\"\n          type=\"daterange\"\n          range-separator=\"-\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n        ></el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['system:role:add']\"\n        >新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n          v-hasPermi=\"['system:role:edit']\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['system:role:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['system:role:export']\"\n        >导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"roleList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"角色编号\" prop=\"roleId\" width=\"120\" />\n      <el-table-column label=\"角色名称\" prop=\"roleName\" :show-overflow-tooltip=\"true\" width=\"150\" />\n      <el-table-column label=\"权限字符\" prop=\"roleKey\" :show-overflow-tooltip=\"true\" width=\"150\" />\n      <el-table-column label=\"显示顺序\" prop=\"roleSort\" width=\"100\" />\n      <el-table-column label=\"状态\" align=\"center\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <el-switch\n            v-model=\"scope.row.status\"\n            active-value=\"0\"\n            inactive-value=\"1\"\n            @change=\"handleStatusChange(scope.row)\"\n          ></el-switch>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\" v-if=\"scope.row.roleId !== 1\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['system:role:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['system:role:remove']\"\n          >删除</el-button>\n          <el-dropdown size=\"mini\" @command=\"(command) => handleCommand(command, scope.row)\" v-hasPermi=\"['system:role:edit']\">\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-d-arrow-right\">更多</el-button>\n            <el-dropdown-menu slot=\"dropdown\">\n              <el-dropdown-item command=\"handleDataScope\" icon=\"el-icon-circle-check\"\n                v-hasPermi=\"['system:role:edit']\">数据权限</el-dropdown-item>\n              <el-dropdown-item command=\"handleAuthUser\" icon=\"el-icon-user\"\n                v-hasPermi=\"['system:role:edit']\">分配用户</el-dropdown-item>\n            </el-dropdown-menu>\n          </el-dropdown>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改角色配置对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n        <el-form-item label=\"角色名称\" prop=\"roleName\">\n          <el-input v-model=\"form.roleName\" placeholder=\"请输入角色名称\" />\n        </el-form-item>\n        <el-form-item prop=\"roleKey\">\n          <span slot=\"label\">\n            <el-tooltip content=\"控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasRole('admin')`)\" placement=\"top\">\n              <i class=\"el-icon-question\"></i>\n            </el-tooltip>\n            权限字符\n          </span>\n          <el-input v-model=\"form.roleKey\" placeholder=\"请输入权限字符\" />\n        </el-form-item>\n        <el-form-item label=\"角色顺序\" prop=\"roleSort\">\n          <el-input-number v-model=\"form.roleSort\" controls-position=\"right\" :min=\"0\" />\n        </el-form-item>\n        <el-form-item label=\"状态\">\n          <el-radio-group v-model=\"form.status\">\n            <el-radio\n              v-for=\"dict in dict.type.sys_normal_disable\"\n              :key=\"dict.value\"\n              :label=\"dict.value\"\n            >{{dict.label}}</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"菜单权限\">\n          <el-checkbox v-model=\"menuExpand\" @change=\"handleCheckedTreeExpand($event, 'menu')\">展开/折叠</el-checkbox>\n          <el-checkbox v-model=\"menuNodeAll\" @change=\"handleCheckedTreeNodeAll($event, 'menu')\">全选/全不选</el-checkbox>\n          <el-checkbox v-model=\"form.menuCheckStrictly\" @change=\"handleCheckedTreeConnect($event, 'menu')\">父子联动</el-checkbox>\n          <el-tree\n            class=\"tree-border\"\n            :data=\"menuOptions\"\n            show-checkbox\n            ref=\"menu\"\n            node-key=\"id\"\n            :check-strictly=\"!form.menuCheckStrictly\"\n            empty-text=\"加载中，请稍候\"\n            :props=\"defaultProps\"\n          ></el-tree>\n        </el-form-item>\n        <el-form-item label=\"备注\">\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\"></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 分配角色数据权限对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"openDataScope\" width=\"500px\" append-to-body>\n      <el-form :model=\"form\" label-width=\"80px\">\n        <el-form-item label=\"角色名称\">\n          <el-input v-model=\"form.roleName\" :disabled=\"true\" />\n        </el-form-item>\n        <el-form-item label=\"权限字符\">\n          <el-input v-model=\"form.roleKey\" :disabled=\"true\" />\n        </el-form-item>\n        <el-form-item label=\"权限范围\">\n          <el-select v-model=\"form.dataScope\" @change=\"dataScopeSelectChange\">\n            <el-option\n              v-for=\"item in dataScopeOptions\"\n              :key=\"item.value\"\n              :label=\"item.label\"\n              :value=\"item.value\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"数据权限\" v-show=\"form.dataScope == 2\">\n          <el-checkbox v-model=\"deptExpand\" @change=\"handleCheckedTreeExpand($event, 'dept')\">展开/折叠</el-checkbox>\n          <el-checkbox v-model=\"deptNodeAll\" @change=\"handleCheckedTreeNodeAll($event, 'dept')\">全选/全不选</el-checkbox>\n          <el-checkbox v-model=\"form.deptCheckStrictly\" @change=\"handleCheckedTreeConnect($event, 'dept')\">父子联动</el-checkbox>\n          <el-tree\n            class=\"tree-border\"\n            :data=\"deptOptions\"\n            show-checkbox\n            default-expand-all\n            ref=\"dept\"\n            node-key=\"id\"\n            :check-strictly=\"!form.deptCheckStrictly\"\n            empty-text=\"加载中，请稍候\"\n            :props=\"defaultProps\"\n          ></el-tree>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitDataScope\">确 定</el-button>\n        <el-button @click=\"cancelDataScope\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listRole, getRole, delRole, addRole, updateRole, dataScope, changeRoleStatus, deptTreeSelect } from \"@/api/system/role\"\nimport { treeselect as menuTreeselect, roleMenuTreeselect } from \"@/api/system/menu\"\n\nexport default {\n  name: \"Role\",\n  dicts: ['sys_normal_disable'],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 角色表格数据\n      roleList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 是否显示弹出层（数据权限）\n      openDataScope: false,\n      menuExpand: false,\n      menuNodeAll: false,\n      deptExpand: true,\n      deptNodeAll: false,\n      // 日期范围\n      dateRange: [],\n      // 数据范围选项\n      dataScopeOptions: [\n        {\n          value: \"1\",\n          label: \"全部数据权限\"\n        },\n        {\n          value: \"2\",\n          label: \"自定数据权限\"\n        },\n        {\n          value: \"3\",\n          label: \"本部门数据权限\"\n        },\n        {\n          value: \"4\",\n          label: \"本部门及以下数据权限\"\n        },\n        {\n          value: \"5\",\n          label: \"仅本人数据权限\"\n        }\n      ],\n      // 菜单列表\n      menuOptions: [],\n      // 部门列表\n      deptOptions: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        roleName: undefined,\n        roleKey: undefined,\n        status: undefined\n      },\n      // 表单参数\n      form: {},\n      defaultProps: {\n        children: \"children\",\n        label: \"label\"\n      },\n      // 表单校验\n      rules: {\n        roleName: [\n          { required: true, message: \"角色名称不能为空\", trigger: \"blur\" }\n        ],\n        roleKey: [\n          { required: true, message: \"权限字符不能为空\", trigger: \"blur\" }\n        ],\n        roleSort: [\n          { required: true, message: \"角色顺序不能为空\", trigger: \"blur\" }\n        ]\n      }\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    /** 查询角色列表 */\n    getList() {\n      this.loading = true\n      listRole(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\n          this.roleList = response.rows\n          this.total = response.total\n          this.loading = false\n        }\n      )\n    },\n    /** 查询菜单树结构 */\n    getMenuTreeselect() {\n      menuTreeselect().then(response => {\n        this.menuOptions = response.data\n      })\n    },\n    // 所有菜单节点数据\n    getMenuAllCheckedKeys() {\n      // 目前被选中的菜单节点\n      let checkedKeys = this.$refs.menu.getCheckedKeys()\n      // 半选中的菜单节点\n      let halfCheckedKeys = this.$refs.menu.getHalfCheckedKeys()\n      checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys)\n      return checkedKeys\n    },\n    // 所有部门节点数据\n    getDeptAllCheckedKeys() {\n      // 目前被选中的部门节点\n      let checkedKeys = this.$refs.dept.getCheckedKeys()\n      // 半选中的部门节点\n      let halfCheckedKeys = this.$refs.dept.getHalfCheckedKeys()\n      checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys)\n      return checkedKeys\n    },\n    /** 根据角色ID查询菜单树结构 */\n    getRoleMenuTreeselect(roleId) {\n      return roleMenuTreeselect(roleId).then(response => {\n        this.menuOptions = response.menus\n        return response\n      })\n    },\n    /** 根据角色ID查询部门树结构 */\n    getDeptTree(roleId) {\n      return deptTreeSelect(roleId).then(response => {\n        this.deptOptions = response.depts\n        return response\n      })\n    },\n    // 角色状态修改\n    handleStatusChange(row) {\n      let text = row.status === \"0\" ? \"启用\" : \"停用\"\n      this.$modal.confirm('确认要\"' + text + '\"\"' + row.roleName + '\"角色吗？').then(function() {\n        return changeRoleStatus(row.roleId, row.status)\n      }).then(() => {\n        this.$modal.msgSuccess(text + \"成功\")\n      }).catch(function() {\n        row.status = row.status === \"0\" ? \"1\" : \"0\"\n      })\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false\n      this.reset()\n    },\n    // 取消按钮（数据权限）\n    cancelDataScope() {\n      this.openDataScope = false\n      this.reset()\n    },\n    // 表单重置\n    reset() {\n      if (this.$refs.menu != undefined) {\n        this.$refs.menu.setCheckedKeys([])\n      }\n      this.menuExpand = false,\n      this.menuNodeAll = false,\n      this.deptExpand = true,\n      this.deptNodeAll = false,\n      this.form = {\n        roleId: undefined,\n        roleName: undefined,\n        roleKey: undefined,\n        roleSort: 0,\n        status: \"0\",\n        menuIds: [],\n        deptIds: [],\n        menuCheckStrictly: true,\n        deptCheckStrictly: true,\n        remark: undefined\n      }\n      this.resetForm(\"form\")\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1\n      this.getList()\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = []\n      this.resetForm(\"queryForm\")\n      this.handleQuery()\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.roleId)\n      this.single = selection.length!=1\n      this.multiple = !selection.length\n    },\n    // 更多操作触发\n    handleCommand(command, row) {\n      switch (command) {\n        case \"handleDataScope\":\n          this.handleDataScope(row)\n          break\n        case \"handleAuthUser\":\n          this.handleAuthUser(row)\n          break\n        default:\n          break\n      }\n    },\n    // 树权限（展开/折叠）\n    handleCheckedTreeExpand(value, type) {\n      if (type == 'menu') {\n        let treeList = this.menuOptions\n        for (let i = 0; i < treeList.length; i++) {\n          this.$refs.menu.store.nodesMap[treeList[i].id].expanded = value\n        }\n      } else if (type == 'dept') {\n        let treeList = this.deptOptions\n        for (let i = 0; i < treeList.length; i++) {\n          this.$refs.dept.store.nodesMap[treeList[i].id].expanded = value\n        }\n      }\n    },\n    // 树权限（全选/全不选）\n    handleCheckedTreeNodeAll(value, type) {\n      if (type == 'menu') {\n        this.$refs.menu.setCheckedNodes(value ? this.menuOptions: [])\n      } else if (type == 'dept') {\n        this.$refs.dept.setCheckedNodes(value ? this.deptOptions: [])\n      }\n    },\n    // 树权限（父子联动）\n    handleCheckedTreeConnect(value, type) {\n      if (type == 'menu') {\n        this.form.menuCheckStrictly = value ? true: false\n      } else if (type == 'dept') {\n        this.form.deptCheckStrictly = value ? true: false\n      }\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset()\n      this.getMenuTreeselect()\n      this.open = true\n      this.title = \"添加角色\"\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset()\n      const roleId = row.roleId || this.ids\n      const roleMenu = this.getRoleMenuTreeselect(roleId)\n      getRole(roleId).then(response => {\n        this.form = response.data\n        this.open = true\n        this.$nextTick(() => {\n          roleMenu.then(res => {\n            let checkedKeys = res.checkedKeys\n            checkedKeys.forEach((v) => {\n                this.$nextTick(()=>{\n                    this.$refs.menu.setChecked(v, true ,false)\n                })\n            })\n          })\n        })\n      })\n      this.title = \"修改角色\"\n    },\n    /** 选择角色权限范围触发 */\n    dataScopeSelectChange(value) {\n      if(value !== '2') {\n        this.$refs.dept.setCheckedKeys([])\n      }\n    },\n    /** 分配数据权限操作 */\n    handleDataScope(row) {\n      this.reset()\n      const deptTreeSelect = this.getDeptTree(row.roleId)\n      getRole(row.roleId).then(response => {\n        this.form = response.data\n        this.openDataScope = true\n        this.$nextTick(() => {\n          deptTreeSelect.then(res => {\n            this.$refs.dept.setCheckedKeys(res.checkedKeys)\n          })\n        })\n      })\n      this.title = \"分配数据权限\"\n    },\n    /** 分配用户操作 */\n    handleAuthUser: function(row) {\n      const roleId = row.roleId\n      this.$router.push(\"/system/role-auth/user/\" + roleId)\n    },\n    /** 提交按钮 */\n    submitForm: function() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.roleId != undefined) {\n            this.form.menuIds = this.getMenuAllCheckedKeys()\n            updateRole(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\")\n              this.open = false\n              this.getList()\n            })\n          } else {\n            this.form.menuIds = this.getMenuAllCheckedKeys()\n            addRole(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\")\n              this.open = false\n              this.getList()\n            })\n          }\n        }\n      })\n    },\n    /** 提交按钮（数据权限） */\n    submitDataScope: function() {\n      if (this.form.roleId != undefined) {\n        this.form.deptIds = this.getDeptAllCheckedKeys()\n        dataScope(this.form).then(response => {\n          this.$modal.msgSuccess(\"修改成功\")\n          this.openDataScope = false\n          this.getList()\n        })\n      }\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const roleIds = row.roleId || this.ids\n      this.$modal.confirm('是否确认删除角色编号为\"' + roleIds + '\"的数据项？').then(function() {\n        return delRole(roleIds)\n      }).then(() => {\n        this.getList()\n        this.$modal.msgSuccess(\"删除成功\")\n      }).catch(() => {})\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('system/role/export', {\n        ...this.queryParams\n      }, `role_${new Date().getTime()}.xlsx`)\n    }\n  }\n}\n</script>"], "mappings": ";;;;;;;;;;;;;;;AA8PA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,QAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,aAAA;MACAC,UAAA;MACAC,WAAA;MACAC,UAAA;MACAC,WAAA;MACA;MACAC,SAAA;MACA;MACAC,gBAAA,GACA;QACAC,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;MACA;MACAC,WAAA;MACA;MACAC,WAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA,EAAAC,SAAA;QACAC,OAAA,EAAAD,SAAA;QACAE,MAAA,EAAAF;MACA;MACA;MACAG,IAAA;MACAC,YAAA;QACAC,QAAA;QACAZ,KAAA;MACA;MACA;MACAa,KAAA;QACAP,QAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,OAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,QAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,aACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAtC,OAAA;MACA,IAAAuC,cAAA,OAAAC,YAAA,MAAApB,WAAA,OAAAN,SAAA,GAAA2B,IAAA,WAAAC,QAAA;QACAJ,KAAA,CAAAhC,QAAA,GAAAoC,QAAA,CAAAC,IAAA;QACAL,KAAA,CAAAjC,KAAA,GAAAqC,QAAA,CAAArC,KAAA;QACAiC,KAAA,CAAAtC,OAAA;MACA,CACA;IACA;IACA,cACA4C,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,gBAAA,IAAAL,IAAA,WAAAC,QAAA;QACAG,MAAA,CAAA3B,WAAA,GAAAwB,QAAA,CAAA3C,IAAA;MACA;IACA;IACA;IACAgD,qBAAA,WAAAA,sBAAA;MACA;MACA,IAAAC,WAAA,QAAAC,KAAA,CAAAC,IAAA,CAAAC,cAAA;MACA;MACA,IAAAC,eAAA,QAAAH,KAAA,CAAAC,IAAA,CAAAG,kBAAA;MACAL,WAAA,CAAAM,OAAA,CAAAC,KAAA,CAAAP,WAAA,EAAAI,eAAA;MACA,OAAAJ,WAAA;IACA;IACA;IACAQ,qBAAA,WAAAA,sBAAA;MACA;MACA,IAAAR,WAAA,QAAAC,KAAA,CAAAQ,IAAA,CAAAN,cAAA;MACA;MACA,IAAAC,eAAA,QAAAH,KAAA,CAAAQ,IAAA,CAAAJ,kBAAA;MACAL,WAAA,CAAAM,OAAA,CAAAC,KAAA,CAAAP,WAAA,EAAAI,eAAA;MACA,OAAAJ,WAAA;IACA;IACA,oBACAU,qBAAA,WAAAA,sBAAAC,MAAA;MAAA,IAAAC,MAAA;MACA,WAAAC,wBAAA,EAAAF,MAAA,EAAAlB,IAAA,WAAAC,QAAA;QACAkB,MAAA,CAAA1C,WAAA,GAAAwB,QAAA,CAAAoB,KAAA;QACA,OAAApB,QAAA;MACA;IACA;IACA,oBACAqB,WAAA,WAAAA,YAAAJ,MAAA;MAAA,IAAAK,MAAA;MACA,WAAAC,oBAAA,EAAAN,MAAA,EAAAlB,IAAA,WAAAC,QAAA;QACAsB,MAAA,CAAA7C,WAAA,GAAAuB,QAAA,CAAAwB,KAAA;QACA,OAAAxB,QAAA;MACA;IACA;IACA;IACAyB,kBAAA,WAAAA,mBAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,IAAA,GAAAF,GAAA,CAAA1C,MAAA;MACA,KAAA6C,MAAA,CAAAC,OAAA,UAAAF,IAAA,UAAAF,GAAA,CAAA7C,QAAA,YAAAkB,IAAA;QACA,WAAAgC,sBAAA,EAAAL,GAAA,CAAAT,MAAA,EAAAS,GAAA,CAAA1C,MAAA;MACA,GAAAe,IAAA;QACA4B,MAAA,CAAAE,MAAA,CAAAG,UAAA,CAAAJ,IAAA;MACA,GAAAK,KAAA;QACAP,GAAA,CAAA1C,MAAA,GAAA0C,GAAA,CAAA1C,MAAA;MACA;IACA;IACA;IACAkD,MAAA,WAAAA,OAAA;MACA,KAAApE,IAAA;MACA,KAAAqE,KAAA;IACA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA,KAAArE,aAAA;MACA,KAAAoE,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,SAAA5B,KAAA,CAAAC,IAAA,IAAA1B,SAAA;QACA,KAAAyB,KAAA,CAAAC,IAAA,CAAA6B,cAAA;MACA;MACA,KAAArE,UAAA,UACA,KAAAC,WAAA,UACA,KAAAC,UAAA,SACA,KAAAC,WAAA,UACA,KAAAc,IAAA;QACAgC,MAAA,EAAAnC,SAAA;QACAD,QAAA,EAAAC,SAAA;QACAC,OAAA,EAAAD,SAAA;QACAU,QAAA;QACAR,MAAA;QACAsD,OAAA;QACAC,OAAA;QACAC,iBAAA;QACAC,iBAAA;QACAC,MAAA,EAAA5D;MACA;MACA,KAAA6D,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAlE,WAAA,CAAAC,OAAA;MACA,KAAAe,OAAA;IACA;IACA,aACAmD,UAAA,WAAAA,WAAA;MACA,KAAAzE,SAAA;MACA,KAAAuE,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAxF,GAAA,GAAAwF,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAhC,MAAA;MAAA;MACA,KAAAzD,MAAA,GAAAuF,SAAA,CAAAG,MAAA;MACA,KAAAzF,QAAA,IAAAsF,SAAA,CAAAG,MAAA;IACA;IACA;IACAC,aAAA,WAAAA,cAAAC,OAAA,EAAA1B,GAAA;MACA,QAAA0B,OAAA;QACA;UACA,KAAAC,eAAA,CAAA3B,GAAA;UACA;QACA;UACA,KAAA4B,cAAA,CAAA5B,GAAA;UACA;QACA;UACA;MACA;IACA;IACA;IACA6B,uBAAA,WAAAA,wBAAAjF,KAAA,EAAAkF,IAAA;MACA,IAAAA,IAAA;QACA,IAAAC,QAAA,QAAAjF,WAAA;QACA,SAAAkF,CAAA,MAAAA,CAAA,GAAAD,QAAA,CAAAP,MAAA,EAAAQ,CAAA;UACA,KAAAnD,KAAA,CAAAC,IAAA,CAAAmD,KAAA,CAAAC,QAAA,CAAAH,QAAA,CAAAC,CAAA,EAAAG,EAAA,EAAAC,QAAA,GAAAxF,KAAA;QACA;MACA,WAAAkF,IAAA;QACA,IAAAC,SAAA,QAAAhF,WAAA;QACA,SAAAiF,EAAA,MAAAA,EAAA,GAAAD,SAAA,CAAAP,MAAA,EAAAQ,EAAA;UACA,KAAAnD,KAAA,CAAAQ,IAAA,CAAA4C,KAAA,CAAAC,QAAA,CAAAH,SAAA,CAAAC,EAAA,EAAAG,EAAA,EAAAC,QAAA,GAAAxF,KAAA;QACA;MACA;IACA;IACA;IACAyF,wBAAA,WAAAA,yBAAAzF,KAAA,EAAAkF,IAAA;MACA,IAAAA,IAAA;QACA,KAAAjD,KAAA,CAAAC,IAAA,CAAAwD,eAAA,CAAA1F,KAAA,QAAAE,WAAA;MACA,WAAAgF,IAAA;QACA,KAAAjD,KAAA,CAAAQ,IAAA,CAAAiD,eAAA,CAAA1F,KAAA,QAAAG,WAAA;MACA;IACA;IACA;IACAwF,wBAAA,WAAAA,yBAAA3F,KAAA,EAAAkF,IAAA;MACA,IAAAA,IAAA;QACA,KAAAvE,IAAA,CAAAuD,iBAAA,GAAAlE,KAAA;MACA,WAAAkF,IAAA;QACA,KAAAvE,IAAA,CAAAwD,iBAAA,GAAAnE,KAAA;MACA;IACA;IACA,aACA4F,SAAA,WAAAA,UAAA;MACA,KAAA/B,KAAA;MACA,KAAAjC,iBAAA;MACA,KAAApC,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAsG,YAAA,WAAAA,aAAAzC,GAAA;MAAA,IAAA0C,MAAA;MACA,KAAAjC,KAAA;MACA,IAAAlB,MAAA,GAAAS,GAAA,CAAAT,MAAA,SAAA1D,GAAA;MACA,IAAA8G,QAAA,QAAArD,qBAAA,CAAAC,MAAA;MACA,IAAAqD,aAAA,EAAArD,MAAA,EAAAlB,IAAA,WAAAC,QAAA;QACAoE,MAAA,CAAAnF,IAAA,GAAAe,QAAA,CAAA3C,IAAA;QACA+G,MAAA,CAAAtG,IAAA;QACAsG,MAAA,CAAAG,SAAA;UACAF,QAAA,CAAAtE,IAAA,WAAAyE,GAAA;YACA,IAAAlE,WAAA,GAAAkE,GAAA,CAAAlE,WAAA;YACAA,WAAA,CAAAmE,OAAA,WAAAC,CAAA;cACAN,MAAA,CAAAG,SAAA;gBACAH,MAAA,CAAA7D,KAAA,CAAAC,IAAA,CAAAmE,UAAA,CAAAD,CAAA;cACA;YACA;UACA;QACA;MACA;MACA,KAAA7G,KAAA;IACA;IACA,iBACA+G,qBAAA,WAAAA,sBAAAtG,KAAA;MACA,IAAAA,KAAA;QACA,KAAAiC,KAAA,CAAAQ,IAAA,CAAAsB,cAAA;MACA;IACA;IACA,eACAgB,eAAA,WAAAA,gBAAA3B,GAAA;MAAA,IAAAmD,MAAA;MACA,KAAA1C,KAAA;MACA,IAAAZ,cAAA,QAAAF,WAAA,CAAAK,GAAA,CAAAT,MAAA;MACA,IAAAqD,aAAA,EAAA5C,GAAA,CAAAT,MAAA,EAAAlB,IAAA,WAAAC,QAAA;QACA6E,MAAA,CAAA5F,IAAA,GAAAe,QAAA,CAAA3C,IAAA;QACAwH,MAAA,CAAA9G,aAAA;QACA8G,MAAA,CAAAN,SAAA;UACAhD,cAAA,CAAAxB,IAAA,WAAAyE,GAAA;YACAK,MAAA,CAAAtE,KAAA,CAAAQ,IAAA,CAAAsB,cAAA,CAAAmC,GAAA,CAAAlE,WAAA;UACA;QACA;MACA;MACA,KAAAzC,KAAA;IACA;IACA;IACAyF,cAAA,WAAAA,eAAA5B,GAAA;MACA,IAAAT,MAAA,GAAAS,GAAA,CAAAT,MAAA;MACA,KAAA6D,OAAA,CAAAC,IAAA,6BAAA9D,MAAA;IACA;IACA;IACA+D,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAA1E,KAAA,SAAA2E,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAF,MAAA,CAAAhG,IAAA,CAAAgC,MAAA,IAAAnC,SAAA;YACAmG,MAAA,CAAAhG,IAAA,CAAAqD,OAAA,GAAA2C,MAAA,CAAA5E,qBAAA;YACA,IAAA+E,gBAAA,EAAAH,MAAA,CAAAhG,IAAA,EAAAc,IAAA,WAAAC,QAAA;cACAiF,MAAA,CAAApD,MAAA,CAAAG,UAAA;cACAiD,MAAA,CAAAnH,IAAA;cACAmH,MAAA,CAAAvF,OAAA;YACA;UACA;YACAuF,MAAA,CAAAhG,IAAA,CAAAqD,OAAA,GAAA2C,MAAA,CAAA5E,qBAAA;YACA,IAAAgF,aAAA,EAAAJ,MAAA,CAAAhG,IAAA,EAAAc,IAAA,WAAAC,QAAA;cACAiF,MAAA,CAAApD,MAAA,CAAAG,UAAA;cACAiD,MAAA,CAAAnH,IAAA;cACAmH,MAAA,CAAAvF,OAAA;YACA;UACA;QACA;MACA;IACA;IACA;IACA4F,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,SAAAtG,IAAA,CAAAgC,MAAA,IAAAnC,SAAA;QACA,KAAAG,IAAA,CAAAsD,OAAA,QAAAzB,qBAAA;QACA,IAAA0E,eAAA,OAAAvG,IAAA,EAAAc,IAAA,WAAAC,QAAA;UACAuF,MAAA,CAAA1D,MAAA,CAAAG,UAAA;UACAuD,MAAA,CAAAxH,aAAA;UACAwH,MAAA,CAAA7F,OAAA;QACA;MACA;IACA;IACA,aACA+F,YAAA,WAAAA,aAAA/D,GAAA;MAAA,IAAAgE,MAAA;MACA,IAAAC,OAAA,GAAAjE,GAAA,CAAAT,MAAA,SAAA1D,GAAA;MACA,KAAAsE,MAAA,CAAAC,OAAA,kBAAA6D,OAAA,aAAA5F,IAAA;QACA,WAAA6F,aAAA,EAAAD,OAAA;MACA,GAAA5F,IAAA;QACA2F,MAAA,CAAAhG,OAAA;QACAgG,MAAA,CAAA7D,MAAA,CAAAG,UAAA;MACA,GAAAC,KAAA;IACA;IACA,aACA4D,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,2BAAAC,cAAA,CAAAC,OAAA,MACA,KAAAtH,WAAA,WAAAuH,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;EACA;AACA", "ignoreList": []}]}