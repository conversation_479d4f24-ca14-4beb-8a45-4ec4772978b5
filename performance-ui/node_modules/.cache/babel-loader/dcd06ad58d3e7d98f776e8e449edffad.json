{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/directive/permission/hasPermi.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/directive/permission/hasPermi.js", "mtime": 1753510684529}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCIvVXNlcnMvbWF6aWhhby9EZXNrdG9wL2Rldi9wZXJmb3JtYW5jZS9wZXJmb3JtYW5jZS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmVycm9yLmNhdXNlLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5pbmNsdWRlcy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLmluY2x1ZGVzLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IuY29uc3RydWN0b3IuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5zb21lLmpzIik7CnZhciBfc3RvcmUgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvc3RvcmUiKSk7Ci8qKgoqIHYtaGFzUGVybWkg5pON5L2c5p2D6ZmQ5aSE55CGCiogQ29weXJpZ2h0IChjKSAyMDE5IHJ1b3lpCiovCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBpbnNlcnRlZDogZnVuY3Rpb24gaW5zZXJ0ZWQoZWwsIGJpbmRpbmcsIHZub2RlKSB7CiAgICB2YXIgdmFsdWUgPSBiaW5kaW5nLnZhbHVlOwogICAgdmFyIGFsbF9wZXJtaXNzaW9uID0gIio6KjoqIjsKICAgIHZhciBwZXJtaXNzaW9ucyA9IF9zdG9yZS5kZWZhdWx0LmdldHRlcnMgJiYgX3N0b3JlLmRlZmF1bHQuZ2V0dGVycy5wZXJtaXNzaW9uczsKICAgIGlmICh2YWx1ZSAmJiB2YWx1ZSBpbnN0YW5jZW9mIEFycmF5ICYmIHZhbHVlLmxlbmd0aCA+IDApIHsKICAgICAgdmFyIHBlcm1pc3Npb25GbGFnID0gdmFsdWU7CiAgICAgIHZhciBoYXNQZXJtaXNzaW9ucyA9IHBlcm1pc3Npb25zLnNvbWUoZnVuY3Rpb24gKHBlcm1pc3Npb24pIHsKICAgICAgICByZXR1cm4gYWxsX3Blcm1pc3Npb24gPT09IHBlcm1pc3Npb24gfHwgcGVybWlzc2lvbkZsYWcuaW5jbHVkZXMocGVybWlzc2lvbik7CiAgICAgIH0pOwogICAgICBpZiAoIWhhc1Blcm1pc3Npb25zKSB7CiAgICAgICAgZWwucGFyZW50Tm9kZSAmJiBlbC5wYXJlbnROb2RlLnJlbW92ZUNoaWxkKGVsKTsKICAgICAgfQogICAgfSBlbHNlIHsKICAgICAgdGhyb3cgbmV3IEVycm9yKCJcdThCRjdcdThCQkVcdTdGNkVcdTY0Q0RcdTRGNUNcdTY3NDNcdTk2NTBcdTY4MDdcdTdCN0VcdTUwM0MiKTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["_store", "_interopRequireDefault", "require", "_default", "exports", "default", "inserted", "el", "binding", "vnode", "value", "all_permission", "permissions", "store", "getters", "Array", "length", "permissionFlag", "hasPermissions", "some", "permission", "includes", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "Error"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/directive/permission/hasPermi.js"], "sourcesContent": [" /**\n * v-hasPermi 操作权限处理\n * Copyright (c) 2019 ruoyi\n */\n\nimport store from '@/store'\n\nexport default {\n  inserted(el, binding, vnode) {\n    const { value } = binding\n    const all_permission = \"*:*:*\"\n    const permissions = store.getters && store.getters.permissions\n\n    if (value && value instanceof Array && value.length > 0) {\n      const permissionFlag = value\n\n      const hasPermissions = permissions.some(permission => {\n        return all_permission === permission || permissionFlag.includes(permission)\n      })\n\n      if (!hasPermissions) {\n        el.parentNode && el.parentNode.removeChild(el)\n      }\n    } else {\n      throw new Error(`请设置操作权限标签值`)\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;AAKA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AALC;AACD;AACA;AACA;AAHC,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAOc;EACbC,QAAQ,WAARA,QAAQA,CAACC,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAE;IAC3B,IAAQC,KAAK,GAAKF,OAAO,CAAjBE,KAAK;IACb,IAAMC,cAAc,GAAG,OAAO;IAC9B,IAAMC,WAAW,GAAGC,cAAK,CAACC,OAAO,IAAID,cAAK,CAACC,OAAO,CAACF,WAAW;IAE9D,IAAIF,KAAK,IAAIA,KAAK,YAAYK,KAAK,IAAIL,KAAK,CAACM,MAAM,GAAG,CAAC,EAAE;MACvD,IAAMC,cAAc,GAAGP,KAAK;MAE5B,IAAMQ,cAAc,GAAGN,WAAW,CAACO,IAAI,CAAC,UAAAC,UAAU,EAAI;QACpD,OAAOT,cAAc,KAAKS,UAAU,IAAIH,cAAc,CAACI,QAAQ,CAACD,UAAU,CAAC;MAC7E,CAAC,CAAC;MAEF,IAAI,CAACF,cAAc,EAAE;QACnBX,EAAE,CAACe,UAAU,IAAIf,EAAE,CAACe,UAAU,CAACC,WAAW,CAAChB,EAAE,CAAC;MAChD;IACF,CAAC,MAAM;MACL,MAAM,IAAIiB,KAAK,+DAAa,CAAC;IAC/B;EACF;AACF,CAAC", "ignoreList": []}]}