{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/core.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/core.js", "mtime": 1753510684086}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_quill", "_interopRequireWildcard", "require", "_block", "_break", "_interopRequireDefault", "_container", "_cursor", "_embed", "_inline", "_scroll", "_text", "_clipboard", "_history", "_keyboard", "_uploader", "_quill<PERSON><PERSON><PERSON>", "_input", "_uiNode", "_module", "<PERSON><PERSON><PERSON>", "register", "Block", "BlockEmbed", "Break", "Container", "<PERSON><PERSON><PERSON>", "Embed", "Inline", "<PERSON><PERSON>", "TextBlot", "Clipboard", "History", "Keyboard", "Uploader", "Input", "UINode", "_default", "exports", "default"], "sources": ["../src/core.ts"], "sourcesContent": ["import Quill, { Parchment, Range } from './core/quill.js';\nimport type {\n  Bounds,\n  DebugLevel,\n  EmitterSource,\n  ExpandedQuillOptions,\n  QuillOptions,\n} from './core/quill.js';\n\nimport Block, { BlockEmbed } from './blots/block.js';\nimport Break from './blots/break.js';\nimport Container from './blots/container.js';\nimport Cursor from './blots/cursor.js';\nimport Embed from './blots/embed.js';\nimport Inline from './blots/inline.js';\nimport Scroll from './blots/scroll.js';\nimport TextBlot from './blots/text.js';\n\nimport Clipboard from './modules/clipboard.js';\nimport History from './modules/history.js';\nimport Keyboard from './modules/keyboard.js';\nimport Uploader from './modules/uploader.js';\nimport Delta, { Op, OpIterator, AttributeMap } from 'quill-delta';\nimport Input from './modules/input.js';\nimport UINode from './modules/uiNode.js';\n\nexport { default as Module } from './core/module.js';\nexport { Delta, Op, OpIterator, AttributeMap, Parchment, Range };\nexport type {\n  Bounds,\n  DebugLevel,\n  EmitterSource,\n  ExpandedQuillOptions,\n  QuillOptions,\n};\n\nQuill.register({\n  'blots/block': Block,\n  'blots/block/embed': BlockEmbed,\n  'blots/break': Break,\n  'blots/container': Container,\n  'blots/cursor': Cursor,\n  'blots/embed': Embed,\n  'blots/inline': Inline,\n  'blots/scroll': Scroll,\n  'blots/text': TextBlot,\n\n  'modules/clipboard': Clipboard,\n  'modules/history': History,\n  'modules/keyboard': Keyboard,\n  'modules/uploader': Uploader,\n  'modules/input': Input,\n  'modules/uiNode': UINode,\n});\n\nexport default Quill;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AASA,IAAAC,MAAA,GAAAF,uBAAA,CAAAC,OAAA;AACA,IAAAE,MAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,UAAA,GAAAD,sBAAA,CAAAH,OAAA;AACA,IAAAK,OAAA,GAAAF,sBAAA,CAAAH,OAAA;AACA,IAAAM,MAAA,GAAAH,sBAAA,CAAAH,OAAA;AACA,IAAAO,OAAA,GAAAJ,sBAAA,CAAAH,OAAA;AACA,IAAAQ,OAAA,GAAAL,sBAAA,CAAAH,OAAA;AACA,IAAAS,KAAA,GAAAN,sBAAA,CAAAH,OAAA;AAEA,IAAAU,UAAA,GAAAP,sBAAA,CAAAH,OAAA;AACA,IAAAW,QAAA,GAAAR,sBAAA,CAAAH,OAAA;AACA,IAAAY,SAAA,GAAAT,sBAAA,CAAAH,OAAA;AACA,IAAAa,SAAA,GAAAV,sBAAA,CAAAH,OAAA;AACA,IAAAc,WAAA,GAAAf,uBAAA,CAAAC,OAAA;AACA,IAAAe,MAAA,GAAAZ,sBAAA,CAAAH,OAAA;AACA,IAAAgB,OAAA,GAAAb,sBAAA,CAAAH,OAAA;AAEA,IAAAiB,OAAA,GAAAd,sBAAA,CAAAH,OAAA;AAUAkB,cAAK,CAACC,QAAQ,CAAC;EACb,aAAa,EAAEC,cAAK;EACpB,mBAAmB,EAAEC,iBAAU;EAC/B,aAAa,EAAEC,cAAK;EACpB,iBAAiB,EAAEC,kBAAS;EAC5B,cAAc,EAAEC,eAAM;EACtB,aAAa,EAAEC,cAAK;EACpB,cAAc,EAAEC,eAAM;EACtB,cAAc,EAAEC,eAAM;EACtB,YAAY,EAAEC,aAAQ;EAEtB,mBAAmB,EAAEC,kBAAS;EAC9B,iBAAiB,EAAEC,gBAAO;EAC1B,kBAAkB,EAAEC,iBAAQ;EAC5B,kBAAkB,EAAEC,iBAAQ;EAC5B,eAAe,EAAEC,cAAK;EACtB,gBAAgB,EAAEC;AACpB,CAAC,CAAC;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEanB,cAAK", "ignoreList": []}]}