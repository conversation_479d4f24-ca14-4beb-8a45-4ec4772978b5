{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/utils/dict/Dict.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/utils/dict/Dict.js", "mtime": 1753510684531}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_vue", "_interopRequireDefault", "require", "_ruoyi", "_DictMeta", "_DictData", "DEFAULT_DICT_OPTIONS", "types", "Dict", "exports", "default", "_classCallCheck2", "owner", "label", "type", "_createClass2", "key", "value", "init", "options", "_this", "Array", "opts", "mergeRecursive", "undefined", "Error", "ps", "_dictMetas", "map", "t", "DictMeta", "parse", "for<PERSON>ach", "dictMeta", "<PERSON><PERSON>", "set", "lazy", "push", "loadDict", "Promise", "all", "reloadDict", "find", "e", "reject", "concat", "dict", "request", "then", "response", "_dict$type$type", "dicts", "responseConverter", "console", "error", "filter", "d", "DictData", "length", "splice", "apply", "Number", "MAX_SAFE_INTEGER", "_toConsumableArray2"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/utils/dict/Dict.js"], "sourcesContent": ["import Vue from 'vue'\nimport { mergeRecursive } from \"@/utils/ruoyi\"\nimport DictMeta from './DictMeta'\nimport DictData from './DictData'\n\nconst DEFAULT_DICT_OPTIONS = {\n  types: [],\n}\n\n/**\n * @classdesc 字典\n * @property {Object} label 标签对象，内部属性名为字典类型名称\n * @property {Object} dict 字段数组，内部属性名为字典类型名称\n * @property {Array.<DictMeta>} _dictMetas 字典元数据数组\n */\nexport default class Dict {\n  constructor() {\n    this.owner = null\n    this.label = {}\n    this.type = {}\n  }\n\n  init(options) {\n    if (options instanceof Array) {\n      options = { types: options }\n    }\n    const opts = mergeRecursive(DEFAULT_DICT_OPTIONS, options)\n    if (opts.types === undefined) {\n      throw new Error('need dict types')\n    }\n    const ps = []\n    this._dictMetas = opts.types.map(t => DictMeta.parse(t))\n    this._dictMetas.forEach(dictMeta => {\n      const type = dictMeta.type\n      Vue.set(this.label, type, {})\n      Vue.set(this.type, type, [])\n      if (dictMeta.lazy) {\n        return\n      }\n      ps.push(loadDict(this, dictMeta))\n    })\n    return Promise.all(ps)\n  }\n\n  /**\n   * 重新加载字典\n   * @param {String} type 字典类型\n   */\n  reloadDict(type) {\n    const dictMeta = this._dictMetas.find(e => e.type === type)\n    if (dictMeta === undefined) {\n      return Promise.reject(`the dict meta of ${type} was not found`)\n    }\n    return loadDict(this, dictMeta)\n  }\n}\n\n/**\n * 加载字典\n * @param {Dict} dict 字典\n * @param {DictMeta} dictMeta 字典元数据\n * @returns {Promise}\n */\nfunction loadDict(dict, dictMeta) {\n  return dictMeta.request(dictMeta)\n    .then(response => {\n      const type = dictMeta.type\n      let dicts = dictMeta.responseConverter(response, dictMeta)\n      if (!(dicts instanceof Array)) {\n        console.error('the return of responseConverter must be Array.<DictData>')\n        dicts = []\n      } else if (dicts.filter(d => d instanceof DictData).length !== dicts.length) {\n        console.error('the type of elements in dicts must be DictData')\n        dicts = []\n      }\n      dict.type[type].splice(0, Number.MAX_SAFE_INTEGER, ...dicts)\n      dicts.forEach(d => {\n        Vue.set(dict.label[type], d.value, d.label)\n      })\n      return dicts\n    })\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,SAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAEA,IAAMI,oBAAoB,GAAG;EAC3BC,KAAK,EAAE;AACT,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AALA,IAMqBC,IAAI,GAAAC,OAAA,CAAAC,OAAA;EACvB,SAAAF,KAAA,EAAc;IAAA,IAAAG,gBAAA,CAAAD,OAAA,QAAAF,IAAA;IACZ,IAAI,CAACI,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACC,IAAI,GAAG,CAAC,CAAC;EAChB;EAAC,WAAAC,aAAA,CAAAL,OAAA,EAAAF,IAAA;IAAAQ,GAAA;IAAAC,KAAA,EAED,SAAAC,IAAIA,CAACC,OAAO,EAAE;MAAA,IAAAC,KAAA;MACZ,IAAID,OAAO,YAAYE,KAAK,EAAE;QAC5BF,OAAO,GAAG;UAAEZ,KAAK,EAAEY;QAAQ,CAAC;MAC9B;MACA,IAAMG,IAAI,GAAG,IAAAC,qBAAc,EAACjB,oBAAoB,EAAEa,OAAO,CAAC;MAC1D,IAAIG,IAAI,CAACf,KAAK,KAAKiB,SAAS,EAAE;QAC5B,MAAM,IAAIC,KAAK,CAAC,iBAAiB,CAAC;MACpC;MACA,IAAMC,EAAE,GAAG,EAAE;MACb,IAAI,CAACC,UAAU,GAAGL,IAAI,CAACf,KAAK,CAACqB,GAAG,CAAC,UAAAC,CAAC;QAAA,OAAIC,iBAAQ,CAACC,KAAK,CAACF,CAAC,CAAC;MAAA,EAAC;MACxD,IAAI,CAACF,UAAU,CAACK,OAAO,CAAC,UAAAC,QAAQ,EAAI;QAClC,IAAMnB,IAAI,GAAGmB,QAAQ,CAACnB,IAAI;QAC1BoB,YAAG,CAACC,GAAG,CAACf,KAAI,CAACP,KAAK,EAAEC,IAAI,EAAE,CAAC,CAAC,CAAC;QAC7BoB,YAAG,CAACC,GAAG,CAACf,KAAI,CAACN,IAAI,EAAEA,IAAI,EAAE,EAAE,CAAC;QAC5B,IAAImB,QAAQ,CAACG,IAAI,EAAE;UACjB;QACF;QACAV,EAAE,CAACW,IAAI,CAACC,QAAQ,CAAClB,KAAI,EAAEa,QAAQ,CAAC,CAAC;MACnC,CAAC,CAAC;MACF,OAAOM,OAAO,CAACC,GAAG,CAACd,EAAE,CAAC;IACxB;;IAEA;AACF;AACA;AACA;EAHE;IAAAV,GAAA;IAAAC,KAAA,EAIA,SAAAwB,UAAUA,CAAC3B,IAAI,EAAE;MACf,IAAMmB,QAAQ,GAAG,IAAI,CAACN,UAAU,CAACe,IAAI,CAAC,UAAAC,CAAC;QAAA,OAAIA,CAAC,CAAC7B,IAAI,KAAKA,IAAI;MAAA,EAAC;MAC3D,IAAImB,QAAQ,KAAKT,SAAS,EAAE;QAC1B,OAAOe,OAAO,CAACK,MAAM,qBAAAC,MAAA,CAAqB/B,IAAI,mBAAgB,CAAC;MACjE;MACA,OAAOwB,QAAQ,CAAC,IAAI,EAAEL,QAAQ,CAAC;IACjC;EAAC;AAAA;AAGH;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,QAAQA,CAACQ,IAAI,EAAEb,QAAQ,EAAE;EAChC,OAAOA,QAAQ,CAACc,OAAO,CAACd,QAAQ,CAAC,CAC9Be,IAAI,CAAC,UAAAC,QAAQ,EAAI;IAAA,IAAAC,eAAA;IAChB,IAAMpC,IAAI,GAAGmB,QAAQ,CAACnB,IAAI;IAC1B,IAAIqC,KAAK,GAAGlB,QAAQ,CAACmB,iBAAiB,CAACH,QAAQ,EAAEhB,QAAQ,CAAC;IAC1D,IAAI,EAAEkB,KAAK,YAAY9B,KAAK,CAAC,EAAE;MAC7BgC,OAAO,CAACC,KAAK,CAAC,0DAA0D,CAAC;MACzEH,KAAK,GAAG,EAAE;IACZ,CAAC,MAAM,IAAIA,KAAK,CAACI,MAAM,CAAC,UAAAC,CAAC;MAAA,OAAIA,CAAC,YAAYC,iBAAQ;IAAA,EAAC,CAACC,MAAM,KAAKP,KAAK,CAACO,MAAM,EAAE;MAC3EL,OAAO,CAACC,KAAK,CAAC,gDAAgD,CAAC;MAC/DH,KAAK,GAAG,EAAE;IACZ;IACA,CAAAD,eAAA,GAAAJ,IAAI,CAAChC,IAAI,CAACA,IAAI,CAAC,EAAC6C,MAAM,CAAAC,KAAA,CAAAV,eAAA,GAAC,CAAC,EAAEW,MAAM,CAACC,gBAAgB,EAAAjB,MAAA,KAAAkB,mBAAA,CAAArD,OAAA,EAAKyC,KAAK,GAAC;IAC5DA,KAAK,CAACnB,OAAO,CAAC,UAAAwB,CAAC,EAAI;MACjBtB,YAAG,CAACC,GAAG,CAACW,IAAI,CAACjC,KAAK,CAACC,IAAI,CAAC,EAAE0C,CAAC,CAACvC,KAAK,EAAEuC,CAAC,CAAC3C,KAAK,CAAC;IAC7C,CAAC,CAAC;IACF,OAAOsC,KAAK;EACd,CAAC,CAAC;AACN", "ignoreList": []}]}