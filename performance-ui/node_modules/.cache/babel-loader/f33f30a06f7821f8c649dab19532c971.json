{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/performance/quarterly.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/performance/quarterly.js", "mtime": 1753510684517}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listQuarterly", "params", "request", "url", "method", "getQuarterly", "id", "addQuarterly", "data", "updateQuarterly", "delQuarterly", "ids", "exportQuarterly", "responseType", "downloadTemplateQuarterly", "downloadExcelTemplateQuarterly"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/performance/quarterly.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询季度绩效记录列表\nexport function listQuarterly(params) {\n  return request({\n    url: '/performance/quarterly/list',\n    method: 'get',\n    params\n  })\n}\n\n// 查询季度绩效记录详细\nexport function getQuarterly(id) {\n  return request({\n    url: '/performance/quarterly/' + id,\n    method: 'get'\n  })\n}\n\n// 新增季度绩效记录\nexport function addQuarterly(data) {\n  return request({\n    url: '/performance/quarterly',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改季度绩效记录\nexport function updateQuarterly(data) {\n  return request({\n    url: '/performance/quarterly',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除季度绩效记录\nexport function delQuarterly(ids) {\n  return request({\n    url: '/performance/quarterly/' + ids,\n    method: 'delete'\n  })\n}\n\n// Word导出\nexport function exportQuarterly(ids) {\n  return request({\n    url: '/performance/quarterly/export',\n    method: 'post',\n    data: ids,\n    responseType: 'blob'\n  })\n}\n\n// 下载Word模板\nexport function downloadTemplateQuarterly() {\n  return request({\n    url: '/performance/quarterly/importTemplate',\n    method: 'get',\n    responseType: 'blob'\n  })\n}\n\n// 下载Excel模板\nexport function downloadExcelTemplateQuarterly() {\n  return request({\n    url: '/performance/quarterly/downloadExcelTemplate',\n    method: 'get',\n    responseType: 'blob'\n  })\n} "], "mappings": ";;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,aAAaA,CAACC,MAAM,EAAE;EACpC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,YAAYA,CAACC,EAAE,EAAE;EAC/B,OAAO,IAAAJ,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB,GAAGG,EAAE;IACnCF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASG,YAAYA,CAACC,IAAI,EAAE;EACjC,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,MAAM;IACdI,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,eAAeA,CAACD,IAAI,EAAE;EACpC,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbI,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,YAAYA,CAACC,GAAG,EAAE;EAChC,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB,GAAGQ,GAAG;IACpCP,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,eAAeA,CAACD,GAAG,EAAE;EACnC,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,+BAA+B;IACpCC,MAAM,EAAE,MAAM;IACdI,IAAI,EAAEG,GAAG;IACTE,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,yBAAyBA,CAAA,EAAG;EAC1C,OAAO,IAAAZ,gBAAO,EAAC;IACbC,GAAG,EAAE,uCAAuC;IAC5CC,MAAM,EAAE,KAAK;IACbS,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,8BAA8BA,CAAA,EAAG;EAC/C,OAAO,IAAAb,gBAAO,EAAC;IACbC,GAAG,EAAE,8CAA8C;IACnDC,MAAM,EAAE,KAAK;IACbS,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ", "ignoreList": []}]}