{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/utils/dict/DictConverter.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/utils/dict/DictConverter.js", "mtime": 1753510684531}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_DictOptions", "_interopRequireDefault", "require", "_DictData", "_default", "dict", "dictMeta", "label", "determineDictField", "apply", "labelField", "concat", "_toConsumableArray2", "default", "DictOptions", "DEFAULT_LABEL_FIELDS", "value", "valueField", "DEFAULT_VALUE_FIELDS", "DictData", "_len", "arguments", "length", "fields", "Array", "_key", "find", "f", "Object", "prototype", "hasOwnProperty", "call"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/utils/dict/DictConverter.js"], "sourcesContent": ["import DictOptions from './DictOptions'\nimport DictData from './DictData'\n\nexport default function(dict, dictMeta) {\n  const label = determineDictField(dict, dictMeta.labelField, ...DictOptions.DEFAULT_LABEL_FIELDS)\n  const value = determineDictField(dict, dictMeta.valueField, ...DictOptions.DEFAULT_VALUE_FIELDS)\n  return new DictData(dict[label], dict[value], dict)\n}\n\n/**\n * 确定字典字段\n * @param {DictData} dict\n * @param  {...String} fields\n */\nfunction determineDictField(dict, ...fields) {\n  return fields.find(f => Object.prototype.hasOwnProperty.call(dict, f))\n}\n"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,YAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,SAAA,GAAAF,sBAAA,CAAAC,OAAA;AAEe,SAAAE,SAASC,IAAI,EAAEC,QAAQ,EAAE;EACtC,IAAMC,KAAK,GAAGC,kBAAkB,CAAAC,KAAA,UAACJ,IAAI,EAAEC,QAAQ,CAACI,UAAU,EAAAC,MAAA,KAAAC,mBAAA,CAAAC,OAAA,EAAKC,oBAAW,CAACC,oBAAoB,GAAC;EAChG,IAAMC,KAAK,GAAGR,kBAAkB,CAAAC,KAAA,UAACJ,IAAI,EAAEC,QAAQ,CAACW,UAAU,EAAAN,MAAA,KAAAC,mBAAA,CAAAC,OAAA,EAAKC,oBAAW,CAACI,oBAAoB,GAAC;EAChG,OAAO,IAAIC,iBAAQ,CAACd,IAAI,CAACE,KAAK,CAAC,EAAEF,IAAI,CAACW,KAAK,CAAC,EAAEX,IAAI,CAAC;AACrD;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASG,kBAAkBA,CAACH,IAAI,EAAa;EAAA,SAAAe,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAARC,MAAM,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAANF,MAAM,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;EAAA;EACzC,OAAOF,MAAM,CAACG,IAAI,CAAC,UAAAC,CAAC;IAAA,OAAIC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC1B,IAAI,EAAEsB,CAAC,CAAC;EAAA,EAAC;AACxE", "ignoreList": []}]}