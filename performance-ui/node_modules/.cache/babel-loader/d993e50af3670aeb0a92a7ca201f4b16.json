{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/modules/toolbar.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/modules/toolbar.js", "mtime": 1753510684097}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_quill<PERSON><PERSON><PERSON>", "_interopRequireDefault", "require", "_parchment", "_quill", "_logger", "_module", "debug", "logger", "<PERSON><PERSON><PERSON>", "exports", "default", "_Module", "quill", "options", "_this", "_classCallCheck2", "_callSuper2", "Array", "isArray", "container", "_quill$container", "document", "createElement", "setAttribute", "addControls", "parentNode", "insertBefore", "querySelector", "HTMLElement", "error", "_possibleConstructorReturn2", "classList", "add", "controls", "handlers", "Object", "keys", "for<PERSON>ach", "format", "_this$options$handler", "handler", "add<PERSON><PERSON><PERSON>", "from", "querySelectorAll", "input", "attach", "on", "<PERSON><PERSON><PERSON>", "events", "EDITOR_CHANGE", "_this$quill$selection", "selection", "getRange", "_this$quill$selection2", "_slicedToArray2", "range", "update", "_inherits2", "_createClass2", "key", "value", "_this2", "find", "className", "indexOf", "slice", "length", "tagName", "scroll", "query", "warn", "eventName", "addEventListener", "e", "selectedIndex", "selected", "hasAttribute", "contains", "preventDefault", "focus", "_this2$quill$selectio", "_this2$quill$selectio2", "call", "prototype", "EmbedBlot", "prompt", "concat", "updateContents", "Delta", "retain", "index", "delete", "insert", "_defineProperty2", "sources", "USER", "push", "formats", "getFormat", "pair", "_pair", "option", "replace", "remove", "isActive", "getAttribute", "toString", "toggle", "<PERSON><PERSON><PERSON>", "DEFAULTS", "addButton", "append<PERSON><PERSON><PERSON>", "groups", "group", "control", "addSelect", "values", "String", "clean", "_this3", "getSelection", "name", "<PERSON><PERSON>", "INLINE", "removeFormat", "direction", "_this$quill$getFormat", "align", "indent", "parseInt", "modifier", "link", "list"], "sources": ["../../src/modules/toolbar.ts"], "sourcesContent": ["import Delta from 'quill-delta';\nimport { EmbedBlot, Scope } from 'parchment';\nimport Quill from '../core/quill.js';\nimport logger from '../core/logger.js';\nimport Module from '../core/module.js';\nimport type { Range } from '../core/selection.js';\n\nconst debug = logger('quill:toolbar');\n\ntype Handler = (this: Toolbar, value: any) => void;\n\nexport type ToolbarConfig = Array<\n  string[] | Array<string | Record<string, unknown>>\n>;\nexport interface ToolbarProps {\n  container?: HTMLElement | ToolbarConfig | null;\n  handlers?: Record<string, Handler>;\n  option?: number;\n  module?: boolean;\n  theme?: boolean;\n}\n\nclass Toolbar extends Module<ToolbarProps> {\n  static DEFAULTS: ToolbarProps;\n\n  container?: HTMLElement | null;\n  controls: [string, HTMLElement][];\n  handlers: Record<string, Handler>;\n\n  constructor(quill: Quill, options: Partial<ToolbarProps>) {\n    super(quill, options);\n    if (Array.isArray(this.options.container)) {\n      const container = document.createElement('div');\n      container.setAttribute('role', 'toolbar');\n      addControls(container, this.options.container);\n      quill.container?.parentNode?.insertBefore(container, quill.container);\n      this.container = container;\n    } else if (typeof this.options.container === 'string') {\n      this.container = document.querySelector(this.options.container);\n    } else {\n      this.container = this.options.container;\n    }\n    if (!(this.container instanceof HTMLElement)) {\n      debug.error('Container required for toolbar', this.options);\n      return;\n    }\n    this.container.classList.add('ql-toolbar');\n    this.controls = [];\n    this.handlers = {};\n    if (this.options.handlers) {\n      Object.keys(this.options.handlers).forEach((format) => {\n        const handler = this.options.handlers?.[format];\n        if (handler) {\n          this.addHandler(format, handler);\n        }\n      });\n    }\n    Array.from(this.container.querySelectorAll('button, select')).forEach(\n      (input) => {\n        // @ts-expect-error\n        this.attach(input);\n      },\n    );\n    this.quill.on(Quill.events.EDITOR_CHANGE, () => {\n      const [range] = this.quill.selection.getRange(); // quill.getSelection triggers update\n      this.update(range);\n    });\n  }\n\n  addHandler(format: string, handler: Handler) {\n    this.handlers[format] = handler;\n  }\n\n  attach(input: HTMLElement) {\n    let format = Array.from(input.classList).find((className) => {\n      return className.indexOf('ql-') === 0;\n    });\n    if (!format) return;\n    format = format.slice('ql-'.length);\n    if (input.tagName === 'BUTTON') {\n      input.setAttribute('type', 'button');\n    }\n    if (\n      this.handlers[format] == null &&\n      this.quill.scroll.query(format) == null\n    ) {\n      debug.warn('ignoring attaching to nonexistent format', format, input);\n      return;\n    }\n    const eventName = input.tagName === 'SELECT' ? 'change' : 'click';\n    input.addEventListener(eventName, (e) => {\n      let value;\n      if (input.tagName === 'SELECT') {\n        // @ts-expect-error\n        if (input.selectedIndex < 0) return;\n        // @ts-expect-error\n        const selected = input.options[input.selectedIndex];\n        if (selected.hasAttribute('selected')) {\n          value = false;\n        } else {\n          value = selected.value || false;\n        }\n      } else {\n        if (input.classList.contains('ql-active')) {\n          value = false;\n        } else {\n          // @ts-expect-error\n          value = input.value || !input.hasAttribute('value');\n        }\n        e.preventDefault();\n      }\n      this.quill.focus();\n      const [range] = this.quill.selection.getRange();\n      if (this.handlers[format] != null) {\n        this.handlers[format].call(this, value);\n      } else if (\n        // @ts-expect-error\n        this.quill.scroll.query(format).prototype instanceof EmbedBlot\n      ) {\n        value = prompt(`Enter ${format}`); // eslint-disable-line no-alert\n        if (!value) return;\n        this.quill.updateContents(\n          new Delta()\n            // @ts-expect-error Fix me later\n            .retain(range.index)\n            // @ts-expect-error Fix me later\n            .delete(range.length)\n            .insert({ [format]: value }),\n          Quill.sources.USER,\n        );\n      } else {\n        this.quill.format(format, value, Quill.sources.USER);\n      }\n      this.update(range);\n    });\n    this.controls.push([format, input]);\n  }\n\n  update(range: Range | null) {\n    const formats = range == null ? {} : this.quill.getFormat(range);\n    this.controls.forEach((pair) => {\n      const [format, input] = pair;\n      if (input.tagName === 'SELECT') {\n        let option: HTMLOptionElement | null = null;\n        if (range == null) {\n          option = null;\n        } else if (formats[format] == null) {\n          option = input.querySelector('option[selected]');\n        } else if (!Array.isArray(formats[format])) {\n          let value = formats[format];\n          if (typeof value === 'string') {\n            value = value.replace(/\"/g, '\\\\\"');\n          }\n          option = input.querySelector(`option[value=\"${value}\"]`);\n        }\n        if (option == null) {\n          // @ts-expect-error TODO fix me later\n          input.value = ''; // TODO make configurable?\n          // @ts-expect-error TODO fix me later\n          input.selectedIndex = -1;\n        } else {\n          option.selected = true;\n        }\n      } else if (range == null) {\n        input.classList.remove('ql-active');\n        input.setAttribute('aria-pressed', 'false');\n      } else if (input.hasAttribute('value')) {\n        // both being null should match (default values)\n        // '1' should match with 1 (headers)\n        const value = formats[format] as boolean | number | string | object;\n        const isActive =\n          value === input.getAttribute('value') ||\n          (value != null && value.toString() === input.getAttribute('value')) ||\n          (value == null && !input.getAttribute('value'));\n        input.classList.toggle('ql-active', isActive);\n        input.setAttribute('aria-pressed', isActive.toString());\n      } else {\n        const isActive = formats[format] != null;\n        input.classList.toggle('ql-active', isActive);\n        input.setAttribute('aria-pressed', isActive.toString());\n      }\n    });\n  }\n}\nToolbar.DEFAULTS = {};\n\nfunction addButton(container: HTMLElement, format: string, value?: string) {\n  const input = document.createElement('button');\n  input.setAttribute('type', 'button');\n  input.classList.add(`ql-${format}`);\n  input.setAttribute('aria-pressed', 'false');\n  if (value != null) {\n    input.value = value;\n    input.setAttribute('aria-label', `${format}: ${value}`);\n  } else {\n    input.setAttribute('aria-label', format);\n  }\n  container.appendChild(input);\n}\n\nfunction addControls(\n  container: HTMLElement,\n  groups:\n    | (string | Record<string, unknown>)[][]\n    | (string | Record<string, unknown>)[],\n) {\n  if (!Array.isArray(groups[0])) {\n    // @ts-expect-error\n    groups = [groups];\n  }\n  groups.forEach((controls: any) => {\n    const group = document.createElement('span');\n    group.classList.add('ql-formats');\n    controls.forEach((control: any) => {\n      if (typeof control === 'string') {\n        addButton(group, control);\n      } else {\n        const format = Object.keys(control)[0];\n        const value = control[format];\n        if (Array.isArray(value)) {\n          addSelect(group, format, value);\n        } else {\n          addButton(group, format, value);\n        }\n      }\n    });\n    container.appendChild(group);\n  });\n}\n\nfunction addSelect(\n  container: HTMLElement,\n  format: string,\n  values: Array<string | boolean>,\n) {\n  const input = document.createElement('select');\n  input.classList.add(`ql-${format}`);\n  values.forEach((value) => {\n    const option = document.createElement('option');\n    if (value !== false) {\n      option.setAttribute('value', String(value));\n    } else {\n      option.setAttribute('selected', 'selected');\n    }\n    input.appendChild(option);\n  });\n  container.appendChild(input);\n}\n\nToolbar.DEFAULTS = {\n  container: null,\n  handlers: {\n    clean() {\n      const range = this.quill.getSelection();\n      if (range == null) return;\n      if (range.length === 0) {\n        const formats = this.quill.getFormat();\n        Object.keys(formats).forEach((name) => {\n          // Clean functionality in existing apps only clean inline formats\n          if (this.quill.scroll.query(name, Scope.INLINE) != null) {\n            this.quill.format(name, false, Quill.sources.USER);\n          }\n        });\n      } else {\n        this.quill.removeFormat(range.index, range.length, Quill.sources.USER);\n      }\n    },\n    direction(value) {\n      const { align } = this.quill.getFormat();\n      if (value === 'rtl' && align == null) {\n        this.quill.format('align', 'right', Quill.sources.USER);\n      } else if (!value && align === 'right') {\n        this.quill.format('align', false, Quill.sources.USER);\n      }\n      this.quill.format('direction', value, Quill.sources.USER);\n    },\n    indent(value) {\n      const range = this.quill.getSelection();\n      // @ts-expect-error\n      const formats = this.quill.getFormat(range);\n      // @ts-expect-error\n      const indent = parseInt(formats.indent || 0, 10);\n      if (value === '+1' || value === '-1') {\n        let modifier = value === '+1' ? 1 : -1;\n        if (formats.direction === 'rtl') modifier *= -1;\n        this.quill.format('indent', indent + modifier, Quill.sources.USER);\n      }\n    },\n    link(value) {\n      if (value === true) {\n        value = prompt('Enter link URL:'); // eslint-disable-line no-alert\n      }\n      this.quill.format('link', value, Quill.sources.USER);\n    },\n    list(value) {\n      const range = this.quill.getSelection();\n      // @ts-expect-error\n      const formats = this.quill.getFormat(range);\n      if (value === 'check') {\n        if (formats.list === 'checked' || formats.list === 'unchecked') {\n          this.quill.format('list', false, Quill.sources.USER);\n        } else {\n          this.quill.format('list', 'unchecked', Quill.sources.USER);\n        }\n      } else {\n        this.quill.format('list', value, Quill.sources.USER);\n      }\n    },\n  },\n};\n\nexport { Toolbar as default, addControls };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,UAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,OAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,OAAA,GAAAL,sBAAA,CAAAC,OAAA;AAGA,IAAMK,KAAK,GAAG,IAAAC,eAAM,EAAC,eAAe,CAAC;AAAA,IAe/BC,OAAO,GAAAC,OAAA,CAAAC,OAAA,0BAAAC,OAAA;EAOX,SAAAH,QAAYI,KAAY,EAAEC,OAA8B,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAAL,OAAA,QAAAF,OAAA;IACxDM,KAAA,OAAAE,WAAA,CAAAN,OAAA,QAAAF,OAAA,GAAMI,KAAK,EAAEC,OAAO;IACpB,IAAII,KAAK,CAACC,OAAO,CAACJ,KAAA,CAAKD,OAAO,CAACM,SAAS,CAAC,EAAE;MAAA,IAAAC,gBAAA;MACzC,IAAMD,SAAS,GAAGE,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC/CH,SAAS,CAACI,YAAY,CAAC,MAAM,EAAE,SAAS,CAAC;MACzCC,WAAW,CAACL,SAAS,EAAEL,KAAA,CAAKD,OAAO,CAACM,SAAS,CAAC;MAC9C,CAAAC,gBAAA,GAAAR,KAAK,CAACO,SAAS,cAAAC,gBAAA,gBAAAA,gBAAA,GAAfA,gBAAA,CAAiBK,UAAU,cAAAL,gBAAA,eAA3BA,gBAAA,CAA6BM,YAAY,CAACP,SAAS,EAAEP,KAAK,CAACO,SAAS,CAAC;MACrEL,KAAA,CAAKK,SAAS,GAAGA,SAAS;IAC5B,CAAC,MAAM,IAAI,OAAOL,KAAA,CAAKD,OAAO,CAACM,SAAS,KAAK,QAAQ,EAAE;MACrDL,KAAA,CAAKK,SAAS,GAAGE,QAAQ,CAACM,aAAa,CAACb,KAAA,CAAKD,OAAO,CAACM,SAAS,CAAC;IACjE,CAAC,MAAM;MACLL,KAAA,CAAKK,SAAS,GAAGL,KAAA,CAAKD,OAAO,CAACM,SAAS;IACzC;IACA,IAAI,EAAEL,KAAA,CAAKK,SAAS,YAAYS,WAAW,CAAC,EAAE;MAC5CtB,KAAK,CAACuB,KAAK,CAAC,gCAAgC,EAAEf,KAAA,CAAKD,OAAO,CAAC;MAC3D,WAAAiB,2BAAA,CAAApB,OAAA,EAAAI,KAAA;IACF;IACAA,KAAA,CAAKK,SAAS,CAACY,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;IAC1ClB,KAAA,CAAKmB,QAAQ,GAAG,EAAE;IAClBnB,KAAA,CAAKoB,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAIpB,KAAA,CAAKD,OAAO,CAACqB,QAAQ,EAAE;MACzBC,MAAM,CAACC,IAAI,CAACtB,KAAA,CAAKD,OAAO,CAACqB,QAAQ,CAAC,CAACG,OAAO,CAAE,UAAAC,MAAM,EAAK;QAAA,IAAAC,qBAAA;QACrD,IAAMC,OAAO,IAAAD,qBAAA,GAAGzB,KAAA,CAAKD,OAAO,CAACqB,QAAQ,cAAAK,qBAAA,uBAArBA,qBAAA,CAAwBD,MAAM,CAAC;QAC/C,IAAIE,OAAO,EAAE;UACX1B,KAAA,CAAK2B,UAAU,CAACH,MAAM,EAAEE,OAAO,CAAC;QAClC;MACF,CAAC,CAAC;IACJ;IACAvB,KAAK,CAACyB,IAAI,CAAC5B,KAAA,CAAKK,SAAS,CAACwB,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,CAACN,OAAO,CAClE,UAAAO,KAAK,EAAK;MACT;MACA9B,KAAA,CAAK+B,MAAM,CAACD,KAAK,CAAC;IACpB,CACF,CAAC;IACD9B,KAAA,CAAKF,KAAK,CAACkC,EAAE,CAACC,cAAK,CAACC,MAAM,CAACC,aAAa,EAAE,YAAM;MAC9C,IAAAC,qBAAA,GAAgBpC,KAAA,CAAKF,KAAK,CAACuC,SAAS,CAACC,QAAQ,CAAC,CAAC;QAAAC,sBAAA,OAAAC,eAAA,CAAA5C,OAAA,EAAAwC,qBAAA;QAAxCK,KAAK,GAAAF,sBAAA,IAAoC,CAAC;MACjDvC,KAAA,CAAK0C,MAAM,CAACD,KAAK,CAAC;IACpB,CAAC,CAAC;IAAA,OAAAzC,KAAA;EACJ;EAAA,IAAA2C,UAAA,CAAA/C,OAAA,EAAAF,OAAA,EAAAG,OAAA;EAAA,WAAA+C,aAAA,CAAAhD,OAAA,EAAAF,OAAA;IAAAmD,GAAA;IAAAC,KAAA,EAEA,SAAAnB,UAAUA,CAACH,MAAc,EAAEE,OAAgB,EAAE;MAC3C,IAAI,CAACN,QAAQ,CAACI,MAAM,CAAC,GAAGE,OAAO;IACjC;EAAA;IAAAmB,GAAA;IAAAC,KAAA,EAEA,SAAAf,MAAMA,CAACD,KAAkB,EAAE;MAAA,IAAAiB,MAAA;MACzB,IAAIvB,MAAM,GAAGrB,KAAK,CAACyB,IAAI,CAACE,KAAK,CAACb,SAAS,CAAC,CAAC+B,IAAI,CAAE,UAAAC,SAAS,EAAK;QAC3D,OAAOA,SAAS,CAACC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC;MACvC,CAAC,CAAC;MACF,IAAI,CAAC1B,MAAM,EAAE;MACbA,MAAM,GAAGA,MAAM,CAAC2B,KAAK,CAAC,KAAK,CAACC,MAAM,CAAC;MACnC,IAAItB,KAAK,CAACuB,OAAO,KAAK,QAAQ,EAAE;QAC9BvB,KAAK,CAACrB,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC;MACtC;MACA,IACE,IAAI,CAACW,QAAQ,CAACI,MAAM,CAAC,IAAI,IAAI,IAC7B,IAAI,CAAC1B,KAAK,CAACwD,MAAM,CAACC,KAAK,CAAC/B,MAAM,CAAC,IAAI,IAAI,EACvC;QACAhC,KAAK,CAACgE,IAAI,CAAC,0CAA0C,EAAEhC,MAAM,EAAEM,KAAK,CAAC;QACrE;MACF;MACA,IAAM2B,SAAS,GAAG3B,KAAK,CAACuB,OAAO,KAAK,QAAQ,GAAG,QAAQ,GAAG,OAAO;MACjEvB,KAAK,CAAC4B,gBAAgB,CAACD,SAAS,EAAG,UAAAE,CAAC,EAAK;QACvC,IAAIb,KAAK;QACT,IAAIhB,KAAK,CAACuB,OAAO,KAAK,QAAQ,EAAE;UAC9B;UACA,IAAIvB,KAAK,CAAC8B,aAAa,GAAG,CAAC,EAAE;UAC7B;UACA,IAAMC,QAAQ,GAAG/B,KAAK,CAAC/B,OAAO,CAAC+B,KAAK,CAAC8B,aAAa,CAAC;UACnD,IAAIC,QAAQ,CAACC,YAAY,CAAC,UAAU,CAAC,EAAE;YACrChB,KAAK,GAAG,KAAK;UACf,CAAC,MAAM;YACLA,KAAK,GAAGe,QAAQ,CAACf,KAAK,IAAI,KAAK;UACjC;QACF,CAAC,MAAM;UACL,IAAIhB,KAAK,CAACb,SAAS,CAAC8C,QAAQ,CAAC,WAAW,CAAC,EAAE;YACzCjB,KAAK,GAAG,KAAK;UACf,CAAC,MAAM;YACL;YACAA,KAAK,GAAGhB,KAAK,CAACgB,KAAK,IAAI,CAAChB,KAAK,CAACgC,YAAY,CAAC,OAAO,CAAC;UACrD;UACAH,CAAC,CAACK,cAAc,CAAC,CAAC;QACpB;QACAjB,MAAI,CAACjD,KAAK,CAACmE,KAAK,CAAC,CAAC;QAClB,IAAAC,qBAAA,GAAgBnB,MAAI,CAACjD,KAAK,CAACuC,SAAS,CAACC,QAAQ,CAAC,CAAC;UAAA6B,sBAAA,OAAA3B,eAAA,CAAA5C,OAAA,EAAAsE,qBAAA;UAAxCzB,KAAK,GAAA0B,sBAAA;QACZ,IAAIpB,MAAI,CAAC3B,QAAQ,CAACI,MAAM,CAAC,IAAI,IAAI,EAAE;UACjCuB,MAAI,CAAC3B,QAAQ,CAACI,MAAM,CAAC,CAAC4C,IAAI,CAACrB,MAAI,EAAED,KAAK,CAAC;QACzC,CAAC,MAAM;QACL;QACAC,MAAI,CAACjD,KAAK,CAACwD,MAAM,CAACC,KAAK,CAAC/B,MAAM,CAAC,CAAC6C,SAAS,YAAYC,oBAAS,EAC9D;UACAxB,KAAK,GAAGyB,MAAM,UAAAC,MAAA,CAAUhD,MAAO,CAAC,CAAC,CAAC,CAAC;UACnC,IAAI,CAACsB,KAAK,EAAE;UACZC,MAAI,CAACjD,KAAK,CAAC2E,cAAc,CACvB,IAAIC,mBAAK,CAAC;UACR;UAAA,CACCC,MAAM,CAAClC,KAAK,CAACmC,KAAK;UACnB;UAAA,CACCC,MAAM,CAACpC,KAAK,CAACW,MAAM,CAAC,CACpB0B,MAAM,KAAAC,gBAAA,CAAAnF,OAAA,MAAI4B,MAAM,EAAGsB,KAAA,CAAO,CAAC,EAC9Bb,cAAK,CAAC+C,OAAO,CAACC,IAChB,CAAC;QACH,CAAC,MAAM;UACLlC,MAAI,CAACjD,KAAK,CAAC0B,MAAM,CAACA,MAAM,EAAEsB,KAAK,EAAEb,cAAK,CAAC+C,OAAO,CAACC,IAAI,CAAC;QACtD;QACAlC,MAAI,CAACL,MAAM,CAACD,KAAK,CAAC;MACpB,CAAC,CAAC;MACF,IAAI,CAACtB,QAAQ,CAAC+D,IAAI,CAAC,CAAC1D,MAAM,EAAEM,KAAK,CAAC,CAAC;IACrC;EAAA;IAAAe,GAAA;IAAAC,KAAA,EAEA,SAAAJ,MAAMA,CAACD,KAAmB,EAAE;MAC1B,IAAM0C,OAAO,GAAG1C,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC3C,KAAK,CAACsF,SAAS,CAAC3C,KAAK,CAAC;MAChE,IAAI,CAACtB,QAAQ,CAACI,OAAO,CAAE,UAAA8D,IAAI,EAAK;QAC9B,IAAAC,KAAA,OAAA9C,eAAA,CAAA5C,OAAA,EAAwByF,IAAI;UAArB7D,MAAM,GAAA8D,KAAA;UAAExD,KAAK,GAAAwD,KAAA;QACpB,IAAIxD,KAAK,CAACuB,OAAO,KAAK,QAAQ,EAAE;UAC9B,IAAIkC,MAAgC,GAAG,IAAI;UAC3C,IAAI9C,KAAK,IAAI,IAAI,EAAE;YACjB8C,MAAM,GAAG,IAAI;UACf,CAAC,MAAM,IAAIJ,OAAO,CAAC3D,MAAM,CAAC,IAAI,IAAI,EAAE;YAClC+D,MAAM,GAAGzD,KAAK,CAACjB,aAAa,CAAC,kBAAkB,CAAC;UAClD,CAAC,MAAM,IAAI,CAACV,KAAK,CAACC,OAAO,CAAC+E,OAAO,CAAC3D,MAAM,CAAC,CAAC,EAAE;YAC1C,IAAIsB,KAAK,GAAGqC,OAAO,CAAC3D,MAAM,CAAC;YAC3B,IAAI,OAAOsB,KAAK,KAAK,QAAQ,EAAE;cAC7BA,KAAK,GAAGA,KAAK,CAAC0C,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC;YACpC;YACAD,MAAM,GAAGzD,KAAK,CAACjB,aAAa,mBAAA2D,MAAA,CAAkB1B,KAAM,QAAG,CAAC;UAC1D;UACA,IAAIyC,MAAM,IAAI,IAAI,EAAE;YAClB;YACAzD,KAAK,CAACgB,KAAK,GAAG,EAAE,CAAC,CAAC;YAClB;YACAhB,KAAK,CAAC8B,aAAa,GAAG,CAAC,CAAC;UAC1B,CAAC,MAAM;YACL2B,MAAM,CAAC1B,QAAQ,GAAG,IAAI;UACxB;QACF,CAAC,MAAM,IAAIpB,KAAK,IAAI,IAAI,EAAE;UACxBX,KAAK,CAACb,SAAS,CAACwE,MAAM,CAAC,WAAW,CAAC;UACnC3D,KAAK,CAACrB,YAAY,CAAC,cAAc,EAAE,OAAO,CAAC;QAC7C,CAAC,MAAM,IAAIqB,KAAK,CAACgC,YAAY,CAAC,OAAO,CAAC,EAAE;UACtC;UACA;UACA,IAAMhB,MAAK,GAAGqC,OAAO,CAAC3D,MAAM,CAAuC;UACnE,IAAMkE,QAAQ,GACZ5C,MAAK,KAAKhB,KAAK,CAAC6D,YAAY,CAAC,OAAO,CAAC,IACpC7C,MAAK,IAAI,IAAI,IAAIA,MAAK,CAAC8C,QAAQ,CAAC,CAAC,KAAK9D,KAAK,CAAC6D,YAAY,CAAC,OAAO,CAAE,IAClE7C,MAAK,IAAI,IAAI,IAAI,CAAChB,KAAK,CAAC6D,YAAY,CAAC,OAAO,CAAE;UACjD7D,KAAK,CAACb,SAAS,CAAC4E,MAAM,CAAC,WAAW,EAAEH,QAAQ,CAAC;UAC7C5D,KAAK,CAACrB,YAAY,CAAC,cAAc,EAAEiF,QAAQ,CAACE,QAAQ,CAAC,CAAC,CAAC;QACzD,CAAC,MAAM;UACL,IAAMF,SAAQ,GAAGP,OAAO,CAAC3D,MAAM,CAAC,IAAI,IAAI;UACxCM,KAAK,CAACb,SAAS,CAAC4E,MAAM,CAAC,WAAW,EAAEH,SAAQ,CAAC;UAC7C5D,KAAK,CAACrB,YAAY,CAAC,cAAc,EAAEiF,SAAQ,CAACE,QAAQ,CAAC,CAAC,CAAC;QACzD;MACF,CAAC,CAAC;IACJ;EAAA;AAAA,EAhKoBE,eAAM;AAkK5BpG,OAAO,CAACqG,QAAQ,GAAG,CAAC,CAAC;AAErB,SAASC,SAASA,CAAC3F,SAAsB,EAAEmB,MAAc,EAAEsB,KAAc,EAAE;EACzE,IAAMhB,KAAK,GAAGvB,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC9CsB,KAAK,CAACrB,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC;EACpCqB,KAAK,CAACb,SAAS,CAACC,GAAG,OAAAsD,MAAA,CAAOhD,MAAO,CAAC,CAAC;EACnCM,KAAK,CAACrB,YAAY,CAAC,cAAc,EAAE,OAAO,CAAC;EAC3C,IAAIqC,KAAK,IAAI,IAAI,EAAE;IACjBhB,KAAK,CAACgB,KAAK,GAAGA,KAAK;IACnBhB,KAAK,CAACrB,YAAY,CAAC,YAAY,KAAA+D,MAAA,CAAKhD,MAAO,QAAAgD,MAAA,CAAI1B,KAAM,CAAC,CAAC;EACzD,CAAC,MAAM;IACLhB,KAAK,CAACrB,YAAY,CAAC,YAAY,EAAEe,MAAM,CAAC;EAC1C;EACAnB,SAAS,CAAC4F,WAAW,CAACnE,KAAK,CAAC;AAC9B;AAEA,SAASpB,WAAWA,CAClBL,SAAsB,EACtB6F,MAEwC,EACxC;EACA,IAAI,CAAC/F,KAAK,CAACC,OAAO,CAAC8F,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;IAC7B;IACAA,MAAM,GAAG,CAACA,MAAM,CAAC;EACnB;EACAA,MAAM,CAAC3E,OAAO,CAAE,UAAAJ,QAAa,EAAK;IAChC,IAAMgF,KAAK,GAAG5F,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IAC5C2F,KAAK,CAAClF,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;IACjCC,QAAQ,CAACI,OAAO,CAAE,UAAA6E,OAAY,EAAK;MACjC,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;QAC/BJ,SAAS,CAACG,KAAK,EAAEC,OAAO,CAAC;MAC3B,CAAC,MAAM;QACL,IAAM5E,MAAM,GAAGH,MAAM,CAACC,IAAI,CAAC8E,OAAO,CAAC,CAAC,CAAC,CAAC;QACtC,IAAMtD,KAAK,GAAGsD,OAAO,CAAC5E,MAAM,CAAC;QAC7B,IAAIrB,KAAK,CAACC,OAAO,CAAC0C,KAAK,CAAC,EAAE;UACxBuD,SAAS,CAACF,KAAK,EAAE3E,MAAM,EAAEsB,KAAK,CAAC;QACjC,CAAC,MAAM;UACLkD,SAAS,CAACG,KAAK,EAAE3E,MAAM,EAAEsB,KAAK,CAAC;QACjC;MACF;IACF,CAAC,CAAC;IACFzC,SAAS,CAAC4F,WAAW,CAACE,KAAK,CAAC;EAC9B,CAAC,CAAC;AACJ;AAEA,SAASE,SAASA,CAChBhG,SAAsB,EACtBmB,MAAc,EACd8E,MAA+B,EAC/B;EACA,IAAMxE,KAAK,GAAGvB,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC9CsB,KAAK,CAACb,SAAS,CAACC,GAAG,OAAAsD,MAAA,CAAOhD,MAAO,CAAC,CAAC;EACnC8E,MAAM,CAAC/E,OAAO,CAAE,UAAAuB,KAAK,EAAK;IACxB,IAAMyC,MAAM,GAAGhF,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/C,IAAIsC,KAAK,KAAK,KAAK,EAAE;MACnByC,MAAM,CAAC9E,YAAY,CAAC,OAAO,EAAE8F,MAAM,CAACzD,KAAK,CAAC,CAAC;IAC7C,CAAC,MAAM;MACLyC,MAAM,CAAC9E,YAAY,CAAC,UAAU,EAAE,UAAU,CAAC;IAC7C;IACAqB,KAAK,CAACmE,WAAW,CAACV,MAAM,CAAC;EAC3B,CAAC,CAAC;EACFlF,SAAS,CAAC4F,WAAW,CAACnE,KAAK,CAAC;AAC9B;AAEApC,OAAO,CAACqG,QAAQ,GAAG;EACjB1F,SAAS,EAAE,IAAI;EACfe,QAAQ,EAAE;IACRoF,KAAK,WAALA,KAAKA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACN,IAAMhE,KAAK,GAAG,IAAI,CAAC3C,KAAK,CAAC4G,YAAY,CAAC,CAAC;MACvC,IAAIjE,KAAK,IAAI,IAAI,EAAE;MACnB,IAAIA,KAAK,CAACW,MAAM,KAAK,CAAC,EAAE;QACtB,IAAM+B,OAAO,GAAG,IAAI,CAACrF,KAAK,CAACsF,SAAS,CAAC,CAAC;QACtC/D,MAAM,CAACC,IAAI,CAAC6D,OAAO,CAAC,CAAC5D,OAAO,CAAE,UAAAoF,IAAI,EAAK;UACrC;UACA,IAAIF,MAAI,CAAC3G,KAAK,CAACwD,MAAM,CAACC,KAAK,CAACoD,IAAI,EAAEC,gBAAK,CAACC,MAAM,CAAC,IAAI,IAAI,EAAE;YACvDJ,MAAI,CAAC3G,KAAK,CAAC0B,MAAM,CAACmF,IAAI,EAAE,KAAK,EAAE1E,cAAK,CAAC+C,OAAO,CAACC,IAAI,CAAC;UACpD;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAACnF,KAAK,CAACgH,YAAY,CAACrE,KAAK,CAACmC,KAAK,EAAEnC,KAAK,CAACW,MAAM,EAAEnB,cAAK,CAAC+C,OAAO,CAACC,IAAI,CAAC;MACxE;IACF,CAAC;IACD8B,SAAS,WAATA,SAASA,CAACjE,KAAK,EAAE;MACf,IAAAkE,qBAAA,GAAkB,IAAI,CAAClH,KAAK,CAACsF,SAAS,CAAC,CAAC;QAAhC6B,KAAA,GAAAD,qBAAA,CAAAC,KAAA;MACR,IAAInE,KAAK,KAAK,KAAK,IAAImE,KAAK,IAAI,IAAI,EAAE;QACpC,IAAI,CAACnH,KAAK,CAAC0B,MAAM,CAAC,OAAO,EAAE,OAAO,EAAES,cAAK,CAAC+C,OAAO,CAACC,IAAI,CAAC;MACzD,CAAC,MAAM,IAAI,CAACnC,KAAK,IAAImE,KAAK,KAAK,OAAO,EAAE;QACtC,IAAI,CAACnH,KAAK,CAAC0B,MAAM,CAAC,OAAO,EAAE,KAAK,EAAES,cAAK,CAAC+C,OAAO,CAACC,IAAI,CAAC;MACvD;MACA,IAAI,CAACnF,KAAK,CAAC0B,MAAM,CAAC,WAAW,EAAEsB,KAAK,EAAEb,cAAK,CAAC+C,OAAO,CAACC,IAAI,CAAC;IAC3D,CAAC;IACDiC,MAAM,WAANA,MAAMA,CAACpE,KAAK,EAAE;MACZ,IAAML,KAAK,GAAG,IAAI,CAAC3C,KAAK,CAAC4G,YAAY,CAAC,CAAC;MACvC;MACA,IAAMvB,OAAO,GAAG,IAAI,CAACrF,KAAK,CAACsF,SAAS,CAAC3C,KAAK,CAAC;MAC3C;MACA,IAAMyE,MAAM,GAAGC,QAAQ,CAAChC,OAAO,CAAC+B,MAAM,IAAI,CAAC,EAAE,EAAE,CAAC;MAChD,IAAIpE,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,IAAI,EAAE;QACpC,IAAIsE,QAAQ,GAAGtE,KAAK,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;QACtC,IAAIqC,OAAO,CAAC4B,SAAS,KAAK,KAAK,EAAEK,QAAQ,IAAI,CAAC,CAAC;QAC/C,IAAI,CAACtH,KAAK,CAAC0B,MAAM,CAAC,QAAQ,EAAE0F,MAAM,GAAGE,QAAQ,EAAEnF,cAAK,CAAC+C,OAAO,CAACC,IAAI,CAAC;MACpE;IACF,CAAC;IACDoC,IAAI,WAAJA,IAAIA,CAACvE,KAAK,EAAE;MACV,IAAIA,KAAK,KAAK,IAAI,EAAE;QAClBA,KAAK,GAAGyB,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC;MACrC;MACA,IAAI,CAACzE,KAAK,CAAC0B,MAAM,CAAC,MAAM,EAAEsB,KAAK,EAAEb,cAAK,CAAC+C,OAAO,CAACC,IAAI,CAAC;IACtD,CAAC;IACDqC,IAAI,WAAJA,IAAIA,CAACxE,KAAK,EAAE;MACV,IAAML,KAAK,GAAG,IAAI,CAAC3C,KAAK,CAAC4G,YAAY,CAAC,CAAC;MACvC;MACA,IAAMvB,OAAO,GAAG,IAAI,CAACrF,KAAK,CAACsF,SAAS,CAAC3C,KAAK,CAAC;MAC3C,IAAIK,KAAK,KAAK,OAAO,EAAE;QACrB,IAAIqC,OAAO,CAACmC,IAAI,KAAK,SAAS,IAAInC,OAAO,CAACmC,IAAI,KAAK,WAAW,EAAE;UAC9D,IAAI,CAACxH,KAAK,CAAC0B,MAAM,CAAC,MAAM,EAAE,KAAK,EAAES,cAAK,CAAC+C,OAAO,CAACC,IAAI,CAAC;QACtD,CAAC,MAAM;UACL,IAAI,CAACnF,KAAK,CAAC0B,MAAM,CAAC,MAAM,EAAE,WAAW,EAAES,cAAK,CAAC+C,OAAO,CAACC,IAAI,CAAC;QAC5D;MACF,CAAC,MAAM;QACL,IAAI,CAACnF,KAAK,CAAC0B,MAAM,CAAC,MAAM,EAAEsB,KAAK,EAAEb,cAAK,CAAC+C,OAAO,CAACC,IAAI,CAAC;MACtD;IACF;EACF;AACF,CAAC", "ignoreList": []}]}