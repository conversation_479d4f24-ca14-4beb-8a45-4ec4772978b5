{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/IconSelect/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/IconSelect/index.vue", "mtime": 1753510684527}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCIvVXNlcnMvbWF6aWhhby9EZXNrdG9wL2Rldi9wZXJmb3JtYW5jZS9wZXJmb3JtYW5jZS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmZpbHRlci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuaW5jbHVkZXMuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmZ1bmN0aW9uLm5hbWUuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5pbmNsdWRlcy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmNvbnN0cnVjdG9yLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IuZmlsdGVyLmpzIik7CnZhciBfcmVxdWlyZUljb25zID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIuL3JlcXVpcmVJY29ucyIpKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG5hbWU6ICdJY29uU2VsZWN0JywKICBwcm9wczogewogICAgYWN0aXZlSWNvbjogewogICAgICB0eXBlOiBTdHJpbmcKICAgIH0KICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBuYW1lOiAnJywKICAgICAgaWNvbkxpc3Q6IF9yZXF1aXJlSWNvbnMuZGVmYXVsdAogICAgfTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGZpbHRlckljb25zOiBmdW5jdGlvbiBmaWx0ZXJJY29ucygpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgdGhpcy5pY29uTGlzdCA9IF9yZXF1aXJlSWNvbnMuZGVmYXVsdDsKICAgICAgaWYgKHRoaXMubmFtZSkgewogICAgICAgIHRoaXMuaWNvbkxpc3QgPSB0aGlzLmljb25MaXN0LmZpbHRlcihmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgcmV0dXJuIGl0ZW0uaW5jbHVkZXMoX3RoaXMubmFtZSk7CiAgICAgICAgfSk7CiAgICAgIH0KICAgIH0sCiAgICBzZWxlY3RlZEljb246IGZ1bmN0aW9uIHNlbGVjdGVkSWNvbihuYW1lKSB7CiAgICAgIHRoaXMuJGVtaXQoJ3NlbGVjdGVkJywgbmFtZSk7CiAgICAgIGRvY3VtZW50LmJvZHkuY2xpY2soKTsKICAgIH0sCiAgICByZXNldDogZnVuY3Rpb24gcmVzZXQoKSB7CiAgICAgIHRoaXMubmFtZSA9ICcnOwogICAgICB0aGlzLmljb25MaXN0ID0gX3JlcXVpcmVJY29ucy5kZWZhdWx0OwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["_requireIcons", "_interopRequireDefault", "require", "name", "props", "activeIcon", "type", "String", "data", "iconList", "icons", "methods", "filterIcons", "_this", "filter", "item", "includes", "selectedIcon", "$emit", "document", "body", "click", "reset"], "sources": ["src/components/IconSelect/index.vue"], "sourcesContent": ["<!-- <AUTHOR> -->\n<template>\n  <div class=\"icon-body\">\n    <el-input v-model=\"name\" class=\"icon-search\" clearable placeholder=\"请输入图标名称\" @clear=\"filterIcons\" @input=\"filterIcons\">\n      <i slot=\"suffix\" class=\"el-icon-search el-input__icon\" />\n    </el-input>\n    <div class=\"icon-list\">\n      <div class=\"list-container\">\n        <div v-for=\"(item, index) in iconList\" class=\"icon-item-wrapper\" :key=\"index\" @click=\"selectedIcon(item)\">\n          <div :class=\"['icon-item', { active: activeIcon === item }]\">\n            <svg-icon :icon-class=\"item\" class-name=\"icon\" style=\"height: 25px;width: 16px;\"/>\n            <span>{{ item }}</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport icons from './requireIcons'\nexport default {\n  name: 'IconSelect',\n  props: {\n    activeIcon: {\n      type: String\n    }\n  },\n  data() {\n    return {\n      name: '',\n      iconList: icons\n    }\n  },\n  methods: {\n    filterIcons() {\n      this.iconList = icons\n      if (this.name) {\n        this.iconList = this.iconList.filter(item => item.includes(this.name))\n      }\n    },\n    selectedIcon(name) {\n      this.$emit('selected', name)\n      document.body.click()\n    },\n    reset() {\n      this.name = ''\n      this.iconList = icons\n    }\n  }\n}\n</script>\n\n<style rel=\"stylesheet/scss\" lang=\"scss\" scoped>\n  .icon-body {\n    width: 100%;\n    padding: 10px;\n    .icon-search {\n      position: relative;\n      margin-bottom: 5px;\n    }\n    .icon-list {\n      height: 200px;\n      overflow: auto;\n      .list-container {\n        display: flex;\n        flex-wrap: wrap;\n        .icon-item-wrapper {\n          width: calc(100% / 3);\n          height: 25px;\n          line-height: 25px;\n          cursor: pointer;\n          display: flex;\n          .icon-item {\n            display: flex;\n            max-width: 100%;\n            height: 100%;\n            padding: 0 5px;\n            &:hover {\n              background: #ececec;\n              border-radius: 5px;\n            }\n            .icon {\n              flex-shrink: 0;\n            }\n            span {\n              display: inline-block;\n              vertical-align: -0.15em;\n              fill: currentColor;\n              padding-left: 2px;\n              overflow: hidden;\n              text-overflow: ellipsis;\n              white-space: nowrap;\n            }\n          }\n          .icon-item.active {\n            background: #ececec;\n            border-radius: 5px;\n          }\n        }\n      }\n    }\n  }\n</style>\n"], "mappings": ";;;;;;;;;;;;;;AAoBA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;iCACA;EACAC,IAAA;EACAC,KAAA;IACAC,UAAA;MACAC,IAAA,EAAAC;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAL,IAAA;MACAM,QAAA,EAAAC;IACA;EACA;EACAC,OAAA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,KAAA;MACA,KAAAJ,QAAA,GAAAC,qBAAA;MACA,SAAAP,IAAA;QACA,KAAAM,QAAA,QAAAA,QAAA,CAAAK,MAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAC,QAAA,CAAAH,KAAA,CAAAV,IAAA;QAAA;MACA;IACA;IACAc,YAAA,WAAAA,aAAAd,IAAA;MACA,KAAAe,KAAA,aAAAf,IAAA;MACAgB,QAAA,CAAAC,IAAA,CAAAC,KAAA;IACA;IACAC,KAAA,WAAAA,MAAA;MACA,KAAAnB,IAAA;MACA,KAAAM,QAAA,GAAAC,qBAAA;IACA;EACA;AACA", "ignoreList": []}]}