{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/index.vue", "mtime": 1753510684533}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAiSW5kZXgiLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4ge307CiAgfSwKICBtZXRob2RzOiB7fQp9Ow=="}, {"version": 3, "names": ["name", "data", "methods"], "sources": ["src/views/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"20\">\n      <el-col :span=\"24\">\n        <el-card class=\"box-card\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span style=\"font-size: 18px; font-weight: bold;\">欢迎使用绩效管理系统</span>\n          </div>\n          <div class=\"welcome-content\">\n            <div class=\"welcome-text\">\n              <h2>科级及以下人员绩效评估系统</h2>\n              <p>本系统用于绩效管理，其中绩效计划包括组织绩效、科室绩效、班子成员绩效和个人绩效的全流程管理。</p>\n            </div>\n            \n            <el-row :gutter=\"20\" style=\"margin-top: 30px;\">\n              <el-col :span=\"6\">\n                <el-card class=\"feature-card\" shadow=\"hover\">\n                  <div class=\"feature-icon\">\n                    <i class=\"el-icon-s-grid\" style=\"color: #409EFF;\"></i>\n                  </div>\n                  <h3>组织绩效</h3>\n                </el-card>\n              </el-col>\n              \n              <el-col :span=\"6\">\n                <el-card class=\"feature-card\" shadow=\"hover\">\n                  <div class=\"feature-icon\">\n                    <i class=\"el-icon-office-building\" style=\"color: #67C23A;\"></i>\n                  </div>\n                  <h3>科室绩效</h3>\n                </el-card>\n              </el-col>\n              \n              <el-col :span=\"6\">\n                <el-card class=\"feature-card\" shadow=\"hover\">\n                  <div class=\"feature-icon\">\n                    <i class=\"el-icon-user-solid\" style=\"color: #E6A23C;\"></i>\n                  </div>\n                  <h3>班子成员绩效</h3>\n                </el-card>\n              </el-col>\n              \n              <el-col :span=\"6\">\n                <el-card class=\"feature-card\" shadow=\"hover\">\n                  <div class=\"feature-icon\">\n                    <i class=\"el-icon-user\" style=\"color: #F56C6C;\"></i>\n                  </div>\n                  <h3>个人绩效</h3>\n                </el-card>\n              </el-col>\n            </el-row>\n            \n\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"Index\",\n  data() {\n    return {\n      \n    }\n  },\n  methods: {\n    \n  }\n}\n</script>\n\n<style scoped>\n.welcome-content {\n  padding: 20px;\n  text-align: center;\n}\n\n.welcome-text h2 {\n  color: #303133;\n  margin-bottom: 15px;\n}\n\n.welcome-text p {\n  color: #606266;\n  font-size: 16px;\n  line-height: 1.6;\n}\n\n.feature-card {\n  text-align: center;\n  padding: 20px;\n  height: 180px;\n  cursor: pointer;\n  transition: all 0.3s;\n}\n\n.feature-card:hover {\n  transform: translateY(-5px);\n}\n\n.feature-icon {\n  font-size: 48px;\n  margin-bottom: 15px;\n}\n\n.feature-card h3 {\n  color: #303133;\n  margin-bottom: 10px;\n}\n\n.feature-card p {\n  color: #909399;\n  font-size: 14px;\n}\n\n\n</style> "], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCA6DA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA,QAEA;EACA;EACAC,OAAA,GAEA;AACA", "ignoreList": []}]}