{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/performance/yearlyResult.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/performance/yearlyResult.js", "mtime": 1753510684517}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCIvVXNlcnMvbWF6aWhhby9EZXNrdG9wL2Rldi9wZXJmb3JtYW5jZS9wZXJmb3JtYW5jZS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMubGlzdERlcGFydG1lbnRQZXJmb3JtYW5jZSA9IGxpc3REZXBhcnRtZW50UGVyZm9ybWFuY2U7CmV4cG9ydHMubGlzdE1lbWJlck9yZ2FuaXphdGlvbiA9IGxpc3RNZW1iZXJPcmdhbml6YXRpb247CmV4cG9ydHMubGlzdE9yZ2FuaXphdGlvblBlcmZvcm1hbmNlID0gbGlzdE9yZ2FuaXphdGlvblBlcmZvcm1hbmNlOwpleHBvcnRzLmxpc3RZZWFybHlHb29kR3JhZGUgPSBsaXN0WWVhcmx5R29vZEdyYWRlOwpleHBvcnRzLmxpc3RZZWFybHlSZXN1bHQgPSBsaXN0WWVhcmx5UmVzdWx0Owp2YXIgX3JlcXVlc3QgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvdXRpbHMvcmVxdWVzdCIpKTsKLy8g6I635Y+W5bm05bqm57uT5p6c5YiG57G75o6S5bqP5YiX6KGoCmZ1bmN0aW9uIGxpc3RZZWFybHlSZXN1bHQocXVlcnkpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9wZXJmb3JtYW5jZS95ZWFybHkvcmVzdWx0L2xpc3QnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfQoKLy8g6I635Y+WIuWlvSLnrYnmrKHkurrlkZjliJfooagKZnVuY3Rpb24gbGlzdFllYXJseUdvb2RHcmFkZShxdWVyeSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3BlcmZvcm1hbmNlL3llYXJseS9yZXN1bHQvZ29vZEdyYWRlJywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0KCi8vIOiOt+WPluenkeWupOe7qeaViOivhOS7t+e7k+aenOWIl+ihqApmdW5jdGlvbiBsaXN0RGVwYXJ0bWVudFBlcmZvcm1hbmNlKHF1ZXJ5KSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvcGVyZm9ybWFuY2UveWVhcmx5L3Jlc3VsdC9kZXBhcnRtZW50JywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0KCi8vIOiOt+WPluePreWtkOaIkOWRmOe7qeaViOivhOS7t+e7k+aenOWIl+ihqApmdW5jdGlvbiBsaXN0TWVtYmVyT3JnYW5pemF0aW9uKHF1ZXJ5KSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvcGVyZm9ybWFuY2UveWVhcmx5L3Jlc3VsdC9tZW1iZXInLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfQoKLy8g6I635Y+W57uE57uH57up5pWI6K+E5Lu357uT5p6c5YiX6KGoCmZ1bmN0aW9uIGxpc3RPcmdhbml6YXRpb25QZXJmb3JtYW5jZShxdWVyeSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3BlcmZvcm1hbmNlL3llYXJseS9yZXN1bHQvb3JnYW5pemF0aW9uJywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listYearlyResult", "query", "request", "url", "method", "params", "listYearlyGoodGrade", "listDepartmentPerformance", "listMemberOrganization", "listOrganizationPerformance"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/performance/yearlyResult.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 获取年度结果分类排序列表\nexport function listYearlyResult(query) {\n  return request({\n    url: '/performance/yearly/result/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 获取\"好\"等次人员列表\nexport function listYearlyGoodGrade(query) {\n  return request({\n    url: '/performance/yearly/result/goodGrade',\n    method: 'get',\n    params: query\n  })\n}\n\n// 获取科室绩效评价结果列表\nexport function listDepartmentPerformance(query) {\n  return request({\n    url: '/performance/yearly/result/department',\n    method: 'get',\n    params: query\n  })\n}\n\n// 获取班子成员绩效评价结果列表\nexport function listMemberOrganization(query) {\n  return request({\n    url: '/performance/yearly/result/member',\n    method: 'get',\n    params: query\n  })\n}\n\n// 获取组织绩效评价结果列表\nexport function listOrganizationPerformance(query) {\n  return request({\n    url: '/performance/yearly/result/organization',\n    method: 'get',\n    params: query\n  })\n} "], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EACtC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,mBAAmBA,CAACL,KAAK,EAAE;EACzC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,sCAAsC;IAC3CC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,yBAAyBA,CAACN,KAAK,EAAE;EAC/C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,uCAAuC;IAC5CC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASO,sBAAsBA,CAACP,KAAK,EAAE;EAC5C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,mCAAmC;IACxCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,2BAA2BA,CAACR,KAAK,EAAE;EACjD,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,yCAAyC;IAC9CC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}