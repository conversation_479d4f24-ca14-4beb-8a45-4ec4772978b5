{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/Screenfull/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/Screenfull/index.vue", "mtime": 1753510684528}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCIvVXNlcnMvbWF6aWhhby9EZXNrdG9wL2Rldi9wZXJmb3JtYW5jZS9wZXJmb3JtYW5jZS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKdmFyIF9zY3JlZW5mdWxsID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJzY3JlZW5mdWxsIikpOwovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSB7CiAgbmFtZTogJ1NjcmVlbmZ1bGwnLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBpc0Z1bGxzY3JlZW46IGZhbHNlCiAgICB9OwogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIHRoaXMuaW5pdCgpOwogIH0sCiAgYmVmb3JlRGVzdHJveTogZnVuY3Rpb24gYmVmb3JlRGVzdHJveSgpIHsKICAgIHRoaXMuZGVzdHJveSgpOwogIH0sCiAgbWV0aG9kczogewogICAgY2xpY2s6IGZ1bmN0aW9uIGNsaWNrKCkgewogICAgICBpZiAoIV9zY3JlZW5mdWxsLmRlZmF1bHQuaXNFbmFibGVkKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICBtZXNzYWdlOiAn5L2g55qE5rWP6KeI5Zmo5LiN5pSv5oyB5YWo5bGPJywKICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAgIH0pOwogICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgfQogICAgICBfc2NyZWVuZnVsbC5kZWZhdWx0LnRvZ2dsZSgpOwogICAgfSwKICAgIGNoYW5nZTogZnVuY3Rpb24gY2hhbmdlKCkgewogICAgICB0aGlzLmlzRnVsbHNjcmVlbiA9IF9zY3JlZW5mdWxsLmRlZmF1bHQuaXNGdWxsc2NyZWVuOwogICAgfSwKICAgIGluaXQ6IGZ1bmN0aW9uIGluaXQoKSB7CiAgICAgIGlmIChfc2NyZWVuZnVsbC5kZWZhdWx0LmlzRW5hYmxlZCkgewogICAgICAgIF9zY3JlZW5mdWxsLmRlZmF1bHQub24oJ2NoYW5nZScsIHRoaXMuY2hhbmdlKTsKICAgICAgfQogICAgfSwKICAgIGRlc3Ryb3k6IGZ1bmN0aW9uIGRlc3Ryb3koKSB7CiAgICAgIGlmIChfc2NyZWVuZnVsbC5kZWZhdWx0LmlzRW5hYmxlZCkgewogICAgICAgIF9zY3JlZW5mdWxsLmRlZmF1bHQub2ZmKCdjaGFuZ2UnLCB0aGlzLmNoYW5nZSk7CiAgICAgIH0KICAgIH0KICB9Cn07"}, {"version": 3, "names": ["_screenfull", "_interopRequireDefault", "require", "name", "data", "isFullscreen", "mounted", "init", "<PERSON><PERSON><PERSON><PERSON>", "destroy", "methods", "click", "screenfull", "isEnabled", "$message", "message", "type", "toggle", "change", "on", "off"], "sources": ["src/components/Screenfull/index.vue"], "sourcesContent": ["<template>\n  <div>\n    <svg-icon :icon-class=\"isFullscreen?'exit-fullscreen':'fullscreen'\" @click=\"click\" />\n  </div>\n</template>\n\n<script>\nimport screenfull from 'screenfull'\n\nexport default {\n  name: 'Screenfull',\n  data() {\n    return {\n      isFullscreen: false\n    }\n  },\n  mounted() {\n    this.init()\n  },\n  beforeDestroy() {\n    this.destroy()\n  },\n  methods: {\n    click() {\n      if (!screenfull.isEnabled) {\n        this.$message({ message: '你的浏览器不支持全屏', type: 'warning' })\n        return false\n      }\n      screenfull.toggle()\n    },\n    change() {\n      this.isFullscreen = screenfull.isFullscreen\n    },\n    init() {\n      if (screenfull.isEnabled) {\n        screenfull.on('change', this.change)\n      }\n    },\n    destroy() {\n      if (screenfull.isEnabled) {\n        screenfull.off('change', this.change)\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.screenfull-svg {\n  display: inline-block;\n  cursor: pointer;\n  fill: #5a5e66;;\n  width: 20px;\n  height: 20px;\n  vertical-align: 10px;\n}\n</style>\n"], "mappings": ";;;;;;;AAOA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,YAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAC,KAAA,WAAAA,MAAA;MACA,KAAAC,mBAAA,CAAAC,SAAA;QACA,KAAAC,QAAA;UAAAC,OAAA;UAAAC,IAAA;QAAA;QACA;MACA;MACAJ,mBAAA,CAAAK,MAAA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAb,YAAA,GAAAO,mBAAA,CAAAP,YAAA;IACA;IACAE,IAAA,WAAAA,KAAA;MACA,IAAAK,mBAAA,CAAAC,SAAA;QACAD,mBAAA,CAAAO,EAAA,gBAAAD,MAAA;MACA;IACA;IACAT,OAAA,WAAAA,QAAA;MACA,IAAAG,mBAAA,CAAAC,SAAA;QACAD,mBAAA,CAAAQ,GAAA,gBAAAF,MAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}