{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/monitor/cache.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/monitor/cache.js", "mtime": 1753510684516}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "getCache", "request", "url", "method", "listCacheName", "list<PERSON><PERSON><PERSON><PERSON>", "cacheName", "getCacheValue", "cache<PERSON>ey", "clearCacheName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clearCacheAll"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/monitor/cache.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询缓存详细\nexport function getCache() {\n  return request({\n    url: '/monitor/cache',\n    method: 'get'\n  })\n}\n\n// 查询缓存名称列表\nexport function listCacheName() {\n  return request({\n    url: '/monitor/cache/getNames',\n    method: 'get'\n  })\n}\n\n// 查询缓存键名列表\nexport function listCacheKey(cacheName) {\n  return request({\n    url: '/monitor/cache/getKeys/' + cacheName,\n    method: 'get'\n  })\n}\n\n// 查询缓存内容\nexport function getCacheValue(cacheName, cacheKey) {\n  return request({\n    url: '/monitor/cache/getValue/' + cacheName + '/' + cacheKey,\n    method: 'get'\n  })\n}\n\n// 清理指定名称缓存\nexport function clearCacheName(cacheName) {\n  return request({\n    url: '/monitor/cache/clearCacheName/' + cacheName,\n    method: 'delete'\n  })\n}\n\n// 清理指定键名缓存\nexport function clearCacheKey(cacheKey) {\n  return request({\n    url: '/monitor/cache/clearCacheKey/' + cacheKey,\n    method: 'delete'\n  })\n}\n\n// 清理全部缓存\nexport function clearCacheAll() {\n  return request({\n    url: '/monitor/cache/clearCacheAll',\n    method: 'delete'\n  })\n}\n"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,QAAQA,CAAA,EAAG;EACzB,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,aAAaA,CAAA,EAAG;EAC9B,OAAO,IAAAH,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,YAAYA,CAACC,SAAS,EAAE;EACtC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB,GAAGI,SAAS;IAC1CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,aAAaA,CAACD,SAAS,EAAEE,QAAQ,EAAE;EACjD,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B,GAAGI,SAAS,GAAG,GAAG,GAAGE,QAAQ;IAC5DL,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,cAAcA,CAACH,SAAS,EAAE;EACxC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC,GAAGI,SAAS;IACjDH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASO,aAAaA,CAACF,QAAQ,EAAE;EACtC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,+BAA+B,GAAGM,QAAQ;IAC/CL,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,aAAaA,CAAA,EAAG;EAC9B,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}