{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/formats/blockquote.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/formats/blockquote.js", "mtime": 1753510684092}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCIvVXNlcnMvbWF6aWhhby9EZXNrdG9wL2Rldi9wZXJmb3JtYW5jZS9wZXJmb3JtYW5jZS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKdmFyIF9jcmVhdGVDbGFzczIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi9Vc2Vycy9tYXppaGFvL0Rlc2t0b3AvZGV2L3BlcmZvcm1hbmNlL3BlcmZvcm1hbmNlLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2NyZWF0ZUNsYXNzLmpzIikpOwp2YXIgX2NsYXNzQ2FsbENoZWNrMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiL1VzZXJzL21hemloYW8vRGVza3RvcC9kZXYvcGVyZm9ybWFuY2UvcGVyZm9ybWFuY2UtdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvY2xhc3NDYWxsQ2hlY2suanMiKSk7CnZhciBfY2FsbFN1cGVyMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiL1VzZXJzL21hemloYW8vRGVza3RvcC9kZXYvcGVyZm9ybWFuY2UvcGVyZm9ybWFuY2UtdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvY2FsbFN1cGVyLmpzIikpOwp2YXIgX2luaGVyaXRzMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiL1VzZXJzL21hemloYW8vRGVza3RvcC9kZXYvcGVyZm9ybWFuY2UvcGVyZm9ybWFuY2UtdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW5oZXJpdHMuanMiKSk7CnZhciBfZGVmaW5lUHJvcGVydHkyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIvVXNlcnMvbWF6aWhhby9EZXNrdG9wL2Rldi9wZXJmb3JtYW5jZS9wZXJmb3JtYW5jZS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9kZWZpbmVQcm9wZXJ0eS5qcyIpKTsKdmFyIF9ibG9jayA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi4vYmxvdHMvYmxvY2suanMiKSk7CnZhciBCbG9ja3F1b3RlID0gLyojX19QVVJFX18qL2Z1bmN0aW9uIChfQmxvY2spIHsKICBmdW5jdGlvbiBCbG9ja3F1b3RlKCkgewogICAgKDAsIF9jbGFzc0NhbGxDaGVjazIuZGVmYXVsdCkodGhpcywgQmxvY2txdW90ZSk7CiAgICByZXR1cm4gKDAsIF9jYWxsU3VwZXIyLmRlZmF1bHQpKHRoaXMsIEJsb2NrcXVvdGUsIGFyZ3VtZW50cyk7CiAgfQogICgwLCBfaW5oZXJpdHMyLmRlZmF1bHQpKEJsb2NrcXVvdGUsIF9CbG9jayk7CiAgcmV0dXJuICgwLCBfY3JlYXRlQ2xhc3MyLmRlZmF1bHQpKEJsb2NrcXVvdGUpOwp9KF9ibG9jay5kZWZhdWx0KTsKKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoQmxvY2txdW90ZSwgImJsb3ROYW1lIiwgJ2Jsb2NrcXVvdGUnKTsKKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoQmxvY2txdW90ZSwgInRhZ05hbWUiLCAnYmxvY2txdW90ZScpOwp2YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSBCbG9ja3F1b3RlOw=="}, {"version": 3, "names": ["_block", "_interopRequireDefault", "require", "Blockquote", "_Block", "_classCallCheck2", "default", "_callSuper2", "arguments", "_inherits2", "_createClass2", "Block", "_defineProperty2", "_default", "exports"], "sources": ["../../src/formats/blockquote.ts"], "sourcesContent": ["import Block from '../blots/block.js';\n\nclass Blockquote extends Block {\n  static blotName = 'blockquote';\n  static tagName = 'blockquote';\n}\n\nexport default Blockquote;\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAqC,IAE/BC,UAAU,0BAAAC,MAAA;EAAA,SAAAD,WAAA;IAAA,IAAAE,gBAAA,CAAAC,OAAA,QAAAH,UAAA;IAAA,WAAAI,WAAA,CAAAD,OAAA,QAAAH,UAAA,EAAAK,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAH,UAAA,EAAAC,MAAA;EAAA,WAAAM,aAAA,CAAAJ,OAAA,EAAAH,UAAA;AAAA,EAASQ,cAAK;AAAA,IAAAC,gBAAA,CAAAN,OAAA,EAAxBH,UAAU,cACI,YAAY;AAAA,IAAAS,gBAAA,CAAAN,OAAA,EAD1BH,UAAU,aAEG,YAAY;AAAA,IAAAU,QAAA,GAAAC,OAAA,CAAAR,OAAA,GAGhBH,UAAU", "ignoreList": []}]}