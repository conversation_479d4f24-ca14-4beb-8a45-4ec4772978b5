{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/@babel/runtime/helpers/possibleConstructorReturn.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/@babel/runtime/helpers/possibleConstructorReturn.js", "mtime": 1753510682283}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:cmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmVycm9yLmNhdXNlLmpzIik7CnZhciBfdHlwZW9mID0gcmVxdWlyZSgiLi90eXBlb2YuanMiKVsiZGVmYXVsdCJdOwp2YXIgYXNzZXJ0VGhpc0luaXRpYWxpemVkID0gcmVxdWlyZSgiLi9hc3NlcnRUaGlzSW5pdGlhbGl6ZWQuanMiKTsKZnVuY3Rpb24gX3Bvc3NpYmxlQ29uc3RydWN0b3JSZXR1cm4odCwgZSkgewogIGlmIChlICYmICgib2JqZWN0IiA9PSBfdHlwZW9mKGUpIHx8ICJmdW5jdGlvbiIgPT0gdHlwZW9mIGUpKSByZXR1cm4gZTsKICBpZiAodm9pZCAwICE9PSBlKSB0aHJvdyBuZXcgVHlwZUVycm9yKCJEZXJpdmVkIGNvbnN0cnVjdG9ycyBtYXkgb25seSByZXR1cm4gb2JqZWN0IG9yIHVuZGVmaW5lZCIpOwogIHJldHVybiBhc3NlcnRUaGlzSW5pdGlhbGl6ZWQodCk7Cn0KbW9kdWxlLmV4cG9ydHMgPSBfcG9zc2libGVDb25zdHJ1Y3RvclJldHVybiwgbW9kdWxlLmV4cG9ydHMuX19lc01vZHVsZSA9IHRydWUsIG1vZHVsZS5leHBvcnRzWyJkZWZhdWx0Il0gPSBtb2R1bGUuZXhwb3J0czs="}, {"version": 3, "names": ["_typeof", "require", "assertThisInitialized", "_possibleConstructorReturn", "t", "e", "TypeError", "module", "exports", "__esModule"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/@babel/runtime/helpers/possibleConstructorReturn.js"], "sourcesContent": ["var _typeof = require(\"./typeof.js\")[\"default\"];\nvar assertThisInitialized = require(\"./assertThisInitialized.js\");\nfunction _possibleConstructorReturn(t, e) {\n  if (e && (\"object\" == _typeof(e) || \"function\" == typeof e)) return e;\n  if (void 0 !== e) throw new TypeError(\"Derived constructors may only return object or undefined\");\n  return assertThisInitialized(t);\n}\nmodule.exports = _possibleConstructorReturn, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "mappings": ";AAAA,IAAIA,OAAO,GAAGC,OAAO,CAAC,aAAa,CAAC,CAAC,SAAS,CAAC;AAC/C,IAAIC,qBAAqB,GAAGD,OAAO,CAAC,4BAA4B,CAAC;AACjE,SAASE,0BAA0BA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACxC,IAAIA,CAAC,KAAK,QAAQ,IAAIL,OAAO,CAACK,CAAC,CAAC,IAAI,UAAU,IAAI,OAAOA,CAAC,CAAC,EAAE,OAAOA,CAAC;EACrE,IAAI,KAAK,CAAC,KAAKA,CAAC,EAAE,MAAM,IAAIC,SAAS,CAAC,0DAA0D,CAAC;EACjG,OAAOJ,qBAAqB,CAACE,CAAC,CAAC;AACjC;AACAG,MAAM,CAACC,OAAO,GAAGL,0BAA0B,EAAEI,MAAM,CAACC,OAAO,CAACC,UAAU,GAAG,IAAI,EAAEF,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO", "ignoreList": []}]}