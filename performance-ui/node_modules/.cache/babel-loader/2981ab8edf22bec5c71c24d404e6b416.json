{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/utils/validate.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/utils/validate.js", "mtime": 1753510684532}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmlzQXJyYXkgPSBpc0FycmF5OwpleHBvcnRzLmlzRW1wdHkgPSBpc0VtcHR5OwpleHBvcnRzLmlzRXh0ZXJuYWwgPSBpc0V4dGVybmFsOwpleHBvcnRzLmlzSHR0cCA9IGlzSHR0cDsKZXhwb3J0cy5pc1BhdGhNYXRjaCA9IGlzUGF0aE1hdGNoOwpleHBvcnRzLmlzU3RyaW5nID0gaXNTdHJpbmc7CmV4cG9ydHMudmFsaWRBbHBoYWJldHMgPSB2YWxpZEFscGhhYmV0czsKZXhwb3J0cy52YWxpZEVtYWlsID0gdmFsaWRFbWFpbDsKZXhwb3J0cy52YWxpZExvd2VyQ2FzZSA9IHZhbGlkTG93ZXJDYXNlOwpleHBvcnRzLnZhbGlkVVJMID0gdmFsaWRVUkw7CmV4cG9ydHMudmFsaWRVcHBlckNhc2UgPSB2YWxpZFVwcGVyQ2FzZTsKZXhwb3J0cy52YWxpZFVzZXJuYW1lID0gdmFsaWRVc2VybmFtZTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC5jb25zdHJ1Y3Rvci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLmRvdC1hbGwuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC5leGVjLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAuc3RpY2t5LmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAudGVzdC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLnRvLXN0cmluZy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLnJlcGxhY2UuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy50cmltLmpzIik7Ci8qKgogKiDot6/lvoTljLnphY3lmagKICogQHBhcmFtIHtzdHJpbmd9IHBhdHRlcm4KICogQHBhcmFtIHtzdHJpbmd9IHBhdGgKICogQHJldHVybnMge0Jvb2xlYW59CiAqLwpmdW5jdGlvbiBpc1BhdGhNYXRjaChwYXR0ZXJuLCBwYXRoKSB7CiAgdmFyIHJlZ2V4UGF0dGVybiA9IHBhdHRlcm4ucmVwbGFjZSgvXC8vZywgJ1xcLycpLnJlcGxhY2UoL1wqXCovZywgJy4qJykucmVwbGFjZSgvXCovZywgJ1teXFwvXSonKTsKICB2YXIgcmVnZXggPSBuZXcgUmVnRXhwKCJeIi5jb25jYXQocmVnZXhQYXR0ZXJuLCAiJCIpKTsKICByZXR1cm4gcmVnZXgudGVzdChwYXRoKTsKfQoKLyoqCiAqIOWIpOaWrXZhbHVl5a2X56ym5Liy5piv5ZCm5Li656m6IAogKiBAcGFyYW0ge3N0cmluZ30gdmFsdWUKICogQHJldHVybnMge0Jvb2xlYW59CiAqLwpmdW5jdGlvbiBpc0VtcHR5KHZhbHVlKSB7CiAgaWYgKHZhbHVlID09IG51bGwgfHwgdmFsdWUgPT0gIiIgfHwgdmFsdWUgPT0gdW5kZWZpbmVkIHx8IHZhbHVlID09ICJ1bmRlZmluZWQiKSB7CiAgICByZXR1cm4gdHJ1ZTsKICB9CiAgcmV0dXJuIGZhbHNlOwp9CgovKioKICog5Yik5patdXJs5piv5ZCm5pivaHR0cOaIlmh0dHBzIAogKiBAcGFyYW0ge3N0cmluZ30gdXJsCiAqIEByZXR1cm5zIHtCb29sZWFufQogKi8KZnVuY3Rpb24gaXNIdHRwKHVybCkgewogIHJldHVybiB1cmwuaW5kZXhPZignaHR0cDovLycpICE9PSAtMSB8fCB1cmwuaW5kZXhPZignaHR0cHM6Ly8nKSAhPT0gLTE7Cn0KCi8qKgogKiDliKTmlq1wYXRo5piv5ZCm5Li65aSW6ZO+CiAqIEBwYXJhbSB7c3RyaW5nfSBwYXRoCiAqIEByZXR1cm5zIHtCb29sZWFufQogKi8KZnVuY3Rpb24gaXNFeHRlcm5hbChwYXRoKSB7CiAgcmV0dXJuIC9eKGh0dHBzPzp8bWFpbHRvOnx0ZWw6KS8udGVzdChwYXRoKTsKfQoKLyoqCiAqIEBwYXJhbSB7c3RyaW5nfSBzdHIKICogQHJldHVybnMge0Jvb2xlYW59CiAqLwpmdW5jdGlvbiB2YWxpZFVzZXJuYW1lKHN0cikgewogIHZhciB2YWxpZF9tYXAgPSBbJ2FkbWluJywgJ2VkaXRvciddOwogIHJldHVybiB2YWxpZF9tYXAuaW5kZXhPZihzdHIudHJpbSgpKSA+PSAwOwp9CgovKioKICogQHBhcmFtIHtzdHJpbmd9IHVybAogKiBAcmV0dXJucyB7Qm9vbGVhbn0KICovCmZ1bmN0aW9uIHZhbGlkVVJMKHVybCkgewogIHZhciByZWcgPSAvXihodHRwcz98ZnRwKTpcL1wvKFthLXpBLVowLTkuLV0rKDpbYS16QS1aMC05LiYlJC1dKykqQCkqKCgyNVswLTVdfDJbMC00XVswLTldfDFbMC05XXsyfXxbMS05XVswLTldPykoXC4oMjVbMC01XXwyWzAtNF1bMC05XXwxWzAtOV17Mn18WzEtOV0/WzAtOV0pKXszfXwoW2EtekEtWjAtOS1dK1wuKSpbYS16QS1aMC05LV0rXC4oY29tfGVkdXxnb3Z8aW50fG1pbHxuZXR8b3JnfGJpenxhcnBhfGluZm98bmFtZXxwcm98YWVyb3xjb29wfG11c2V1bXxbYS16QS1aXXsyfSkpKDpbMC05XSspKihcLygkfFthLXpBLVowLTkuLD8nXFwrJiUkIz1+Xy1dKykpKiQvOwogIHJldHVybiByZWcudGVzdCh1cmwpOwp9CgovKioKICogQHBhcmFtIHtzdHJpbmd9IHN0cgogKiBAcmV0dXJucyB7Qm9vbGVhbn0KICovCmZ1bmN0aW9uIHZhbGlkTG93ZXJDYXNlKHN0cikgewogIHZhciByZWcgPSAvXlthLXpdKyQvOwogIHJldHVybiByZWcudGVzdChzdHIpOwp9CgovKioKICogQHBhcmFtIHtzdHJpbmd9IHN0cgogKiBAcmV0dXJucyB7Qm9vbGVhbn0KICovCmZ1bmN0aW9uIHZhbGlkVXBwZXJDYXNlKHN0cikgewogIHZhciByZWcgPSAvXltBLVpdKyQvOwogIHJldHVybiByZWcudGVzdChzdHIpOwp9CgovKioKICogQHBhcmFtIHtzdHJpbmd9IHN0cgogKiBAcmV0dXJucyB7Qm9vbGVhbn0KICovCmZ1bmN0aW9uIHZhbGlkQWxwaGFiZXRzKHN0cikgewogIHZhciByZWcgPSAvXltBLVphLXpdKyQvOwogIHJldHVybiByZWcudGVzdChzdHIpOwp9CgovKioKICogQHBhcmFtIHtzdHJpbmd9IGVtYWlsCiAqIEByZXR1cm5zIHtCb29sZWFufQogKi8KZnVuY3Rpb24gdmFsaWRFbWFpbChlbWFpbCkgewogIHZhciByZWcgPSAvXigoW148PigpXFtcXVxcLiw7OlxzQCJdKyhcLltePD4oKVxbXF1cXC4sOzpcc0AiXSspKil8KCIuKyIpKUAoKFxbWzAtOV17MSwzfVwuWzAtOV17MSwzfVwuWzAtOV17MSwzfVwuWzAtOV17MSwzfVxdKXwoKFthLXpBLVpcLTAtOV0rXC4pK1thLXpBLVpdezIsfSkpJC87CiAgcmV0dXJuIHJlZy50ZXN0KGVtYWlsKTsKfQoKLyoqCiAqIEBwYXJhbSB7c3RyaW5nfSBzdHIKICogQHJldHVybnMge0Jvb2xlYW59CiAqLwpmdW5jdGlvbiBpc1N0cmluZyhzdHIpIHsKICByZXR1cm4gdHlwZW9mIHN0ciA9PT0gJ3N0cmluZycgfHwgc3RyIGluc3RhbmNlb2YgU3RyaW5nOwp9CgovKioKICogQHBhcmFtIHtBcnJheX0gYXJnCiAqIEByZXR1cm5zIHtCb29sZWFufQogKi8KZnVuY3Rpb24gaXNBcnJheShhcmcpIHsKICBpZiAodHlwZW9mIEFycmF5LmlzQXJyYXkgPT09ICd1bmRlZmluZWQnKSB7CiAgICByZXR1cm4gT2JqZWN0LnByb3RvdHlwZS50b1N0cmluZy5jYWxsKGFyZykgPT09ICdbb2JqZWN0IEFycmF5XSc7CiAgfQogIHJldHVybiBBcnJheS5pc0FycmF5KGFyZyk7Cn0="}, {"version": 3, "names": ["isPathMatch", "pattern", "path", "regexPattern", "replace", "regex", "RegExp", "concat", "test", "isEmpty", "value", "undefined", "isHttp", "url", "indexOf", "isExternal", "validUsername", "str", "valid_map", "trim", "validURL", "reg", "validLowerCase", "validUpperCase", "validAlphabets", "validEmail", "email", "isString", "String", "isArray", "arg", "Array", "Object", "prototype", "toString", "call"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/utils/validate.js"], "sourcesContent": ["/**\n * 路径匹配器\n * @param {string} pattern\n * @param {string} path\n * @returns {Boolean}\n */\nexport function isPathMatch(pattern, path) {\n  const regexPattern = pattern.replace(/\\//g, '\\\\/').replace(/\\*\\*/g, '.*').replace(/\\*/g, '[^\\\\/]*')\n  const regex = new RegExp(`^${regexPattern}$`)\n  return regex.test(path)\n}\n\n/**\n * 判断value字符串是否为空 \n * @param {string} value\n * @returns {Boolean}\n */\nexport function isEmpty(value) {\n  if (value == null || value == \"\" || value == undefined || value == \"undefined\") {\n    return true\n  }\n  return false\n}\n\n/**\n * 判断url是否是http或https \n * @param {string} url\n * @returns {Boolean}\n */\nexport function isHttp(url) {\n  return url.indexOf('http://') !== -1 || url.indexOf('https://') !== -1\n}\n\n/**\n * 判断path是否为外链\n * @param {string} path\n * @returns {Boolean}\n */\nexport function isExternal(path) {\n  return /^(https?:|mailto:|tel:)/.test(path)\n}\n\n/**\n * @param {string} str\n * @returns {Boolean}\n */\nexport function validUsername(str) {\n  const valid_map = ['admin', 'editor']\n  return valid_map.indexOf(str.trim()) >= 0\n}\n\n/**\n * @param {string} url\n * @returns {Boolean}\n */\nexport function validURL(url) {\n  const reg = /^(https?|ftp):\\/\\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\\.)*[a-zA-Z0-9-]+\\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\\/($|[a-zA-Z0-9.,?'\\\\+&%$#=~_-]+))*$/\n  return reg.test(url)\n}\n\n/**\n * @param {string} str\n * @returns {Boolean}\n */\nexport function validLowerCase(str) {\n  const reg = /^[a-z]+$/\n  return reg.test(str)\n}\n\n/**\n * @param {string} str\n * @returns {Boolean}\n */\nexport function validUpperCase(str) {\n  const reg = /^[A-Z]+$/\n  return reg.test(str)\n}\n\n/**\n * @param {string} str\n * @returns {Boolean}\n */\nexport function validAlphabets(str) {\n  const reg = /^[A-Za-z]+$/\n  return reg.test(str)\n}\n\n/**\n * @param {string} email\n * @returns {Boolean}\n */\nexport function validEmail(email) {\n  const reg = /^(([^<>()\\[\\]\\\\.,;:\\s@\"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$/\n  return reg.test(email)\n}\n\n/**\n * @param {string} str\n * @returns {Boolean}\n */\nexport function isString(str) {\n  return typeof str === 'string' || str instanceof String\n}\n\n/**\n * @param {Array} arg\n * @returns {Boolean}\n */\nexport function isArray(arg) {\n  if (typeof Array.isArray === 'undefined') {\n    return Object.prototype.toString.call(arg) === '[object Array]'\n  }\n  return Array.isArray(arg)\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACO,SAASA,WAAWA,CAACC,OAAO,EAAEC,IAAI,EAAE;EACzC,IAAMC,YAAY,GAAGF,OAAO,CAACG,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAACA,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC;EACnG,IAAMC,KAAK,GAAG,IAAIC,MAAM,KAAAC,MAAA,CAAKJ,YAAY,MAAG,CAAC;EAC7C,OAAOE,KAAK,CAACG,IAAI,CAACN,IAAI,CAAC;AACzB;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASO,OAAOA,CAACC,KAAK,EAAE;EAC7B,IAAIA,KAAK,IAAI,IAAI,IAAIA,KAAK,IAAI,EAAE,IAAIA,KAAK,IAAIC,SAAS,IAAID,KAAK,IAAI,WAAW,EAAE;IAC9E,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASE,MAAMA,CAACC,GAAG,EAAE;EAC1B,OAAOA,GAAG,CAACC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAID,GAAG,CAACC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AACxE;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASC,UAAUA,CAACb,IAAI,EAAE;EAC/B,OAAO,yBAAyB,CAACM,IAAI,CAACN,IAAI,CAAC;AAC7C;;AAEA;AACA;AACA;AACA;AACO,SAASc,aAAaA,CAACC,GAAG,EAAE;EACjC,IAAMC,SAAS,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC;EACrC,OAAOA,SAAS,CAACJ,OAAO,CAACG,GAAG,CAACE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;AAC3C;;AAEA;AACA;AACA;AACA;AACO,SAASC,QAAQA,CAACP,GAAG,EAAE;EAC5B,IAAMQ,GAAG,GAAG,4TAA4T;EACxU,OAAOA,GAAG,CAACb,IAAI,CAACK,GAAG,CAAC;AACtB;;AAEA;AACA;AACA;AACA;AACO,SAASS,cAAcA,CAACL,GAAG,EAAE;EAClC,IAAMI,GAAG,GAAG,UAAU;EACtB,OAAOA,GAAG,CAACb,IAAI,CAACS,GAAG,CAAC;AACtB;;AAEA;AACA;AACA;AACA;AACO,SAASM,cAAcA,CAACN,GAAG,EAAE;EAClC,IAAMI,GAAG,GAAG,UAAU;EACtB,OAAOA,GAAG,CAACb,IAAI,CAACS,GAAG,CAAC;AACtB;;AAEA;AACA;AACA;AACA;AACO,SAASO,cAAcA,CAACP,GAAG,EAAE;EAClC,IAAMI,GAAG,GAAG,aAAa;EACzB,OAAOA,GAAG,CAACb,IAAI,CAACS,GAAG,CAAC;AACtB;;AAEA;AACA;AACA;AACA;AACO,SAASQ,UAAUA,CAACC,KAAK,EAAE;EAChC,IAAML,GAAG,GAAG,yJAAyJ;EACrK,OAAOA,GAAG,CAACb,IAAI,CAACkB,KAAK,CAAC;AACxB;;AAEA;AACA;AACA;AACA;AACO,SAASC,QAAQA,CAACV,GAAG,EAAE;EAC5B,OAAO,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,YAAYW,MAAM;AACzD;;AAEA;AACA;AACA;AACA;AACO,SAASC,OAAOA,CAACC,GAAG,EAAE;EAC3B,IAAI,OAAOC,KAAK,CAACF,OAAO,KAAK,WAAW,EAAE;IACxC,OAAOG,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,GAAG,CAAC,KAAK,gBAAgB;EACjE;EACA,OAAOC,KAAK,CAACF,OAAO,CAACC,GAAG,CAAC;AAC3B", "ignoreList": []}]}