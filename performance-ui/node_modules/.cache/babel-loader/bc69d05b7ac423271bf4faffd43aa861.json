{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/layout/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/layout/index.vue", "mtime": 1753510684530}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_components", "require", "_ResizeHandler", "_interopRequireDefault", "_vuex", "_variables2", "name", "components", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Settings", "Sidebar", "TagsView", "mixins", "ResizeMixin", "computed", "_objectSpread2", "default", "mapState", "theme", "state", "settings", "sideTheme", "sidebar", "app", "device", "needTagsView", "tagsView", "fixedHeader", "classObj", "hideSidebar", "opened", "openSidebar", "withoutAnimation", "mobile", "variables", "methods", "handleClickOutside", "$store", "dispatch", "setLayout", "$refs", "settingRef", "openSetting"], "sources": ["src/layout/index.vue"], "sourcesContent": ["<template>\n  <div :class=\"classObj\" class=\"app-wrapper\" :style=\"{'--current-color': theme}\">\n    <div v-if=\"device==='mobile'&&sidebar.opened\" class=\"drawer-bg\" @click=\"handleClickOutside\"/>\n    <sidebar v-if=\"!sidebar.hide\" class=\"sidebar-container\"/>\n    <div :class=\"{hasTagsView:needTagsView,sidebarHide:sidebar.hide}\" class=\"main-container\">\n      <div :class=\"{'fixed-header':fixedHeader}\">\n        <navbar @setLayout=\"setLayout\"/>\n        <tags-view v-if=\"needTagsView\"/>\n      </div>\n      <app-main/>\n      <settings ref=\"settingRef\"/>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { AppMain, Navbar, Settings, Sidebar, TagsView } from './components'\nimport ResizeMixin from './mixin/ResizeHandler'\nimport { mapState } from 'vuex'\nimport variables from '@/assets/styles/variables.scss'\n\nexport default {\n  name: 'Layout',\n  components: {\n    AppMain,\n    Navbar,\n    Settings,\n    Sidebar,\n    TagsView\n  },\n  mixins: [ResizeMixin],\n  computed: {\n    ...mapState({\n      theme: state => state.settings.theme,\n      sideTheme: state => state.settings.sideTheme,\n      sidebar: state => state.app.sidebar,\n      device: state => state.app.device,\n      needTagsView: state => state.settings.tagsView,\n      fixedHeader: state => state.settings.fixedHeader\n    }),\n    classObj() {\n      return {\n        hideSidebar: !this.sidebar.opened,\n        openSidebar: this.sidebar.opened,\n        withoutAnimation: this.sidebar.withoutAnimation,\n        mobile: this.device === 'mobile'\n      }\n    },\n    variables() {\n      return variables\n    }\n  },\n  methods: {\n    handleClickOutside() {\n      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })\n    },\n    setLayout() {\n      this.$refs.settingRef.openSetting()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n  @import \"~@/assets/styles/mixin.scss\";\n  @import \"~@/assets/styles/variables.scss\";\n\n  .app-wrapper {\n    @include clearfix;\n    position: relative;\n    height: 100%;\n    width: 100%;\n\n    &.mobile.openSidebar {\n      position: fixed;\n      top: 0;\n    }\n  }\n\n  .drawer-bg {\n    background: #000;\n    opacity: 0.3;\n    width: 100%;\n    top: 0;\n    height: 100%;\n    position: absolute;\n    z-index: 999;\n  }\n\n  .fixed-header {\n    position: fixed;\n    top: 0;\n    right: 0;\n    z-index: 9;\n    width: calc(100% - #{$base-sidebar-width});\n    transition: width 0.28s;\n  }\n\n  .hideSidebar .fixed-header {\n    width: calc(100% - 54px);\n  }\n\n  .sidebarHide .fixed-header {\n    width: 100%;\n  }\n\n  .mobile .fixed-header {\n    width: 100%;\n  }\n</style>\n"], "mappings": ";;;;;;;;AAgBA,IAAAA,WAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AACA,IAAAI,WAAA,GAAAF,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;iCAEA;EACAK,IAAA;EACAC,UAAA;IACAC,OAAA,EAAAA,mBAAA;IACAC,MAAA,EAAAA,kBAAA;IACAC,QAAA,EAAAA,oBAAA;IACAC,OAAA,EAAAA,mBAAA;IACAC,QAAA,EAAAA;EACA;EACAC,MAAA,GAAAC,sBAAA;EACAC,QAAA,MAAAC,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA,IAAAC,cAAA;IACAC,KAAA,WAAAA,MAAAC,KAAA;MAAA,OAAAA,KAAA,CAAAC,QAAA,CAAAF,KAAA;IAAA;IACAG,SAAA,WAAAA,UAAAF,KAAA;MAAA,OAAAA,KAAA,CAAAC,QAAA,CAAAC,SAAA;IAAA;IACAC,OAAA,WAAAA,QAAAH,KAAA;MAAA,OAAAA,KAAA,CAAAI,GAAA,CAAAD,OAAA;IAAA;IACAE,MAAA,WAAAA,OAAAL,KAAA;MAAA,OAAAA,KAAA,CAAAI,GAAA,CAAAC,MAAA;IAAA;IACAC,YAAA,WAAAA,aAAAN,KAAA;MAAA,OAAAA,KAAA,CAAAC,QAAA,CAAAM,QAAA;IAAA;IACAC,WAAA,WAAAA,YAAAR,KAAA;MAAA,OAAAA,KAAA,CAAAC,QAAA,CAAAO,WAAA;IAAA;EACA;IACAC,QAAA,WAAAA,SAAA;MACA;QACAC,WAAA,QAAAP,OAAA,CAAAQ,MAAA;QACAC,WAAA,OAAAT,OAAA,CAAAQ,MAAA;QACAE,gBAAA,OAAAV,OAAA,CAAAU,gBAAA;QACAC,MAAA,OAAAT,MAAA;MACA;IACA;IACAU,SAAA,WAAAA,UAAA;MACA,OAAAA,mBAAA;IACA;EAAA,EACA;EACAC,OAAA;IACAC,kBAAA,WAAAA,mBAAA;MACA,KAAAC,MAAA,CAAAC,QAAA;QAAAN,gBAAA;MAAA;IACA;IACAO,SAAA,WAAAA,UAAA;MACA,KAAAC,KAAA,CAAAC,UAAA,CAAAC,WAAA;IACA;EACA;AACA", "ignoreList": []}]}