{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/system/dict/data.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/system/dict/data.js", "mtime": 1753510684517}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCIvVXNlcnMvbWF6aWhhby9EZXNrdG9wL2Rldi9wZXJmb3JtYW5jZS9wZXJmb3JtYW5jZS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuYWRkRGF0YSA9IGFkZERhdGE7CmV4cG9ydHMuZGVsRGF0YSA9IGRlbERhdGE7CmV4cG9ydHMuZ2V0RGF0YSA9IGdldERhdGE7CmV4cG9ydHMuZ2V0RGljdHMgPSBnZXREaWN0czsKZXhwb3J0cy5saXN0RGF0YSA9IGxpc3REYXRhOwpleHBvcnRzLnVwZGF0ZURhdGEgPSB1cGRhdGVEYXRhOwp2YXIgX3JlcXVlc3QgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvdXRpbHMvcmVxdWVzdCIpKTsKLy8g5p+l6K+i5a2X5YW45pWw5o2u5YiX6KGoCmZ1bmN0aW9uIGxpc3REYXRhKHF1ZXJ5KSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvc3lzdGVtL2RpY3QvZGF0YS9saXN0JywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0KCi8vIOafpeivouWtl+WFuOaVsOaNruivpue7hgpmdW5jdGlvbiBnZXREYXRhKGRpY3RDb2RlKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvc3lzdGVtL2RpY3QvZGF0YS8nICsgZGljdENvZGUsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8vIOagueaNruWtl+WFuOexu+Wei+afpeivouWtl+WFuOaVsOaNruS/oeaBrwpmdW5jdGlvbiBnZXREaWN0cyhkaWN0VHlwZSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS9kaWN0L2RhdGEvdHlwZS8nICsgZGljdFR5cGUsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8vIOaWsOWinuWtl+WFuOaVsOaNrgpmdW5jdGlvbiBhZGREYXRhKGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9zeXN0ZW0vZGljdC9kYXRhJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDkv67mlLnlrZflhbjmlbDmja4KZnVuY3Rpb24gdXBkYXRlRGF0YShkYXRhKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvc3lzdGVtL2RpY3QvZGF0YScsCiAgICBtZXRob2Q6ICdwdXQnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDliKDpmaTlrZflhbjmlbDmja4KZnVuY3Rpb24gZGVsRGF0YShkaWN0Q29kZSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS9kaWN0L2RhdGEvJyArIGRpY3RDb2RlLAogICAgbWV0aG9kOiAnZGVsZXRlJwogIH0pOwp9"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listData", "query", "request", "url", "method", "params", "getData", "dictCode", "getDicts", "dictType", "addData", "data", "updateData", "delData"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/system/dict/data.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询字典数据列表\nexport function listData(query) {\n  return request({\n    url: '/system/dict/data/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询字典数据详细\nexport function getData(dictCode) {\n  return request({\n    url: '/system/dict/data/' + dictCode,\n    method: 'get'\n  })\n}\n\n// 根据字典类型查询字典数据信息\nexport function getDicts(dictType) {\n  return request({\n    url: '/system/dict/data/type/' + dictType,\n    method: 'get'\n  })\n}\n\n// 新增字典数据\nexport function addData(data) {\n  return request({\n    url: '/system/dict/data',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改字典数据\nexport function updateData(data) {\n  return request({\n    url: '/system/dict/data',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除字典数据\nexport function delData(dictCode) {\n  return request({\n    url: '/system/dict/data/' + dictCode,\n    method: 'delete'\n  })\n}\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,OAAOA,CAACC,QAAQ,EAAE;EAChC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGI,QAAQ;IACpCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,QAAQA,CAACC,QAAQ,EAAE;EACjC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB,GAAGM,QAAQ;IACzCL,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,MAAM;IACdO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,OAAOA,CAACN,QAAQ,EAAE;EAChC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGI,QAAQ;IACpCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}