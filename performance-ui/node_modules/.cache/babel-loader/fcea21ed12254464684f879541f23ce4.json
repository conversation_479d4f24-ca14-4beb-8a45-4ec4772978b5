{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/result/yearResult/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/result/yearResult/index.vue", "mtime": 1753510684535}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_yearlyResult", "require", "_default", "exports", "default", "name", "data", "activeTab", "loading", "categoryList", "resultList", "goodGradeList", "departmentList", "memberList", "organizationList", "queryParams", "year", "Date", "getFullYear", "created", "getResultList", "getGoodGradeList", "methods", "_this", "listYearlyResult", "then", "res", "code", "_toConsumableArray2", "Set", "map", "item", "category", "_this2", "listYearlyGoodGrade", "handleQuery", "getDepartmentList", "getMemberList", "getOrganizationList", "reset<PERSON><PERSON>y", "_this3", "listDepartmentPerformance", "_this4", "listMemberOrganization", "_this5", "listOrganizationPerformance", "handleTabClick", "tab"], "sources": ["src/views/result/yearResult/index.vue"], "sourcesContent": ["<script>\nimport { listYearlyResult, listYearlyGoodGrade, listDepartmentPerformance, listMemberOrganization, listOrganizationPerformance } from '@/api/performance/yearlyResult'\n\nexport default {\n  name: 'YearResult',\n  data() {\n    return {\n      // 当前激活的tab\n      activeTab: 'tab1',\n      // 加载状态\n      loading: true,\n      // 分类列表\n      categoryList: [],\n      // 年度结果列表（按分类排序）\n      resultList: [],\n      // \"好\"等次人员列表\n      goodGradeList: [],\n      // 科室绩效评价结果列表\n      departmentList: [],\n      // 班子成员绩效评价结果列表\n      memberList: [],\n      // 组织绩效评价结果列表\n      organizationList: [],\n      // 查询参数\n      queryParams: {\n        year: new Date().getFullYear()\n      }\n    }\n  },\n  created() {\n    this.getResultList()\n    this.getGoodGradeList()\n  },\n  methods: {\n    // 获取年度结果列表（按分类排序）\n    getResultList() {\n      this.loading = true\n      listYearlyResult(this.queryParams).then(res => {\n        if (res.code === 200) {\n          this.resultList = res.data || []\n          // 提取所有分类\n          this.categoryList = [...new Set(this.resultList.map(item => item.category))]\n        }\n        this.loading = false\n      })\n    },\n    // 获取\"好\"等次人员列表\n    getGoodGradeList() {\n      listYearlyGoodGrade(this.queryParams).then(res => {\n        if (res.code === 200) {\n          this.goodGradeList = res.data || []\n        }\n      })\n    },\n    // 按年度查询\n    handleQuery() {\n      if (this.activeTab === 'tab1') {\n        this.getResultList()\n        this.getGoodGradeList()\n      } else if (this.activeTab === 'tab2') {\n        this.getDepartmentList()\n      } else if (this.activeTab === 'tab3') {\n        this.getMemberList()\n      } else if (this.activeTab === 'tab4') {\n        this.getOrganizationList()\n      }\n    },\n    // 重置查询\n    resetQuery() {\n      this.queryParams = {\n        year: new Date().getFullYear()\n      }\n      this.handleQuery()\n    },\n    // 获取科室绩效评价结果列表\n    getDepartmentList() {\n      this.loading = true\n      listDepartmentPerformance(this.queryParams).then(res => {\n        if (res.code === 200) {\n          this.departmentList = res.data || []\n        }\n        this.loading = false\n      })\n    },\n    // 获取班子成员绩效评价结果列表\n    getMemberList() {\n      this.loading = true\n      listMemberOrganization(this.queryParams).then(res => {\n        if (res.code === 200) {\n          this.memberList = res.data || []\n        }\n        this.loading = false\n      })\n    },\n    // 获取组织绩效评价结果列表\n    getOrganizationList() {\n      this.loading = true\n      listOrganizationPerformance(this.queryParams).then(res => {\n        if (res.code === 200) {\n          this.organizationList = res.data || []\n        }\n        this.loading = false\n      })\n    },\n    // 切换Tab\n    handleTabClick(tab) {\n      if (tab.name === 'tab1') {\n        this.getResultList()\n        this.getGoodGradeList()\n      } else if (tab.name === 'tab2') {\n        this.getDepartmentList()\n      } else if (tab.name === 'tab3') {\n        this.getMemberList()\n      } else if (tab.name === 'tab4') {\n        this.getOrganizationList()\n      }\n    }\n  }\n}\n</script>\n\n<template>\n  <div class=\"app-container\">\n    <!-- Tab栏 -->\n    <el-tabs v-model=\"activeTab\" @tab-click=\"handleTabClick\">\n      <el-tab-pane label=\"个人年度分类排序\" name=\"tab1\">\n        <!-- 查询区域 -->\n        <el-form :model=\"queryParams\" ref=\"que  ryForm\" :inline=\"true\" class=\"search-form\">\n          <el-form-item label=\"年度\">\n            <el-input v-model=\"queryParams.year\" placeholder=\"请输入年度\" clearable type=\"number\" />\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" @click=\"handleQuery\">查询</el-button>\n            <el-button @click=\"resetQuery\">重置</el-button>\n          </el-form-item>\n        </el-form>\n\n        <!-- 分类排序展示区域 -->\n        <el-card class=\"box-card\" v-loading=\"loading\">\n          <div slot=\"header\" class=\"card-header\">\n            <span>{{ queryParams.year }}年度绩效结果分类排序</span>\n          </div>\n          <div v-for=\"category in categoryList\" :key=\"category\" class=\"category-section\">\n            <h3>{{ category }}</h3>\n            <el-table :data=\"resultList.filter(item => item.category === category)\" border style=\"width: 100%\">\n              <el-table-column prop=\"deptName\" label=\"科室\" />\n              <el-table-column prop=\"position\" label=\"职务\" />\n              <el-table-column prop=\"userName\" label=\"姓名\" />\n              <el-table-column prop=\"yearScore\" label=\"年度得分\" />\n              <el-table-column prop=\"sameLevelOrder\" label=\"同层级排序\" />\n              <el-table-column prop=\"grade\" label=\"等次\" />\n            </el-table>\n          </div>\n        </el-card>\n\n        <!-- \"好\"等次人员名单 -->\n        <el-card class=\"box-card\" style=\"margin-top: 20px;\">\n          <div slot=\"header\" class=\"card-header\">\n            <span>{{ queryParams.year }}年度\"好\"等次人员名单</span>\n          </div>\n          <el-table :data=\"goodGradeList\" border style=\"width: 100%\">\n            <el-table-column prop=\"deptName\" label=\"科室\" />\n            <el-table-column prop=\"position\" label=\"职务\" />\n            <el-table-column prop=\"category\" label=\"分类\" />\n            <el-table-column prop=\"userName\" label=\"姓名\" />\n            <el-table-column prop=\"yearScore\" label=\"年度得分\" />\n            <el-table-column prop=\"sameLevelOrder\" label=\"同层级排序\" />\n          </el-table>\n        </el-card>\n      </el-tab-pane>\n\n      <el-tab-pane label=\"科室绩效评价\" name=\"tab2\">\n        <!-- 查询区域 -->\n        <el-form :model=\"queryParams\" ref=\"queryForm2\" :inline=\"true\" class=\"search-form\">\n          <el-form-item label=\"年度\">\n            <el-input v-model=\"queryParams.year\" placeholder=\"请输入年度\" clearable type=\"number\" />\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" @click=\"handleQuery\">查询</el-button>\n            <el-button @click=\"resetQuery\">重置</el-button>\n          </el-form-item>\n        </el-form>\n\n        <!-- 科室绩效评价结果表格 -->\n        <el-card class=\"box-card\" v-loading=\"loading\">\n          <div slot=\"header\" class=\"card-header\">\n            <span>{{ queryParams.year }}年度科室绩效评价结果（按总分排序）</span>\n          </div>\n          <el-table :data=\"departmentList\" border style=\"width: 100%\">\n            <el-table-column prop=\"deptName\" label=\"科室名称\" />\n            <el-table-column prop=\"testTime\" label=\"测评时间\" />\n            <el-table-column prop=\"year\" label=\"年份\" />\n            <el-table-column prop=\"totalScore\" label=\"总分\" sortable />\n            <el-table-column prop=\"remark\" label=\"备注\" />\n          </el-table>\n        </el-card>\n      </el-tab-pane>\n\n      <el-tab-pane label=\"班子成员绩效评价\" name=\"tab3\">\n        <!-- 查询区域 -->\n        <el-form :model=\"queryParams\" ref=\"queryForm3\" :inline=\"true\" class=\"search-form\">\n          <el-form-item label=\"年度\">\n            <el-input v-model=\"queryParams.year\" placeholder=\"请输入年度\" clearable type=\"number\" />\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" @click=\"handleQuery\">查询</el-button>\n            <el-button @click=\"resetQuery\">重置</el-button>\n          </el-form-item>\n        </el-form>\n\n        <!-- 班子成员绩效评价结果表格 -->\n        <el-card class=\"box-card\" v-loading=\"loading\">\n          <div slot=\"header\" class=\"card-header\">\n            <span>{{ queryParams.year }}年度班子成员绩效评价结果（按评价分值排序）</span>\n          </div>\n          <el-table :data=\"memberList\" border style=\"width: 100%\">\n            <el-table-column prop=\"memberName\" label=\"姓名\" />\n            <el-table-column prop=\"testTime\" label=\"测评时间\" />\n            <el-table-column prop=\"taskType\" label=\"任务类型\" />\n            <el-table-column prop=\"performanceTask\" label=\"绩效任务\" />\n            <el-table-column prop=\"responsibleLeader\" label=\"责任领导\" />\n            <el-table-column prop=\"evaluationScore\" label=\"评价分值\" sortable />\n            <el-table-column prop=\"remark\" label=\"备注\" />\n          </el-table>\n        </el-card>\n      </el-tab-pane>\n\n      <el-tab-pane label=\"组织绩效评价\" name=\"tab4\">\n        <!-- 查询区域 -->\n        <el-form :model=\"queryParams\" ref=\"queryForm4\" :inline=\"true\" class=\"search-form\">\n          <el-form-item label=\"年度\">\n            <el-input v-model=\"queryParams.year\" placeholder=\"请输入年度\" clearable type=\"number\" />\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" @click=\"handleQuery\">查询</el-button>\n            <el-button @click=\"resetQuery\">重置</el-button>\n          </el-form-item>\n        </el-form>\n\n        <!-- 组织绩效评价结果表格 -->\n        <el-card class=\"box-card\" v-loading=\"loading\">\n          <div slot=\"header\" class=\"card-header\">\n            <span>{{ queryParams.year }}年度组织绩效评价结果（按评价分值排序）</span>\n          </div>\n          <el-table :data=\"organizationList\" border style=\"width: 100%\">\n            <el-table-column prop=\"taskType\" label=\"任务类型\" />\n            <el-table-column prop=\"taskSource\" label=\"任务来源\" />\n            <el-table-column prop=\"performanceTask\" label=\"绩效任务\" />\n            <el-table-column prop=\"responsibleDept\" label=\"责任科室\" />\n            <el-table-column prop=\"responsibleLeader\" label=\"责任领导\" />\n            <el-table-column prop=\"evaluationScore\" label=\"评价分值\" sortable />\n            <el-table-column prop=\"remark\" label=\"备注\" />\n          </el-table>\n        </el-card>\n      </el-tab-pane>\n    </el-tabs>\n  </div>\n</template>\n\n<style scoped lang=\"scss\">\n.search-form {\n  margin-bottom: 20px;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.category-section {\n  margin-bottom: 20px;\n\n  h3 {\n    margin-bottom: 10px;\n    padding-left: 5px;\n    border-left: 4px solid #409EFF;\n  }\n}\n\n.empty-tab {\n  padding: 40px;\n  text-align: center;\n  color: #909399;\n  font-size: 16px;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AACA,IAAAA,aAAA,GAAAC,OAAA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,SAAA;MACA;MACAC,OAAA;MACA;MACAC,YAAA;MACA;MACAC,UAAA;MACA;MACAC,aAAA;MACA;MACAC,cAAA;MACA;MACAC,UAAA;MACA;MACAC,gBAAA;MACA;MACAC,WAAA;QACAC,IAAA,MAAAC,IAAA,GAAAC,WAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,aAAA;IACA,KAAAC,gBAAA;EACA;EACAC,OAAA;IACA;IACAF,aAAA,WAAAA,cAAA;MAAA,IAAAG,KAAA;MACA,KAAAf,OAAA;MACA,IAAAgB,8BAAA,OAAAT,WAAA,EAAAU,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAJ,KAAA,CAAAb,UAAA,GAAAgB,GAAA,CAAApB,IAAA;UACA;UACAiB,KAAA,CAAAd,YAAA,OAAAmB,mBAAA,CAAAxB,OAAA,MAAAyB,GAAA,CAAAN,KAAA,CAAAb,UAAA,CAAAoB,GAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAAC,QAAA;UAAA;QACA;QACAT,KAAA,CAAAf,OAAA;MACA;IACA;IACA;IACAa,gBAAA,WAAAA,iBAAA;MAAA,IAAAY,MAAA;MACA,IAAAC,iCAAA,OAAAnB,WAAA,EAAAU,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAM,MAAA,CAAAtB,aAAA,GAAAe,GAAA,CAAApB,IAAA;QACA;MACA;IACA;IACA;IACA6B,WAAA,WAAAA,YAAA;MACA,SAAA5B,SAAA;QACA,KAAAa,aAAA;QACA,KAAAC,gBAAA;MACA,gBAAAd,SAAA;QACA,KAAA6B,iBAAA;MACA,gBAAA7B,SAAA;QACA,KAAA8B,aAAA;MACA,gBAAA9B,SAAA;QACA,KAAA+B,mBAAA;MACA;IACA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,KAAAxB,WAAA;QACAC,IAAA,MAAAC,IAAA,GAAAC,WAAA;MACA;MACA,KAAAiB,WAAA;IACA;IACA;IACAC,iBAAA,WAAAA,kBAAA;MAAA,IAAAI,MAAA;MACA,KAAAhC,OAAA;MACA,IAAAiC,uCAAA,OAAA1B,WAAA,EAAAU,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAa,MAAA,CAAA5B,cAAA,GAAAc,GAAA,CAAApB,IAAA;QACA;QACAkC,MAAA,CAAAhC,OAAA;MACA;IACA;IACA;IACA6B,aAAA,WAAAA,cAAA;MAAA,IAAAK,MAAA;MACA,KAAAlC,OAAA;MACA,IAAAmC,oCAAA,OAAA5B,WAAA,EAAAU,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAe,MAAA,CAAA7B,UAAA,GAAAa,GAAA,CAAApB,IAAA;QACA;QACAoC,MAAA,CAAAlC,OAAA;MACA;IACA;IACA;IACA8B,mBAAA,WAAAA,oBAAA;MAAA,IAAAM,MAAA;MACA,KAAApC,OAAA;MACA,IAAAqC,yCAAA,OAAA9B,WAAA,EAAAU,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAiB,MAAA,CAAA9B,gBAAA,GAAAY,GAAA,CAAApB,IAAA;QACA;QACAsC,MAAA,CAAApC,OAAA;MACA;IACA;IACA;IACAsC,cAAA,WAAAA,eAAAC,GAAA;MACA,IAAAA,GAAA,CAAA1C,IAAA;QACA,KAAAe,aAAA;QACA,KAAAC,gBAAA;MACA,WAAA0B,GAAA,CAAA1C,IAAA;QACA,KAAA+B,iBAAA;MACA,WAAAW,GAAA,CAAA1C,IAAA;QACA,KAAAgC,aAAA;MACA,WAAAU,GAAA,CAAA1C,IAAA;QACA,KAAAiC,mBAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}