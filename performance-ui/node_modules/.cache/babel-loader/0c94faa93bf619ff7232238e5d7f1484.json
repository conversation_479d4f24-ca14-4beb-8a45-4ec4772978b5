{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/performance/organization.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/performance/organization.js", "mtime": 1753510684516}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCIvVXNlcnMvbWF6aWhhby9EZXNrdG9wL2Rldi9wZXJmb3JtYW5jZS9wZXJmb3JtYW5jZS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuYWRkT3JnYW5pemF0aW9uID0gYWRkT3JnYW5pemF0aW9uOwpleHBvcnRzLmRlbE9yZ2FuaXphdGlvbiA9IGRlbE9yZ2FuaXphdGlvbjsKZXhwb3J0cy5kb3dubG9hZFRlbXBsYXRlID0gZG93bmxvYWRUZW1wbGF0ZTsKZXhwb3J0cy5leHBvcnRXb3JkID0gZXhwb3J0V29yZDsKZXhwb3J0cy5nZXRPcmdhbml6YXRpb24gPSBnZXRPcmdhbml6YXRpb247CmV4cG9ydHMuaW1wb3J0V29yZCA9IGltcG9ydFdvcmQ7CmV4cG9ydHMubGlzdE9yZ2FuaXphdGlvbiA9IGxpc3RPcmdhbml6YXRpb247CmV4cG9ydHMudXBkYXRlT3JnYW5pemF0aW9uID0gdXBkYXRlT3JnYW5pemF0aW9uOwp2YXIgX3JlcXVlc3QgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvdXRpbHMvcmVxdWVzdCIpKTsKLy8g5p+l6K+i57uE57uH57up5pWI6ICD5qC45rWL6K+E6KGo5YiX6KGoCmZ1bmN0aW9uIGxpc3RPcmdhbml6YXRpb24ocXVlcnkpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9wZXJmb3JtYW5jZS9vcmdhbml6YXRpb24vbGlzdCcsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDmn6Xor6Lnu4Tnu4fnu6nmlYjogIPmoLjmtYvor4Tooajor6bnu4YKZnVuY3Rpb24gZ2V0T3JnYW5pemF0aW9uKGlkKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvcGVyZm9ybWFuY2Uvb3JnYW5pemF0aW9uLycgKyBpZCwKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQoKLy8g5paw5aKe57uE57uH57up5pWI6ICD5qC45rWL6K+E6KGoCmZ1bmN0aW9uIGFkZE9yZ2FuaXphdGlvbihkYXRhKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvcGVyZm9ybWFuY2Uvb3JnYW5pemF0aW9uJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDkv67mlLnnu4Tnu4fnu6nmlYjogIPmoLjmtYvor4TooagKZnVuY3Rpb24gdXBkYXRlT3JnYW5pemF0aW9uKGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9wZXJmb3JtYW5jZS9vcmdhbml6YXRpb24nLAogICAgbWV0aG9kOiAncHV0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5Yig6Zmk57uE57uH57up5pWI6ICD5qC45rWL6K+E6KGoCmZ1bmN0aW9uIGRlbE9yZ2FuaXphdGlvbihpZHMpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9wZXJmb3JtYW5jZS9vcmdhbml6YXRpb24vJyArIGlkcywKICAgIG1ldGhvZDogJ2RlbGV0ZScKICB9KTsKfQoKLy8g5a+85YWlV29yZOaVsOaNrgpmdW5jdGlvbiBpbXBvcnRXb3JkKGZpbGUpIHsKICB2YXIgZm9ybURhdGEgPSBuZXcgRm9ybURhdGEoKTsKICBmb3JtRGF0YS5hcHBlbmQoJ2ZpbGUnLCBmaWxlKTsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9wZXJmb3JtYW5jZS9vcmdhbml6YXRpb24vaW1wb3J0V29yZCcsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGZvcm1EYXRhLAogICAgaGVhZGVyczogewogICAgICAnQ29udGVudC1UeXBlJzogJ211bHRpcGFydC9mb3JtLWRhdGEnCiAgICB9CiAgfSk7Cn0KCi8vIOWvvOWHuldvcmTmlbDmja4KZnVuY3Rpb24gZXhwb3J0V29yZChpZHMpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9wZXJmb3JtYW5jZS9vcmdhbml6YXRpb24vZXhwb3J0V29yZCcsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGlkcywKICAgIHJlc3BvbnNlVHlwZTogJ2Jsb2InCiAgfSk7Cn0KCi8vIOS4i+i9veaooeadvwpmdW5jdGlvbiBkb3dubG9hZFRlbXBsYXRlKCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3BlcmZvcm1hbmNlL29yZ2FuaXphdGlvbi9kb3dubG9hZFRlbXBsYXRlJywKICAgIG1ldGhvZDogJ2dldCcsCiAgICByZXNwb25zZVR5cGU6ICdibG9iJwogIH0pOwp9"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listOrganization", "query", "request", "url", "method", "params", "getOrganization", "id", "addOrganization", "data", "updateOrganization", "delOrganization", "ids", "importWord", "file", "formData", "FormData", "append", "headers", "exportWord", "responseType", "downloadTemplate"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/performance/organization.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询组织绩效考核测评表列表\nexport function listOrganization(query) {\n  return request({\n    url: '/performance/organization/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询组织绩效考核测评表详细\nexport function getOrganization(id) {\n  return request({\n    url: '/performance/organization/' + id,\n    method: 'get'\n  })\n}\n\n// 新增组织绩效考核测评表\nexport function addOrganization(data) {\n  return request({\n    url: '/performance/organization',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改组织绩效考核测评表\nexport function updateOrganization(data) {\n  return request({\n    url: '/performance/organization',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除组织绩效考核测评表\nexport function delOrganization(ids) {\n  return request({\n    url: '/performance/organization/' + ids,\n    method: 'delete'\n  })\n}\n\n// 导入Word数据\nexport function importWord(file) {\n  const formData = new FormData()\n  formData.append('file', file)\n  return request({\n    url: '/performance/organization/importWord',\n    method: 'post',\n    data: formData,\n    headers: {\n      'Content-Type': 'multipart/form-data'\n    }\n  })\n}\n\n// 导出Word数据\nexport function exportWord(ids) {\n  return request({\n    url: '/performance/organization/exportWord',\n    method: 'post',\n    data: ids,\n    responseType: 'blob'\n  })\n}\n\n// 下载模板\nexport function downloadTemplate() {\n  return request({\n    url: '/performance/organization/downloadTemplate',\n    method: 'get',\n    responseType: 'blob'\n  })\n} "], "mappings": ";;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EACtC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,eAAeA,CAACC,EAAE,EAAE;EAClC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B,GAAGI,EAAE;IACtCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,eAAeA,CAACC,IAAI,EAAE;EACpC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,kBAAkBA,CAACD,IAAI,EAAE;EACvC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,eAAeA,CAACC,GAAG,EAAE;EACnC,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B,GAAGS,GAAG;IACvCR,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,UAAUA,CAACC,IAAI,EAAE;EAC/B,IAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC;EAC7B,OAAO,IAAAZ,gBAAO,EAAC;IACbC,GAAG,EAAE,sCAAsC;IAC3CC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEM,QAAQ;IACdG,OAAO,EAAE;MACP,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,UAAUA,CAACP,GAAG,EAAE;EAC9B,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,sCAAsC;IAC3CC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEG,GAAG;IACTQ,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,gBAAgBA,CAAA,EAAG;EACjC,OAAO,IAAAnB,gBAAO,EAAC;IACbC,GAAG,EAAE,4CAA4C;IACjDC,MAAM,EAAE,KAAK;IACbgB,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ", "ignoreList": []}]}