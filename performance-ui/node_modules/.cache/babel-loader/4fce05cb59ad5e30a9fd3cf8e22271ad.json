{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/modules/history.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/modules/history.js", "mtime": 1753510684095}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCIvVXNlcnMvbWF6aWhhby9EZXNrdG9wL2Rldi9wZXJmb3JtYW5jZS9wZXJmb3JtYW5jZS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKZXhwb3J0cy5nZXRMYXN0Q2hhbmdlSW5kZXggPSBnZXRMYXN0Q2hhbmdlSW5kZXg7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5wdXNoLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5yZWR1Y2UuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnNwbGljZS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LmtleXMuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC5leGVjLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAudGVzdC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLmVuZHMtd2l0aC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmNvbnN0cnVjdG9yLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IucmVkdWNlLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3Iuc29tZS5qcyIpOwp2YXIgX2NsYXNzQ2FsbENoZWNrMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiL1VzZXJzL21hemloYW8vRGVza3RvcC9kZXYvcGVyZm9ybWFuY2UvcGVyZm9ybWFuY2UtdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvY2xhc3NDYWxsQ2hlY2suanMiKSk7CnZhciBfY3JlYXRlQ2xhc3MyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIvVXNlcnMvbWF6aWhhby9EZXNrdG9wL2Rldi9wZXJmb3JtYW5jZS9wZXJmb3JtYW5jZS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9jcmVhdGVDbGFzcy5qcyIpKTsKdmFyIF9jYWxsU3VwZXIyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIvVXNlcnMvbWF6aWhhby9EZXNrdG9wL2Rldi9wZXJmb3JtYW5jZS9wZXJmb3JtYW5jZS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9jYWxsU3VwZXIuanMiKSk7CnZhciBfaW5oZXJpdHMyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIvVXNlcnMvbWF6aWhhby9EZXNrdG9wL2Rldi9wZXJmb3JtYW5jZS9wZXJmb3JtYW5jZS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbmhlcml0cy5qcyIpKTsKdmFyIF9kZWZpbmVQcm9wZXJ0eTIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi9Vc2Vycy9tYXppaGFvL0Rlc2t0b3AvZGV2L3BlcmZvcm1hbmNlL3BlcmZvcm1hbmNlLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2RlZmluZVByb3BlcnR5LmpzIikpOwp2YXIgX3BhcmNobWVudCA9IHJlcXVpcmUoInBhcmNobWVudCIpOwp2YXIgX21vZHVsZSA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi4vY29yZS9tb2R1bGUuanMiKSk7CnZhciBfcXVpbGwgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi4uL2NvcmUvcXVpbGwuanMiKSk7CnZhciBIaXN0b3J5ID0gZXhwb3J0cy5kZWZhdWx0ID0gLyojX19QVVJFX18qL2Z1bmN0aW9uIChfTW9kdWxlKSB7CiAgZnVuY3Rpb24gSGlzdG9yeShxdWlsbCwgb3B0aW9ucykgewogICAgdmFyIF90aGlzOwogICAgKDAsIF9jbGFzc0NhbGxDaGVjazIuZGVmYXVsdCkodGhpcywgSGlzdG9yeSk7CiAgICBfdGhpcyA9ICgwLCBfY2FsbFN1cGVyMi5kZWZhdWx0KSh0aGlzLCBIaXN0b3J5LCBbcXVpbGwsIG9wdGlvbnNdKTsKICAgICgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKF90aGlzLCAibGFzdFJlY29yZGVkIiwgMCk7CiAgICAoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KShfdGhpcywgImlnbm9yZUNoYW5nZSIsIGZhbHNlKTsKICAgICgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKF90aGlzLCAic3RhY2siLCB7CiAgICAgIHVuZG86IFtdLAogICAgICByZWRvOiBbXQogICAgfSk7CiAgICAoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KShfdGhpcywgImN1cnJlbnRSYW5nZSIsIG51bGwpOwogICAgX3RoaXMucXVpbGwub24oX3F1aWxsLmRlZmF1bHQuZXZlbnRzLkVESVRPUl9DSEFOR0UsIGZ1bmN0aW9uIChldmVudE5hbWUsIHZhbHVlLCBvbGRWYWx1ZSwgc291cmNlKSB7CiAgICAgIGlmIChldmVudE5hbWUgPT09IF9xdWlsbC5kZWZhdWx0LmV2ZW50cy5TRUxFQ1RJT05fQ0hBTkdFKSB7CiAgICAgICAgaWYgKHZhbHVlICYmIHNvdXJjZSAhPT0gX3F1aWxsLmRlZmF1bHQuc291cmNlcy5TSUxFTlQpIHsKICAgICAgICAgIF90aGlzLmN1cnJlbnRSYW5nZSA9IHZhbHVlOwogICAgICAgIH0KICAgICAgfSBlbHNlIGlmIChldmVudE5hbWUgPT09IF9xdWlsbC5kZWZhdWx0LmV2ZW50cy5URVhUX0NIQU5HRSkgewogICAgICAgIGlmICghX3RoaXMuaWdub3JlQ2hhbmdlKSB7CiAgICAgICAgICBpZiAoIV90aGlzLm9wdGlvbnMudXNlck9ubHkgfHwgc291cmNlID09PSBfcXVpbGwuZGVmYXVsdC5zb3VyY2VzLlVTRVIpIHsKICAgICAgICAgICAgX3RoaXMucmVjb3JkKHZhbHVlLCBvbGRWYWx1ZSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBfdGhpcy50cmFuc2Zvcm0odmFsdWUpOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgICBfdGhpcy5jdXJyZW50UmFuZ2UgPSB0cmFuc2Zvcm1SYW5nZShfdGhpcy5jdXJyZW50UmFuZ2UsIHZhbHVlKTsKICAgICAgfQogICAgfSk7CiAgICBfdGhpcy5xdWlsbC5rZXlib2FyZC5hZGRCaW5kaW5nKHsKICAgICAga2V5OiAneicsCiAgICAgIHNob3J0S2V5OiB0cnVlCiAgICB9LCBfdGhpcy51bmRvLmJpbmQoX3RoaXMpKTsKICAgIF90aGlzLnF1aWxsLmtleWJvYXJkLmFkZEJpbmRpbmcoewogICAgICBrZXk6IFsneicsICdaJ10sCiAgICAgIHNob3J0S2V5OiB0cnVlLAogICAgICBzaGlmdEtleTogdHJ1ZQogICAgfSwgX3RoaXMucmVkby5iaW5kKF90aGlzKSk7CiAgICBpZiAoL1dpbi9pLnRlc3QobmF2aWdhdG9yLnBsYXRmb3JtKSkgewogICAgICBfdGhpcy5xdWlsbC5rZXlib2FyZC5hZGRCaW5kaW5nKHsKICAgICAgICBrZXk6ICd5JywKICAgICAgICBzaG9ydEtleTogdHJ1ZQogICAgICB9LCBfdGhpcy5yZWRvLmJpbmQoX3RoaXMpKTsKICAgIH0KICAgIF90aGlzLnF1aWxsLnJvb3QuYWRkRXZlbnRMaXN0ZW5lcignYmVmb3JlaW5wdXQnLCBmdW5jdGlvbiAoZXZlbnQpIHsKICAgICAgaWYgKGV2ZW50LmlucHV0VHlwZSA9PT0gJ2hpc3RvcnlVbmRvJykgewogICAgICAgIF90aGlzLnVuZG8oKTsKICAgICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpOwogICAgICB9IGVsc2UgaWYgKGV2ZW50LmlucHV0VHlwZSA9PT0gJ2hpc3RvcnlSZWRvJykgewogICAgICAgIF90aGlzLnJlZG8oKTsKICAgICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpOwogICAgICB9CiAgICB9KTsKICAgIHJldHVybiBfdGhpczsKICB9CiAgKDAsIF9pbmhlcml0czIuZGVmYXVsdCkoSGlzdG9yeSwgX01vZHVsZSk7CiAgcmV0dXJuICgwLCBfY3JlYXRlQ2xhc3MyLmRlZmF1bHQpKEhpc3RvcnksIFt7CiAgICBrZXk6ICJjaGFuZ2UiLAogICAgdmFsdWU6IGZ1bmN0aW9uIGNoYW5nZShzb3VyY2UsIGRlc3QpIHsKICAgICAgaWYgKHRoaXMuc3RhY2tbc291cmNlXS5sZW5ndGggPT09IDApIHJldHVybjsKICAgICAgdmFyIGl0ZW0gPSB0aGlzLnN0YWNrW3NvdXJjZV0ucG9wKCk7CiAgICAgIGlmICghaXRlbSkgcmV0dXJuOwogICAgICB2YXIgYmFzZSA9IHRoaXMucXVpbGwuZ2V0Q29udGVudHMoKTsKICAgICAgdmFyIGludmVyc2VEZWx0YSA9IGl0ZW0uZGVsdGEuaW52ZXJ0KGJhc2UpOwogICAgICB0aGlzLnN0YWNrW2Rlc3RdLnB1c2goewogICAgICAgIGRlbHRhOiBpbnZlcnNlRGVsdGEsCiAgICAgICAgcmFuZ2U6IHRyYW5zZm9ybVJhbmdlKGl0ZW0ucmFuZ2UsIGludmVyc2VEZWx0YSkKICAgICAgfSk7CiAgICAgIHRoaXMubGFzdFJlY29yZGVkID0gMDsKICAgICAgdGhpcy5pZ25vcmVDaGFuZ2UgPSB0cnVlOwogICAgICB0aGlzLnF1aWxsLnVwZGF0ZUNvbnRlbnRzKGl0ZW0uZGVsdGEsIF9xdWlsbC5kZWZhdWx0LnNvdXJjZXMuVVNFUik7CiAgICAgIHRoaXMuaWdub3JlQ2hhbmdlID0gZmFsc2U7CiAgICAgIHRoaXMucmVzdG9yZVNlbGVjdGlvbihpdGVtKTsKICAgIH0KICB9LCB7CiAgICBrZXk6ICJjbGVhciIsCiAgICB2YWx1ZTogZnVuY3Rpb24gY2xlYXIoKSB7CiAgICAgIHRoaXMuc3RhY2sgPSB7CiAgICAgICAgdW5kbzogW10sCiAgICAgICAgcmVkbzogW10KICAgICAgfTsKICAgIH0KICB9LCB7CiAgICBrZXk6ICJjdXRvZmYiLAogICAgdmFsdWU6IGZ1bmN0aW9uIGN1dG9mZigpIHsKICAgICAgdGhpcy5sYXN0UmVjb3JkZWQgPSAwOwogICAgfQogIH0sIHsKICAgIGtleTogInJlY29yZCIsCiAgICB2YWx1ZTogZnVuY3Rpb24gcmVjb3JkKGNoYW5nZURlbHRhLCBvbGREZWx0YSkgewogICAgICBpZiAoY2hhbmdlRGVsdGEub3BzLmxlbmd0aCA9PT0gMCkgcmV0dXJuOwogICAgICB0aGlzLnN0YWNrLnJlZG8gPSBbXTsKICAgICAgdmFyIHVuZG9EZWx0YSA9IGNoYW5nZURlbHRhLmludmVydChvbGREZWx0YSk7CiAgICAgIHZhciB1bmRvUmFuZ2UgPSB0aGlzLmN1cnJlbnRSYW5nZTsKICAgICAgdmFyIHRpbWVzdGFtcCA9IERhdGUubm93KCk7CiAgICAgIGlmICgKICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvciBGaXggbWUgbGF0ZXIKICAgICAgdGhpcy5sYXN0UmVjb3JkZWQgKyB0aGlzLm9wdGlvbnMuZGVsYXkgPiB0aW1lc3RhbXAgJiYgdGhpcy5zdGFjay51bmRvLmxlbmd0aCA+IDApIHsKICAgICAgICB2YXIgaXRlbSA9IHRoaXMuc3RhY2sudW5kby5wb3AoKTsKICAgICAgICBpZiAoaXRlbSkgewogICAgICAgICAgdW5kb0RlbHRhID0gdW5kb0RlbHRhLmNvbXBvc2UoaXRlbS5kZWx0YSk7CiAgICAgICAgICB1bmRvUmFuZ2UgPSBpdGVtLnJhbmdlOwogICAgICAgIH0KICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmxhc3RSZWNvcmRlZCA9IHRpbWVzdGFtcDsKICAgICAgfQogICAgICBpZiAodW5kb0RlbHRhLmxlbmd0aCgpID09PSAwKSByZXR1cm47CiAgICAgIHRoaXMuc3RhY2sudW5kby5wdXNoKHsKICAgICAgICBkZWx0YTogdW5kb0RlbHRhLAogICAgICAgIHJhbmdlOiB1bmRvUmFuZ2UKICAgICAgfSk7CiAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3IgRml4IG1lIGxhdGVyCiAgICAgIGlmICh0aGlzLnN0YWNrLnVuZG8ubGVuZ3RoID4gdGhpcy5vcHRpb25zLm1heFN0YWNrKSB7CiAgICAgICAgdGhpcy5zdGFjay51bmRvLnNoaWZ0KCk7CiAgICAgIH0KICAgIH0KICB9LCB7CiAgICBrZXk6ICJyZWRvIiwKICAgIHZhbHVlOiBmdW5jdGlvbiByZWRvKCkgewogICAgICB0aGlzLmNoYW5nZSgncmVkbycsICd1bmRvJyk7CiAgICB9CiAgfSwgewogICAga2V5OiAidHJhbnNmb3JtIiwKICAgIHZhbHVlOiBmdW5jdGlvbiB0cmFuc2Zvcm0oZGVsdGEpIHsKICAgICAgdHJhbnNmb3JtU3RhY2sodGhpcy5zdGFjay51bmRvLCBkZWx0YSk7CiAgICAgIHRyYW5zZm9ybVN0YWNrKHRoaXMuc3RhY2sucmVkbywgZGVsdGEpOwogICAgfQogIH0sIHsKICAgIGtleTogInVuZG8iLAogICAgdmFsdWU6IGZ1bmN0aW9uIHVuZG8oKSB7CiAgICAgIHRoaXMuY2hhbmdlKCd1bmRvJywgJ3JlZG8nKTsKICAgIH0KICB9LCB7CiAgICBrZXk6ICJyZXN0b3JlU2VsZWN0aW9uIiwKICAgIHZhbHVlOiBmdW5jdGlvbiByZXN0b3JlU2VsZWN0aW9uKHN0YWNrSXRlbSkgewogICAgICBpZiAoc3RhY2tJdGVtLnJhbmdlKSB7CiAgICAgICAgdGhpcy5xdWlsbC5zZXRTZWxlY3Rpb24oc3RhY2tJdGVtLnJhbmdlLCBfcXVpbGwuZGVmYXVsdC5zb3VyY2VzLlVTRVIpOwogICAgICB9IGVsc2UgewogICAgICAgIHZhciBpbmRleCA9IGdldExhc3RDaGFuZ2VJbmRleCh0aGlzLnF1aWxsLnNjcm9sbCwgc3RhY2tJdGVtLmRlbHRhKTsKICAgICAgICB0aGlzLnF1aWxsLnNldFNlbGVjdGlvbihpbmRleCwgX3F1aWxsLmRlZmF1bHQuc291cmNlcy5VU0VSKTsKICAgICAgfQogICAgfQogIH1dKTsKfShfbW9kdWxlLmRlZmF1bHQpOwooMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KShIaXN0b3J5LCAiREVGQVVMVFMiLCB7CiAgZGVsYXk6IDEwMDAsCiAgbWF4U3RhY2s6IDEwMCwKICB1c2VyT25seTogZmFsc2UKfSk7CmZ1bmN0aW9uIHRyYW5zZm9ybVN0YWNrKHN0YWNrLCBkZWx0YSkgewogIHZhciByZW1vdGVEZWx0YSA9IGRlbHRhOwogIGZvciAodmFyIGkgPSBzdGFjay5sZW5ndGggLSAxOyBpID49IDA7IGkgLT0gMSkgewogICAgdmFyIG9sZEl0ZW0gPSBzdGFja1tpXTsKICAgIHN0YWNrW2ldID0gewogICAgICBkZWx0YTogcmVtb3RlRGVsdGEudHJhbnNmb3JtKG9sZEl0ZW0uZGVsdGEsIHRydWUpLAogICAgICByYW5nZTogb2xkSXRlbS5yYW5nZSAmJiB0cmFuc2Zvcm1SYW5nZShvbGRJdGVtLnJhbmdlLCByZW1vdGVEZWx0YSkKICAgIH07CiAgICByZW1vdGVEZWx0YSA9IG9sZEl0ZW0uZGVsdGEudHJhbnNmb3JtKHJlbW90ZURlbHRhKTsKICAgIGlmIChzdGFja1tpXS5kZWx0YS5sZW5ndGgoKSA9PT0gMCkgewogICAgICBzdGFjay5zcGxpY2UoaSwgMSk7CiAgICB9CiAgfQp9CmZ1bmN0aW9uIGVuZHNXaXRoTmV3bGluZUNoYW5nZShzY3JvbGwsIGRlbHRhKSB7CiAgdmFyIGxhc3RPcCA9IGRlbHRhLm9wc1tkZWx0YS5vcHMubGVuZ3RoIC0gMV07CiAgaWYgKGxhc3RPcCA9PSBudWxsKSByZXR1cm4gZmFsc2U7CiAgaWYgKGxhc3RPcC5pbnNlcnQgIT0gbnVsbCkgewogICAgcmV0dXJuIHR5cGVvZiBsYXN0T3AuaW5zZXJ0ID09PSAnc3RyaW5nJyAmJiBsYXN0T3AuaW5zZXJ0LmVuZHNXaXRoKCdcbicpOwogIH0KICBpZiAobGFzdE9wLmF0dHJpYnV0ZXMgIT0gbnVsbCkgewogICAgcmV0dXJuIE9iamVjdC5rZXlzKGxhc3RPcC5hdHRyaWJ1dGVzKS5zb21lKGZ1bmN0aW9uIChhdHRyKSB7CiAgICAgIHJldHVybiBzY3JvbGwucXVlcnkoYXR0ciwgX3BhcmNobWVudC5TY29wZS5CTE9DSykgIT0gbnVsbDsKICAgIH0pOwogIH0KICByZXR1cm4gZmFsc2U7Cn0KZnVuY3Rpb24gZ2V0TGFzdENoYW5nZUluZGV4KHNjcm9sbCwgZGVsdGEpIHsKICB2YXIgZGVsZXRlTGVuZ3RoID0gZGVsdGEucmVkdWNlKGZ1bmN0aW9uIChsZW5ndGgsIG9wKSB7CiAgICByZXR1cm4gbGVuZ3RoICsgKG9wLmRlbGV0ZSB8fCAwKTsKICB9LCAwKTsKICB2YXIgY2hhbmdlSW5kZXggPSBkZWx0YS5sZW5ndGgoKSAtIGRlbGV0ZUxlbmd0aDsKICBpZiAoZW5kc1dpdGhOZXdsaW5lQ2hhbmdlKHNjcm9sbCwgZGVsdGEpKSB7CiAgICBjaGFuZ2VJbmRleCAtPSAxOwogIH0KICByZXR1cm4gY2hhbmdlSW5kZXg7Cn0KZnVuY3Rpb24gdHJhbnNmb3JtUmFuZ2UocmFuZ2UsIGRlbHRhKSB7CiAgaWYgKCFyYW5nZSkgcmV0dXJuIHJhbmdlOwogIHZhciBzdGFydCA9IGRlbHRhLnRyYW5zZm9ybVBvc2l0aW9uKHJhbmdlLmluZGV4KTsKICB2YXIgZW5kID0gZGVsdGEudHJhbnNmb3JtUG9zaXRpb24ocmFuZ2UuaW5kZXggKyByYW5nZS5sZW5ndGgpOwogIHJldHVybiB7CiAgICBpbmRleDogc3RhcnQsCiAgICBsZW5ndGg6IGVuZCAtIHN0YXJ0CiAgfTsKfQ=="}, {"version": 3, "names": ["_parchment", "require", "_module", "_interopRequireDefault", "_quill", "History", "exports", "default", "_Module", "quill", "options", "_this", "_classCallCheck2", "_callSuper2", "_defineProperty2", "undo", "redo", "on", "<PERSON><PERSON><PERSON>", "events", "EDITOR_CHANGE", "eventName", "value", "oldValue", "source", "SELECTION_CHANGE", "sources", "SILENT", "currentRange", "TEXT_CHANGE", "ignoreChange", "userOnly", "USER", "record", "transform", "transformRange", "keyboard", "addBinding", "key", "<PERSON><PERSON><PERSON>", "bind", "shift<PERSON>ey", "test", "navigator", "platform", "root", "addEventListener", "event", "inputType", "preventDefault", "_inherits2", "_createClass2", "change", "dest", "stack", "length", "item", "pop", "base", "getContents", "inverse<PERSON><PERSON><PERSON>", "delta", "invert", "push", "range", "lastRecorded", "updateContents", "restoreSelection", "clear", "cutoff", "change<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "ops", "undoDelta", "undoRange", "timestamp", "Date", "now", "delay", "compose", "maxStack", "shift", "transformStack", "stackItem", "setSelection", "index", "getLastChangeIndex", "scroll", "<PERSON><PERSON><PERSON>", "remoteDelta", "i", "oldItem", "splice", "endsWithNewlineChange", "lastOp", "insert", "endsWith", "attributes", "Object", "keys", "some", "attr", "query", "<PERSON><PERSON>", "BLOCK", "deleteLength", "reduce", "op", "delete", "changeIndex", "start", "transformPosition", "end"], "sources": ["../../src/modules/history.ts"], "sourcesContent": ["import { Scope } from 'parchment';\nimport type Delta from 'quill-delta';\nimport Module from '../core/module.js';\nimport Quill from '../core/quill.js';\nimport type Scroll from '../blots/scroll.js';\nimport type { Range } from '../core/selection.js';\n\nexport interface HistoryOptions {\n  userOnly: boolean;\n  delay: number;\n  maxStack: number;\n}\n\nexport interface StackItem {\n  delta: Delta;\n  range: Range | null;\n}\n\ninterface Stack {\n  undo: StackItem[];\n  redo: StackItem[];\n}\n\nclass History extends Module<HistoryOptions> {\n  static DEFAULTS: HistoryOptions = {\n    delay: 1000,\n    maxStack: 100,\n    userOnly: false,\n  };\n\n  lastRecorded = 0;\n  ignoreChange = false;\n  stack: Stack = { undo: [], redo: [] };\n  currentRange: Range | null = null;\n\n  constructor(quill: Quill, options: Partial<HistoryOptions>) {\n    super(quill, options);\n    this.quill.on(\n      Quill.events.EDITOR_CHANGE,\n      (eventName, value, oldValue, source) => {\n        if (eventName === Quill.events.SELECTION_CHANGE) {\n          if (value && source !== Quill.sources.SILENT) {\n            this.currentRange = value;\n          }\n        } else if (eventName === Quill.events.TEXT_CHANGE) {\n          if (!this.ignoreChange) {\n            if (!this.options.userOnly || source === Quill.sources.USER) {\n              this.record(value, oldValue);\n            } else {\n              this.transform(value);\n            }\n          }\n\n          this.currentRange = transformRange(this.currentRange, value);\n        }\n      },\n    );\n\n    this.quill.keyboard.addBinding(\n      { key: 'z', shortKey: true },\n      this.undo.bind(this),\n    );\n    this.quill.keyboard.addBinding(\n      { key: ['z', 'Z'], shortKey: true, shiftKey: true },\n      this.redo.bind(this),\n    );\n    if (/Win/i.test(navigator.platform)) {\n      this.quill.keyboard.addBinding(\n        { key: 'y', shortKey: true },\n        this.redo.bind(this),\n      );\n    }\n\n    this.quill.root.addEventListener('beforeinput', (event) => {\n      if (event.inputType === 'historyUndo') {\n        this.undo();\n        event.preventDefault();\n      } else if (event.inputType === 'historyRedo') {\n        this.redo();\n        event.preventDefault();\n      }\n    });\n  }\n\n  change(source: 'undo' | 'redo', dest: 'redo' | 'undo') {\n    if (this.stack[source].length === 0) return;\n    const item = this.stack[source].pop();\n    if (!item) return;\n    const base = this.quill.getContents();\n    const inverseDelta = item.delta.invert(base);\n    this.stack[dest].push({\n      delta: inverseDelta,\n      range: transformRange(item.range, inverseDelta),\n    });\n    this.lastRecorded = 0;\n    this.ignoreChange = true;\n    this.quill.updateContents(item.delta, Quill.sources.USER);\n    this.ignoreChange = false;\n\n    this.restoreSelection(item);\n  }\n\n  clear() {\n    this.stack = { undo: [], redo: [] };\n  }\n\n  cutoff() {\n    this.lastRecorded = 0;\n  }\n\n  record(changeDelta: Delta, oldDelta: Delta) {\n    if (changeDelta.ops.length === 0) return;\n    this.stack.redo = [];\n    let undoDelta = changeDelta.invert(oldDelta);\n    let undoRange = this.currentRange;\n    const timestamp = Date.now();\n    if (\n      // @ts-expect-error Fix me later\n      this.lastRecorded + this.options.delay > timestamp &&\n      this.stack.undo.length > 0\n    ) {\n      const item = this.stack.undo.pop();\n      if (item) {\n        undoDelta = undoDelta.compose(item.delta);\n        undoRange = item.range;\n      }\n    } else {\n      this.lastRecorded = timestamp;\n    }\n    if (undoDelta.length() === 0) return;\n    this.stack.undo.push({ delta: undoDelta, range: undoRange });\n    // @ts-expect-error Fix me later\n    if (this.stack.undo.length > this.options.maxStack) {\n      this.stack.undo.shift();\n    }\n  }\n\n  redo() {\n    this.change('redo', 'undo');\n  }\n\n  transform(delta: Delta) {\n    transformStack(this.stack.undo, delta);\n    transformStack(this.stack.redo, delta);\n  }\n\n  undo() {\n    this.change('undo', 'redo');\n  }\n\n  protected restoreSelection(stackItem: StackItem) {\n    if (stackItem.range) {\n      this.quill.setSelection(stackItem.range, Quill.sources.USER);\n    } else {\n      const index = getLastChangeIndex(this.quill.scroll, stackItem.delta);\n      this.quill.setSelection(index, Quill.sources.USER);\n    }\n  }\n}\n\nfunction transformStack(stack: StackItem[], delta: Delta) {\n  let remoteDelta = delta;\n  for (let i = stack.length - 1; i >= 0; i -= 1) {\n    const oldItem = stack[i];\n    stack[i] = {\n      delta: remoteDelta.transform(oldItem.delta, true),\n      range: oldItem.range && transformRange(oldItem.range, remoteDelta),\n    };\n    remoteDelta = oldItem.delta.transform(remoteDelta);\n    if (stack[i].delta.length() === 0) {\n      stack.splice(i, 1);\n    }\n  }\n}\n\nfunction endsWithNewlineChange(scroll: Scroll, delta: Delta) {\n  const lastOp = delta.ops[delta.ops.length - 1];\n  if (lastOp == null) return false;\n  if (lastOp.insert != null) {\n    return typeof lastOp.insert === 'string' && lastOp.insert.endsWith('\\n');\n  }\n  if (lastOp.attributes != null) {\n    return Object.keys(lastOp.attributes).some((attr) => {\n      return scroll.query(attr, Scope.BLOCK) != null;\n    });\n  }\n  return false;\n}\n\nfunction getLastChangeIndex(scroll: Scroll, delta: Delta) {\n  const deleteLength = delta.reduce((length, op) => {\n    return length + (op.delete || 0);\n  }, 0);\n  let changeIndex = delta.length() - deleteLength;\n  if (endsWithNewlineChange(scroll, delta)) {\n    changeIndex -= 1;\n  }\n  return changeIndex;\n}\n\nfunction transformRange(range: Range | null, delta: Delta) {\n  if (!range) return range;\n  const start = delta.transformPosition(range.index);\n  const end = delta.transformPosition(range.index + range.length);\n  return { index: start, length: end - start };\n}\n\nexport { History as default, getLastChangeIndex };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAEA,IAAAC,OAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,MAAA,GAAAD,sBAAA,CAAAF,OAAA;AAAoC,IAoB9BI,OAAO,GAAAC,OAAA,CAAAC,OAAA,0BAAAC,OAAA;EAYX,SAAAH,QAAYI,KAAY,EAAEC,OAAgC,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAAL,OAAA,QAAAF,OAAA;IAC1DM,KAAA,OAAAE,WAAA,CAAAN,OAAA,QAAAF,OAAA,GAAMI,KAAK,EAAEC,OAAO;IAAC,IAAAI,gBAAA,CAAAP,OAAA,EAAAI,KAAA,kBANR,CAAC;IAAA,IAAAG,gBAAA,CAAAP,OAAA,EAAAI,KAAA,kBACD,KAAK;IAAA,IAAAG,gBAAA,CAAAP,OAAA,EAAAI,KAAA,WACL;MAAEI,IAAI,EAAE,EAAE;MAAEC,IAAI,EAAE;IAAG,CAAC;IAAA,IAAAF,gBAAA,CAAAP,OAAA,EAAAI,KAAA,kBACR,IAAI;IAI/BA,KAAA,CAAKF,KAAK,CAACQ,EAAE,CACXC,cAAK,CAACC,MAAM,CAACC,aAAa,EAC1B,UAACC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAK;MACtC,IAAIH,SAAS,KAAKH,cAAK,CAACC,MAAM,CAACM,gBAAgB,EAAE;QAC/C,IAAIH,KAAK,IAAIE,MAAM,KAAKN,cAAK,CAACQ,OAAO,CAACC,MAAM,EAAE;UAC5ChB,KAAA,CAAKiB,YAAY,GAAGN,KAAK;QAC3B;MACF,CAAC,MAAM,IAAID,SAAS,KAAKH,cAAK,CAACC,MAAM,CAACU,WAAW,EAAE;QACjD,IAAI,CAAClB,KAAA,CAAKmB,YAAY,EAAE;UACtB,IAAI,CAACnB,KAAA,CAAKD,OAAO,CAACqB,QAAQ,IAAIP,MAAM,KAAKN,cAAK,CAACQ,OAAO,CAACM,IAAI,EAAE;YAC3DrB,KAAA,CAAKsB,MAAM,CAACX,KAAK,EAAEC,QAAQ,CAAC;UAC9B,CAAC,MAAM;YACLZ,KAAA,CAAKuB,SAAS,CAACZ,KAAK,CAAC;UACvB;QACF;QAEAX,KAAA,CAAKiB,YAAY,GAAGO,cAAc,CAACxB,KAAA,CAAKiB,YAAY,EAAEN,KAAK,CAAC;MAC9D;IACF,CACF,CAAC;IAEDX,KAAA,CAAKF,KAAK,CAAC2B,QAAQ,CAACC,UAAU,CAC5B;MAAEC,GAAG,EAAE,GAAG;MAAEC,QAAQ,EAAE;IAAK,CAAC,EAC5B5B,KAAA,CAAKI,IAAI,CAACyB,IAAI,CAAA7B,KAAK,CACrB,CAAC;IACDA,KAAA,CAAKF,KAAK,CAAC2B,QAAQ,CAACC,UAAU,CAC5B;MAAEC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;MAAEC,QAAQ,EAAE,IAAI;MAAEE,QAAQ,EAAE;IAAK,CAAC,EACnD9B,KAAA,CAAKK,IAAI,CAACwB,IAAI,CAAA7B,KAAK,CACrB,CAAC;IACD,IAAI,MAAM,CAAC+B,IAAI,CAACC,SAAS,CAACC,QAAQ,CAAC,EAAE;MACnCjC,KAAA,CAAKF,KAAK,CAAC2B,QAAQ,CAACC,UAAU,CAC5B;QAAEC,GAAG,EAAE,GAAG;QAAEC,QAAQ,EAAE;MAAK,CAAC,EAC5B5B,KAAA,CAAKK,IAAI,CAACwB,IAAI,CAAA7B,KAAK,CACrB,CAAC;IACH;IAEAA,KAAA,CAAKF,KAAK,CAACoC,IAAI,CAACC,gBAAgB,CAAC,aAAa,EAAG,UAAAC,KAAK,EAAK;MACzD,IAAIA,KAAK,CAACC,SAAS,KAAK,aAAa,EAAE;QACrCrC,KAAA,CAAKI,IAAI,CAAC,CAAC;QACXgC,KAAK,CAACE,cAAc,CAAC,CAAC;MACxB,CAAC,MAAM,IAAIF,KAAK,CAACC,SAAS,KAAK,aAAa,EAAE;QAC5CrC,KAAA,CAAKK,IAAI,CAAC,CAAC;QACX+B,KAAK,CAACE,cAAc,CAAC,CAAC;MACxB;IACF,CAAC,CAAC;IAAA,OAAAtC,KAAA;EACJ;EAAA,IAAAuC,UAAA,CAAA3C,OAAA,EAAAF,OAAA,EAAAG,OAAA;EAAA,WAAA2C,aAAA,CAAA5C,OAAA,EAAAF,OAAA;IAAAiC,GAAA;IAAAhB,KAAA,EAEA,SAAA8B,MAAMA,CAAC5B,MAAuB,EAAE6B,IAAqB,EAAE;MACrD,IAAI,IAAI,CAACC,KAAK,CAAC9B,MAAM,CAAC,CAAC+B,MAAM,KAAK,CAAC,EAAE;MACrC,IAAMC,IAAI,GAAG,IAAI,CAACF,KAAK,CAAC9B,MAAM,CAAC,CAACiC,GAAG,CAAC,CAAC;MACrC,IAAI,CAACD,IAAI,EAAE;MACX,IAAME,IAAI,GAAG,IAAI,CAACjD,KAAK,CAACkD,WAAW,CAAC,CAAC;MACrC,IAAMC,YAAY,GAAGJ,IAAI,CAACK,KAAK,CAACC,MAAM,CAACJ,IAAI,CAAC;MAC5C,IAAI,CAACJ,KAAK,CAACD,IAAI,CAAC,CAACU,IAAI,CAAC;QACpBF,KAAK,EAAED,YAAY;QACnBI,KAAK,EAAE7B,cAAc,CAACqB,IAAI,CAACQ,KAAK,EAAEJ,YAAY;MAChD,CAAC,CAAC;MACF,IAAI,CAACK,YAAY,GAAG,CAAC;MACrB,IAAI,CAACnC,YAAY,GAAG,IAAI;MACxB,IAAI,CAACrB,KAAK,CAACyD,cAAc,CAACV,IAAI,CAACK,KAAK,EAAE3C,cAAK,CAACQ,OAAO,CAACM,IAAI,CAAC;MACzD,IAAI,CAACF,YAAY,GAAG,KAAK;MAEzB,IAAI,CAACqC,gBAAgB,CAACX,IAAI,CAAC;IAC7B;EAAA;IAAAlB,GAAA;IAAAhB,KAAA,EAEA,SAAA8C,KAAKA,CAAA,EAAG;MACN,IAAI,CAACd,KAAK,GAAG;QAAEvC,IAAI,EAAE,EAAE;QAAEC,IAAI,EAAE;MAAG,CAAC;IACrC;EAAA;IAAAsB,GAAA;IAAAhB,KAAA,EAEA,SAAA+C,MAAMA,CAAA,EAAG;MACP,IAAI,CAACJ,YAAY,GAAG,CAAC;IACvB;EAAA;IAAA3B,GAAA;IAAAhB,KAAA,EAEA,SAAAW,MAAMA,CAACqC,WAAkB,EAAEC,QAAe,EAAE;MAC1C,IAAID,WAAW,CAACE,GAAG,CAACjB,MAAM,KAAK,CAAC,EAAE;MAClC,IAAI,CAACD,KAAK,CAACtC,IAAI,GAAG,EAAE;MACpB,IAAIyD,SAAS,GAAGH,WAAW,CAACR,MAAM,CAACS,QAAQ,CAAC;MAC5C,IAAIG,SAAS,GAAG,IAAI,CAAC9C,YAAY;MACjC,IAAM+C,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;MAC5B;MACE;MACA,IAAI,CAACZ,YAAY,GAAG,IAAI,CAACvD,OAAO,CAACoE,KAAK,GAAGH,SAAS,IAClD,IAAI,CAACrB,KAAK,CAACvC,IAAI,CAACwC,MAAM,GAAG,CAAC,EAC1B;QACA,IAAMC,IAAI,GAAG,IAAI,CAACF,KAAK,CAACvC,IAAI,CAAC0C,GAAG,CAAC,CAAC;QAClC,IAAID,IAAI,EAAE;UACRiB,SAAS,GAAGA,SAAS,CAACM,OAAO,CAACvB,IAAI,CAACK,KAAK,CAAC;UACzCa,SAAS,GAAGlB,IAAI,CAACQ,KAAK;QACxB;MACF,CAAC,MAAM;QACL,IAAI,CAACC,YAAY,GAAGU,SAAS;MAC/B;MACA,IAAIF,SAAS,CAAClB,MAAM,CAAC,CAAC,KAAK,CAAC,EAAE;MAC9B,IAAI,CAACD,KAAK,CAACvC,IAAI,CAACgD,IAAI,CAAC;QAAEF,KAAK,EAAEY,SAAS;QAAET,KAAK,EAAEU;MAAU,CAAC,CAAC;MAC5D;MACA,IAAI,IAAI,CAACpB,KAAK,CAACvC,IAAI,CAACwC,MAAM,GAAG,IAAI,CAAC7C,OAAO,CAACsE,QAAQ,EAAE;QAClD,IAAI,CAAC1B,KAAK,CAACvC,IAAI,CAACkE,KAAK,CAAC,CAAC;MACzB;IACF;EAAA;IAAA3C,GAAA;IAAAhB,KAAA,EAEA,SAAAN,IAAIA,CAAA,EAAG;MACL,IAAI,CAACoC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC;IAC7B;EAAA;IAAAd,GAAA;IAAAhB,KAAA,EAEA,SAAAY,SAASA,CAAC2B,KAAY,EAAE;MACtBqB,cAAc,CAAC,IAAI,CAAC5B,KAAK,CAACvC,IAAI,EAAE8C,KAAK,CAAC;MACtCqB,cAAc,CAAC,IAAI,CAAC5B,KAAK,CAACtC,IAAI,EAAE6C,KAAK,CAAC;IACxC;EAAA;IAAAvB,GAAA;IAAAhB,KAAA,EAEA,SAAAP,IAAIA,CAAA,EAAG;MACL,IAAI,CAACqC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC;IAC7B;EAAA;IAAAd,GAAA;IAAAhB,KAAA,EAEU,SAAA6C,gBAAgBA,CAACgB,SAAoB,EAAE;MAC/C,IAAIA,SAAS,CAACnB,KAAK,EAAE;QACnB,IAAI,CAACvD,KAAK,CAAC2E,YAAY,CAACD,SAAS,CAACnB,KAAK,EAAE9C,cAAK,CAACQ,OAAO,CAACM,IAAI,CAAC;MAC9D,CAAC,MAAM;QACL,IAAMqD,KAAK,GAAGC,kBAAkB,CAAC,IAAI,CAAC7E,KAAK,CAAC8E,MAAM,EAAEJ,SAAS,CAACtB,KAAK,CAAC;QACpE,IAAI,CAACpD,KAAK,CAAC2E,YAAY,CAACC,KAAK,EAAEnE,cAAK,CAACQ,OAAO,CAACM,IAAI,CAAC;MACpD;IACF;EAAA;AAAA,EAtIoBwD,eAAM;AAAA,IAAA1E,gBAAA,CAAAP,OAAA,EAAtBF,OAAO,cACuB;EAChCyE,KAAK,EAAE,IAAI;EACXE,QAAQ,EAAE,GAAG;EACbjD,QAAQ,EAAE;AACZ,CAAC;AAoIH,SAASmD,cAAcA,CAAC5B,KAAkB,EAAEO,KAAY,EAAE;EACxD,IAAI4B,WAAW,GAAG5B,KAAK;EACvB,KAAK,IAAI6B,CAAC,GAAGpC,KAAK,CAACC,MAAM,GAAG,CAAC,EAAEmC,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;IAC7C,IAAMC,OAAO,GAAGrC,KAAK,CAACoC,CAAC,CAAC;IACxBpC,KAAK,CAACoC,CAAC,CAAC,GAAG;MACT7B,KAAK,EAAE4B,WAAW,CAACvD,SAAS,CAACyD,OAAO,CAAC9B,KAAK,EAAE,IAAI,CAAC;MACjDG,KAAK,EAAE2B,OAAO,CAAC3B,KAAK,IAAI7B,cAAc,CAACwD,OAAO,CAAC3B,KAAK,EAAEyB,WAAW;IACnE,CAAC;IACDA,WAAW,GAAGE,OAAO,CAAC9B,KAAK,CAAC3B,SAAS,CAACuD,WAAW,CAAC;IAClD,IAAInC,KAAK,CAACoC,CAAC,CAAC,CAAC7B,KAAK,CAACN,MAAM,CAAC,CAAC,KAAK,CAAC,EAAE;MACjCD,KAAK,CAACsC,MAAM,CAACF,CAAC,EAAE,CAAC,CAAC;IACpB;EACF;AACF;AAEA,SAASG,qBAAqBA,CAACN,MAAc,EAAE1B,KAAY,EAAE;EAC3D,IAAMiC,MAAM,GAAGjC,KAAK,CAACW,GAAG,CAACX,KAAK,CAACW,GAAG,CAACjB,MAAM,GAAG,CAAC,CAAC;EAC9C,IAAIuC,MAAM,IAAI,IAAI,EAAE,OAAO,KAAK;EAChC,IAAIA,MAAM,CAACC,MAAM,IAAI,IAAI,EAAE;IACzB,OAAO,OAAOD,MAAM,CAACC,MAAM,KAAK,QAAQ,IAAID,MAAM,CAACC,MAAM,CAACC,QAAQ,CAAC,IAAI,CAAC;EAC1E;EACA,IAAIF,MAAM,CAACG,UAAU,IAAI,IAAI,EAAE;IAC7B,OAAOC,MAAM,CAACC,IAAI,CAACL,MAAM,CAACG,UAAU,CAAC,CAACG,IAAI,CAAE,UAAAC,IAAI,EAAK;MACnD,OAAOd,MAAM,CAACe,KAAK,CAACD,IAAI,EAAEE,gBAAK,CAACC,KAAK,CAAC,IAAI,IAAI;IAChD,CAAC,CAAC;EACJ;EACA,OAAO,KAAK;AACd;AAEA,SAASlB,kBAAkBA,CAACC,MAAc,EAAE1B,KAAY,EAAE;EACxD,IAAM4C,YAAY,GAAG5C,KAAK,CAAC6C,MAAM,CAAC,UAACnD,MAAM,EAAEoD,EAAE,EAAK;IAChD,OAAOpD,MAAM,IAAIoD,EAAE,CAACC,MAAM,IAAI,CAAC,CAAC;EAClC,CAAC,EAAE,CAAC,CAAC;EACL,IAAIC,WAAW,GAAGhD,KAAK,CAACN,MAAM,CAAC,CAAC,GAAGkD,YAAY;EAC/C,IAAIZ,qBAAqB,CAACN,MAAM,EAAE1B,KAAK,CAAC,EAAE;IACxCgD,WAAW,IAAI,CAAC;EAClB;EACA,OAAOA,WAAW;AACpB;AAEA,SAAS1E,cAAcA,CAAC6B,KAAmB,EAAEH,KAAY,EAAE;EACzD,IAAI,CAACG,KAAK,EAAE,OAAOA,KAAK;EACxB,IAAM8C,KAAK,GAAGjD,KAAK,CAACkD,iBAAiB,CAAC/C,KAAK,CAACqB,KAAK,CAAC;EAClD,IAAM2B,GAAG,GAAGnD,KAAK,CAACkD,iBAAiB,CAAC/C,KAAK,CAACqB,KAAK,GAAGrB,KAAK,CAACT,MAAM,CAAC;EAC/D,OAAO;IAAE8B,KAAK,EAAEyB,KAAK;IAAEvD,MAAM,EAAEyD,GAAG,GAAGF;EAAM,CAAC;AAC9C", "ignoreList": []}]}