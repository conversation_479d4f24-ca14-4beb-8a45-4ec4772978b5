{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/layout/components/Navbar.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/layout/components/Navbar.vue", "mtime": 1753510684529}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_vuex", "require", "_Breadcrumb", "_interopRequireDefault", "_TopNav", "_<PERSON>er", "_Screenfull", "_SizeSelect", "_HeaderSearch", "_Git", "_Doc", "emits", "components", "Breadcrumb", "TopNav", "<PERSON><PERSON>", "Screenfull", "SizeSelect", "Search", "RuoYiGit", "RuoYiDoc", "computed", "_objectSpread2", "default", "mapGetters", "setting", "get", "$store", "state", "settings", "showSettings", "topNav", "methods", "toggleSideBar", "dispatch", "setLayout", "event", "$emit", "logout", "_this", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "location", "href", "catch"], "sources": ["src/layout/components/Navbar.vue"], "sourcesContent": ["<template>\n  <div class=\"navbar\">\n    <hamburger id=\"hamburger-container\" :is-active=\"sidebar.opened\" class=\"hamburger-container\" @toggleClick=\"toggleSideBar\" />\n\n    <breadcrumb v-if=\"!topNav\" id=\"breadcrumb-container\" class=\"breadcrumb-container\" />\n    <top-nav v-if=\"topNav\" id=\"topmenu-container\" class=\"topmenu-container\" />\n\n    <div class=\"right-menu\">\n      <template v-if=\"device!=='mobile'\">\n        <!-- 移除搜索、github、帮助、全屏、布局大小等按钮，只保留用户头像和设置按钮 -->\n      </template>\n      <el-dropdown class=\"avatar-container right-menu-item hover-effect\" trigger=\"hover\">\n        <div class=\"avatar-wrapper\">\n          <img :src=\"avatar\" class=\"user-avatar\">\n          <span class=\"user-nickname\"> {{ nickName }} </span>\n        </div>\n        <el-dropdown-menu slot=\"dropdown\">\n          <router-link to=\"/user/profile\">\n            <el-dropdown-item>个人中心</el-dropdown-item>\n          </router-link>\n          <el-dropdown-item divided @click.native=\"logout\">\n            <span>退出登录</span>\n          </el-dropdown-item>\n        </el-dropdown-menu>\n      </el-dropdown>\n      <div class=\"right-menu-item hover-effect setting\" @click=\"setLayout\" v-if=\"setting\">\n        <svg-icon icon-class=\"more-up\" />\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport Breadcrumb from '@/components/Breadcrumb'\nimport TopNav from '@/components/TopNav'\nimport Hamburger from '@/components/Hamburger'\nimport Screenfull from '@/components/Screenfull'\nimport SizeSelect from '@/components/SizeSelect'\nimport Search from '@/components/HeaderSearch'\nimport RuoYiGit from '@/components/RuoYi/Git'\nimport RuoYiDoc from '@/components/RuoYi/Doc'\n\nexport default {\n  emits: ['setLayout'],\n  components: {\n    Breadcrumb,\n    TopNav,\n    Hamburger,\n    Screenfull,\n    SizeSelect,\n    Search,\n    RuoYiGit,\n    RuoYiDoc\n  },\n  computed: {\n    ...mapGetters([\n      'sidebar',\n      'avatar',\n      'device',\n      'nickName'\n    ]),\n    setting: {\n      get() {\n        return this.$store.state.settings.showSettings\n      }\n    },\n    topNav: {\n      get() {\n        return this.$store.state.settings.topNav\n      }\n    }\n  },\n  methods: {\n    toggleSideBar() {\n      this.$store.dispatch('app/toggleSideBar')\n    },\n    setLayout(event) {\n      this.$emit('setLayout')\n    },\n    logout() {\n      this.$confirm('确定注销并退出系统吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.$store.dispatch('LogOut').then(() => {\n          location.href = '/index'\n        })\n      }).catch(() => {})\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.navbar {\n  height: 50px;\n  overflow: hidden;\n  position: relative;\n  background: #fff;\n  box-shadow: 0 1px 4px rgba(0,21,41,.08);\n\n  .hamburger-container {\n    line-height: 46px;\n    height: 100%;\n    float: left;\n    cursor: pointer;\n    transition: background .3s;\n    -webkit-tap-highlight-color:transparent;\n\n    &:hover {\n      background: rgba(0, 0, 0, .025)\n    }\n  }\n\n  .breadcrumb-container {\n    float: left;\n  }\n\n  .topmenu-container {\n    position: absolute;\n    left: 50px;\n  }\n\n  .errLog-container {\n    display: inline-block;\n    vertical-align: top;\n  }\n\n  .right-menu {\n    float: right;\n    height: 100%;\n    line-height: 50px;\n\n    &:focus {\n      outline: none;\n    }\n\n    .right-menu-item {\n      display: inline-block;\n      padding: 0 8px;\n      height: 100%;\n      font-size: 18px;\n      color: #5a5e66;\n      vertical-align: text-bottom;\n\n      &.hover-effect {\n        cursor: pointer;\n        transition: background .3s;\n\n        &:hover {\n          background: rgba(0, 0, 0, .025)\n        }\n      }\n    }\n\n    .avatar-container {\n      margin-right: 0px;\n      padding-right: 0px;\n\n      .avatar-wrapper {\n        margin-top: 10px;\n        position: relative;\n\n        .user-avatar {\n          cursor: pointer;\n          width: 30px;\n          height: 30px;\n          border-radius: 50%;\n        }\n\n        .user-nickname{\n          position: relative;\n          bottom: 10px;\n          font-size: 14px;\n          font-weight: bold;\n        }\n\n        .el-icon-caret-bottom {\n          cursor: pointer;\n          position: absolute;\n          right: -20px;\n          top: 25px;\n          font-size: 12px;\n        }\n      }\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;AAiCA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,WAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,OAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,UAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,WAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,WAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,aAAA,GAAAL,sBAAA,CAAAF,OAAA;AACA,IAAAQ,IAAA,GAAAN,sBAAA,CAAAF,OAAA;AACA,IAAAS,IAAA,GAAAP,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAU,KAAA;EACAC,UAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,MAAA,EAAAA,eAAA;IACAC,SAAA,EAAAA,kBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,MAAA,EAAAA,qBAAA;IACAC,QAAA,EAAAA,YAAA;IACAC,QAAA,EAAAA;EACA;EACAC,QAAA,MAAAC,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA,IAAAC,gBAAA,GACA,WACA,UACA,UACA,WACA;IACAC,OAAA;MACAC,GAAA,WAAAA,IAAA;QACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,YAAA;MACA;IACA;IACAC,MAAA;MACAL,GAAA,WAAAA,IAAA;QACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAE,MAAA;MACA;IACA;EAAA,EACA;EACAC,OAAA;IACAC,aAAA,WAAAA,cAAA;MACA,KAAAN,MAAA,CAAAO,QAAA;IACA;IACAC,SAAA,WAAAA,UAAAC,KAAA;MACA,KAAAC,KAAA;IACA;IACAC,MAAA,WAAAA,OAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA;QACAL,KAAA,CAAAZ,MAAA,CAAAO,QAAA,WAAAU,IAAA;UACAC,QAAA,CAAAC,IAAA;QACA;MACA,GAAAC,KAAA;IACA;EACA;AACA", "ignoreList": []}]}