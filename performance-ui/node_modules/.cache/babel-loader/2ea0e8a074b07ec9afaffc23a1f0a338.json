{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/plugins/index.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/plugins/index.js", "mtime": 1753510684530}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCIvVXNlcnMvbWF6aWhhby9EZXNrdG9wL2Rldi9wZXJmb3JtYW5jZS9wZXJmb3JtYW5jZS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKdmFyIF90YWIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi4vdGFiIikpOwp2YXIgX2F1dGggPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi4vYXV0aCIpKTsKdmFyIF9jYWNoZSA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi9jYWNoZSIpKTsKdmFyIF9tb2RhbCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi9tb2RhbCIpKTsKdmFyIF9kb3dubG9hZCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi9kb3dubG9hZCIpKTsKdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIGluc3RhbGw6IGZ1bmN0aW9uIGluc3RhbGwoVnVlKSB7CiAgICAvLyDpobXnrb7mk43kvZwKICAgIFZ1ZS5wcm90b3R5cGUuJHRhYiA9IF90YWIuZGVmYXVsdDsKICAgIC8vIOiupOivgeWvueixoQogICAgVnVlLnByb3RvdHlwZS4kYXV0aCA9IF9hdXRoLmRlZmF1bHQ7CiAgICAvLyDnvJPlrZjlr7nosaEKICAgIFZ1ZS5wcm90b3R5cGUuJGNhY2hlID0gX2NhY2hlLmRlZmF1bHQ7CiAgICAvLyDmqKHmgIHmoYblr7nosaEKICAgIFZ1ZS5wcm90b3R5cGUuJG1vZGFsID0gX21vZGFsLmRlZmF1bHQ7CiAgICAvLyDkuIvovb3mlofku7YKICAgIFZ1ZS5wcm90b3R5cGUuJGRvd25sb2FkID0gX2Rvd25sb2FkLmRlZmF1bHQ7CiAgfQp9Ow=="}, {"version": 3, "names": ["_tab", "_interopRequireDefault", "require", "_auth", "_cache", "_modal", "_download", "_default", "exports", "default", "install", "<PERSON><PERSON>", "prototype", "$tab", "tab", "$auth", "auth", "$cache", "cache", "$modal", "modal", "$download", "download"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/plugins/index.js"], "sourcesContent": ["import tab from './tab'\nimport auth from './auth'\nimport cache from './cache'\nimport modal from './modal'\nimport download from './download'\n\nexport default {\n  install(Vue) {\n    // 页签操作\n    Vue.prototype.$tab = tab\n    // 认证对象\n    Vue.prototype.$auth = auth\n    // 缓存对象\n    Vue.prototype.$cache = cache\n    // 模态框对象\n    Vue.prototype.$modal = modal\n    // 下载文件\n    Vue.prototype.$download = download\n  }\n}\n"], "mappings": ";;;;;;;AAAA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,KAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,MAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,MAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,SAAA,GAAAL,sBAAA,CAAAC,OAAA;AAAiC,IAAAK,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAElB;EACbC,OAAO,WAAPA,OAAOA,CAACC,GAAG,EAAE;IACX;IACAA,GAAG,CAACC,SAAS,CAACC,IAAI,GAAGC,YAAG;IACxB;IACAH,GAAG,CAACC,SAAS,CAACG,KAAK,GAAGC,aAAI;IAC1B;IACAL,GAAG,CAACC,SAAS,CAACK,MAAM,GAAGC,cAAK;IAC5B;IACAP,GAAG,CAACC,SAAS,CAACO,MAAM,GAAGC,cAAK;IAC5B;IACAT,GAAG,CAACC,SAAS,CAACS,SAAS,GAAGC,iBAAQ;EACpC;AACF,CAAC", "ignoreList": []}]}