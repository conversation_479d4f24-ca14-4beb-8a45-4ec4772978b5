{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/layout/components/Sidebar/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/layout/components/Sidebar/index.vue", "mtime": 1753510684529}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_vuex", "require", "_Logo", "_interopRequireDefault", "_SidebarItem", "_variables2", "components", "SidebarItem", "Logo", "computed", "_objectSpread2", "default", "mapState", "mapGetters", "activeMenu", "route", "$route", "meta", "path", "showLogo", "$store", "state", "settings", "sidebarLogo", "variables", "isCollapse", "sidebar", "opened", "created", "console", "log", "sidebarRouters"], "sources": ["src/layout/components/Sidebar/index.vue"], "sourcesContent": ["<template>\n    <div :class=\"{'has-logo':showLogo}\" :style=\"{ backgroundColor: settings.sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground }\">\n        <logo v-if=\"showLogo\" :collapse=\"isCollapse\" />\n        <el-scrollbar :class=\"settings.sideTheme\" wrap-class=\"scrollbar-wrapper\">\n            <el-menu\n                :default-active=\"activeMenu\"\n                :collapse=\"isCollapse\"\n                :background-color=\"settings.sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground\"\n                :text-color=\"settings.sideTheme === 'theme-dark' ? variables.menuColor : variables.menuLightColor\"\n                :unique-opened=\"true\"\n                :active-text-color=\"settings.theme\"\n                :collapse-transition=\"false\"\n                mode=\"vertical\"\n            >\n                <sidebar-item\n                    v-for=\"(route, index) in sidebarRouters\"\n                    :key=\"route.path  + index\"\n                    :item=\"route\"\n                    :base-path=\"route.path\"\n                />\n            </el-menu>\n        </el-scrollbar>\n    </div>\n</template>\n\n<script>\nimport { mapGetters, mapState } from \"vuex\"\nimport Logo from \"./Logo\"\nimport SidebarItem from \"./SidebarItem\"\nimport variables from \"@/assets/styles/variables.scss\"\n\nexport default {\n    components: { SidebarItem, Logo },\n    computed: {\n        ...mapState([\"settings\"]),\n        ...mapGetters([\"sidebarRouters\", \"sidebar\"]),\n        activeMenu() {\n            const route = this.$route\n            const { meta, path } = route\n            // if set path, the sidebar will highlight the path you set\n            if (meta.activeMenu) {\n                return meta.activeMenu\n            }\n            return path\n        },\n        showLogo() {\n            return this.$store.state.settings.sidebarLogo\n        },\n        variables() {\n            return variables\n        },\n        isCollapse() {\n            return !this.sidebar.opened\n        }\n    },\n    created() {\n        console.log('Sidebar组件中的sidebarRouters数据:', this.sidebarRouters)\n    }\n}\n</script>\n"], "mappings": ";;;;;;;;AA0BA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,YAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,WAAA,GAAAF,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAK,UAAA;IAAAC,WAAA,EAAAA,oBAAA;IAAAC,IAAA,EAAAA;EAAA;EACAC,QAAA,MAAAC,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA,IAAAC,cAAA,kBACA,IAAAC,gBAAA;IACAC,UAAA,WAAAA,WAAA;MACA,IAAAC,KAAA,QAAAC,MAAA;MACA,IAAAC,IAAA,GAAAF,KAAA,CAAAE,IAAA;QAAAC,IAAA,GAAAH,KAAA,CAAAG,IAAA;MACA;MACA,IAAAD,IAAA,CAAAH,UAAA;QACA,OAAAG,IAAA,CAAAH,UAAA;MACA;MACA,OAAAI,IAAA;IACA;IACAC,QAAA,WAAAA,SAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,WAAA;IACA;IACAC,SAAA,WAAAA,UAAA;MACA,OAAAA,mBAAA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,aAAAC,OAAA,CAAAC,MAAA;IACA;EAAA,EACA;EACAC,OAAA,WAAAA,QAAA;IACAC,OAAA,CAAAC,GAAA,sCAAAC,cAAA;EACA;AACA", "ignoreList": []}]}