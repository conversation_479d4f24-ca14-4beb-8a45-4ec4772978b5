{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/layout/components/Settings/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/layout/components/Settings/index.vue", "mtime": 1753510684529}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_ThemePicker", "_interopRequireDefault", "require", "components", "ThemePicker", "expose", "data", "theme", "$store", "state", "settings", "sideTheme", "showSettings", "computed", "fixedHeader", "get", "set", "val", "dispatch", "key", "value", "topNav", "commit", "permission", "defaultRoutes", "tagsView", "tagsIcon", "sidebarLogo", "dynamicTitle", "title", "footerVisible", "methods", "themeChange", "handleTheme", "openSetting", "closeSetting", "saveSetting", "$modal", "loading", "$cache", "local", "concat", "setTimeout", "closeLoading", "resetSetting", "remove"], "sources": ["src/layout/components/Settings/index.vue"], "sourcesContent": ["<template>\n  <el-drawer size=\"280px\" :visible=\"showSettings\" :with-header=\"false\" :append-to-body=\"true\" :before-close=\"closeSetting\" :lock-scroll=\"false\">\n    <div class=\"drawer-container\">\n      <div>\n        <div class=\"setting-drawer-content\">\n          <div class=\"setting-drawer-title\">\n            <h3 class=\"drawer-title\">主题风格设置</h3>\n          </div>\n          <div class=\"setting-drawer-block-checbox\">\n            <div class=\"setting-drawer-block-checbox-item\" @click=\"handleTheme('theme-dark')\">\n              <img src=\"@/assets/images/dark.svg\" alt=\"dark\">\n              <div v-if=\"sideTheme === 'theme-dark'\" class=\"setting-drawer-block-checbox-selectIcon\" style=\"display: block;\">\n                <i aria-label=\"图标: check\" class=\"anticon anticon-check\">\n                  <svg viewBox=\"64 64 896 896\" data-icon=\"check\" width=\"1em\" height=\"1em\" :fill=\"theme\" aria-hidden=\"true\" focusable=\"false\" class=\"\">\n                    <path d=\"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z\"/>\n                  </svg>\n                </i>\n              </div>\n            </div>\n            <div class=\"setting-drawer-block-checbox-item\" @click=\"handleTheme('theme-light')\">\n              <img src=\"@/assets/images/light.svg\" alt=\"light\">\n              <div v-if=\"sideTheme === 'theme-light'\" class=\"setting-drawer-block-checbox-selectIcon\" style=\"display: block;\">\n                <i aria-label=\"图标: check\" class=\"anticon anticon-check\">\n                  <svg viewBox=\"64 64 896 896\" data-icon=\"check\" width=\"1em\" height=\"1em\" :fill=\"theme\" aria-hidden=\"true\" focusable=\"false\" class=\"\">\n                    <path d=\"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z\"/>\n                  </svg>\n                </i>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"drawer-item\">\n            <span>主题颜色</span>\n            <theme-picker style=\"float: right;height: 26px;margin: -3px 8px 0 0;\" @change=\"themeChange\" />\n          </div>\n        </div>\n\n        <el-divider/>\n\n        <h3 class=\"drawer-title\">系统布局配置</h3>\n\n        <div class=\"drawer-item\">\n          <span>开启 TopNav</span>\n          <el-switch v-model=\"topNav\" class=\"drawer-switch\" />\n        </div>\n\n        <div class=\"drawer-item\">\n          <span>开启 Tags-Views</span>\n          <el-switch v-model=\"tagsView\" class=\"drawer-switch\" />\n        </div>\n\n        <div class=\"drawer-item\">\n          <span>显示页签图标</span>\n          <el-switch v-model=\"tagsIcon\" :disabled=\"!tagsView\" class=\"drawer-switch\" />\n        </div>\n\n        <div class=\"drawer-item\">\n          <span>固定 Header</span>\n          <el-switch v-model=\"fixedHeader\" class=\"drawer-switch\" />\n        </div>\n\n        <div class=\"drawer-item\">\n          <span>显示 Logo</span>\n          <el-switch v-model=\"sidebarLogo\" class=\"drawer-switch\" />\n        </div>\n\n        <div class=\"drawer-item\">\n          <span>动态标题</span>\n          <el-switch v-model=\"dynamicTitle\" class=\"drawer-switch\" />\n        </div>\n\n        <div class=\"drawer-item\">\n          <span>底部版权</span>\n          <el-switch v-model=\"footerVisible\" class=\"drawer-switch\" />\n        </div>\n\n        <el-divider/>\n\n        <el-button size=\"small\" type=\"primary\" plain icon=\"el-icon-document-add\" @click=\"saveSetting\">保存配置</el-button>\n        <el-button size=\"small\" plain icon=\"el-icon-refresh\" @click=\"resetSetting\">重置配置</el-button>\n      </div>\n    </div>\n  </el-drawer>\n</template>\n\n<script>\nimport ThemePicker from '@/components/ThemePicker'\n\nexport default {\n  components: { ThemePicker },\n  expose: ['openSetting'],\n  data() {\n    return {\n      theme: this.$store.state.settings.theme,\n      sideTheme: this.$store.state.settings.sideTheme,\n      showSettings: false\n    }\n  },\n  computed: {\n    fixedHeader: {\n      get() {\n        return this.$store.state.settings.fixedHeader\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'fixedHeader',\n          value: val\n        })\n      }\n    },\n    topNav: {\n      get() {\n        return this.$store.state.settings.topNav\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'topNav',\n          value: val\n        })\n        if (!val) {\n          this.$store.dispatch('app/toggleSideBarHide', false)\n          this.$store.commit(\"SET_SIDEBAR_ROUTERS\", this.$store.state.permission.defaultRoutes)\n        }\n      }\n    },\n    tagsView: {\n      get() {\n        return this.$store.state.settings.tagsView\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'tagsView',\n          value: val\n        })\n      }\n    },\n    tagsIcon: {\n      get() {\n        return this.$store.state.settings.tagsIcon\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'tagsIcon',\n          value: val\n        })\n      }\n    },\n    sidebarLogo: {\n      get() {\n        return this.$store.state.settings.sidebarLogo\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'sidebarLogo',\n          value: val\n        })\n      }\n    },\n    dynamicTitle: {\n      get() {\n        return this.$store.state.settings.dynamicTitle\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'dynamicTitle',\n          value: val\n        })\n        this.$store.dispatch('settings/setTitle', this.$store.state.settings.title)\n      }\n    },\n    footerVisible: {\n      get() {\n        return this.$store.state.settings.footerVisible\n      },\n      set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'footerVisible',\n          value: val\n        })\n      }\n    }\n  },\n  methods: {\n    themeChange(val) {\n      this.$store.dispatch('settings/changeSetting', {\n        key: 'theme',\n        value: val\n      })\n      this.theme = val\n    },\n    handleTheme(val) {\n      this.$store.dispatch('settings/changeSetting', {\n        key: 'sideTheme',\n        value: val\n      })\n      this.sideTheme = val\n    },\n    openSetting() {\n      this.showSettings = true\n    },\n    closeSetting(){\n      this.showSettings = false\n    },\n    saveSetting() {\n      this.$modal.loading(\"正在保存到本地，请稍候...\")\n      this.$cache.local.set(\n        \"layout-setting\",\n        `{\n            \"topNav\":${this.topNav},\n            \"tagsView\":${this.tagsView},\n            \"tagsIcon\":${this.tagsIcon},\n            \"fixedHeader\":${this.fixedHeader},\n            \"sidebarLogo\":${this.sidebarLogo},\n            \"dynamicTitle\":${this.dynamicTitle},\n            \"footerVisible\":${this.footerVisible},\n            \"sideTheme\":\"${this.sideTheme}\",\n            \"theme\":\"${this.theme}\"\n          }`\n      )\n      setTimeout(this.$modal.closeLoading(), 1000)\n    },\n    resetSetting() {\n      this.$modal.loading(\"正在清除设置缓存并刷新，请稍候...\")\n      this.$cache.local.remove(\"layout-setting\")\n      setTimeout(\"window.location.reload()\", 1000)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n  .setting-drawer-content {\n    .setting-drawer-title {\n      margin-bottom: 12px;\n      color: rgba(0, 0, 0, .85);\n      font-size: 14px;\n      line-height: 22px;\n      font-weight: bold;\n    }\n\n    .setting-drawer-block-checbox {\n      display: flex;\n      justify-content: flex-start;\n      align-items: center;\n      margin-top: 10px;\n      margin-bottom: 20px;\n\n      .setting-drawer-block-checbox-item {\n        position: relative;\n        margin-right: 16px;\n        border-radius: 2px;\n        cursor: pointer;\n\n        img {\n          width: 48px;\n          height: 48px;\n        }\n\n        .setting-drawer-block-checbox-selectIcon {\n          position: absolute;\n          top: 0;\n          right: 0;\n          width: 100%;\n          height: 100%;\n          padding-top: 15px;\n          padding-left: 24px;\n          color: #1890ff;\n          font-weight: 700;\n          font-size: 14px;\n        }\n      }\n    }\n  }\n\n  .drawer-container {\n    padding: 20px;\n    font-size: 14px;\n    line-height: 1.5;\n    word-wrap: break-word;\n\n    .drawer-title {\n      margin-bottom: 12px;\n      color: rgba(0, 0, 0, .85);\n      font-size: 14px;\n      line-height: 22px;\n    }\n\n    .drawer-item {\n      color: rgba(0, 0, 0, .65);\n      font-size: 14px;\n      padding: 12px 0;\n    }\n\n    .drawer-switch {\n      float: right\n    }\n  }\n</style>\n"], "mappings": ";;;;;;;;AAsFA,IAAAA,YAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,UAAA;IAAAC,WAAA,EAAAA;EAAA;EACAC,MAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAH,KAAA;MACAI,SAAA,OAAAH,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,SAAA;MACAC,YAAA;IACA;EACA;EACAC,QAAA;IACAC,WAAA;MACAC,GAAA,WAAAA,IAAA;QACA,YAAAP,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAI,WAAA;MACA;MACAE,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAT,MAAA,CAAAU,QAAA;UACAC,GAAA;UACAC,KAAA,EAAAH;QACA;MACA;IACA;IACAI,MAAA;MACAN,GAAA,WAAAA,IAAA;QACA,YAAAP,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAW,MAAA;MACA;MACAL,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAT,MAAA,CAAAU,QAAA;UACAC,GAAA;UACAC,KAAA,EAAAH;QACA;QACA,KAAAA,GAAA;UACA,KAAAT,MAAA,CAAAU,QAAA;UACA,KAAAV,MAAA,CAAAc,MAAA,6BAAAd,MAAA,CAAAC,KAAA,CAAAc,UAAA,CAAAC,aAAA;QACA;MACA;IACA;IACAC,QAAA;MACAV,GAAA,WAAAA,IAAA;QACA,YAAAP,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAe,QAAA;MACA;MACAT,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAT,MAAA,CAAAU,QAAA;UACAC,GAAA;UACAC,KAAA,EAAAH;QACA;MACA;IACA;IACAS,QAAA;MACAX,GAAA,WAAAA,IAAA;QACA,YAAAP,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAgB,QAAA;MACA;MACAV,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAT,MAAA,CAAAU,QAAA;UACAC,GAAA;UACAC,KAAA,EAAAH;QACA;MACA;IACA;IACAU,WAAA;MACAZ,GAAA,WAAAA,IAAA;QACA,YAAAP,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAiB,WAAA;MACA;MACAX,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAT,MAAA,CAAAU,QAAA;UACAC,GAAA;UACAC,KAAA,EAAAH;QACA;MACA;IACA;IACAW,YAAA;MACAb,GAAA,WAAAA,IAAA;QACA,YAAAP,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAkB,YAAA;MACA;MACAZ,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAT,MAAA,CAAAU,QAAA;UACAC,GAAA;UACAC,KAAA,EAAAH;QACA;QACA,KAAAT,MAAA,CAAAU,QAAA,2BAAAV,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAmB,KAAA;MACA;IACA;IACAC,aAAA;MACAf,GAAA,WAAAA,IAAA;QACA,YAAAP,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAoB,aAAA;MACA;MACAd,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAT,MAAA,CAAAU,QAAA;UACAC,GAAA;UACAC,KAAA,EAAAH;QACA;MACA;IACA;EACA;EACAc,OAAA;IACAC,WAAA,WAAAA,YAAAf,GAAA;MACA,KAAAT,MAAA,CAAAU,QAAA;QACAC,GAAA;QACAC,KAAA,EAAAH;MACA;MACA,KAAAV,KAAA,GAAAU,GAAA;IACA;IACAgB,WAAA,WAAAA,YAAAhB,GAAA;MACA,KAAAT,MAAA,CAAAU,QAAA;QACAC,GAAA;QACAC,KAAA,EAAAH;MACA;MACA,KAAAN,SAAA,GAAAM,GAAA;IACA;IACAiB,WAAA,WAAAA,YAAA;MACA,KAAAtB,YAAA;IACA;IACAuB,YAAA,WAAAA,aAAA;MACA,KAAAvB,YAAA;IACA;IACAwB,WAAA,WAAAA,YAAA;MACA,KAAAC,MAAA,CAAAC,OAAA;MACA,KAAAC,MAAA,CAAAC,KAAA,CAAAxB,GAAA,CACA,+CAAAyB,MAAA,CAEA,KAAApB,MAAA,kCAAAoB,MAAA,CACA,KAAAhB,QAAA,kCAAAgB,MAAA,CACA,KAAAf,QAAA,qCAAAe,MAAA,CACA,KAAA3B,WAAA,qCAAA2B,MAAA,CACA,KAAAd,WAAA,sCAAAc,MAAA,CACA,KAAAb,YAAA,uCAAAa,MAAA,CACA,KAAAX,aAAA,qCAAAW,MAAA,CACA,KAAA9B,SAAA,mCAAA8B,MAAA,CACA,KAAAlC,KAAA,oBAEA;MACAmC,UAAA,MAAAL,MAAA,CAAAM,YAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA,KAAAP,MAAA,CAAAC,OAAA;MACA,KAAAC,MAAA,CAAAC,KAAA,CAAAK,MAAA;MACAH,UAAA;IACA;EACA;AACA", "ignoreList": []}]}