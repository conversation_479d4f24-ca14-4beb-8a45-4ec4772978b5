{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/tool/build/RightPanel.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/tool/build/RightPanel.vue", "mtime": 1753510684536}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_util", "require", "_vuedraggable", "_interopRequireDefault", "_TreeNodeDialog", "_index", "_IconsDialog", "_config", "dateTimeFormat", "date", "week", "month", "year", "datetime", "daterange", "monthrange", "datetimerange", "_default", "exports", "default", "components", "draggable", "TreeNodeDialog", "IconsDialog", "props", "data", "currentTab", "currentNode", "dialogVisible", "iconsVisible", "currentIconModel", "dateTypeOptions", "label", "value", "dateRangeTypeOptions", "colorFormatOptions", "justifyOptions", "layoutTreeProps", "node", "componentName", "concat", "vModel", "computed", "documentLink", "activeData", "document", "dateOptions", "type", "undefined", "tag", "tagList", "options", "inputComponents", "selectComponents", "methods", "addReg", "regList", "push", "pattern", "message", "addSelectItem", "addTreeItem", "idGlobal", "renderContent", "h", "_ref", "_this", "store", "click", "append", "remove", "children", "$set", "parent", "index", "findIndex", "d", "id", "splice", "addNode", "setOptionValue", "item", "val", "isNumberStr", "setDefaultValue", "Array", "isArray", "join", "indexOf", "onDefaultValueInput", "str", "defaultValue", "split", "map", "JSON", "parse", "onSwitchValueInput", "name", "setTimeValue", "valueFormat", "spanChange", "formConf", "span", "multipleChange", "dateTypeChange", "rangeChange", "min", "max", "rateTextChange", "rateScoreChange", "colorFormatChange", "<PERSON><PERSON><PERSON>", "Date", "openIconsDialog", "model", "setIcon", "tagChange", "tagIcon", "target", "find", "$emit"], "sources": ["src/views/tool/build/RightPanel.vue"], "sourcesContent": ["<template>\n  <div class=\"right-board\">\n    <el-tabs v-model=\"currentTab\" class=\"center-tabs\">\n      <el-tab-pane label=\"组件属性\" name=\"field\" />\n      <el-tab-pane label=\"表单属性\" name=\"form\" />\n    </el-tabs>\n    <div class=\"field-box\">\n      <a class=\"document-link\" target=\"_blank\" :href=\"documentLink\" title=\"查看组件文档\">\n        <i class=\"el-icon-link\" />\n      </a>\n      <el-scrollbar class=\"right-scrollbar\">\n        <!-- 组件属性 -->\n        <el-form v-show=\"currentTab==='field' && showField\" size=\"small\" label-width=\"90px\">\n          <el-form-item v-if=\"activeData.changeTag\" label=\"组件类型\">\n            <el-select\n              v-model=\"activeData.tagIcon\"\n              placeholder=\"请选择组件类型\"\n              :style=\"{width: '100%'}\"\n              @change=\"tagChange\"\n            >\n              <el-option-group v-for=\"group in tagList\" :key=\"group.label\" :label=\"group.label\">\n                <el-option\n                  v-for=\"item in group.options\"\n                  :key=\"item.label\"\n                  :label=\"item.label\"\n                  :value=\"item.tagIcon\"\n                >\n                  <svg-icon class=\"node-icon\" :icon-class=\"item.tagIcon\" />\n                  <span> {{ item.label }}</span>\n                </el-option>\n              </el-option-group>\n            </el-select>\n          </el-form-item>\n          <el-form-item v-if=\"activeData.vModel!==undefined\" label=\"字段名\">\n            <el-input v-model=\"activeData.vModel\" placeholder=\"请输入字段名（v-model）\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.componentName!==undefined\" label=\"组件名\">\n            {{ activeData.componentName }}\n          </el-form-item>\n          <el-form-item v-if=\"activeData.label!==undefined\" label=\"标题\">\n            <el-input v-model=\"activeData.label\" placeholder=\"请输入标题\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.placeholder!==undefined\" label=\"占位提示\">\n            <el-input v-model=\"activeData.placeholder\" placeholder=\"请输入占位提示\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['start-placeholder']!==undefined\" label=\"开始占位\">\n            <el-input v-model=\"activeData['start-placeholder']\" placeholder=\"请输入占位提示\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['end-placeholder']!==undefined\" label=\"结束占位\">\n            <el-input v-model=\"activeData['end-placeholder']\" placeholder=\"请输入占位提示\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.span!==undefined\" label=\"表单栅格\">\n            <el-slider v-model=\"activeData.span\" :max=\"24\" :min=\"1\" :marks=\"{12:''}\" @change=\"spanChange\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.layout==='rowFormItem'\" label=\"栅格间隔\">\n            <el-input-number v-model=\"activeData.gutter\" :min=\"0\" placeholder=\"栅格间隔\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.layout==='rowFormItem'\" label=\"布局模式\">\n            <el-radio-group v-model=\"activeData.type\">\n              <el-radio-button label=\"default\" />\n              <el-radio-button label=\"flex\" />\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item v-if=\"activeData.justify!==undefined&&activeData.type==='flex'\" label=\"水平排列\">\n            <el-select v-model=\"activeData.justify\" placeholder=\"请选择水平排列\" :style=\"{width: '100%'}\">\n              <el-option\n                v-for=\"(item, index) in justifyOptions\"\n                :key=\"index\"\n                :label=\"item.label\"\n                :value=\"item.value\"\n              />\n            </el-select>\n          </el-form-item>\n          <el-form-item v-if=\"activeData.align!==undefined&&activeData.type==='flex'\" label=\"垂直排列\">\n            <el-radio-group v-model=\"activeData.align\">\n              <el-radio-button label=\"top\" />\n              <el-radio-button label=\"middle\" />\n              <el-radio-button label=\"bottom\" />\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item v-if=\"activeData.labelWidth!==undefined\" label=\"标签宽度\">\n            <el-input v-model.number=\"activeData.labelWidth\" type=\"number\" placeholder=\"请输入标签宽度\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.style&&activeData.style.width!==undefined\" label=\"组件宽度\">\n            <el-input v-model=\"activeData.style.width\" placeholder=\"请输入组件宽度\" clearable />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.vModel!==undefined\" label=\"默认值\">\n            <el-input\n              :value=\"setDefaultValue(activeData.defaultValue)\"\n              placeholder=\"请输入默认值\"\n              @input=\"onDefaultValueInput\"\n            />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.tag==='el-checkbox-group'\" label=\"至少应选\">\n            <el-input-number\n              :value=\"activeData.min\"\n              :min=\"0\"\n              placeholder=\"至少应选\"\n              @input=\"$set(activeData, 'min', $event?$event:undefined)\"\n            />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.tag==='el-checkbox-group'\" label=\"最多可选\">\n            <el-input-number\n              :value=\"activeData.max\"\n              :min=\"0\"\n              placeholder=\"最多可选\"\n              @input=\"$set(activeData, 'max', $event?$event:undefined)\"\n            />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.prepend!==undefined\" label=\"前缀\">\n            <el-input v-model=\"activeData.prepend\" placeholder=\"请输入前缀\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.append!==undefined\" label=\"后缀\">\n            <el-input v-model=\"activeData.append\" placeholder=\"请输入后缀\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['prefix-icon']!==undefined\" label=\"前图标\">\n            <el-input v-model=\"activeData['prefix-icon']\" placeholder=\"请输入前图标名称\">\n              <el-button slot=\"append\" icon=\"el-icon-thumb\" @click=\"openIconsDialog('prefix-icon')\">\n                选择\n              </el-button>\n            </el-input>\n          </el-form-item>\n          <el-form-item v-if=\"activeData['suffix-icon'] !== undefined\" label=\"后图标\">\n            <el-input v-model=\"activeData['suffix-icon']\" placeholder=\"请输入后图标名称\">\n              <el-button slot=\"append\" icon=\"el-icon-thumb\" @click=\"openIconsDialog('suffix-icon')\">\n                选择\n              </el-button>\n            </el-input>\n          </el-form-item>\n          <el-form-item v-if=\"activeData.tag === 'el-cascader'\" label=\"选项分隔符\">\n            <el-input v-model=\"activeData.separator\" placeholder=\"请输入选项分隔符\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.autosize !== undefined\" label=\"最小行数\">\n            <el-input-number v-model=\"activeData.autosize.minRows\" :min=\"1\" placeholder=\"最小行数\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.autosize !== undefined\" label=\"最大行数\">\n            <el-input-number v-model=\"activeData.autosize.maxRows\" :min=\"1\" placeholder=\"最大行数\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.min !== undefined\" label=\"最小值\">\n            <el-input-number v-model=\"activeData.min\" placeholder=\"最小值\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.max !== undefined\" label=\"最大值\">\n            <el-input-number v-model=\"activeData.max\" placeholder=\"最大值\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.step !== undefined\" label=\"步长\">\n            <el-input-number v-model=\"activeData.step\" placeholder=\"步数\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.tag === 'el-input-number'\" label=\"精度\">\n            <el-input-number v-model=\"activeData.precision\" :min=\"0\" placeholder=\"精度\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.tag === 'el-input-number'\" label=\"按钮位置\">\n            <el-radio-group v-model=\"activeData['controls-position']\">\n              <el-radio-button label=\"\">\n                默认\n              </el-radio-button>\n              <el-radio-button label=\"right\">\n                右侧\n              </el-radio-button>\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item v-if=\"activeData.maxlength !== undefined\" label=\"最多输入\">\n            <el-input v-model=\"activeData.maxlength\" placeholder=\"请输入字符长度\">\n              <template slot=\"append\">\n                个字符\n              </template>\n            </el-input>\n          </el-form-item>\n          <el-form-item v-if=\"activeData['active-text'] !== undefined\" label=\"开启提示\">\n            <el-input v-model=\"activeData['active-text']\" placeholder=\"请输入开启提示\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['inactive-text'] !== undefined\" label=\"关闭提示\">\n            <el-input v-model=\"activeData['inactive-text']\" placeholder=\"请输入关闭提示\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['active-value'] !== undefined\" label=\"开启值\">\n            <el-input\n              :value=\"setDefaultValue(activeData['active-value'])\"\n              placeholder=\"请输入开启值\"\n              @input=\"onSwitchValueInput($event, 'active-value')\"\n            />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['inactive-value'] !== undefined\" label=\"关闭值\">\n            <el-input\n              :value=\"setDefaultValue(activeData['inactive-value'])\"\n              placeholder=\"请输入关闭值\"\n              @input=\"onSwitchValueInput($event, 'inactive-value')\"\n            />\n          </el-form-item>\n          <el-form-item\n            v-if=\"activeData.type !== undefined && 'el-date-picker' === activeData.tag\"\n            label=\"时间类型\"\n          >\n            <el-select\n              v-model=\"activeData.type\"\n              placeholder=\"请选择时间类型\"\n              :style=\"{ width: '100%' }\"\n              @change=\"dateTypeChange\"\n            >\n              <el-option\n                v-for=\"(item, index) in dateOptions\"\n                :key=\"index\"\n                :label=\"item.label\"\n                :value=\"item.value\"\n              />\n            </el-select>\n          </el-form-item>\n          <el-form-item v-if=\"activeData.name !== undefined\" label=\"文件字段名\">\n            <el-input v-model=\"activeData.name\" placeholder=\"请输入上传文件字段名\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.accept !== undefined\" label=\"文件类型\">\n            <el-select\n              v-model=\"activeData.accept\"\n              placeholder=\"请选择文件类型\"\n              :style=\"{ width: '100%' }\"\n              clearable\n            >\n              <el-option label=\"图片\" value=\"image/*\" />\n              <el-option label=\"视频\" value=\"video/*\" />\n              <el-option label=\"音频\" value=\"audio/*\" />\n              <el-option label=\"excel\" value=\".xls,.xlsx\" />\n              <el-option label=\"word\" value=\".doc,.docx\" />\n              <el-option label=\"pdf\" value=\".pdf\" />\n              <el-option label=\"txt\" value=\".txt\" />\n            </el-select>\n          </el-form-item>\n          <el-form-item v-if=\"activeData.fileSize !== undefined\" label=\"文件大小\">\n            <el-input v-model.number=\"activeData.fileSize\" placeholder=\"请输入文件大小\">\n              <el-select slot=\"append\" v-model=\"activeData.sizeUnit\" :style=\"{ width: '66px' }\">\n                <el-option label=\"KB\" value=\"KB\" />\n                <el-option label=\"MB\" value=\"MB\" />\n                <el-option label=\"GB\" value=\"GB\" />\n              </el-select>\n            </el-input>\n          </el-form-item>\n          <el-form-item v-if=\"activeData.action !== undefined\" label=\"上传地址\">\n            <el-input v-model=\"activeData.action\" placeholder=\"请输入上传地址\" clearable />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['list-type'] !== undefined\" label=\"列表类型\">\n            <el-radio-group v-model=\"activeData['list-type']\" size=\"small\">\n              <el-radio-button label=\"text\">\n                text\n              </el-radio-button>\n              <el-radio-button label=\"picture\">\n                picture\n              </el-radio-button>\n              <el-radio-button label=\"picture-card\">\n                picture-card\n              </el-radio-button>\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item\n            v-if=\"activeData.buttonText !== undefined\"\n            v-show=\"'picture-card' !== activeData['list-type']\"\n            label=\"按钮文字\"\n          >\n            <el-input v-model=\"activeData.buttonText\" placeholder=\"请输入按钮文字\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['range-separator'] !== undefined\" label=\"分隔符\">\n            <el-input v-model=\"activeData['range-separator']\" placeholder=\"请输入分隔符\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['picker-options'] !== undefined\" label=\"时间段\">\n            <el-input\n              v-model=\"activeData['picker-options'].selectableRange\"\n              placeholder=\"请输入时间段\"\n            />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.format !== undefined\" label=\"时间格式\">\n            <el-input\n              :value=\"activeData.format\"\n              placeholder=\"请输入时间格式\"\n              @input=\"setTimeValue($event)\"\n            />\n          </el-form-item>\n          <template v-if=\"['el-checkbox-group', 'el-radio-group', 'el-select'].indexOf(activeData.tag) > -1\">\n            <el-divider>选项</el-divider>\n            <draggable\n              :list=\"activeData.options\"\n              :animation=\"340\"\n              group=\"selectItem\"\n              handle=\".option-drag\"\n            >\n              <div v-for=\"(item, index) in activeData.options\" :key=\"index\" class=\"select-item\">\n                <div class=\"select-line-icon option-drag\">\n                  <i class=\"el-icon-s-operation\" />\n                </div>\n                <el-input v-model=\"item.label\" placeholder=\"选项名\" size=\"small\" />\n                <el-input\n                  placeholder=\"选项值\"\n                  size=\"small\"\n                  :value=\"item.value\"\n                  @input=\"setOptionValue(item, $event)\"\n                />\n                <div class=\"close-btn select-line-icon\" @click=\"activeData.options.splice(index, 1)\">\n                  <i class=\"el-icon-remove-outline\" />\n                </div>\n              </div>\n            </draggable>\n            <div style=\"margin-left: 20px;\">\n              <el-button\n                style=\"padding-bottom: 0\"\n                icon=\"el-icon-circle-plus-outline\"\n                type=\"text\"\n                @click=\"addSelectItem\"\n              >\n                添加选项\n              </el-button>\n            </div>\n            <el-divider />\n          </template>\n\n          <template v-if=\"['el-cascader'].indexOf(activeData.tag) > -1\">\n            <el-divider>选项</el-divider>\n            <el-form-item label=\"数据类型\">\n              <el-radio-group v-model=\"activeData.dataType\" size=\"small\">\n                <el-radio-button label=\"dynamic\">\n                  动态数据\n                </el-radio-button>\n                <el-radio-button label=\"static\">\n                  静态数据\n                </el-radio-button>\n              </el-radio-group>\n            </el-form-item>\n\n            <template v-if=\"activeData.dataType === 'dynamic'\">\n              <el-form-item label=\"标签键名\">\n                <el-input v-model=\"activeData.labelKey\" placeholder=\"请输入标签键名\" />\n              </el-form-item>\n              <el-form-item label=\"值键名\">\n                <el-input v-model=\"activeData.valueKey\" placeholder=\"请输入值键名\" />\n              </el-form-item>\n              <el-form-item label=\"子级键名\">\n                <el-input v-model=\"activeData.childrenKey\" placeholder=\"请输入子级键名\" />\n              </el-form-item>\n            </template>\n\n            <el-tree\n              v-if=\"activeData.dataType === 'static'\"\n              draggable\n              :data=\"activeData.options\"\n              node-key=\"id\"\n              :expand-on-click-node=\"false\"\n              :render-content=\"renderContent\"\n            />\n            <div v-if=\"activeData.dataType === 'static'\" style=\"margin-left: 20px\">\n              <el-button\n                style=\"padding-bottom: 0\"\n                icon=\"el-icon-circle-plus-outline\"\n                type=\"text\"\n                @click=\"addTreeItem\"\n              >\n                添加父级\n              </el-button>\n            </div>\n            <el-divider />\n          </template>\n\n          <el-form-item v-if=\"activeData.optionType !== undefined\" label=\"选项样式\">\n            <el-radio-group v-model=\"activeData.optionType\">\n              <el-radio-button label=\"default\">\n                默认\n              </el-radio-button>\n              <el-radio-button label=\"button\">\n                按钮\n              </el-radio-button>\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item v-if=\"activeData['active-color'] !== undefined\" label=\"开启颜色\">\n            <el-color-picker v-model=\"activeData['active-color']\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['inactive-color'] !== undefined\" label=\"关闭颜色\">\n            <el-color-picker v-model=\"activeData['inactive-color']\" />\n          </el-form-item>\n\n          <el-form-item v-if=\"activeData['allow-half'] !== undefined\" label=\"允许半选\">\n            <el-switch v-model=\"activeData['allow-half']\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['show-text'] !== undefined\" label=\"辅助文字\">\n            <el-switch v-model=\"activeData['show-text']\" @change=\"rateTextChange\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['show-score'] !== undefined\" label=\"显示分数\">\n            <el-switch v-model=\"activeData['show-score']\" @change=\"rateScoreChange\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['show-stops'] !== undefined\" label=\"显示间断点\">\n            <el-switch v-model=\"activeData['show-stops']\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.range !== undefined\" label=\"范围选择\">\n            <el-switch v-model=\"activeData.range\" @change=\"rangeChange\" />\n          </el-form-item>\n          <el-form-item\n            v-if=\"activeData.border !== undefined && activeData.optionType === 'default'\"\n            label=\"是否带边框\"\n          >\n            <el-switch v-model=\"activeData.border\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.tag === 'el-color-picker'\" label=\"颜色格式\">\n            <el-select\n              v-model=\"activeData['color-format']\"\n              placeholder=\"请选择颜色格式\"\n              :style=\"{ width: '100%' }\"\n              @change=\"colorFormatChange\"\n            >\n              <el-option\n                v-for=\"(item, index) in colorFormatOptions\"\n                :key=\"index\"\n                :label=\"item.label\"\n                :value=\"item.value\"\n              />\n            </el-select>\n          </el-form-item>\n          <el-form-item\n            v-if=\"activeData.size !== undefined &&\n              (activeData.optionType === 'button' ||\n                activeData.border ||\n                activeData.tag === 'el-color-picker')\"\n            label=\"选项尺寸\"\n          >\n            <el-radio-group v-model=\"activeData.size\">\n              <el-radio-button label=\"medium\">\n                中等\n              </el-radio-button>\n              <el-radio-button label=\"small\">\n                较小\n              </el-radio-button>\n              <el-radio-button label=\"mini\">\n                迷你\n              </el-radio-button>\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item v-if=\"activeData['show-word-limit'] !== undefined\" label=\"输入统计\">\n            <el-switch v-model=\"activeData['show-word-limit']\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.tag === 'el-input-number'\" label=\"严格步数\">\n            <el-switch v-model=\"activeData['step-strictly']\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.tag === 'el-cascader'\" label=\"是否多选\">\n            <el-switch v-model=\"activeData.props.props.multiple\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.tag === 'el-cascader'\" label=\"展示全路径\">\n            <el-switch v-model=\"activeData['show-all-levels']\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.tag === 'el-cascader'\" label=\"可否筛选\">\n            <el-switch v-model=\"activeData.filterable\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.clearable !== undefined\" label=\"能否清空\">\n            <el-switch v-model=\"activeData.clearable\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.showTip !== undefined\" label=\"显示提示\">\n            <el-switch v-model=\"activeData.showTip\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.multiple !== undefined\" label=\"多选文件\">\n            <el-switch v-model=\"activeData.multiple\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['auto-upload'] !== undefined\" label=\"自动上传\">\n            <el-switch v-model=\"activeData['auto-upload']\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.readonly !== undefined\" label=\"是否只读\">\n            <el-switch v-model=\"activeData.readonly\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.disabled !== undefined\" label=\"是否禁用\">\n            <el-switch v-model=\"activeData.disabled\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.tag === 'el-select'\" label=\"是否可搜索\">\n            <el-switch v-model=\"activeData.filterable\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.tag === 'el-select'\" label=\"是否多选\">\n            <el-switch v-model=\"activeData.multiple\" @change=\"multipleChange\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.required !== undefined\" label=\"是否必填\">\n            <el-switch v-model=\"activeData.required\" />\n          </el-form-item>\n\n          <template v-if=\"activeData.layoutTree\">\n            <el-divider>布局结构树</el-divider>\n            <el-tree\n              :data=\"[activeData]\"\n              :props=\"layoutTreeProps\"\n              node-key=\"renderKey\"\n              default-expand-all\n              draggable\n            >\n              <span slot-scope=\"{ node, data }\">\n                <span class=\"node-label\">\n                  <svg-icon class=\"node-icon\" :icon-class=\"data.tagIcon\" />\n                  {{ node.label }}\n                </span>\n              </span>\n            </el-tree>\n          </template>\n\n          <template v-if=\"activeData.layout === 'colFormItem' && activeData.tag !== 'el-button'\">\n            <el-divider>正则校验</el-divider>\n            <div\n              v-for=\"(item, index) in activeData.regList\"\n              :key=\"index\"\n              class=\"reg-item\"\n            >\n              <span class=\"close-btn\" @click=\"activeData.regList.splice(index, 1)\">\n                <i class=\"el-icon-close\" />\n              </span>\n              <el-form-item label=\"表达式\">\n                <el-input v-model=\"item.pattern\" placeholder=\"请输入正则\" />\n              </el-form-item>\n              <el-form-item label=\"错误提示\" style=\"margin-bottom:0\">\n                <el-input v-model=\"item.message\" placeholder=\"请输入错误提示\" />\n              </el-form-item>\n            </div>\n            <div style=\"margin-left: 20px\">\n              <el-button icon=\"el-icon-circle-plus-outline\" type=\"text\" @click=\"addReg\">\n                添加规则\n              </el-button>\n            </div>\n          </template>\n        </el-form>\n        <!-- 表单属性 -->\n        <el-form v-show=\"currentTab === 'form'\" size=\"small\" label-width=\"90px\">\n          <el-form-item label=\"表单名\">\n            <el-input v-model=\"formConf.formRef\" placeholder=\"请输入表单名（ref）\" />\n          </el-form-item>\n          <el-form-item label=\"表单模型\">\n            <el-input v-model=\"formConf.formModel\" placeholder=\"请输入数据模型\" />\n          </el-form-item>\n          <el-form-item label=\"校验模型\">\n            <el-input v-model=\"formConf.formRules\" placeholder=\"请输入校验模型\" />\n          </el-form-item>\n          <el-form-item label=\"表单尺寸\">\n            <el-radio-group v-model=\"formConf.size\">\n              <el-radio-button label=\"medium\">\n                中等\n              </el-radio-button>\n              <el-radio-button label=\"small\">\n                较小\n              </el-radio-button>\n              <el-radio-button label=\"mini\">\n                迷你\n              </el-radio-button>\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item label=\"标签对齐\">\n            <el-radio-group v-model=\"formConf.labelPosition\">\n              <el-radio-button label=\"left\">\n                左对齐\n              </el-radio-button>\n              <el-radio-button label=\"right\">\n                右对齐\n              </el-radio-button>\n              <el-radio-button label=\"top\">\n                顶部对齐\n              </el-radio-button>\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item label=\"标签宽度\">\n            <el-input-number v-model=\"formConf.labelWidth\" placeholder=\"标签宽度\" />\n          </el-form-item>\n          <el-form-item label=\"栅格间隔\">\n            <el-input-number v-model=\"formConf.gutter\" :min=\"0\" placeholder=\"栅格间隔\" />\n          </el-form-item>\n          <el-form-item label=\"禁用表单\">\n            <el-switch v-model=\"formConf.disabled\" />\n          </el-form-item>\n          <el-form-item label=\"表单按钮\">\n            <el-switch v-model=\"formConf.formBtns\" />\n          </el-form-item>\n          <el-form-item label=\"显示未选中组件边框\">\n            <el-switch v-model=\"formConf.unFocusedComponentBorder\" />\n          </el-form-item>\n        </el-form>\n      </el-scrollbar>\n    </div>\n\n    <treeNode-dialog :visible.sync=\"dialogVisible\" title=\"添加选项\" @commit=\"addNode\" />\n    <icons-dialog :visible.sync=\"iconsVisible\" :current=\"activeData[currentIconModel]\" @select=\"setIcon\" />\n  </div>\n</template>\n\n<script>\nimport { isArray } from 'util'\nimport draggable from 'vuedraggable'\nimport TreeNodeDialog from './TreeNodeDialog'\nimport { isNumberStr } from '@/utils/index'\nimport IconsDialog from './IconsDialog'\nimport {\n  inputComponents,\n  selectComponents,\n  layoutComponents\n} from '@/utils/generator/config'\n\nconst dateTimeFormat = {\n  date: 'yyyy-MM-dd',\n  week: 'yyyy 第 WW 周',\n  month: 'yyyy-MM',\n  year: 'yyyy',\n  datetime: 'yyyy-MM-dd HH:mm:ss',\n  daterange: 'yyyy-MM-dd',\n  monthrange: 'yyyy-MM',\n  datetimerange: 'yyyy-MM-dd HH:mm:ss'\n}\n\nexport default {\n  components: {\n    draggable,\n    TreeNodeDialog,\n    IconsDialog\n  },\n  props: ['showField', 'activeData', 'formConf'],\n  data() {\n    return {\n      currentTab: 'field',\n      currentNode: null,\n      dialogVisible: false,\n      iconsVisible: false,\n      currentIconModel: null,\n      dateTypeOptions: [\n        {\n          label: '日(date)',\n          value: 'date'\n        },\n        {\n          label: '周(week)',\n          value: 'week'\n        },\n        {\n          label: '月(month)',\n          value: 'month'\n        },\n        {\n          label: '年(year)',\n          value: 'year'\n        },\n        {\n          label: '日期时间(datetime)',\n          value: 'datetime'\n        }\n      ],\n      dateRangeTypeOptions: [\n        {\n          label: '日期范围(daterange)',\n          value: 'daterange'\n        },\n        {\n          label: '月范围(monthrange)',\n          value: 'monthrange'\n        },\n        {\n          label: '日期时间范围(datetimerange)',\n          value: 'datetimerange'\n        }\n      ],\n      colorFormatOptions: [\n        {\n          label: 'hex',\n          value: 'hex'\n        },\n        {\n          label: 'rgb',\n          value: 'rgb'\n        },\n        {\n          label: 'rgba',\n          value: 'rgba'\n        },\n        {\n          label: 'hsv',\n          value: 'hsv'\n        },\n        {\n          label: 'hsl',\n          value: 'hsl'\n        }\n      ],\n      justifyOptions: [\n        {\n          label: 'start',\n          value: 'start'\n        },\n        {\n          label: 'end',\n          value: 'end'\n        },\n        {\n          label: 'center',\n          value: 'center'\n        },\n        {\n          label: 'space-around',\n          value: 'space-around'\n        },\n        {\n          label: 'space-between',\n          value: 'space-between'\n        }\n      ],\n      layoutTreeProps: {\n        label(data, node) {\n          return data.componentName || `${data.label}: ${data.vModel}`\n        }\n      }\n    }\n  },\n  computed: {\n    documentLink() {\n      return (\n        this.activeData.document\n        || 'https://element.eleme.cn/#/zh-CN/component/installation'\n      )\n    },\n    dateOptions() {\n      if (\n        this.activeData.type !== undefined\n        && this.activeData.tag === 'el-date-picker'\n      ) {\n        if (this.activeData['start-placeholder'] === undefined) {\n          return this.dateTypeOptions\n        }\n        return this.dateRangeTypeOptions\n      }\n      return []\n    },\n    tagList() {\n      return [\n        {\n          label: '输入型组件',\n          options: inputComponents\n        },\n        {\n          label: '选择型组件',\n          options: selectComponents\n        }\n      ]\n    }\n  },\n  methods: {\n    addReg() {\n      this.activeData.regList.push({\n        pattern: '',\n        message: ''\n      })\n    },\n    addSelectItem() {\n      this.activeData.options.push({\n        label: '',\n        value: ''\n      })\n    },\n    addTreeItem() {\n      ++this.idGlobal\n      this.dialogVisible = true\n      this.currentNode = this.activeData.options\n    },\n    renderContent(h, { node, data, store }) {\n      return (\n        <div class=\"custom-tree-node\">\n          <span>{node.label}</span>\n          <span class=\"node-operation\">\n            <i on-click={() => this.append(data)}\n              class=\"el-icon-plus\"\n              title=\"添加\"\n            ></i>\n            <i on-click={() => this.remove(node, data)}\n              class=\"el-icon-delete\"\n              title=\"删除\"\n            ></i>\n          </span>\n        </div>\n      )\n    },\n    append(data) {\n      if (!data.children) {\n        this.$set(data, 'children', [])\n      }\n      this.dialogVisible = true\n      this.currentNode = data.children\n    },\n    remove(node, data) {\n      const { parent } = node\n      const children = parent.data.children || parent.data\n      const index = children.findIndex(d => d.id === data.id)\n      children.splice(index, 1)\n    },\n    addNode(data) {\n      this.currentNode.push(data)\n    },\n    setOptionValue(item, val) {\n      item.value = isNumberStr(val) ? +val : val\n    },\n    setDefaultValue(val) {\n      if (Array.isArray(val)) {\n        return val.join(',')\n      }\n      if (['string', 'number'].indexOf(val) > -1) {\n        return val\n      }\n      if (typeof val === 'boolean') {\n        return `${val}`\n      }\n      return val\n    },\n    onDefaultValueInput(str) {\n      if (isArray(this.activeData.defaultValue)) {\n        // 数组\n        this.$set(\n          this.activeData,\n          'defaultValue',\n          str.split(',').map(val => (isNumberStr(val) ? +val : val))\n        )\n      } else if (['true', 'false'].indexOf(str) > -1) {\n        // 布尔\n        this.$set(this.activeData, 'defaultValue', JSON.parse(str))\n      } else {\n        // 字符串和数字\n        this.$set(\n          this.activeData,\n          'defaultValue',\n          isNumberStr(str) ? +str : str\n        )\n      }\n    },\n    onSwitchValueInput(val, name) {\n      if (['true', 'false'].indexOf(val) > -1) {\n        this.$set(this.activeData, name, JSON.parse(val))\n      } else {\n        this.$set(this.activeData, name, isNumberStr(val) ? +val : val)\n      }\n    },\n    setTimeValue(val, type) {\n      const valueFormat = type === 'week' ? dateTimeFormat.date : val\n      this.$set(this.activeData, 'defaultValue', null)\n      this.$set(this.activeData, 'value-format', valueFormat)\n      this.$set(this.activeData, 'format', val)\n    },\n    spanChange(val) {\n      this.formConf.span = val\n    },\n    multipleChange(val) {\n      this.$set(this.activeData, 'defaultValue', val ? [] : '')\n    },\n    dateTypeChange(val) {\n      this.setTimeValue(dateTimeFormat[val], val)\n    },\n    rangeChange(val) {\n      this.$set(\n        this.activeData,\n        'defaultValue',\n        val ? [this.activeData.min, this.activeData.max] : this.activeData.min\n      )\n    },\n    rateTextChange(val) {\n      if (val) this.activeData['show-score'] = false\n    },\n    rateScoreChange(val) {\n      if (val) this.activeData['show-text'] = false\n    },\n    colorFormatChange(val) {\n      this.activeData.defaultValue = null\n      this.activeData['show-alpha'] = val.indexOf('a') > -1\n      this.activeData.renderKey = +new Date() // 更新renderKey,重新渲染该组件\n    },\n    openIconsDialog(model) {\n      this.iconsVisible = true\n      this.currentIconModel = model\n    },\n    setIcon(val) {\n      this.activeData[this.currentIconModel] = val\n    },\n    tagChange(tagIcon) {\n      let target = inputComponents.find(item => item.tagIcon === tagIcon)\n      if (!target) target = selectComponents.find(item => item.tagIcon === tagIcon)\n      this.$emit('tag-change', target)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.right-board {\n  width: 350px;\n  position: absolute;\n  right: 0;\n  top: 0;\n  padding-top: 3px;\n  .field-box {\n    position: relative;\n    height: calc(100vh - 42px);\n    box-sizing: border-box;\n    overflow: hidden;\n  }\n  .el-scrollbar {\n    height: 100%;\n  }\n}\n.select-item {\n  display: flex;\n  border: 1px dashed #fff;\n  box-sizing: border-box;\n  & .close-btn {\n    cursor: pointer;\n    color: #f56c6c;\n  }\n  & .el-input + .el-input {\n    margin-left: 4px;\n  }\n}\n.select-item + .select-item {\n  margin-top: 4px;\n}\n.select-item.sortable-chosen {\n  border: 1px dashed #409eff;\n}\n.select-line-icon {\n  line-height: 32px;\n  font-size: 22px;\n  padding: 0 4px;\n  color: #777;\n}\n.option-drag {\n  cursor: move;\n}\n.time-range {\n  .el-date-editor {\n    width: 227px;\n  }\n  ::v-deep .el-icon-time {\n    display: none;\n  }\n}\n.document-link {\n  position: absolute;\n  display: block;\n  width: 26px;\n  height: 26px;\n  top: 0;\n  left: 0;\n  cursor: pointer;\n  background: #409eff;\n  z-index: 1;\n  border-radius: 0 0 6px 0;\n  text-align: center;\n  line-height: 26px;\n  color: #fff;\n  font-size: 18px;\n}\n.node-label{\n  font-size: 14px;\n}\n.node-icon{\n  color: #bebfc3;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AA8jBA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,aAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,eAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AACA,IAAAK,YAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,OAAA,GAAAN,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAAO,cAAA;EACAC,IAAA;EACAC,IAAA;EACAC,KAAA;EACAC,IAAA;EACAC,QAAA;EACAC,SAAA;EACAC,UAAA;EACAC,aAAA;AACA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,UAAA;IACAC,SAAA,EAAAA,qBAAA;IACAC,cAAA,EAAAA,uBAAA;IACAC,WAAA,EAAAA;EACA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,WAAA;MACAC,aAAA;MACAC,YAAA;MACAC,gBAAA;MACAC,eAAA,GACA;QACAC,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;MACAC,oBAAA,GACA;QACAF,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;MACAE,kBAAA,GACA;QACAH,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;MACAG,cAAA,GACA;QACAJ,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;MACAI,eAAA;QACAL,KAAA,WAAAA,MAAAP,IAAA,EAAAa,IAAA;UACA,OAAAb,IAAA,CAAAc,aAAA,OAAAC,MAAA,CAAAf,IAAA,CAAAO,KAAA,QAAAQ,MAAA,CAAAf,IAAA,CAAAgB,MAAA;QACA;MACA;IACA;EACA;EACAC,QAAA;IACAC,YAAA,WAAAA,aAAA;MACA,OACA,KAAAC,UAAA,CAAAC,QAAA,IACA;IAEA;IACAC,WAAA,WAAAA,YAAA;MACA,IACA,KAAAF,UAAA,CAAAG,IAAA,KAAAC,SAAA,IACA,KAAAJ,UAAA,CAAAK,GAAA,uBACA;QACA,SAAAL,UAAA,0BAAAI,SAAA;UACA,YAAAjB,eAAA;QACA;QACA,YAAAG,oBAAA;MACA;MACA;IACA;IACAgB,OAAA,WAAAA,QAAA;MACA,QACA;QACAlB,KAAA;QACAmB,OAAA,EAAAC;MACA,GACA;QACApB,KAAA;QACAmB,OAAA,EAAAE;MACA,EACA;IACA;EACA;EACAC,OAAA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAX,UAAA,CAAAY,OAAA,CAAAC,IAAA;QACAC,OAAA;QACAC,OAAA;MACA;IACA;IACAC,aAAA,WAAAA,cAAA;MACA,KAAAhB,UAAA,CAAAO,OAAA,CAAAM,IAAA;QACAzB,KAAA;QACAC,KAAA;MACA;IACA;IACA4B,WAAA,WAAAA,YAAA;MACA,OAAAC,QAAA;MACA,KAAAlC,aAAA;MACA,KAAAD,WAAA,QAAAiB,UAAA,CAAAO,OAAA;IACA;IACAY,aAAA,WAAAA,cAAAC,CAAA,EAAAC,IAAA;MAAA,IAAAC,KAAA;MAAA,IAAA5B,IAAA,GAAA2B,IAAA,CAAA3B,IAAA;QAAAb,IAAA,GAAAwC,IAAA,CAAAxC,IAAA;QAAA0C,KAAA,GAAAF,IAAA,CAAAE,KAAA;MACA,OAAAH,CAAA;QAAA,SACA;MAAA,IAAAA,CAAA,UACA1B,IAAA,CAAAN,KAAA,IAAAgC,CAAA;QAAA,SACA;MAAA,IAAAA,CAAA;QAAA;UAAA,SACA,SAAAI,MAAA;YAAA,OAAAF,KAAA,CAAAG,MAAA,CAAA5C,IAAA;UAAA;QAAA;QAAA,SACA;QAAA;UAAA,SACA;QAAA;MAAA,IAAAuC,CAAA;QAAA;UAAA,SAEA,SAAAI,MAAA;YAAA,OAAAF,KAAA,CAAAI,MAAA,CAAAhC,IAAA,EAAAb,IAAA;UAAA;QAAA;QAAA,SACA;QAAA;UAAA,SACA;QAAA;MAAA;IAKA;IACA4C,MAAA,WAAAA,OAAA5C,IAAA;MACA,KAAAA,IAAA,CAAA8C,QAAA;QACA,KAAAC,IAAA,CAAA/C,IAAA;MACA;MACA,KAAAG,aAAA;MACA,KAAAD,WAAA,GAAAF,IAAA,CAAA8C,QAAA;IACA;IACAD,MAAA,WAAAA,OAAAhC,IAAA,EAAAb,IAAA;MACA,IAAAgD,MAAA,GAAAnC,IAAA,CAAAmC,MAAA;MACA,IAAAF,QAAA,GAAAE,MAAA,CAAAhD,IAAA,CAAA8C,QAAA,IAAAE,MAAA,CAAAhD,IAAA;MACA,IAAAiD,KAAA,GAAAH,QAAA,CAAAI,SAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAC,EAAA,KAAApD,IAAA,CAAAoD,EAAA;MAAA;MACAN,QAAA,CAAAO,MAAA,CAAAJ,KAAA;IACA;IACAK,OAAA,WAAAA,QAAAtD,IAAA;MACA,KAAAE,WAAA,CAAA8B,IAAA,CAAAhC,IAAA;IACA;IACAuD,cAAA,WAAAA,eAAAC,IAAA,EAAAC,GAAA;MACAD,IAAA,CAAAhD,KAAA,OAAAkD,kBAAA,EAAAD,GAAA,KAAAA,GAAA,GAAAA,GAAA;IACA;IACAE,eAAA,WAAAA,gBAAAF,GAAA;MACA,IAAAG,KAAA,CAAAC,OAAA,CAAAJ,GAAA;QACA,OAAAA,GAAA,CAAAK,IAAA;MACA;MACA,yBAAAC,OAAA,CAAAN,GAAA;QACA,OAAAA,GAAA;MACA;MACA,WAAAA,GAAA;QACA,UAAA1C,MAAA,CAAA0C,GAAA;MACA;MACA,OAAAA,GAAA;IACA;IACAO,mBAAA,WAAAA,oBAAAC,GAAA;MACA,QAAAJ,aAAA,OAAA1C,UAAA,CAAA+C,YAAA;QACA;QACA,KAAAnB,IAAA,CACA,KAAA5B,UAAA,EACA,gBACA8C,GAAA,CAAAE,KAAA,MAAAC,GAAA,WAAAX,GAAA;UAAA,WAAAC,kBAAA,EAAAD,GAAA,KAAAA,GAAA,GAAAA,GAAA;QAAA,EACA;MACA,6BAAAM,OAAA,CAAAE,GAAA;QACA;QACA,KAAAlB,IAAA,MAAA5B,UAAA,kBAAAkD,IAAA,CAAAC,KAAA,CAAAL,GAAA;MACA;QACA;QACA,KAAAlB,IAAA,CACA,KAAA5B,UAAA,EACA,gBACA,IAAAuC,kBAAA,EAAAO,GAAA,KAAAA,GAAA,GAAAA,GACA;MACA;IACA;IACAM,kBAAA,WAAAA,mBAAAd,GAAA,EAAAe,IAAA;MACA,sBAAAT,OAAA,CAAAN,GAAA;QACA,KAAAV,IAAA,MAAA5B,UAAA,EAAAqD,IAAA,EAAAH,IAAA,CAAAC,KAAA,CAAAb,GAAA;MACA;QACA,KAAAV,IAAA,MAAA5B,UAAA,EAAAqD,IAAA,MAAAd,kBAAA,EAAAD,GAAA,KAAAA,GAAA,GAAAA,GAAA;MACA;IACA;IACAgB,YAAA,WAAAA,aAAAhB,GAAA,EAAAnC,IAAA;MACA,IAAAoD,WAAA,GAAApD,IAAA,cAAAvC,cAAA,CAAAC,IAAA,GAAAyE,GAAA;MACA,KAAAV,IAAA,MAAA5B,UAAA;MACA,KAAA4B,IAAA,MAAA5B,UAAA,kBAAAuD,WAAA;MACA,KAAA3B,IAAA,MAAA5B,UAAA,YAAAsC,GAAA;IACA;IACAkB,UAAA,WAAAA,WAAAlB,GAAA;MACA,KAAAmB,QAAA,CAAAC,IAAA,GAAApB,GAAA;IACA;IACAqB,cAAA,WAAAA,eAAArB,GAAA;MACA,KAAAV,IAAA,MAAA5B,UAAA,kBAAAsC,GAAA;IACA;IACAsB,cAAA,WAAAA,eAAAtB,GAAA;MACA,KAAAgB,YAAA,CAAA1F,cAAA,CAAA0E,GAAA,GAAAA,GAAA;IACA;IACAuB,WAAA,WAAAA,YAAAvB,GAAA;MACA,KAAAV,IAAA,CACA,KAAA5B,UAAA,EACA,gBACAsC,GAAA,SAAAtC,UAAA,CAAA8D,GAAA,OAAA9D,UAAA,CAAA+D,GAAA,SAAA/D,UAAA,CAAA8D,GACA;IACA;IACAE,cAAA,WAAAA,eAAA1B,GAAA;MACA,IAAAA,GAAA,OAAAtC,UAAA;IACA;IACAiE,eAAA,WAAAA,gBAAA3B,GAAA;MACA,IAAAA,GAAA,OAAAtC,UAAA;IACA;IACAkE,iBAAA,WAAAA,kBAAA5B,GAAA;MACA,KAAAtC,UAAA,CAAA+C,YAAA;MACA,KAAA/C,UAAA,iBAAAsC,GAAA,CAAAM,OAAA;MACA,KAAA5C,UAAA,CAAAmE,SAAA,QAAAC,IAAA;IACA;IACAC,eAAA,WAAAA,gBAAAC,KAAA;MACA,KAAArF,YAAA;MACA,KAAAC,gBAAA,GAAAoF,KAAA;IACA;IACAC,OAAA,WAAAA,QAAAjC,GAAA;MACA,KAAAtC,UAAA,MAAAd,gBAAA,IAAAoD,GAAA;IACA;IACAkC,SAAA,WAAAA,UAAAC,OAAA;MACA,IAAAC,MAAA,GAAAlE,uBAAA,CAAAmE,IAAA,WAAAtC,IAAA;QAAA,OAAAA,IAAA,CAAAoC,OAAA,KAAAA,OAAA;MAAA;MACA,KAAAC,MAAA,EAAAA,MAAA,GAAAjE,wBAAA,CAAAkE,IAAA,WAAAtC,IAAA;QAAA,OAAAA,IAAA,CAAAoC,OAAA,KAAAA,OAAA;MAAA;MACA,KAAAG,KAAA,eAAAF,MAAA;IACA;EACA;AACA", "ignoreList": []}]}