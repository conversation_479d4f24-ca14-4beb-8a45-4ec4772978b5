{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/blots/cursor.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/blots/cursor.js", "mtime": 1753510684085}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_parchment", "require", "_text", "_interopRequireDefault", "<PERSON><PERSON><PERSON>", "_EmbedBlot", "scroll", "domNode", "selection", "_this", "_classCallCheck2", "default", "_callSuper2", "textNode", "document", "createTextNode", "CONTENTS", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "_inherits2", "_createClass2", "key", "value", "detach", "parent", "<PERSON><PERSON><PERSON><PERSON>", "format", "name", "_superPropGet2", "target", "index", "statics", "scope", "<PERSON><PERSON>", "BLOCK_BLOT", "offset", "length", "optimize", "formatAt", "node", "position", "data", "remove", "restore", "composing", "range", "getNativeRange", "<PERSON><PERSON><PERSON><PERSON>", "parentNode", "insertBefore", "prevTextBlot", "prev", "TextBlot", "prevTextLength", "nextTextBlot", "next", "nextText", "text", "newText", "split", "join", "mergedTextBlot", "insertAt", "newTextNode", "create", "remapOffset", "start", "end", "startNode", "startOffset", "endNode", "endOffset", "update", "mutations", "context", "_this2", "some", "mutation", "type", "tagName", "isolate", "unwrap", "undefined", "EmbedBlot", "_defineProperty2", "_default", "exports"], "sources": ["../../src/blots/cursor.ts"], "sourcesContent": ["import { EmbedB<PERSON>, Scope } from 'parchment';\nimport type { Parent, ScrollBlot } from 'parchment';\nimport type Selection from '../core/selection.js';\nimport TextBlot from './text.js';\nimport type { EmbedContextRange } from './embed.js';\n\nclass Cursor extends EmbedBlot {\n  static blotName = 'cursor';\n  static className = 'ql-cursor';\n  static tagName = 'span';\n  static CONTENTS = '\\uFEFF'; // Zero width no break space\n\n  static value() {\n    return undefined;\n  }\n\n  selection: Selection;\n  textNode: Text;\n  savedLength: number;\n\n  constructor(scroll: ScrollBlot, domNode: HTMLElement, selection: Selection) {\n    super(scroll, domNode);\n    this.selection = selection;\n    this.textNode = document.createTextNode(Cursor.CONTENTS);\n    this.domNode.appendChild(this.textNode);\n    this.savedLength = 0;\n  }\n\n  detach() {\n    // super.detach() will also clear domNode.__blot\n    if (this.parent != null) this.parent.removeChild(this);\n  }\n\n  format(name: string, value: unknown) {\n    if (this.savedLength !== 0) {\n      super.format(name, value);\n      return;\n    }\n    // TODO: Fix this next time the file is edited.\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    let target: Parent | this = this;\n    let index = 0;\n    while (target != null && target.statics.scope !== Scope.BLOCK_BLOT) {\n      index += target.offset(target.parent);\n      target = target.parent;\n    }\n    if (target != null) {\n      this.savedLength = Cursor.CONTENTS.length;\n      // @ts-expect-error TODO: allow empty context in Parchment\n      target.optimize();\n      target.formatAt(index, Cursor.CONTENTS.length, name, value);\n      this.savedLength = 0;\n    }\n  }\n\n  index(node: Node, offset: number) {\n    if (node === this.textNode) return 0;\n    return super.index(node, offset);\n  }\n\n  length() {\n    return this.savedLength;\n  }\n\n  position(): [Text, number] {\n    return [this.textNode, this.textNode.data.length];\n  }\n\n  remove() {\n    super.remove();\n    // @ts-expect-error Fix me later\n    this.parent = null;\n  }\n\n  restore(): EmbedContextRange | null {\n    if (this.selection.composing || this.parent == null) return null;\n    const range = this.selection.getNativeRange();\n    // Browser may push down styles/nodes inside the cursor blot.\n    // https://dvcs.w3.org/hg/editing/raw-file/tip/editing.html#push-down-values\n    while (\n      this.domNode.lastChild != null &&\n      this.domNode.lastChild !== this.textNode\n    ) {\n      // @ts-expect-error Fix me later\n      this.domNode.parentNode.insertBefore(\n        this.domNode.lastChild,\n        this.domNode,\n      );\n    }\n\n    const prevTextBlot = this.prev instanceof TextBlot ? this.prev : null;\n    const prevTextLength = prevTextBlot ? prevTextBlot.length() : 0;\n    const nextTextBlot = this.next instanceof TextBlot ? this.next : null;\n    // @ts-expect-error TODO: make TextBlot.text public\n    const nextText = nextTextBlot ? nextTextBlot.text : '';\n    const { textNode } = this;\n    // take text from inside this blot and reset it\n    const newText = textNode.data.split(Cursor.CONTENTS).join('');\n    textNode.data = Cursor.CONTENTS;\n\n    // proactively merge TextBlots around cursor so that optimization\n    // doesn't lose the cursor.  the reason we are here in cursor.restore\n    // could be that the user clicked in prevTextBlot or nextTextBlot, or\n    // the user typed something.\n    let mergedTextBlot;\n    if (prevTextBlot) {\n      mergedTextBlot = prevTextBlot;\n      if (newText || nextTextBlot) {\n        prevTextBlot.insertAt(prevTextBlot.length(), newText + nextText);\n        if (nextTextBlot) {\n          nextTextBlot.remove();\n        }\n      }\n    } else if (nextTextBlot) {\n      mergedTextBlot = nextTextBlot;\n      nextTextBlot.insertAt(0, newText);\n    } else {\n      const newTextNode = document.createTextNode(newText);\n      mergedTextBlot = this.scroll.create(newTextNode);\n      this.parent.insertBefore(mergedTextBlot, this);\n    }\n\n    this.remove();\n    if (range) {\n      // calculate selection to restore\n      const remapOffset = (node: Node, offset: number) => {\n        if (prevTextBlot && node === prevTextBlot.domNode) {\n          return offset;\n        }\n        if (node === textNode) {\n          return prevTextLength + offset - 1;\n        }\n        if (nextTextBlot && node === nextTextBlot.domNode) {\n          return prevTextLength + newText.length + offset;\n        }\n        return null;\n      };\n\n      const start = remapOffset(range.start.node, range.start.offset);\n      const end = remapOffset(range.end.node, range.end.offset);\n      if (start !== null && end !== null) {\n        return {\n          startNode: mergedTextBlot.domNode,\n          startOffset: start,\n          endNode: mergedTextBlot.domNode,\n          endOffset: end,\n        };\n      }\n    }\n    return null;\n  }\n\n  update(mutations: MutationRecord[], context: Record<string, unknown>) {\n    if (\n      mutations.some((mutation) => {\n        return (\n          mutation.type === 'characterData' && mutation.target === this.textNode\n        );\n      })\n    ) {\n      const range = this.restore();\n      if (range) context.range = range;\n    }\n  }\n\n  // Avoid .ql-cursor being a descendant of `<a/>`.\n  // The reason is Safari pushes down `<a/>` on text insertion.\n  // That will cause DOM nodes not sync with the model.\n  //\n  // For example ({I} is the caret), given the markup:\n  //    <a><span class=\"ql-cursor\">\\uFEFF{I}</span></a>\n  // When typing a char \"x\", `<a/>` will be pushed down inside the `<span>` first:\n  //    <span class=\"ql-cursor\"><a>\\uFEFF{I}</a></span>\n  // And then \"x\" will be inserted after `<a/>`:\n  //    <span class=\"ql-cursor\"><a>\\uFEFF</a>d{I}</span>\n  optimize(context?: unknown) {\n    // @ts-expect-error Fix me later\n    super.optimize(context);\n\n    let { parent } = this;\n    while (parent) {\n      if (parent.domNode.tagName === 'A') {\n        this.savedLength = Cursor.CONTENTS.length;\n        // @ts-expect-error TODO: make isolate generic\n        parent.isolate(this.offset(parent), this.length()).unwrap();\n        this.savedLength = 0;\n        break;\n      }\n      parent = parent.parent;\n    }\n  }\n\n  value() {\n    return '';\n  }\n}\n\nexport default Cursor;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAGA,IAAAC,KAAA,GAAAC,sBAAA,CAAAF,OAAA;AAAgC,IAG1BG,MAAM,0BAAAC,UAAA;EAcV,SAAAD,OAAYE,MAAkB,EAAEC,OAAoB,EAAEC,SAAoB,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAP,MAAA;IAC1EK,KAAA,OAAAG,WAAA,CAAAD,OAAA,QAAAP,MAAA,GAAME,MAAM,EAAEC,OAAO;IACrBE,KAAA,CAAKD,SAAS,GAAGA,SAAS;IAC1BC,KAAA,CAAKI,QAAQ,GAAGC,QAAQ,CAACC,cAAc,CAACX,MAAM,CAACY,QAAQ,CAAC;IACxDP,KAAA,CAAKF,OAAO,CAACU,WAAW,CAACR,KAAA,CAAKI,QAAQ,CAAC;IACvCJ,KAAA,CAAKS,WAAW,GAAG,CAAC;IAAA,OAAAT,KAAA;EACtB;EAAA,IAAAU,UAAA,CAAAR,OAAA,EAAAP,MAAA,EAAAC,UAAA;EAAA,WAAAe,aAAA,CAAAT,OAAA,EAAAP,MAAA;IAAAiB,GAAA;IAAAC,KAAA,EAEA,SAAAC,MAAMA,CAAA,EAAG;MACP;MACA,IAAI,IAAI,CAACC,MAAM,IAAI,IAAI,EAAE,IAAI,CAACA,MAAM,CAACC,WAAW,CAAC,IAAI,CAAC;IACxD;EAAA;IAAAJ,GAAA;IAAAC,KAAA,EAEA,SAAAI,MAAMA,CAACC,IAAY,EAAEL,KAAc,EAAE;MACnC,IAAI,IAAI,CAACJ,WAAW,KAAK,CAAC,EAAE;QAC1B,IAAAU,cAAA,CAAAjB,OAAA,EAAAP,MAAA,sBAAauB,IAAI,EAAEL,KAAK;QACxB;MACF;MACA;MACA;MACA,IAAIO,MAAqB,GAAG,IAAI;MAChC,IAAIC,KAAK,GAAG,CAAC;MACb,OAAOD,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACE,OAAO,CAACC,KAAK,KAAKC,gBAAK,CAACC,UAAU,EAAE;QAClEJ,KAAK,IAAID,MAAM,CAACM,MAAM,CAACN,MAAM,CAACL,MAAM,CAAC;QACrCK,MAAM,GAAGA,MAAM,CAACL,MAAM;MACxB;MACA,IAAIK,MAAM,IAAI,IAAI,EAAE;QAClB,IAAI,CAACX,WAAW,GAAGd,MAAM,CAACY,QAAQ,CAACoB,MAAM;QACzC;QACAP,MAAM,CAACQ,QAAQ,CAAC,CAAC;QACjBR,MAAM,CAACS,QAAQ,CAACR,KAAK,EAAE1B,MAAM,CAACY,QAAQ,CAACoB,MAAM,EAAET,IAAI,EAAEL,KAAK,CAAC;QAC3D,IAAI,CAACJ,WAAW,GAAG,CAAC;MACtB;IACF;EAAA;IAAAG,GAAA;IAAAC,KAAA,EAEA,SAAAQ,KAAKA,CAACS,IAAU,EAAEJ,MAAc,EAAE;MAChC,IAAII,IAAI,KAAK,IAAI,CAAC1B,QAAQ,EAAE,OAAO,CAAC;MACpC,WAAAe,cAAA,CAAAjB,OAAA,EAAAP,MAAA,qBAAmBmC,IAAI,EAAEJ,MAAM;IACjC;EAAA;IAAAd,GAAA;IAAAC,KAAA,EAEA,SAAAc,MAAMA,CAAA,EAAG;MACP,OAAO,IAAI,CAAClB,WAAW;IACzB;EAAA;IAAAG,GAAA;IAAAC,KAAA,EAEA,SAAAkB,QAAQA,CAAA,EAAmB;MACzB,OAAO,CAAC,IAAI,CAAC3B,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAAC4B,IAAI,CAACL,MAAM,CAAC;IACnD;EAAA;IAAAf,GAAA;IAAAC,KAAA,EAEA,SAAAoB,MAAMA,CAAA,EAAG;MACP,IAAAd,cAAA,CAAAjB,OAAA,EAAAP,MAAA;MACA;MACA,IAAI,CAACoB,MAAM,GAAG,IAAI;IACpB;EAAA;IAAAH,GAAA;IAAAC,KAAA,EAEA,SAAAqB,OAAOA,CAAA,EAA6B;MAClC,IAAI,IAAI,CAACnC,SAAS,CAACoC,SAAS,IAAI,IAAI,CAACpB,MAAM,IAAI,IAAI,EAAE,OAAO,IAAI;MAChE,IAAMqB,KAAK,GAAG,IAAI,CAACrC,SAAS,CAACsC,cAAc,CAAC,CAAC;MAC7C;MACA;MACA,OACE,IAAI,CAACvC,OAAO,CAACwC,SAAS,IAAI,IAAI,IAC9B,IAAI,CAACxC,OAAO,CAACwC,SAAS,KAAK,IAAI,CAAClC,QAAQ,EACxC;QACA;QACA,IAAI,CAACN,OAAO,CAACyC,UAAU,CAACC,YAAY,CAClC,IAAI,CAAC1C,OAAO,CAACwC,SAAS,EACtB,IAAI,CAACxC,OACP,CAAC;MACH;MAEA,IAAM2C,YAAY,GAAG,IAAI,CAACC,IAAI,YAAYC,aAAQ,GAAG,IAAI,CAACD,IAAI,GAAG,IAAI;MACrE,IAAME,cAAc,GAAGH,YAAY,GAAGA,YAAY,CAACd,MAAM,CAAC,CAAC,GAAG,CAAC;MAC/D,IAAMkB,YAAY,GAAG,IAAI,CAACC,IAAI,YAAYH,aAAQ,GAAG,IAAI,CAACG,IAAI,GAAG,IAAI;MACrE;MACA,IAAMC,QAAQ,GAAGF,YAAY,GAAGA,YAAY,CAACG,IAAI,GAAG,EAAE;MACtD,IAAQ5C,QAAA,GAAa,IAAI,CAAjBA,QAAA;MACR;MACA,IAAM6C,OAAO,GAAG7C,QAAQ,CAAC4B,IAAI,CAACkB,KAAK,CAACvD,MAAM,CAACY,QAAQ,CAAC,CAAC4C,IAAI,CAAC,EAAE,CAAC;MAC7D/C,QAAQ,CAAC4B,IAAI,GAAGrC,MAAM,CAACY,QAAQ;;MAE/B;MACA;MACA;MACA;MACA,IAAI6C,cAAc;MAClB,IAAIX,YAAY,EAAE;QAChBW,cAAc,GAAGX,YAAY;QAC7B,IAAIQ,OAAO,IAAIJ,YAAY,EAAE;UAC3BJ,YAAY,CAACY,QAAQ,CAACZ,YAAY,CAACd,MAAM,CAAC,CAAC,EAAEsB,OAAO,GAAGF,QAAQ,CAAC;UAChE,IAAIF,YAAY,EAAE;YAChBA,YAAY,CAACZ,MAAM,CAAC,CAAC;UACvB;QACF;MACF,CAAC,MAAM,IAAIY,YAAY,EAAE;QACvBO,cAAc,GAAGP,YAAY;QAC7BA,YAAY,CAACQ,QAAQ,CAAC,CAAC,EAAEJ,OAAO,CAAC;MACnC,CAAC,MAAM;QACL,IAAMK,WAAW,GAAGjD,QAAQ,CAACC,cAAc,CAAC2C,OAAO,CAAC;QACpDG,cAAc,GAAG,IAAI,CAACvD,MAAM,CAAC0D,MAAM,CAACD,WAAW,CAAC;QAChD,IAAI,CAACvC,MAAM,CAACyB,YAAY,CAACY,cAAc,EAAE,IAAI,CAAC;MAChD;MAEA,IAAI,CAACnB,MAAM,CAAC,CAAC;MACb,IAAIG,KAAK,EAAE;QACT;QACA,IAAMoB,WAAW,GAAG,SAAdA,WAAWA,CAAI1B,IAAU,EAAEJ,MAAc,EAAK;UAClD,IAAIe,YAAY,IAAIX,IAAI,KAAKW,YAAY,CAAC3C,OAAO,EAAE;YACjD,OAAO4B,MAAM;UACf;UACA,IAAII,IAAI,KAAK1B,QAAQ,EAAE;YACrB,OAAOwC,cAAc,GAAGlB,MAAM,GAAG,CAAC;UACpC;UACA,IAAImB,YAAY,IAAIf,IAAI,KAAKe,YAAY,CAAC/C,OAAO,EAAE;YACjD,OAAO8C,cAAc,GAAGK,OAAO,CAACtB,MAAM,GAAGD,MAAM;UACjD;UACA,OAAO,IAAI;QACb,CAAC;QAED,IAAM+B,KAAK,GAAGD,WAAW,CAACpB,KAAK,CAACqB,KAAK,CAAC3B,IAAI,EAAEM,KAAK,CAACqB,KAAK,CAAC/B,MAAM,CAAC;QAC/D,IAAMgC,GAAG,GAAGF,WAAW,CAACpB,KAAK,CAACsB,GAAG,CAAC5B,IAAI,EAAEM,KAAK,CAACsB,GAAG,CAAChC,MAAM,CAAC;QACzD,IAAI+B,KAAK,KAAK,IAAI,IAAIC,GAAG,KAAK,IAAI,EAAE;UAClC,OAAO;YACLC,SAAS,EAAEP,cAAc,CAACtD,OAAO;YACjC8D,WAAW,EAAEH,KAAK;YAClBI,OAAO,EAAET,cAAc,CAACtD,OAAO;YAC/BgE,SAAS,EAAEJ;UACb,CAAC;QACH;MACF;MACA,OAAO,IAAI;IACb;EAAA;IAAA9C,GAAA;IAAAC,KAAA,EAEA,SAAAkD,MAAMA,CAACC,SAA2B,EAAEC,OAAgC,EAAE;MAAA,IAAAC,MAAA;MACpE,IACEF,SAAS,CAACG,IAAI,CAAE,UAAAC,QAAQ,EAAK;QAC3B,OACEA,QAAQ,CAACC,IAAI,KAAK,eAAe,IAAID,QAAQ,CAAChD,MAAM,KAAK8C,MAAI,CAAC9D,QAAQ;MAE1E,CAAC,CAAC,EACF;QACA,IAAMgC,KAAK,GAAG,IAAI,CAACF,OAAO,CAAC,CAAC;QAC5B,IAAIE,KAAK,EAAE6B,OAAO,CAAC7B,KAAK,GAAGA,KAAK;MAClC;IACF;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA;IAAAxB,GAAA;IAAAC,KAAA,EACA,SAAAe,QAAQA,CAACqC,OAAiB,EAAE;MAC1B;MACA,IAAA9C,cAAA,CAAAjB,OAAA,EAAAP,MAAA,wBAAesE,OAAO;MAEtB,IAAMlD,MAAA,GAAW,IAAI,CAAfA,MAAA;MACN,OAAOA,MAAM,EAAE;QACb,IAAIA,MAAM,CAACjB,OAAO,CAACwE,OAAO,KAAK,GAAG,EAAE;UAClC,IAAI,CAAC7D,WAAW,GAAGd,MAAM,CAACY,QAAQ,CAACoB,MAAM;UACzC;UACAZ,MAAM,CAACwD,OAAO,CAAC,IAAI,CAAC7C,MAAM,CAACX,MAAM,CAAC,EAAE,IAAI,CAACY,MAAM,CAAC,CAAC,CAAC,CAAC6C,MAAM,CAAC,CAAC;UAC3D,IAAI,CAAC/D,WAAW,GAAG,CAAC;UACpB;QACF;QACAM,MAAM,GAAGA,MAAM,CAACA,MAAM;MACxB;IACF;EAAA;IAAAH,GAAA;IAAAC,KAAA,EAEA,SAAAA,KAAKA,CAAA,EAAG;MACN,OAAO,EAAE;IACX;EAAA;IAAAD,GAAA;IAAAC,KAAA;IAxL4B;;IAE5B,SAAOA,KAAKA,CAAA,EAAG;MACb,OAAO4D,SAAS;IAClB;EAAA;AAAA,EARmBC,oBAAS;AAAA,IAAAC,gBAAA,CAAAzE,OAAA,EAAxBP,MAAM,cACQ,QAAQ;AAAA,IAAAgF,gBAAA,CAAAzE,OAAA,EADtBP,MAAM,eAES,WAAW;AAAA,IAAAgF,gBAAA,CAAAzE,OAAA,EAF1BP,MAAM,aAGO,MAAM;AAAA,IAAAgF,gBAAA,CAAAzE,OAAA,EAHnBP,MAAM,cAIQ,QAAQ;AAAA,IAAAiF,QAAA,GAAAC,OAAA,CAAA3E,OAAA,GA2LbP,MAAM", "ignoreList": []}]}