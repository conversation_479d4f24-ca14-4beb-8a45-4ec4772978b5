{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/result/quarterResult/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/result/quarterResult/index.vue", "mtime": 1753510684535}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_quarterlyResult", "require", "_default", "exports", "default", "name", "data", "loading", "categoryList", "resultList", "goodGradeList", "queryParams", "year", "Date", "getFullYear", "quarter", "Math", "floor", "getMonth", "created", "getResultList", "getGoodGradeList", "methods", "_this", "listQuarterlyResult", "then", "res", "code", "_toConsumableArray2", "Set", "map", "item", "category", "_this2", "listQuarterlyGoodGrade", "handleQuery", "reset<PERSON><PERSON>y"], "sources": ["src/views/result/quarterResult/index.vue"], "sourcesContent": ["<script>\nimport { listQuarterlyResult, listQuarterlyGoodGrade } from '@/api/performance/quarterlyResult'\n\nexport default {\n  name: 'QuarterResult',\n  data() {\n    return {\n      // 加载状态\n      loading: true,\n      // 分类列表\n      categoryList: [],\n      // 季度结果列表（按分类排序）\n      resultList: [],\n      // \"好\"等次人员列表\n      goodGradeList: [],\n      // 查询参数\n      queryParams: {\n        year: new Date().getFullYear(),\n        quarter: Math.floor((new Date().getMonth() + 3) / 3)\n      }\n    }\n  },\n  created() {\n    this.getResultList()\n    this.getGoodGradeList()\n  },\n  methods: {\n    // 获取季度结果列表（按分类排序）\n    getResultList() {\n      this.loading = true\n      listQuarterlyResult(this.queryParams).then(res => {\n        if (res.code === 200) {\n          this.resultList = res.data || []\n          // 提取所有分类\n          this.categoryList = [...new Set(this.resultList.map(item => item.category))]\n        }\n        this.loading = false\n      })\n    },\n    // 获取\"好\"等次人员列表\n    getGoodGradeList() {\n      listQuarterlyGoodGrade(this.queryParams).then(res => {\n        if (res.code === 200) {\n          this.goodGradeList = res.data || []\n        }\n      })\n    },\n    // 按年度查询\n    handleQuery() {\n      this.getResultList()\n      this.getGoodGradeList()\n    },\n    // 重置查询\n    resetQuery() {\n      this.queryParams = {\n        year: new Date().getFullYear(),\n        quarter: Math.floor((new Date().getMonth() + 3) / 3)\n      }\n      this.handleQuery()\n    }\n  }\n}\n</script>\n\n<template>\n  <div class=\"app-container\">\n    <!-- 查询区域 -->\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" class=\"search-form\">\n      <el-form-item label=\"年度\">\n        <el-input v-model=\"queryParams.year\" placeholder=\"请输入年度\" clearable type=\"number\" />\n      </el-form-item>\n      <el-form-item label=\"季度\">\n        <el-select v-model=\"queryParams.quarter\" placeholder=\"请选择季度\" clearable>\n          <el-option label=\"第一季度\" :value=\"1\" />\n          <el-option label=\"第二季度\" :value=\"2\" />\n          <el-option label=\"第三季度\" :value=\"3\" />\n          <el-option label=\"第四季度\" :value=\"4\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" @click=\"handleQuery\">查询</el-button>\n        <el-button @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <!-- 分类排序展示区域 -->\n    <el-card class=\"box-card\" v-loading=\"loading\">\n      <div slot=\"header\" class=\"card-header\">\n        <span>{{ queryParams.year }}年第{{ queryParams.quarter }}季度绩效结果分类排序</span>\n      </div>\n      <div v-for=\"category in categoryList\" :key=\"category\" class=\"category-section\">\n        <h3>{{ category }}</h3>\n        <el-table :data=\"resultList.filter(item => item.category === category)\" border style=\"width: 100%\">\n          <el-table-column prop=\"deptName\" label=\"科室\" />\n          <el-table-column prop=\"position\" label=\"职务\" />\n          <el-table-column prop=\"userName\" label=\"姓名\" />\n          <el-table-column prop=\"quarterScore\" label=\"季度得分\" />\n          <el-table-column prop=\"sameLevelOrder\" label=\"同层级排序\" />\n          <el-table-column prop=\"grade\" label=\"等次\" />\n        </el-table>\n      </div>\n    </el-card>\n\n    <!-- \"好\"等次人员名单 -->\n    <el-card class=\"box-card\" style=\"margin-top: 20px;\">\n      <div slot=\"header\" class=\"card-header\">\n        <span>{{ queryParams.year }}年第{{ queryParams.quarter }}季度\"好\"等次人员名单</span>\n      </div>\n      <el-table :data=\"goodGradeList\" border style=\"width: 100%\">\n        <el-table-column prop=\"deptName\" label=\"科室\" />\n        <el-table-column prop=\"position\" label=\"职务\" />\n        <el-table-column prop=\"category\" label=\"分类\" />\n        <el-table-column prop=\"userName\" label=\"姓名\" />\n        <el-table-column prop=\"quarterScore\" label=\"季度得分\" />\n        <el-table-column prop=\"sameLevelOrder\" label=\"同层级排序\" />\n      </el-table>\n    </el-card>\n  </div>\n</template>\n\n<style scoped lang=\"scss\">\n.search-form {\n  margin-bottom: 20px;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.category-section {\n  margin-bottom: 20px;\n\n  h3 {\n    margin-bottom: 10px;\n    padding-left: 5px;\n    border-left: 4px solid #409EFF;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AACA,IAAAA,gBAAA,GAAAC,OAAA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,YAAA;MACA;MACAC,UAAA;MACA;MACAC,aAAA;MACA;MACAC,WAAA;QACAC,IAAA,MAAAC,IAAA,GAAAC,WAAA;QACAC,OAAA,EAAAC,IAAA,CAAAC,KAAA,MAAAJ,IAAA,GAAAK,QAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,aAAA;IACA,KAAAC,gBAAA;EACA;EACAC,OAAA;IACA;IACAF,aAAA,WAAAA,cAAA;MAAA,IAAAG,KAAA;MACA,KAAAhB,OAAA;MACA,IAAAiB,oCAAA,OAAAb,WAAA,EAAAc,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAJ,KAAA,CAAAd,UAAA,GAAAiB,GAAA,CAAApB,IAAA;UACA;UACAiB,KAAA,CAAAf,YAAA,OAAAoB,mBAAA,CAAAxB,OAAA,MAAAyB,GAAA,CAAAN,KAAA,CAAAd,UAAA,CAAAqB,GAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAAC,QAAA;UAAA;QACA;QACAT,KAAA,CAAAhB,OAAA;MACA;IACA;IACA;IACAc,gBAAA,WAAAA,iBAAA;MAAA,IAAAY,MAAA;MACA,IAAAC,uCAAA,OAAAvB,WAAA,EAAAc,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAM,MAAA,CAAAvB,aAAA,GAAAgB,GAAA,CAAApB,IAAA;QACA;MACA;IACA;IACA;IACA6B,WAAA,WAAAA,YAAA;MACA,KAAAf,aAAA;MACA,KAAAC,gBAAA;IACA;IACA;IACAe,UAAA,WAAAA,WAAA;MACA,KAAAzB,WAAA;QACAC,IAAA,MAAAC,IAAA,GAAAC,WAAA;QACAC,OAAA,EAAAC,IAAA,CAAAC,KAAA,MAAAJ,IAAA,GAAAK,QAAA;MACA;MACA,KAAAiB,WAAA;IACA;EACA;AACA", "ignoreList": []}]}