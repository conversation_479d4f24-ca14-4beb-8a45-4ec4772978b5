{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/ImageUpload/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/ImageUpload/index.vue", "mtime": 1753510684527}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_auth", "require", "_validate", "_sortablejs", "_interopRequireDefault", "props", "value", "String", "Object", "Array", "action", "type", "default", "data", "limit", "Number", "fileSize", "fileType", "isShowTip", "Boolean", "disabled", "drag", "number", "uploadList", "dialogImageUrl", "dialogVisible", "hideUpload", "baseUrl", "process", "env", "VUE_APP_BASE_API", "uploadImgUrl", "headers", "Authorization", "getToken", "fileList", "mounted", "_this", "$nextTick", "_this$$refs$imageUplo", "element", "$refs", "imageUpload", "$el", "querySelector", "Sortable", "create", "onEnd", "evt", "movedItem", "splice", "oldIndex", "newIndex", "$emit", "listToString", "watch", "handler", "val", "_this2", "list", "isArray", "split", "map", "item", "indexOf", "isExternal", "name", "url", "deep", "immediate", "computed", "showTip", "methods", "handleBeforeUpload", "file", "isImg", "length", "fileExtension", "lastIndexOf", "slice", "some", "$modal", "msgError", "concat", "join", "includes", "isLt", "size", "loading", "handleExceed", "handleUploadSuccess", "res", "code", "push", "fileName", "uploadedSuccessfully", "closeLoading", "msg", "handleRemove", "handleDelete", "findex", "f", "handleUploadError", "handlePictureCardPreview", "separator", "strs", "i", "replace", "substr"], "sources": ["src/components/ImageUpload/index.vue"], "sourcesContent": ["<template>\n  <div class=\"component-upload-image\">\n    <el-upload\n      multiple\n      :disabled=\"disabled\"\n      :action=\"uploadImgUrl\"\n      list-type=\"picture-card\"\n      :on-success=\"handleUploadSuccess\"\n      :before-upload=\"handleBeforeUpload\"\n      :data=\"data\"\n      :limit=\"limit\"\n      :on-error=\"handleUploadError\"\n      :on-exceed=\"handleExceed\"\n      ref=\"imageUpload\"\n      :on-remove=\"handleDelete\"\n      :show-file-list=\"true\"\n      :headers=\"headers\"\n      :file-list=\"fileList\"\n      :on-preview=\"handlePictureCardPreview\"\n      :class=\"{hide: this.fileList.length >= this.limit}\"\n    >\n      <i class=\"el-icon-plus\"></i>\n    </el-upload>\n\n    <!-- 上传提示 -->\n    <div class=\"el-upload__tip\" slot=\"tip\" v-if=\"showTip && !disabled\">\n      请上传\n      <template v-if=\"fileSize\"> 大小不超过 <b style=\"color: #f56c6c\">{{ fileSize }}MB</b> </template>\n      <template v-if=\"fileType\"> 格式为 <b style=\"color: #f56c6c\">{{ fileType.join(\"/\") }}</b> </template>\n      的文件\n    </div>\n\n    <el-dialog\n      :visible.sync=\"dialogVisible\"\n      title=\"预览\"\n      width=\"800\"\n      append-to-body\n    >\n      <img\n        :src=\"dialogImageUrl\"\n        style=\"display: block; max-width: 100%; margin: 0 auto\"\n      />\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\"\nimport { isExternal } from \"@/utils/validate\"\nimport Sortable from 'sortablejs'\n\nexport default {\n  props: {\n    value: [String, Object, Array],\n    // 上传接口地址\n    action: {\n      type: String,\n      default: \"/common/upload\"\n    },\n    // 上传携带的参数\n    data: {\n      type: Object\n    },\n    // 图片数量限制\n    limit: {\n      type: Number,\n      default: 5\n    },\n    // 大小限制(MB)\n    fileSize: {\n       type: Number,\n      default: 5\n    },\n    // 文件类型, 例如['png', 'jpg', 'jpeg']\n    fileType: {\n      type: Array,\n      default: () => [\"png\", \"jpg\", \"jpeg\"]\n    },\n    // 是否显示提示\n    isShowTip: {\n      type: Boolean,\n      default: true\n    },\n    // 禁用组件（仅查看图片）\n    disabled: {\n      type: Boolean,\n      default: false\n    },\n    // 拖动排序\n    drag: {\n      type: Boolean,\n      default: true\n    }\n  },\n  data() {\n    return {\n      number: 0,\n      uploadList: [],\n      dialogImageUrl: \"\",\n      dialogVisible: false,\n      hideUpload: false,\n      baseUrl: process.env.VUE_APP_BASE_API,\n      uploadImgUrl: process.env.VUE_APP_BASE_API + this.action, // 上传的图片服务器地址\n      headers: {\n        Authorization: \"Bearer \" + getToken(),\n      },\n      fileList: []\n    }\n  },\n  mounted() {\n    if (this.drag && !this.disabled) {\n      this.$nextTick(() => {\n        const element = this.$refs.imageUpload?.$el?.querySelector('.el-upload-list')\n        Sortable.create(element, {\n          onEnd: (evt) => {\n            const movedItem = this.fileList.splice(evt.oldIndex, 1)[0]\n            this.fileList.splice(evt.newIndex, 0, movedItem)\n            this.$emit(\"input\", this.listToString(this.fileList))\n          }\n        })\n      })\n    }\n  },\n  watch: {\n    value: {\n      handler(val) {\n        if (val) {\n          // 首先将值转为数组\n          const list = Array.isArray(val) ? val : this.value.split(',')\n          // 然后将数组转为对象数组\n          this.fileList = list.map(item => {\n            if (typeof item === \"string\") {\n              if (item.indexOf(this.baseUrl) === -1 && !isExternal(item)) {\n                  item = { name: this.baseUrl + item, url: this.baseUrl + item }\n              } else {\n                  item = { name: item, url: item }\n              }\n            }\n            return item\n          })\n        } else {\n          this.fileList = []\n          return []\n        }\n      },\n      deep: true,\n      immediate: true\n    }\n  },\n  computed: {\n    // 是否显示提示\n    showTip() {\n      return this.isShowTip && (this.fileType || this.fileSize)\n    },\n  },\n  methods: {\n    // 上传前loading加载\n    handleBeforeUpload(file) {\n      let isImg = false\n      if (this.fileType.length) {\n        let fileExtension = \"\"\n        if (file.name.lastIndexOf(\".\") > -1) {\n          fileExtension = file.name.slice(file.name.lastIndexOf(\".\") + 1)\n        }\n        isImg = this.fileType.some(type => {\n          if (file.type.indexOf(type) > -1) return true\n          if (fileExtension && fileExtension.indexOf(type) > -1) return true\n          return false\n        })\n      } else {\n        isImg = file.type.indexOf(\"image\") > -1\n      }\n\n      if (!isImg) {\n        this.$modal.msgError(`文件格式不正确，请上传${this.fileType.join(\"/\")}图片格式文件!`)\n        return false\n      }\n      if (file.name.includes(',')) {\n        this.$modal.msgError('文件名不正确，不能包含英文逗号!')\n        return false\n      }\n      if (this.fileSize) {\n        const isLt = file.size / 1024 / 1024 < this.fileSize\n        if (!isLt) {\n          this.$modal.msgError(`上传头像图片大小不能超过 ${this.fileSize} MB!`)\n          return false\n        }\n      }\n      this.$modal.loading(\"正在上传图片，请稍候...\")\n      this.number++\n    },\n    // 文件个数超出\n    handleExceed() {\n      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`)\n    },\n    // 上传成功回调\n    handleUploadSuccess(res, file) {\n      if (res.code === 200) {\n        this.uploadList.push({ name: res.fileName, url: res.fileName })\n        this.uploadedSuccessfully()\n      } else {\n        this.number--\n        this.$modal.closeLoading()\n        this.$modal.msgError(res.msg)\n        this.$refs.imageUpload.handleRemove(file)\n        this.uploadedSuccessfully()\n      }\n    },\n    // 删除图片\n    handleDelete(file) {\n      const findex = this.fileList.map(f => f.name).indexOf(file.name)\n      if (findex > -1) {\n        this.fileList.splice(findex, 1)\n        this.$emit(\"input\", this.listToString(this.fileList))\n      }\n    },\n    // 上传失败\n    handleUploadError() {\n      this.$modal.msgError(\"上传图片失败，请重试\")\n      this.$modal.closeLoading()\n    },\n    // 上传结束处理\n    uploadedSuccessfully() {\n      if (this.number > 0 && this.uploadList.length === this.number) {\n        this.fileList = this.fileList.concat(this.uploadList)\n        this.uploadList = []\n        this.number = 0\n        this.$emit(\"input\", this.listToString(this.fileList))\n        this.$modal.closeLoading()\n      }\n    },\n    // 预览\n    handlePictureCardPreview(file) {\n      this.dialogImageUrl = file.url\n      this.dialogVisible = true\n    },\n    // 对象转成指定字符串分隔\n    listToString(list, separator) {\n      let strs = \"\"\n      separator = separator || \",\"\n      for (let i in list) {\n        if (list[i].url) {\n          strs += list[i].url.replace(this.baseUrl, \"\") + separator\n        }\n      }\n      return strs != '' ? strs.substr(0, strs.length - 1) : ''\n    }\n  }\n}\n</script>\n<style scoped lang=\"scss\">\n// .el-upload--picture-card 控制加号部分\n::v-deep.hide .el-upload--picture-card {\n  display: none;\n}\n\n::v-deep .el-upload-list--picture-card.is-disabled + .el-upload--picture-card {\n  display: none !important;\n} \n\n// 去掉动画效果\n::v-deep .el-list-enter-active,\n::v-deep .el-list-leave-active {\n  transition: all 0s;\n}\n\n::v-deep .el-list-enter, .el-list-leave-active {\n  opacity: 0;\n  transform: translateY(0);\n}\n</style>\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AA+CA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,WAAA,GAAAC,sBAAA,CAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAI,KAAA;IACAC,KAAA,GAAAC,MAAA,EAAAC,MAAA,EAAAC,KAAA;IACA;IACAC,MAAA;MACAC,IAAA,EAAAJ,MAAA;MACAK,OAAA;IACA;IACA;IACAC,IAAA;MACAF,IAAA,EAAAH;IACA;IACA;IACAM,KAAA;MACAH,IAAA,EAAAI,MAAA;MACAH,OAAA;IACA;IACA;IACAI,QAAA;MACAL,IAAA,EAAAI,MAAA;MACAH,OAAA;IACA;IACA;IACAK,QAAA;MACAN,IAAA,EAAAF,KAAA;MACAG,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACA;IACAM,SAAA;MACAP,IAAA,EAAAQ,OAAA;MACAP,OAAA;IACA;IACA;IACAQ,QAAA;MACAT,IAAA,EAAAQ,OAAA;MACAP,OAAA;IACA;IACA;IACAS,IAAA;MACAV,IAAA,EAAAQ,OAAA;MACAP,OAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAS,MAAA;MACAC,UAAA;MACAC,cAAA;MACAC,aAAA;MACAC,UAAA;MACAC,OAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACAC,YAAA,EAAAH,OAAA,CAAAC,GAAA,CAAAC,gBAAA,QAAApB,MAAA;MAAA;MACAsB,OAAA;QACAC,aAAA,kBAAAC,cAAA;MACA;MACAC,QAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,SAAAhB,IAAA,UAAAD,QAAA;MACA,KAAAkB,SAAA;QAAA,IAAAC,qBAAA;QACA,IAAAC,OAAA,IAAAD,qBAAA,GAAAF,KAAA,CAAAI,KAAA,CAAAC,WAAA,cAAAH,qBAAA,gBAAAA,qBAAA,GAAAA,qBAAA,CAAAI,GAAA,cAAAJ,qBAAA,uBAAAA,qBAAA,CAAAK,aAAA;QACAC,mBAAA,CAAAC,MAAA,CAAAN,OAAA;UACAO,KAAA,WAAAA,MAAAC,GAAA;YACA,IAAAC,SAAA,GAAAZ,KAAA,CAAAF,QAAA,CAAAe,MAAA,CAAAF,GAAA,CAAAG,QAAA;YACAd,KAAA,CAAAF,QAAA,CAAAe,MAAA,CAAAF,GAAA,CAAAI,QAAA,KAAAH,SAAA;YACAZ,KAAA,CAAAgB,KAAA,UAAAhB,KAAA,CAAAiB,YAAA,CAAAjB,KAAA,CAAAF,QAAA;UACA;QACA;MACA;IACA;EACA;EACAoB,KAAA;IACAjD,KAAA;MACAkD,OAAA,WAAAA,QAAAC,GAAA;QAAA,IAAAC,MAAA;QACA,IAAAD,GAAA;UACA;UACA,IAAAE,IAAA,GAAAlD,KAAA,CAAAmD,OAAA,CAAAH,GAAA,IAAAA,GAAA,QAAAnD,KAAA,CAAAuD,KAAA;UACA;UACA,KAAA1B,QAAA,GAAAwB,IAAA,CAAAG,GAAA,WAAAC,IAAA;YACA,WAAAA,IAAA;cACA,IAAAA,IAAA,CAAAC,OAAA,CAAAN,MAAA,CAAA/B,OAAA,iBAAAsC,oBAAA,EAAAF,IAAA;gBACAA,IAAA;kBAAAG,IAAA,EAAAR,MAAA,CAAA/B,OAAA,GAAAoC,IAAA;kBAAAI,GAAA,EAAAT,MAAA,CAAA/B,OAAA,GAAAoC;gBAAA;cACA;gBACAA,IAAA;kBAAAG,IAAA,EAAAH,IAAA;kBAAAI,GAAA,EAAAJ;gBAAA;cACA;YACA;YACA,OAAAA,IAAA;UACA;QACA;UACA,KAAA5B,QAAA;UACA;QACA;MACA;MACAiC,IAAA;MACAC,SAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,OAAA,WAAAA,QAAA;MACA,YAAArD,SAAA,UAAAD,QAAA,SAAAD,QAAA;IACA;EACA;EACAwD,OAAA;IACA;IACAC,kBAAA,WAAAA,mBAAAC,IAAA;MACA,IAAAC,KAAA;MACA,SAAA1D,QAAA,CAAA2D,MAAA;QACA,IAAAC,aAAA;QACA,IAAAH,IAAA,CAAAR,IAAA,CAAAY,WAAA;UACAD,aAAA,GAAAH,IAAA,CAAAR,IAAA,CAAAa,KAAA,CAAAL,IAAA,CAAAR,IAAA,CAAAY,WAAA;QACA;QACAH,KAAA,QAAA1D,QAAA,CAAA+D,IAAA,WAAArE,IAAA;UACA,IAAA+D,IAAA,CAAA/D,IAAA,CAAAqD,OAAA,CAAArD,IAAA;UACA,IAAAkE,aAAA,IAAAA,aAAA,CAAAb,OAAA,CAAArD,IAAA;UACA;QACA;MACA;QACAgE,KAAA,GAAAD,IAAA,CAAA/D,IAAA,CAAAqD,OAAA;MACA;MAEA,KAAAW,KAAA;QACA,KAAAM,MAAA,CAAAC,QAAA,sEAAAC,MAAA,MAAAlE,QAAA,CAAAmE,IAAA;QACA;MACA;MACA,IAAAV,IAAA,CAAAR,IAAA,CAAAmB,QAAA;QACA,KAAAJ,MAAA,CAAAC,QAAA;QACA;MACA;MACA,SAAAlE,QAAA;QACA,IAAAsE,IAAA,GAAAZ,IAAA,CAAAa,IAAA,sBAAAvE,QAAA;QACA,KAAAsE,IAAA;UACA,KAAAL,MAAA,CAAAC,QAAA,6EAAAC,MAAA,MAAAnE,QAAA;UACA;QACA;MACA;MACA,KAAAiE,MAAA,CAAAO,OAAA;MACA,KAAAlE,MAAA;IACA;IACA;IACAmE,YAAA,WAAAA,aAAA;MACA,KAAAR,MAAA,CAAAC,QAAA,iEAAAC,MAAA,MAAArE,KAAA;IACA;IACA;IACA4E,mBAAA,WAAAA,oBAAAC,GAAA,EAAAjB,IAAA;MACA,IAAAiB,GAAA,CAAAC,IAAA;QACA,KAAArE,UAAA,CAAAsE,IAAA;UAAA3B,IAAA,EAAAyB,GAAA,CAAAG,QAAA;UAAA3B,GAAA,EAAAwB,GAAA,CAAAG;QAAA;QACA,KAAAC,oBAAA;MACA;QACA,KAAAzE,MAAA;QACA,KAAA2D,MAAA,CAAAe,YAAA;QACA,KAAAf,MAAA,CAAAC,QAAA,CAAAS,GAAA,CAAAM,GAAA;QACA,KAAAxD,KAAA,CAAAC,WAAA,CAAAwD,YAAA,CAAAxB,IAAA;QACA,KAAAqB,oBAAA;MACA;IACA;IACA;IACAI,YAAA,WAAAA,aAAAzB,IAAA;MACA,IAAA0B,MAAA,QAAAjE,QAAA,CAAA2B,GAAA,WAAAuC,CAAA;QAAA,OAAAA,CAAA,CAAAnC,IAAA;MAAA,GAAAF,OAAA,CAAAU,IAAA,CAAAR,IAAA;MACA,IAAAkC,MAAA;QACA,KAAAjE,QAAA,CAAAe,MAAA,CAAAkD,MAAA;QACA,KAAA/C,KAAA,eAAAC,YAAA,MAAAnB,QAAA;MACA;IACA;IACA;IACAmE,iBAAA,WAAAA,kBAAA;MACA,KAAArB,MAAA,CAAAC,QAAA;MACA,KAAAD,MAAA,CAAAe,YAAA;IACA;IACA;IACAD,oBAAA,WAAAA,qBAAA;MACA,SAAAzE,MAAA,aAAAC,UAAA,CAAAqD,MAAA,UAAAtD,MAAA;QACA,KAAAa,QAAA,QAAAA,QAAA,CAAAgD,MAAA,MAAA5D,UAAA;QACA,KAAAA,UAAA;QACA,KAAAD,MAAA;QACA,KAAA+B,KAAA,eAAAC,YAAA,MAAAnB,QAAA;QACA,KAAA8C,MAAA,CAAAe,YAAA;MACA;IACA;IACA;IACAO,wBAAA,WAAAA,yBAAA7B,IAAA;MACA,KAAAlD,cAAA,GAAAkD,IAAA,CAAAP,GAAA;MACA,KAAA1C,aAAA;IACA;IACA;IACA6B,YAAA,WAAAA,aAAAK,IAAA,EAAA6C,SAAA;MACA,IAAAC,IAAA;MACAD,SAAA,GAAAA,SAAA;MACA,SAAAE,CAAA,IAAA/C,IAAA;QACA,IAAAA,IAAA,CAAA+C,CAAA,EAAAvC,GAAA;UACAsC,IAAA,IAAA9C,IAAA,CAAA+C,CAAA,EAAAvC,GAAA,CAAAwC,OAAA,MAAAhF,OAAA,QAAA6E,SAAA;QACA;MACA;MACA,OAAAC,IAAA,SAAAA,IAAA,CAAAG,MAAA,IAAAH,IAAA,CAAA7B,MAAA;IACA;EACA;AACA", "ignoreList": []}]}