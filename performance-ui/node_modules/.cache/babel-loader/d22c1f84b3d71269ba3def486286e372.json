{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/@babel/runtime/helpers/interopRequireDefault.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/@babel/runtime/helpers/interopRequireDefault.js", "mtime": 1753510682282}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZnVuY3Rpb24gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChlKSB7CiAgcmV0dXJuIGUgJiYgZS5fX2VzTW9kdWxlID8gZSA6IHsKICAgICJkZWZhdWx0IjogZQogIH07Cn0KbW9kdWxlLmV4cG9ydHMgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0LCBtb2R1bGUuZXhwb3J0cy5fX2VzTW9kdWxlID0gdHJ1ZSwgbW9kdWxlLmV4cG9ydHNbImRlZmF1bHQiXSA9IG1vZHVsZS5leHBvcnRzOw=="}, {"version": 3, "names": ["_interopRequireDefault", "e", "__esModule", "module", "exports"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/@babel/runtime/helpers/interopRequireDefault.js"], "sourcesContent": ["function _interopRequireDefault(e) {\n  return e && e.__esModule ? e : {\n    \"default\": e\n  };\n}\nmodule.exports = _interopRequireDefault, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "mappings": "AAAA,SAASA,sBAAsBA,CAACC,CAAC,EAAE;EACjC,OAAOA,CAAC,IAAIA,CAAC,CAACC,UAAU,GAAGD,CAAC,GAAG;IAC7B,SAAS,EAAEA;EACb,CAAC;AACH;AACAE,MAAM,CAACC,OAAO,GAAGJ,sBAAsB,EAAEG,MAAM,CAACC,OAAO,CAACF,UAAU,GAAG,IAAI,EAAEC,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO", "ignoreList": []}]}