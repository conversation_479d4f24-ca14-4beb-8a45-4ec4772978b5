{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/system/dept/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/system/dept/index.vue", "mtime": 1753510684535}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_dept", "require", "_vueTreeselect", "_interopRequireDefault", "name", "dicts", "components", "Treeselect", "data", "loading", "showSearch", "deptList", "deptOptions", "title", "open", "isExpandAll", "refreshTable", "queryParams", "deptName", "undefined", "status", "form", "rules", "parentId", "required", "message", "trigger", "orderNum", "email", "type", "phone", "pattern", "created", "getList", "methods", "_this", "listDept", "then", "response", "handleTree", "normalizer", "node", "children", "length", "id", "deptId", "label", "cancel", "reset", "leader", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleAdd", "row", "_this2", "toggleExpandAll", "_this3", "$nextTick", "handleUpdate", "_this4", "getDept", "listDept<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "noResultsOptions", "parentName", "push", "submitForm", "_this5", "$refs", "validate", "valid", "updateDept", "$modal", "msgSuccess", "addDept", "handleDelete", "_this6", "confirm", "delDept", "catch"], "sources": ["src/views/system/dept/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\">\n      <el-form-item label=\"部门名称\" prop=\"deptName\">\n        <el-input\n          v-model=\"queryParams.deptName\"\n          placeholder=\"请输入部门名称\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"状态\" prop=\"status\">\n        <el-select v-model=\"queryParams.status\" placeholder=\"部门状态\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.sys_normal_disable\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['system:dept:add']\"\n        >新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"info\"\n          plain\n          icon=\"el-icon-sort\"\n          size=\"mini\"\n          @click=\"toggleExpandAll\"\n        >展开/折叠</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table\n      v-if=\"refreshTable\"\n      v-loading=\"loading\"\n      :data=\"deptList\"\n      row-key=\"deptId\"\n      :default-expand-all=\"isExpandAll\"\n      :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\n    >\n      <el-table-column prop=\"deptName\" label=\"部门名称\" width=\"260\"></el-table-column>\n      <el-table-column prop=\"orderNum\" label=\"排序\" width=\"200\"></el-table-column>\n      <el-table-column prop=\"status\" label=\"状态\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"200\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['system:dept:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-plus\"\n            @click=\"handleAdd(scope.row)\"\n            v-hasPermi=\"['system:dept:add']\"\n          >新增</el-button>\n          <el-button\n            v-if=\"scope.row.parentId != 0\"\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['system:dept:remove']\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 添加或修改部门对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n        <el-row>\n          <el-col :span=\"24\" v-if=\"form.parentId !== 0\">\n            <el-form-item label=\"上级部门\" prop=\"parentId\">\n              <treeselect v-model=\"form.parentId\" :options=\"deptOptions\" :normalizer=\"normalizer\" placeholder=\"选择上级部门\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"部门名称\" prop=\"deptName\">\n              <el-input v-model=\"form.deptName\" placeholder=\"请输入部门名称\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"显示排序\" prop=\"orderNum\">\n              <el-input-number v-model=\"form.orderNum\" controls-position=\"right\" :min=\"0\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"负责人\" prop=\"leader\">\n              <el-input v-model=\"form.leader\" placeholder=\"请输入负责人\" maxlength=\"20\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"联系电话\" prop=\"phone\">\n              <el-input v-model=\"form.phone\" placeholder=\"请输入联系电话\" maxlength=\"11\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"邮箱\" prop=\"email\">\n              <el-input v-model=\"form.email\" placeholder=\"请输入邮箱\" maxlength=\"50\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"部门状态\">\n              <el-radio-group v-model=\"form.status\">\n                <el-radio\n                  v-for=\"dict in dict.type.sys_normal_disable\"\n                  :key=\"dict.value\"\n                  :label=\"dict.value\"\n                >{{dict.label}}</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listDept, getDept, delDept, addDept, updateDept, listDeptExcludeChild } from \"@/api/system/dept\"\nimport Treeselect from \"@riophae/vue-treeselect\"\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\"\n\nexport default {\n  name: \"Dept\",\n  dicts: ['sys_normal_disable'],\n  components: { Treeselect },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 表格树数据\n      deptList: [],\n      // 部门树选项\n      deptOptions: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 是否展开，默认全部展开\n      isExpandAll: true,\n      // 重新渲染表格状态\n      refreshTable: true,\n      // 查询参数\n      queryParams: {\n        deptName: undefined,\n        status: undefined\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        parentId: [\n          { required: true, message: \"上级部门不能为空\", trigger: \"blur\" }\n        ],\n        deptName: [\n          { required: true, message: \"部门名称不能为空\", trigger: \"blur\" }\n        ],\n        orderNum: [\n          { required: true, message: \"显示排序不能为空\", trigger: \"blur\" }\n        ],\n        email: [\n          {\n            type: \"email\",\n            message: \"请输入正确的邮箱地址\",\n            trigger: [\"blur\", \"change\"]\n          }\n        ],\n        phone: [\n          {\n            pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/,\n            message: \"请输入正确的手机号码\",\n            trigger: \"blur\"\n          }\n        ]\n      }\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    /** 查询部门列表 */\n    getList() {\n      this.loading = true\n      listDept(this.queryParams).then(response => {\n        this.deptList = this.handleTree(response.data, \"deptId\")\n        this.loading = false\n      })\n    },\n    /** 转换部门数据结构 */\n    normalizer(node) {\n      if (node.children && !node.children.length) {\n        delete node.children\n      }\n      return {\n        id: node.deptId,\n        label: node.deptName,\n        children: node.children\n      }\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false\n      this.reset()\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        deptId: undefined,\n        parentId: undefined,\n        deptName: undefined,\n        orderNum: undefined,\n        leader: undefined,\n        phone: undefined,\n        email: undefined,\n        status: \"0\"\n      }\n      this.resetForm(\"form\")\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.getList()\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\")\n      this.handleQuery()\n    },\n    /** 新增按钮操作 */\n    handleAdd(row) {\n      this.reset()\n      if (row != undefined) {\n        this.form.parentId = row.deptId\n      }\n      this.open = true\n      this.title = \"添加部门\"\n      listDept().then(response => {\n        this.deptOptions = this.handleTree(response.data, \"deptId\")\n      })\n    },\n    /** 展开/折叠操作 */\n    toggleExpandAll() {\n      this.refreshTable = false\n      this.isExpandAll = !this.isExpandAll\n      this.$nextTick(() => {\n        this.refreshTable = true\n      })\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset()\n      getDept(row.deptId).then(response => {\n        this.form = response.data\n        this.open = true\n        this.title = \"修改部门\"\n        listDeptExcludeChild(row.deptId).then(response => {\n          this.deptOptions = this.handleTree(response.data, \"deptId\")\n          if (this.deptOptions.length == 0) {\n            const noResultsOptions = { deptId: this.form.parentId, deptName: this.form.parentName, children: [] }\n            this.deptOptions.push(noResultsOptions)\n          }\n        })\n      })\n    },\n    /** 提交按钮 */\n    submitForm: function() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.deptId != undefined) {\n            updateDept(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\")\n              this.open = false\n              this.getList()\n            })\n          } else {\n            addDept(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\")\n              this.open = false\n              this.getList()\n            })\n          }\n        }\n      })\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      this.$modal.confirm('是否确认删除名称为\"' + row.deptName + '\"的数据项？').then(function() {\n        return delDept(row.deptId)\n      }).then(() => {\n        this.getList()\n        this.$modal.msgSuccess(\"删除成功\")\n      }).catch(() => {})\n    }\n  }\n}\n</script>\n"], "mappings": ";;;;;;;;AAgKA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAC,sBAAA,CAAAF,OAAA;AACAA,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,KAAA;EACAC,UAAA;IAAAC,UAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,UAAA;MACA;MACAC,QAAA;MACA;MACAC,WAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;MACA;MACAC,YAAA;MACA;MACAC,WAAA;QACAC,QAAA,EAAAC,SAAA;QACAC,MAAA,EAAAD;MACA;MACA;MACAE,IAAA;MACA;MACAC,KAAA;QACAC,QAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,QAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,QAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,KAAA,GACA;UACAC,IAAA;UACAJ,OAAA;UACAC,OAAA;QACA,EACA;QACAI,KAAA,GACA;UACAC,OAAA;UACAN,OAAA;UACAC,OAAA;QACA;MAEA;IACA;EACA;EACAM,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,aACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAA1B,OAAA;MACA,IAAA2B,cAAA,OAAAnB,WAAA,EAAAoB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAxB,QAAA,GAAAwB,KAAA,CAAAI,UAAA,CAAAD,QAAA,CAAA9B,IAAA;QACA2B,KAAA,CAAA1B,OAAA;MACA;IACA;IACA,eACA+B,UAAA,WAAAA,WAAAC,IAAA;MACA,IAAAA,IAAA,CAAAC,QAAA,KAAAD,IAAA,CAAAC,QAAA,CAAAC,MAAA;QACA,OAAAF,IAAA,CAAAC,QAAA;MACA;MACA;QACAE,EAAA,EAAAH,IAAA,CAAAI,MAAA;QACAC,KAAA,EAAAL,IAAA,CAAAvB,QAAA;QACAwB,QAAA,EAAAD,IAAA,CAAAC;MACA;IACA;IACA;IACAK,MAAA,WAAAA,OAAA;MACA,KAAAjC,IAAA;MACA,KAAAkC,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAA3B,IAAA;QACAwB,MAAA,EAAA1B,SAAA;QACAI,QAAA,EAAAJ,SAAA;QACAD,QAAA,EAAAC,SAAA;QACAQ,QAAA,EAAAR,SAAA;QACA8B,MAAA,EAAA9B,SAAA;QACAW,KAAA,EAAAX,SAAA;QACAS,KAAA,EAAAT,SAAA;QACAC,MAAA;MACA;MACA,KAAA8B,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAlB,OAAA;IACA;IACA,aACAmB,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA,aACAE,SAAA,WAAAA,UAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAP,KAAA;MACA,IAAAM,GAAA,IAAAnC,SAAA;QACA,KAAAE,IAAA,CAAAE,QAAA,GAAA+B,GAAA,CAAAT,MAAA;MACA;MACA,KAAA/B,IAAA;MACA,KAAAD,KAAA;MACA,IAAAuB,cAAA,IAAAC,IAAA,WAAAC,QAAA;QACAiB,MAAA,CAAA3C,WAAA,GAAA2C,MAAA,CAAAhB,UAAA,CAAAD,QAAA,CAAA9B,IAAA;MACA;IACA;IACA,cACAgD,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAAzC,YAAA;MACA,KAAAD,WAAA,SAAAA,WAAA;MACA,KAAA2C,SAAA;QACAD,MAAA,CAAAzC,YAAA;MACA;IACA;IACA,aACA2C,YAAA,WAAAA,aAAAL,GAAA;MAAA,IAAAM,MAAA;MACA,KAAAZ,KAAA;MACA,IAAAa,aAAA,EAAAP,GAAA,CAAAT,MAAA,EAAAR,IAAA,WAAAC,QAAA;QACAsB,MAAA,CAAAvC,IAAA,GAAAiB,QAAA,CAAA9B,IAAA;QACAoD,MAAA,CAAA9C,IAAA;QACA8C,MAAA,CAAA/C,KAAA;QACA,IAAAiD,0BAAA,EAAAR,GAAA,CAAAT,MAAA,EAAAR,IAAA,WAAAC,QAAA;UACAsB,MAAA,CAAAhD,WAAA,GAAAgD,MAAA,CAAArB,UAAA,CAAAD,QAAA,CAAA9B,IAAA;UACA,IAAAoD,MAAA,CAAAhD,WAAA,CAAA+B,MAAA;YACA,IAAAoB,gBAAA;cAAAlB,MAAA,EAAAe,MAAA,CAAAvC,IAAA,CAAAE,QAAA;cAAAL,QAAA,EAAA0C,MAAA,CAAAvC,IAAA,CAAA2C,UAAA;cAAAtB,QAAA;YAAA;YACAkB,MAAA,CAAAhD,WAAA,CAAAqD,IAAA,CAAAF,gBAAA;UACA;QACA;MACA;IACA;IACA;IACAG,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAA9C,IAAA,CAAAwB,MAAA,IAAA1B,SAAA;YACA,IAAAoD,gBAAA,EAAAJ,MAAA,CAAA9C,IAAA,EAAAgB,IAAA,WAAAC,QAAA;cACA6B,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAArD,IAAA;cACAqD,MAAA,CAAAlC,OAAA;YACA;UACA;YACA,IAAAyC,aAAA,EAAAP,MAAA,CAAA9C,IAAA,EAAAgB,IAAA,WAAAC,QAAA;cACA6B,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAArD,IAAA;cACAqD,MAAA,CAAAlC,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACA0C,YAAA,WAAAA,aAAArB,GAAA;MAAA,IAAAsB,MAAA;MACA,KAAAJ,MAAA,CAAAK,OAAA,gBAAAvB,GAAA,CAAApC,QAAA,aAAAmB,IAAA;QACA,WAAAyC,aAAA,EAAAxB,GAAA,CAAAT,MAAA;MACA,GAAAR,IAAA;QACAuC,MAAA,CAAA3C,OAAA;QACA2C,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAM,KAAA;IACA;EACA;AACA", "ignoreList": []}]}