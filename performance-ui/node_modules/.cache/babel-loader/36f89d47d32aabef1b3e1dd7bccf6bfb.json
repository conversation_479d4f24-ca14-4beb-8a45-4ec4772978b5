{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/plugins/auth.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/plugins/auth.js", "mtime": 1753510684530}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_store", "_interopRequireDefault", "require", "authPermission", "permission", "all_permission", "permissions", "store", "getters", "length", "some", "v", "authRole", "role", "super_admin", "roles", "_default", "exports", "default", "<PERSON><PERSON><PERSON><PERSON>", "hasPermiOr", "item", "hasPermiAnd", "every", "hasRole", "hasRoleOr", "hasRoleAnd"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/plugins/auth.js"], "sourcesContent": ["import store from '@/store'\n\nfunction authPermission(permission) {\n  const all_permission = \"*:*:*\"\n  const permissions = store.getters && store.getters.permissions\n  if (permission && permission.length > 0) {\n    return permissions.some(v => {\n      return all_permission === v || v === permission\n    })\n  } else {\n    return false\n  }\n}\n\nfunction authRole(role) {\n  const super_admin = \"admin\"\n  const roles = store.getters && store.getters.roles\n  if (role && role.length > 0) {\n    return roles.some(v => {\n      return super_admin === v || v === role\n    })\n  } else {\n    return false\n  }\n}\n\nexport default {\n  // 验证用户是否具备某权限\n  hasPermi(permission) {\n    return authPermission(permission)\n  },\n  // 验证用户是否含有指定权限，只需包含其中一个\n  hasPermiOr(permissions) {\n    return permissions.some(item => {\n      return authPermission(item)\n    })\n  },\n  // 验证用户是否含有指定权限，必须全部拥有\n  hasPermiAnd(permissions) {\n    return permissions.every(item => {\n      return authPermission(item)\n    })\n  },\n  // 验证用户是否具备某角色\n  hasRole(role) {\n    return authRole(role)\n  },\n  // 验证用户是否含有指定角色，只需包含其中一个\n  hasRoleOr(roles) {\n    return roles.some(item => {\n      return authRole(item)\n    })\n  },\n  // 验证用户是否含有指定角色，必须全部拥有\n  hasRoleAnd(roles) {\n    return roles.every(item => {\n      return authRole(item)\n    })\n  }\n}\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,SAASC,cAAcA,CAACC,UAAU,EAAE;EAClC,IAAMC,cAAc,GAAG,OAAO;EAC9B,IAAMC,WAAW,GAAGC,cAAK,CAACC,OAAO,IAAID,cAAK,CAACC,OAAO,CAACF,WAAW;EAC9D,IAAIF,UAAU,IAAIA,UAAU,CAACK,MAAM,GAAG,CAAC,EAAE;IACvC,OAAOH,WAAW,CAACI,IAAI,CAAC,UAAAC,CAAC,EAAI;MAC3B,OAAON,cAAc,KAAKM,CAAC,IAAIA,CAAC,KAAKP,UAAU;IACjD,CAAC,CAAC;EACJ,CAAC,MAAM;IACL,OAAO,KAAK;EACd;AACF;AAEA,SAASQ,QAAQA,CAACC,IAAI,EAAE;EACtB,IAAMC,WAAW,GAAG,OAAO;EAC3B,IAAMC,KAAK,GAAGR,cAAK,CAACC,OAAO,IAAID,cAAK,CAACC,OAAO,CAACO,KAAK;EAClD,IAAIF,IAAI,IAAIA,IAAI,CAACJ,MAAM,GAAG,CAAC,EAAE;IAC3B,OAAOM,KAAK,CAACL,IAAI,CAAC,UAAAC,CAAC,EAAI;MACrB,OAAOG,WAAW,KAAKH,CAAC,IAAIA,CAAC,KAAKE,IAAI;IACxC,CAAC,CAAC;EACJ,CAAC,MAAM;IACL,OAAO,KAAK;EACd;AACF;AAAC,IAAAG,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEc;EACb;EACAC,QAAQ,WAARA,QAAQA,CAACf,UAAU,EAAE;IACnB,OAAOD,cAAc,CAACC,UAAU,CAAC;EACnC,CAAC;EACD;EACAgB,UAAU,WAAVA,UAAUA,CAACd,WAAW,EAAE;IACtB,OAAOA,WAAW,CAACI,IAAI,CAAC,UAAAW,IAAI,EAAI;MAC9B,OAAOlB,cAAc,CAACkB,IAAI,CAAC;IAC7B,CAAC,CAAC;EACJ,CAAC;EACD;EACAC,WAAW,WAAXA,WAAWA,CAAChB,WAAW,EAAE;IACvB,OAAOA,WAAW,CAACiB,KAAK,CAAC,UAAAF,IAAI,EAAI;MAC/B,OAAOlB,cAAc,CAACkB,IAAI,CAAC;IAC7B,CAAC,CAAC;EACJ,CAAC;EACD;EACAG,OAAO,WAAPA,OAAOA,CAACX,IAAI,EAAE;IACZ,OAAOD,QAAQ,CAACC,IAAI,CAAC;EACvB,CAAC;EACD;EACAY,SAAS,WAATA,SAASA,CAACV,KAAK,EAAE;IACf,OAAOA,KAAK,CAACL,IAAI,CAAC,UAAAW,IAAI,EAAI;MACxB,OAAOT,QAAQ,CAACS,IAAI,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC;EACD;EACAK,UAAU,WAAVA,UAAUA,CAACX,KAAK,EAAE;IAChB,OAAOA,KAAK,CAACQ,KAAK,CAAC,UAAAF,IAAI,EAAI;MACzB,OAAOT,QAAQ,CAACS,IAAI,CAAC;IACvB,CAAC,CAAC;EACJ;AACF,CAAC", "ignoreList": []}]}