{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/performance/personnelTask.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/performance/personnelTask.js", "mtime": 1753510684517}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCIvVXNlcnMvbWF6aWhhby9EZXNrdG9wL2Rldi9wZXJmb3JtYW5jZS9wZXJmb3JtYW5jZS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuYWRkUGVyc29ubmVsVGFzayA9IGFkZFBlcnNvbm5lbFRhc2s7CmV4cG9ydHMuZGVsUGVyc29ubmVsVGFzayA9IGRlbFBlcnNvbm5lbFRhc2s7CmV4cG9ydHMuZ2V0UGVyc29ubmVsVGFzayA9IGdldFBlcnNvbm5lbFRhc2s7CmV4cG9ydHMubGlzdFBlcnNvbm5lbFRhc2sgPSBsaXN0UGVyc29ubmVsVGFzazsKZXhwb3J0cy51cGRhdGVQZXJzb25uZWxUYXNrID0gdXBkYXRlUGVyc29ubmVsVGFzazsKdmFyIF9yZXF1ZXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3V0aWxzL3JlcXVlc3QiKSk7Ci8vIOafpeivoue7qeaViOS7u+WKoeaYjue7huWIl+ihqApmdW5jdGlvbiBsaXN0UGVyc29ubmVsVGFzayhxdWVyeSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3BlcmZvcm1hbmNlL3BlcnNvbm5lbC90YXNrL2xpc3QnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfQoKLy8g5p+l6K+i57up5pWI5Lu75Yqh5piO57uG6K+m57uG5L+h5oGvCmZ1bmN0aW9uIGdldFBlcnNvbm5lbFRhc2soaWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9wZXJmb3JtYW5jZS9wZXJzb25uZWwvdGFzay8nICsgaWQsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8vIOaWsOWinue7qeaViOS7u+WKoeaYjue7hgpmdW5jdGlvbiBhZGRQZXJzb25uZWxUYXNrKGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9wZXJmb3JtYW5jZS9wZXJzb25uZWwvdGFzaycsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5L+u5pS557up5pWI5Lu75Yqh5piO57uGCmZ1bmN0aW9uIHVwZGF0ZVBlcnNvbm5lbFRhc2soZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3BlcmZvcm1hbmNlL3BlcnNvbm5lbC90YXNrJywKICAgIG1ldGhvZDogJ3B1dCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOWIoOmZpOe7qeaViOS7u+WKoeaYjue7hgpmdW5jdGlvbiBkZWxQZXJzb25uZWxUYXNrKGlkcykgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3BlcmZvcm1hbmNlL3BlcnNvbm5lbC90YXNrLycgKyBpZHMsCiAgICBtZXRob2Q6ICdkZWxldGUnCiAgfSk7Cn0="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listPersonnelTask", "query", "request", "url", "method", "params", "getPersonnelTask", "id", "addPersonnelTask", "data", "updatePersonnelTask", "delPersonnelTask", "ids"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/performance/personnelTask.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询绩效任务明细列表\nexport function listPersonnelTask(query) {\n  return request({\n    url: '/performance/personnel/task/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询绩效任务明细详细信息\nexport function getPersonnelTask(id) {\n  return request({\n    url: '/performance/personnel/task/' + id,\n    method: 'get'\n  })\n}\n\n// 新增绩效任务明细\nexport function addPersonnelTask(data) {\n  return request({\n    url: '/performance/personnel/task',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改绩效任务明细\nexport function updatePersonnelTask(data) {\n  return request({\n    url: '/performance/personnel/task',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除绩效任务明细\nexport function delPersonnelTask(ids) {\n  return request({\n    url: '/performance/personnel/task/' + ids,\n    method: 'delete'\n  })\n} "], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,iBAAiBA,CAACC,KAAK,EAAE;EACvC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC;IACvCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,gBAAgBA,CAACC,EAAE,EAAE;EACnC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B,GAAGI,EAAE;IACxCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,gBAAgBA,CAACC,IAAI,EAAE;EACrC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,mBAAmBA,CAACD,IAAI,EAAE;EACxC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,gBAAgBA,CAACC,GAAG,EAAE;EACpC,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B,GAAGS,GAAG;IACzCR,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}