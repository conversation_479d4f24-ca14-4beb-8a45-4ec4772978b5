{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/layout/components/TagsView/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/layout/components/TagsView/index.vue", "mtime": 1753510684530}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_ScrollPane", "_interopRequireDefault", "require", "_path", "components", "ScrollPane", "data", "visible", "top", "left", "selectedTag", "affixTags", "computed", "visitedViews", "$store", "state", "tagsView", "routes", "permission", "theme", "settings", "tagsIcon", "watch", "$route", "addTags", "moveToCurrentTag", "value", "document", "body", "addEventListener", "closeMenu", "removeEventListener", "mounted", "initTags", "methods", "isActive", "route", "path", "activeStyle", "tag", "isAffix", "meta", "affix", "isFirstView", "fullPath", "err", "isLastView", "length", "filterAffixTags", "_this", "basePath", "arguments", "undefined", "tags", "for<PERSON>ach", "tagPath", "resolve", "push", "name", "_objectSpread2", "default", "children", "tempTags", "concat", "_toConsumableArray2", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "n", "done", "dispatch", "e", "f", "_this2", "$refs", "$nextTick", "_iterator2", "_step2", "to", "scrollPane", "move<PERSON><PERSON><PERSON>arget", "refreshSelectedTag", "view", "$tab", "refreshPage", "link", "closeSelectedTag", "_this3", "closePage", "then", "_ref", "toLastView", "closeRightTags", "_this4", "closeRightPage", "find", "i", "closeLeftTags", "_this5", "closeLeftPage", "closeOthersTags", "_this6", "$router", "catch", "closeOtherPage", "closeAllTags", "_this7", "closeAllPage", "_ref2", "some", "latestView", "slice", "replace", "openMenu", "menu<PERSON>in<PERSON>idth", "offsetLeft", "$el", "getBoundingClientRect", "offsetWidth", "maxLeft", "clientX", "clientY", "handleScroll"], "sources": ["src/layout/components/TagsView/index.vue"], "sourcesContent": ["<template>\n  <div id=\"tags-view-container\" class=\"tags-view-container\">\n    <scroll-pane ref=\"scrollPane\" class=\"tags-view-wrapper\" @scroll=\"handleScroll\">\n      <router-link\n        v-for=\"tag in visitedViews\"\n        ref=\"tag\"\n        :key=\"tag.path\"\n        :class=\"{ 'active': isActive(tag), 'has-icon': tagsIcon }\"\n        :to=\"{ path: tag.path, query: tag.query, fullPath: tag.fullPath }\"\n        tag=\"span\"\n        class=\"tags-view-item\"\n        :style=\"activeStyle(tag)\"\n        @click.middle.native=\"!isAffix(tag)?closeSelectedTag(tag):''\"\n        @contextmenu.prevent.native=\"openMenu(tag,$event)\"\n      >\n        <svg-icon v-if=\"tagsIcon && tag.meta && tag.meta.icon && tag.meta.icon !== '#'\" :icon-class=\"tag.meta.icon\" />\n        {{ tag.title }}\n        <span v-if=\"!isAffix(tag)\" class=\"el-icon-close\" @click.prevent.stop=\"closeSelectedTag(tag)\" />\n      </router-link>\n    </scroll-pane>\n    <ul v-show=\"visible\" :style=\"{left:left+'px',top:top+'px'}\" class=\"contextmenu\">\n      <li @click=\"refreshSelectedTag(selectedTag)\"><i class=\"el-icon-refresh-right\"></i> 刷新页面</li>\n      <li v-if=\"!isAffix(selectedTag)\" @click=\"closeSelectedTag(selectedTag)\"><i class=\"el-icon-close\"></i> 关闭当前</li>\n      <li @click=\"closeOthersTags\"><i class=\"el-icon-circle-close\"></i> 关闭其他</li>\n      <li v-if=\"!isFirstView()\" @click=\"closeLeftTags\"><i class=\"el-icon-back\"></i> 关闭左侧</li>\n      <li v-if=\"!isLastView()\" @click=\"closeRightTags\"><i class=\"el-icon-right\"></i> 关闭右侧</li>\n      <li @click=\"closeAllTags(selectedTag)\"><i class=\"el-icon-circle-close\"></i> 全部关闭</li>\n    </ul>\n  </div>\n</template>\n\n<script>\nimport ScrollPane from './ScrollPane'\nimport path from 'path'\n\nexport default {\n  components: { ScrollPane },\n  data() {\n    return {\n      visible: false,\n      top: 0,\n      left: 0,\n      selectedTag: {},\n      affixTags: []\n    }\n  },\n  computed: {\n    visitedViews() {\n      return this.$store.state.tagsView.visitedViews\n    },\n    routes() {\n      return this.$store.state.permission.routes\n    },\n    theme() {\n      return this.$store.state.settings.theme\n    },\n    tagsIcon() {\n      return this.$store.state.settings.tagsIcon\n    }\n  },\n  watch: {\n    $route() {\n      this.addTags()\n      this.moveToCurrentTag()\n    },\n    visible(value) {\n      if (value) {\n        document.body.addEventListener('click', this.closeMenu)\n      } else {\n        document.body.removeEventListener('click', this.closeMenu)\n      }\n    }\n  },\n  mounted() {\n    this.initTags()\n    this.addTags()\n  },\n  methods: {\n    isActive(route) {\n      return route.path === this.$route.path\n    },\n    activeStyle(tag) {\n      if (!this.isActive(tag)) return {}\n      return {\n        \"background-color\": this.theme,\n        \"border-color\": this.theme\n      }\n    },\n    isAffix(tag) {\n      return tag.meta && tag.meta.affix\n    },\n    isFirstView() {\n      try {\n        return this.selectedTag.fullPath === '/index' || this.selectedTag.fullPath === this.visitedViews[1].fullPath\n      } catch (err) {\n        return false\n      }\n    },\n    isLastView() {\n      try {\n        return this.selectedTag.fullPath === this.visitedViews[this.visitedViews.length - 1].fullPath\n      } catch (err) {\n        return false\n      }\n    },\n    filterAffixTags(routes, basePath = '/') {\n      let tags = []\n      routes.forEach(route => {\n        if (route.meta && route.meta.affix) {\n          const tagPath = path.resolve(basePath, route.path)\n          tags.push({\n            fullPath: tagPath,\n            path: tagPath,\n            name: route.name,\n            meta: { ...route.meta }\n          })\n        }\n        if (route.children) {\n          const tempTags = this.filterAffixTags(route.children, route.path)\n          if (tempTags.length >= 1) {\n            tags = [...tags, ...tempTags]\n          }\n        }\n      })\n      return tags\n    },\n    initTags() {\n      const affixTags = this.affixTags = this.filterAffixTags(this.routes)\n      for (const tag of affixTags) {\n        // Must have tag name\n        if (tag.name) {\n          this.$store.dispatch('tagsView/addVisitedView', tag)\n        }\n      }\n    },\n    addTags() {\n      const { name } = this.$route\n      if (name) {\n        this.$store.dispatch('tagsView/addView', this.$route)\n      }\n    },\n    moveToCurrentTag() {\n      const tags = this.$refs.tag\n      this.$nextTick(() => {\n        for (const tag of tags) {\n          if (tag.to.path === this.$route.path) {\n            this.$refs.scrollPane.moveToTarget(tag)\n            // when query is different then update\n            if (tag.to.fullPath !== this.$route.fullPath) {\n              this.$store.dispatch('tagsView/updateVisitedView', this.$route)\n            }\n            break\n          }\n        }\n      })\n    },\n    refreshSelectedTag(view) {\n      this.$tab.refreshPage(view)\n      if (this.$route.meta.link) {\n        this.$store.dispatch('tagsView/delIframeView', this.$route)\n      }\n    },\n    closeSelectedTag(view) {\n      this.$tab.closePage(view).then(({ visitedViews }) => {\n        if (this.isActive(view)) {\n          this.toLastView(visitedViews, view)\n        }\n      })\n    },\n    closeRightTags() {\n      this.$tab.closeRightPage(this.selectedTag).then(visitedViews => {\n        if (!visitedViews.find(i => i.fullPath === this.$route.fullPath)) {\n          this.toLastView(visitedViews)\n        }\n      })\n    },\n    closeLeftTags() {\n      this.$tab.closeLeftPage(this.selectedTag).then(visitedViews => {\n        if (!visitedViews.find(i => i.fullPath === this.$route.fullPath)) {\n          this.toLastView(visitedViews)\n        }\n      })\n    },\n    closeOthersTags() {\n      this.$router.push(this.selectedTag.fullPath).catch(()=>{})\n      this.$tab.closeOtherPage(this.selectedTag).then(() => {\n        this.moveToCurrentTag()\n      })\n    },\n    closeAllTags(view) {\n      this.$tab.closeAllPage().then(({ visitedViews }) => {\n        if (this.affixTags.some(tag => tag.path === this.$route.path)) {\n          return\n        }\n        this.toLastView(visitedViews, view)\n      })\n    },\n    toLastView(visitedViews, view) {\n      const latestView = visitedViews.slice(-1)[0]\n      if (latestView) {\n        this.$router.push(latestView.fullPath)\n      } else {\n        // now the default is to redirect to the home page if there is no tags-view,\n        // you can adjust it according to your needs.\n        if (view.name === 'Dashboard') {\n          // to reload home page\n          this.$router.replace({ path: '/redirect' + view.fullPath })\n        } else {\n          this.$router.push('/')\n        }\n      }\n    },\n    openMenu(tag, e) {\n      const menuMinWidth = 105\n      const offsetLeft = this.$el.getBoundingClientRect().left // container margin left\n      const offsetWidth = this.$el.offsetWidth // container width\n      const maxLeft = offsetWidth - menuMinWidth // left boundary\n      const left = e.clientX - offsetLeft + 15 // 15: margin right\n\n      if (left > maxLeft) {\n        this.left = maxLeft\n      } else {\n        this.left = left\n      }\n\n      this.top = e.clientY\n      this.visible = true\n      this.selectedTag = tag\n    },\n    closeMenu() {\n      this.visible = false\n    },\n    handleScroll() {\n      this.closeMenu()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.tags-view-container {\n  height: 34px;\n  width: 100%;\n  background: #fff;\n  border-bottom: 1px solid #d8dce5;\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .12), 0 0 3px 0 rgba(0, 0, 0, .04);\n  .tags-view-wrapper {\n    .tags-view-item {\n      display: inline-block;\n      position: relative;\n      cursor: pointer;\n      height: 26px;\n      line-height: 26px;\n      border: 1px solid #d8dce5;\n      color: #495060;\n      background: #fff;\n      padding: 0 8px;\n      font-size: 12px;\n      margin-left: 5px;\n      margin-top: 4px;\n      &:first-of-type {\n        margin-left: 15px;\n      }\n      &:last-of-type {\n        margin-right: 15px;\n      }\n      &.active {\n        background-color: #42b983;\n        color: #fff;\n        border-color: #42b983;\n        &::before {\n          content: '';\n          background: #fff;\n          display: inline-block;\n          width: 8px;\n          height: 8px;\n          border-radius: 50%;\n          position: relative;\n          margin-right: 2px;\n        }\n      }\n    }\n  }\n\n  .tags-view-item.active.has-icon::before {\n    content: none !important;\n  }\n\n  .contextmenu {\n    margin: 0;\n    background: #fff;\n    z-index: 3000;\n    position: absolute;\n    list-style-type: none;\n    padding: 5px 0;\n    border-radius: 4px;\n    font-size: 12px;\n    font-weight: 400;\n    color: #333;\n    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .3);\n    li {\n      margin: 0;\n      padding: 7px 16px;\n      cursor: pointer;\n      &:hover {\n        background: #eee;\n      }\n    }\n  }\n}\n</style>\n\n<style lang=\"scss\">\n//reset element css of el-icon-close\n.tags-view-wrapper {\n  .tags-view-item {\n    .el-icon-close {\n      width: 16px;\n      height: 16px;\n      vertical-align: 2px;\n      border-radius: 50%;\n      text-align: center;\n      transition: all .3s cubic-bezier(.645, .045, .355, 1);\n      transform-origin: 100% 50%;\n      &:before {\n        transform: scale(.6);\n        display: inline-block;\n        vertical-align: -3px;\n      }\n      &:hover {\n        background-color: #b4bccc;\n        color: #fff;\n      }\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAgCA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,KAAA,GAAAF,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,UAAA;IAAAC,UAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,GAAA;MACAC,IAAA;MACAC,WAAA;MACAC,SAAA;IACA;EACA;EACAC,QAAA;IACAC,YAAA,WAAAA,aAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAH,YAAA;IACA;IACAI,MAAA,WAAAA,OAAA;MACA,YAAAH,MAAA,CAAAC,KAAA,CAAAG,UAAA,CAAAD,MAAA;IACA;IACAE,KAAA,WAAAA,MAAA;MACA,YAAAL,MAAA,CAAAC,KAAA,CAAAK,QAAA,CAAAD,KAAA;IACA;IACAE,QAAA,WAAAA,SAAA;MACA,YAAAP,MAAA,CAAAC,KAAA,CAAAK,QAAA,CAAAC,QAAA;IACA;EACA;EACAC,KAAA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAC,OAAA;MACA,KAAAC,gBAAA;IACA;IACAlB,OAAA,WAAAA,QAAAmB,KAAA;MACA,IAAAA,KAAA;QACAC,QAAA,CAAAC,IAAA,CAAAC,gBAAA,eAAAC,SAAA;MACA;QACAH,QAAA,CAAAC,IAAA,CAAAG,mBAAA,eAAAD,SAAA;MACA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAC,QAAA;IACA,KAAAT,OAAA;EACA;EACAU,OAAA;IACAC,QAAA,WAAAA,SAAAC,KAAA;MACA,OAAAA,KAAA,CAAAC,IAAA,UAAAd,MAAA,CAAAc,IAAA;IACA;IACAC,WAAA,WAAAA,YAAAC,GAAA;MACA,UAAAJ,QAAA,CAAAI,GAAA;MACA;QACA,yBAAApB,KAAA;QACA,qBAAAA;MACA;IACA;IACAqB,OAAA,WAAAA,QAAAD,GAAA;MACA,OAAAA,GAAA,CAAAE,IAAA,IAAAF,GAAA,CAAAE,IAAA,CAAAC,KAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA;QACA,YAAAjC,WAAA,CAAAkC,QAAA,sBAAAlC,WAAA,CAAAkC,QAAA,UAAA/B,YAAA,IAAA+B,QAAA;MACA,SAAAC,GAAA;QACA;MACA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA;QACA,YAAApC,WAAA,CAAAkC,QAAA,UAAA/B,YAAA,MAAAA,YAAA,CAAAkC,MAAA,MAAAH,QAAA;MACA,SAAAC,GAAA;QACA;MACA;IACA;IACAG,eAAA,WAAAA,gBAAA/B,MAAA;MAAA,IAAAgC,KAAA;MAAA,IAAAC,QAAA,GAAAC,SAAA,CAAAJ,MAAA,QAAAI,SAAA,QAAAC,SAAA,GAAAD,SAAA;MACA,IAAAE,IAAA;MACApC,MAAA,CAAAqC,OAAA,WAAAlB,KAAA;QACA,IAAAA,KAAA,CAAAK,IAAA,IAAAL,KAAA,CAAAK,IAAA,CAAAC,KAAA;UACA,IAAAa,OAAA,GAAAlB,aAAA,CAAAmB,OAAA,CAAAN,QAAA,EAAAd,KAAA,CAAAC,IAAA;UACAgB,IAAA,CAAAI,IAAA;YACAb,QAAA,EAAAW,OAAA;YACAlB,IAAA,EAAAkB,OAAA;YACAG,IAAA,EAAAtB,KAAA,CAAAsB,IAAA;YACAjB,IAAA,MAAAkB,cAAA,CAAAC,OAAA,MAAAxB,KAAA,CAAAK,IAAA;UACA;QACA;QACA,IAAAL,KAAA,CAAAyB,QAAA;UACA,IAAAC,QAAA,GAAAb,KAAA,CAAAD,eAAA,CAAAZ,KAAA,CAAAyB,QAAA,EAAAzB,KAAA,CAAAC,IAAA;UACA,IAAAyB,QAAA,CAAAf,MAAA;YACAM,IAAA,MAAAU,MAAA,KAAAC,mBAAA,CAAAJ,OAAA,EAAAP,IAAA,OAAAW,mBAAA,CAAAJ,OAAA,EAAAE,QAAA;UACA;QACA;MACA;MACA,OAAAT,IAAA;IACA;IACApB,QAAA,WAAAA,SAAA;MACA,IAAAtB,SAAA,QAAAA,SAAA,QAAAqC,eAAA,MAAA/B,MAAA;MAAA,IAAAgD,SAAA,OAAAC,2BAAA,CAAAN,OAAA,EACAjD,SAAA;QAAAwD,KAAA;MAAA;QAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;UAAA,IAAA/B,GAAA,GAAA4B,KAAA,CAAAzC,KAAA;UACA;UACA,IAAAa,GAAA,CAAAmB,IAAA;YACA,KAAA5C,MAAA,CAAAyD,QAAA,4BAAAhC,GAAA;UACA;QACA;MAAA,SAAAM,GAAA;QAAAoB,SAAA,CAAAO,CAAA,CAAA3B,GAAA;MAAA;QAAAoB,SAAA,CAAAQ,CAAA;MAAA;IACA;IACAjD,OAAA,WAAAA,QAAA;MACA,IAAAkC,IAAA,QAAAnC,MAAA,CAAAmC,IAAA;MACA,IAAAA,IAAA;QACA,KAAA5C,MAAA,CAAAyD,QAAA,0BAAAhD,MAAA;MACA;IACA;IACAE,gBAAA,WAAAA,iBAAA;MAAA,IAAAiD,MAAA;MACA,IAAArB,IAAA,QAAAsB,KAAA,CAAApC,GAAA;MACA,KAAAqC,SAAA;QAAA,IAAAC,UAAA,OAAAX,2BAAA,CAAAN,OAAA,EACAP,IAAA;UAAAyB,MAAA;QAAA;UAAA,KAAAD,UAAA,CAAAT,CAAA,MAAAU,MAAA,GAAAD,UAAA,CAAAR,CAAA,IAAAC,IAAA;YAAA,IAAA/B,GAAA,GAAAuC,MAAA,CAAApD,KAAA;YACA,IAAAa,GAAA,CAAAwC,EAAA,CAAA1C,IAAA,KAAAqC,MAAA,CAAAnD,MAAA,CAAAc,IAAA;cACAqC,MAAA,CAAAC,KAAA,CAAAK,UAAA,CAAAC,YAAA,CAAA1C,GAAA;cACA;cACA,IAAAA,GAAA,CAAAwC,EAAA,CAAAnC,QAAA,KAAA8B,MAAA,CAAAnD,MAAA,CAAAqB,QAAA;gBACA8B,MAAA,CAAA5D,MAAA,CAAAyD,QAAA,+BAAAG,MAAA,CAAAnD,MAAA;cACA;cACA;YACA;UACA;QAAA,SAAAsB,GAAA;UAAAgC,UAAA,CAAAL,CAAA,CAAA3B,GAAA;QAAA;UAAAgC,UAAA,CAAAJ,CAAA;QAAA;MACA;IACA;IACAS,kBAAA,WAAAA,mBAAAC,IAAA;MACA,KAAAC,IAAA,CAAAC,WAAA,CAAAF,IAAA;MACA,SAAA5D,MAAA,CAAAkB,IAAA,CAAA6C,IAAA;QACA,KAAAxE,MAAA,CAAAyD,QAAA,gCAAAhD,MAAA;MACA;IACA;IACAgE,gBAAA,WAAAA,iBAAAJ,IAAA;MAAA,IAAAK,MAAA;MACA,KAAAJ,IAAA,CAAAK,SAAA,CAAAN,IAAA,EAAAO,IAAA,WAAAC,IAAA;QAAA,IAAA9E,YAAA,GAAA8E,IAAA,CAAA9E,YAAA;QACA,IAAA2E,MAAA,CAAArD,QAAA,CAAAgD,IAAA;UACAK,MAAA,CAAAI,UAAA,CAAA/E,YAAA,EAAAsE,IAAA;QACA;MACA;IACA;IACAU,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAAV,IAAA,CAAAW,cAAA,MAAArF,WAAA,EAAAgF,IAAA,WAAA7E,YAAA;QACA,KAAAA,YAAA,CAAAmF,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAArD,QAAA,KAAAkD,MAAA,CAAAvE,MAAA,CAAAqB,QAAA;QAAA;UACAkD,MAAA,CAAAF,UAAA,CAAA/E,YAAA;QACA;MACA;IACA;IACAqF,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,KAAAf,IAAA,CAAAgB,aAAA,MAAA1F,WAAA,EAAAgF,IAAA,WAAA7E,YAAA;QACA,KAAAA,YAAA,CAAAmF,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAArD,QAAA,KAAAuD,MAAA,CAAA5E,MAAA,CAAAqB,QAAA;QAAA;UACAuD,MAAA,CAAAP,UAAA,CAAA/E,YAAA;QACA;MACA;IACA;IACAwF,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,OAAA,CAAA9C,IAAA,MAAA/C,WAAA,CAAAkC,QAAA,EAAA4D,KAAA;MACA,KAAApB,IAAA,CAAAqB,cAAA,MAAA/F,WAAA,EAAAgF,IAAA;QACAY,MAAA,CAAA7E,gBAAA;MACA;IACA;IACAiF,YAAA,WAAAA,aAAAvB,IAAA;MAAA,IAAAwB,MAAA;MACA,KAAAvB,IAAA,CAAAwB,YAAA,GAAAlB,IAAA,WAAAmB,KAAA;QAAA,IAAAhG,YAAA,GAAAgG,KAAA,CAAAhG,YAAA;QACA,IAAA8F,MAAA,CAAAhG,SAAA,CAAAmG,IAAA,WAAAvE,GAAA;UAAA,OAAAA,GAAA,CAAAF,IAAA,KAAAsE,MAAA,CAAApF,MAAA,CAAAc,IAAA;QAAA;UACA;QACA;QACAsE,MAAA,CAAAf,UAAA,CAAA/E,YAAA,EAAAsE,IAAA;MACA;IACA;IACAS,UAAA,WAAAA,WAAA/E,YAAA,EAAAsE,IAAA;MACA,IAAA4B,UAAA,GAAAlG,YAAA,CAAAmG,KAAA;MACA,IAAAD,UAAA;QACA,KAAAR,OAAA,CAAA9C,IAAA,CAAAsD,UAAA,CAAAnE,QAAA;MACA;QACA;QACA;QACA,IAAAuC,IAAA,CAAAzB,IAAA;UACA;UACA,KAAA6C,OAAA,CAAAU,OAAA;YAAA5E,IAAA,gBAAA8C,IAAA,CAAAvC;UAAA;QACA;UACA,KAAA2D,OAAA,CAAA9C,IAAA;QACA;MACA;IACA;IACAyD,QAAA,WAAAA,SAAA3E,GAAA,EAAAiC,CAAA;MACA,IAAA2C,YAAA;MACA,IAAAC,UAAA,QAAAC,GAAA,CAAAC,qBAAA,GAAA7G,IAAA;MACA,IAAA8G,WAAA,QAAAF,GAAA,CAAAE,WAAA;MACA,IAAAC,OAAA,GAAAD,WAAA,GAAAJ,YAAA;MACA,IAAA1G,IAAA,GAAA+D,CAAA,CAAAiD,OAAA,GAAAL,UAAA;;MAEA,IAAA3G,IAAA,GAAA+G,OAAA;QACA,KAAA/G,IAAA,GAAA+G,OAAA;MACA;QACA,KAAA/G,IAAA,GAAAA,IAAA;MACA;MAEA,KAAAD,GAAA,GAAAgE,CAAA,CAAAkD,OAAA;MACA,KAAAnH,OAAA;MACA,KAAAG,WAAA,GAAA6B,GAAA;IACA;IACAT,SAAA,WAAAA,UAAA;MACA,KAAAvB,OAAA;IACA;IACAoH,YAAA,WAAAA,aAAA;MACA,KAAA7F,SAAA;IACA;EACA;AACA", "ignoreList": []}]}