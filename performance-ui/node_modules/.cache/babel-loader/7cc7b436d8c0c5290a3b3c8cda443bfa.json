{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/permission.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/permission.js", "mtime": 1753510684530}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_router", "_interopRequireDefault", "require", "_store", "_elementUi", "_nprogress", "_auth", "_validate", "_request", "NProgress", "configure", "showSpinner", "whiteList", "isWhiteList", "path", "some", "pattern", "isPathMatch", "router", "beforeEach", "to", "from", "next", "start", "getToken", "meta", "title", "store", "dispatch", "done", "getters", "roles", "length", "then", "accessRoutes", "addRoutes", "_objectSpread2", "default", "replace", "catch", "err", "Message", "error", "concat", "encodeURIComponent", "fullPath", "after<PERSON>ach"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/permission.js"], "sourcesContent": ["import router from './router'\nimport store from './store'\nimport { Message } from 'element-ui'\nimport NProgress from 'nprogress'\nimport 'nprogress/nprogress.css'\nimport { getToken } from '@/utils/auth'\nimport { isPathMatch } from '@/utils/validate'\nimport { isRelogin } from '@/utils/request'\n\nNProgress.configure({ showSpinner: false })\n\nconst whiteList = ['/login', '/register']\n\nconst isWhiteList = (path) => {\n  return whiteList.some(pattern => isPathMatch(pattern, path))\n}\n\nrouter.beforeEach((to, from, next) => {\n  NProgress.start()\n  if (getToken()) {\n    to.meta.title && store.dispatch('settings/setTitle', to.meta.title)\n    /* has token*/\n    if (to.path === '/login') {\n      next({ path: '/' })\n      NProgress.done()\n    } else if (isWhiteList(to.path)) {\n      next()\n    } else {\n      if (store.getters.roles.length === 0) {\n        // 判断当前用户是否已拉取完user_info信息\n        store.dispatch('GetInfo').then(() => {\n          store.dispatch('GenerateRoutes').then(accessRoutes => {\n            // 根据roles权限生成可访问的路由表\n            router.addRoutes(accessRoutes) // 动态添加可访问路由表\n            next({ ...to, replace: true }) // hack方法 确保addRoutes已完成\n          })\n        }).catch(err => {\n            store.dispatch('LogOut').then(() => {\n              Message.error(err)\n              next({ path: '/login' })\n            })\n          })\n      } else {\n        next()\n      }\n    }\n  } else {\n    // 没有token\n    if (isWhiteList(to.path)) {\n      // 在免登录白名单，直接进入\n      next()\n    } else {\n      next(`/login?redirect=${encodeURIComponent(to.fullPath)}`) // 否则全部重定向到登录页\n      NProgress.done()\n    }\n  }\n})\n\nrouter.afterEach(() => {\n  NProgress.done()\n})\n"], "mappings": ";;;;;AAAA,IAAAA,OAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,UAAA,GAAAF,OAAA;AACA,IAAAG,UAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACAA,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;AACA,IAAAK,SAAA,GAAAL,OAAA;AACA,IAAAM,QAAA,GAAAN,OAAA;AAEAO,kBAAS,CAACC,SAAS,CAAC;EAAEC,WAAW,EAAE;AAAM,CAAC,CAAC;AAE3C,IAAMC,SAAS,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC;AAEzC,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAIC,IAAI,EAAK;EAC5B,OAAOF,SAAS,CAACG,IAAI,CAAC,UAAAC,OAAO;IAAA,OAAI,IAAAC,qBAAW,EAACD,OAAO,EAAEF,IAAI,CAAC;EAAA,EAAC;AAC9D,CAAC;AAEDI,eAAM,CAACC,UAAU,CAAC,UAACC,EAAE,EAAEC,IAAI,EAAEC,IAAI,EAAK;EACpCb,kBAAS,CAACc,KAAK,CAAC,CAAC;EACjB,IAAI,IAAAC,cAAQ,EAAC,CAAC,EAAE;IACdJ,EAAE,CAACK,IAAI,CAACC,KAAK,IAAIC,cAAK,CAACC,QAAQ,CAAC,mBAAmB,EAAER,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;IACnE;IACA,IAAIN,EAAE,CAACN,IAAI,KAAK,QAAQ,EAAE;MACxBQ,IAAI,CAAC;QAAER,IAAI,EAAE;MAAI,CAAC,CAAC;MACnBL,kBAAS,CAACoB,IAAI,CAAC,CAAC;IAClB,CAAC,MAAM,IAAIhB,WAAW,CAACO,EAAE,CAACN,IAAI,CAAC,EAAE;MAC/BQ,IAAI,CAAC,CAAC;IACR,CAAC,MAAM;MACL,IAAIK,cAAK,CAACG,OAAO,CAACC,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;QACpC;QACAL,cAAK,CAACC,QAAQ,CAAC,SAAS,CAAC,CAACK,IAAI,CAAC,YAAM;UACnCN,cAAK,CAACC,QAAQ,CAAC,gBAAgB,CAAC,CAACK,IAAI,CAAC,UAAAC,YAAY,EAAI;YACpD;YACAhB,eAAM,CAACiB,SAAS,CAACD,YAAY,CAAC,EAAC;YAC/BZ,IAAI,KAAAc,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAMjB,EAAE;cAAEkB,OAAO,EAAE;YAAI,EAAE,CAAC,EAAC;UACjC,CAAC,CAAC;QACJ,CAAC,CAAC,CAACC,KAAK,CAAC,UAAAC,GAAG,EAAI;UACZb,cAAK,CAACC,QAAQ,CAAC,QAAQ,CAAC,CAACK,IAAI,CAAC,YAAM;YAClCQ,kBAAO,CAACC,KAAK,CAACF,GAAG,CAAC;YAClBlB,IAAI,CAAC;cAAER,IAAI,EAAE;YAAS,CAAC,CAAC;UAC1B,CAAC,CAAC;QACJ,CAAC,CAAC;MACN,CAAC,MAAM;QACLQ,IAAI,CAAC,CAAC;MACR;IACF;EACF,CAAC,MAAM;IACL;IACA,IAAIT,WAAW,CAACO,EAAE,CAACN,IAAI,CAAC,EAAE;MACxB;MACAQ,IAAI,CAAC,CAAC;IACR,CAAC,MAAM;MACLA,IAAI,oBAAAqB,MAAA,CAAoBC,kBAAkB,CAACxB,EAAE,CAACyB,QAAQ,CAAC,CAAE,CAAC,EAAC;MAC3DpC,kBAAS,CAACoB,IAAI,CAAC,CAAC;IAClB;EACF;AACF,CAAC,CAAC;AAEFX,eAAM,CAAC4B,SAAS,CAAC,YAAM;EACrBrC,kBAAS,CAACoB,IAAI,CAAC,CAAC;AAClB,CAAC,CAAC", "ignoreList": []}]}