{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/formats/video.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/formats/video.js", "mtime": 1753510684095}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_block", "require", "_link", "_interopRequireDefault", "ATTRIBUTES", "Video", "_BlockEmbed", "_classCallCheck2", "default", "_callSuper2", "arguments", "_inherits2", "_createClass2", "key", "value", "format", "name", "indexOf", "domNode", "setAttribute", "removeAttribute", "_superPropGet2", "html", "_this$value", "video", "concat", "create", "node", "sanitize", "formats", "reduce", "attribute", "hasAttribute", "getAttribute", "url", "Link", "BlockEmbed", "_defineProperty2", "_default", "exports"], "sources": ["../../src/formats/video.ts"], "sourcesContent": ["import { BlockEmbed } from '../blots/block.js';\nimport Link from './link.js';\n\nconst ATTRIBUTES = ['height', 'width'];\n\nclass Video extends BlockEmbed {\n  static blotName = 'video';\n  static className = 'ql-video';\n  static tagName = 'IFRAME';\n\n  static create(value: string) {\n    const node = super.create(value) as Element;\n    node.setAttribute('frameborder', '0');\n    node.setAttribute('allowfullscreen', 'true');\n    node.setAttribute('src', this.sanitize(value));\n    return node;\n  }\n\n  static formats(domNode: Element) {\n    return ATTRIBUTES.reduce(\n      (formats: Record<string, string | null>, attribute) => {\n        if (domNode.hasAttribute(attribute)) {\n          formats[attribute] = domNode.getAttribute(attribute);\n        }\n        return formats;\n      },\n      {},\n    );\n  }\n\n  static sanitize(url: string) {\n    return Link.sanitize(url);\n  }\n\n  static value(domNode: Element) {\n    return domNode.getAttribute('src');\n  }\n\n  domNode: HTMLVideoElement;\n\n  format(name: string, value: string) {\n    if (ATTRIBUTES.indexOf(name) > -1) {\n      if (value) {\n        this.domNode.setAttribute(name, value);\n      } else {\n        this.domNode.removeAttribute(name);\n      }\n    } else {\n      super.format(name, value);\n    }\n  }\n\n  html() {\n    const { video } = this.value();\n    return `<a href=\"${video}\">${video}</a>`;\n  }\n}\n\nexport default Video;\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAC,sBAAA,CAAAF,OAAA;AAEA,IAAMG,UAAU,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC;AAAA,IAEhCC,KAAK,0BAAAC,WAAA;EAAA,SAAAD,MAAA;IAAA,IAAAE,gBAAA,CAAAC,OAAA,QAAAH,KAAA;IAAA,WAAAI,WAAA,CAAAD,OAAA,QAAAH,KAAA,EAAAK,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAH,KAAA,EAAAC,WAAA;EAAA,WAAAM,aAAA,CAAAJ,OAAA,EAAAH,KAAA;IAAAQ,GAAA;IAAAC,KAAA,EAmCT,SAAAC,MAAMA,CAACC,IAAY,EAAEF,KAAa,EAAE;MAClC,IAAIV,UAAU,CAACa,OAAO,CAACD,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;QACjC,IAAIF,KAAK,EAAE;UACT,IAAI,CAACI,OAAO,CAACC,YAAY,CAACH,IAAI,EAAEF,KAAK,CAAC;QACxC,CAAC,MAAM;UACL,IAAI,CAACI,OAAO,CAACE,eAAe,CAACJ,IAAI,CAAC;QACpC;MACF,CAAC,MAAM;QACL,IAAAK,cAAA,CAAAb,OAAA,EAAAH,KAAA,sBAAaW,IAAI,EAAEF,KAAK;MAC1B;IACF;EAAA;IAAAD,GAAA;IAAAC,KAAA,EAEA,SAAAQ,IAAIA,CAAA,EAAG;MACL,IAAAC,WAAA,GAAkB,IAAI,CAACT,KAAK,CAAC,CAAC;QAAtBU,KAAA,GAAAD,WAAA,CAAAC,KAAA;MACR,oBAAAC,MAAA,CAAmBD,KAAM,SAAAC,MAAA,CAAID,KAAM;IACrC;EAAA;IAAAX,GAAA;IAAAC,KAAA,EA7CA,SAAOY,MAAMA,CAACZ,KAAa,EAAE;MAC3B,IAAMa,IAAI,OAAAN,cAAA,CAAAb,OAAA,EAAAH,KAAA,sBAAgBS,KAAK,EAAY;MAC3Ca,IAAI,CAACR,YAAY,CAAC,aAAa,EAAE,GAAG,CAAC;MACrCQ,IAAI,CAACR,YAAY,CAAC,iBAAiB,EAAE,MAAM,CAAC;MAC5CQ,IAAI,CAACR,YAAY,CAAC,KAAK,EAAE,IAAI,CAACS,QAAQ,CAACd,KAAK,CAAC,CAAC;MAC9C,OAAOa,IAAI;IACb;EAAA;IAAAd,GAAA;IAAAC,KAAA,EAEA,SAAOe,OAAOA,CAACX,OAAgB,EAAE;MAC/B,OAAOd,UAAU,CAAC0B,MAAM,CACtB,UAACD,OAAsC,EAAEE,SAAS,EAAK;QACrD,IAAIb,OAAO,CAACc,YAAY,CAACD,SAAS,CAAC,EAAE;UACnCF,OAAO,CAACE,SAAS,CAAC,GAAGb,OAAO,CAACe,YAAY,CAACF,SAAS,CAAC;QACtD;QACA,OAAOF,OAAO;MAChB,CAAC,EACD,CAAC,CACH,CAAC;IACH;EAAA;IAAAhB,GAAA;IAAAC,KAAA,EAEA,SAAOc,QAAQA,CAACM,GAAW,EAAE;MAC3B,OAAOC,aAAI,CAACP,QAAQ,CAACM,GAAG,CAAC;IAC3B;EAAA;IAAArB,GAAA;IAAAC,KAAA,EAEA,SAAOA,KAAKA,CAACI,OAAgB,EAAE;MAC7B,OAAOA,OAAO,CAACe,YAAY,CAAC,KAAK,CAAC;IACpC;EAAA;AAAA,EA/BkBG,iBAAU;AAAA,IAAAC,gBAAA,CAAA7B,OAAA,EAAxBH,KAAK,cACS,OAAO;AAAA,IAAAgC,gBAAA,CAAA7B,OAAA,EADrBH,KAAK,eAEU,UAAU;AAAA,IAAAgC,gBAAA,CAAA7B,OAAA,EAFzBH,KAAK,aAGQ,QAAQ;AAAA,IAAAiC,QAAA,GAAAC,OAAA,CAAA/B,OAAA,GAkDZH,KAAK", "ignoreList": []}]}