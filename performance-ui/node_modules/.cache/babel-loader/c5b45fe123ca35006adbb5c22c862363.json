{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/performance/leader/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/performance/leader/index.vue", "mtime": 1754318751675}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_leader", "require", "name", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "list", "title", "open", "queryParams", "pageNum", "pageSize", "taskType", "orgName", "planYear", "form", "rules", "required", "message", "trigger", "max", "performanceTask", "targetMeasures", "evaluationCriteria", "responsibility", "taskCategory", "weightScore", "deadline", "importUrl", "process", "env", "VUE_APP_BASE_API", "importParams", "uploadHeaders", "created", "getList", "Authorization", "$store", "getters", "token", "methods", "_this", "<PERSON><PERSON><PERSON><PERSON>", "then", "response", "rows", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "cancel", "reset", "id", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleEdit", "row", "_this2", "<PERSON><PERSON><PERSON><PERSON>", "catch", "$modal", "msgError", "submitForm", "_this3", "$refs", "validate", "valid", "updateLeader", "msgSuccess", "addLeader", "handleDelete", "_this4", "concat", "confirm", "<PERSON><PERSON><PERSON><PERSON>", "join", "handleExportSelected", "_this5", "selectedRow", "find", "handleExportSingle", "batchExportLeader", "blob", "Blob", "type", "link", "document", "createElement", "href", "window", "URL", "createObjectURL", "download", "click", "revokeObjectURL", "closeLoading", "_this6", "exportLeader", "beforeImportUpload", "file", "isExcel", "handleImportSuccess", "code", "msg", "handleImportError", "error", "console", "status", "dispatch", "location", "downloadTemplate", "downloadExcelTemplate"], "sources": ["src/views/performance/leader/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"姓名\" prop=\"name\">\n        <el-input\n          v-model=\"queryParams.name\"\n          placeholder=\"请输入姓名\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"任务类型\" prop=\"taskType\">\n        <el-input\n          v-model=\"queryParams.taskType\"\n          placeholder=\"请输入任务类型\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\" v-hasPermi=\"['performance:leader:add']\">新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"danger\" plain icon=\"el-icon-delete\" size=\"mini\" :disabled=\"multiple\" @click=\"handleDelete\" v-hasPermi=\"['performance:leader:remove']\">删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" :disabled=\"multiple\" @click=\"handleExportSelected\" v-hasPermi=\"['performance:leader:export']\">导出Word</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-upload\n          class=\"upload-demo\"\n          :action=\"importUrl\"\n          :headers=\"uploadHeaders\"\n          :on-success=\"handleImportSuccess\"\n          :on-error=\"handleImportError\"\n          :before-upload=\"beforeImportUpload\"\n          :show-file-list=\"false\"\n          :data=\"importParams\"\n          style=\"display: inline-block;\"\n          v-hasPermi=\"['performance:leader:import']\">\n          <el-button type=\"info\" plain icon=\"el-icon-upload2\" size=\"mini\">导入Excel</el-button>\n        </el-upload>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"info\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"downloadTemplate\" v-hasPermi=\"['performance:leader:list']\">下载模板</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n    \n    <el-table \n      v-loading=\"loading\"\n      :data=\"list\" \n      @selection-change=\"handleSelectionChange\"\n      style=\"width: 100%\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column prop=\"name\" label=\"姓名\" width=\"100\"/>\n      <el-table-column prop=\"taskType\" label=\"任务类型\" width=\"120\"/>\n      <el-table-column prop=\"performanceTask\" label=\"绩效任务\" min-width=\"200\" show-overflow-tooltip/>\n      <el-table-column prop=\"targetMeasures\" label=\"目标及措施\" min-width=\"200\" show-overflow-tooltip/>\n      <el-table-column prop=\"evaluationCriteria\" label=\"评价标准\" min-width=\"200\" show-overflow-tooltip/>\n      <el-table-column prop=\"taskCategory\" label=\"责任分类\" width=\"100\"/>\n      <el-table-column prop=\"weightScore\" label=\"权重分值\" width=\"100\"/>\n      <el-table-column prop=\"deadline\" label=\"完成时限\" width=\"120\"/>\n      <el-table-column label=\"操作\" align=\"center\" width=\"180\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleEdit(scope.row)\"\n            v-hasPermi=\"['performance:leader:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['performance:leader:remove']\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"900px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"姓名\" prop=\"name\">\n              <el-input v-model=\"form.name\" placeholder=\"请输入姓名\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"任务类型\" prop=\"taskType\">\n              <el-input v-model=\"form.taskType\" placeholder=\"请输入任务类型/类别\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-form-item label=\"绩效任务\" prop=\"performanceTask\">\n          <el-input v-model=\"form.performanceTask\" type=\"textarea\" placeholder=\"请输入绩效任务\" />\n        </el-form-item>\n        <el-form-item label=\"目标及措施\" prop=\"targetMeasures\">\n          <el-input v-model=\"form.targetMeasures\" type=\"textarea\" placeholder=\"请输入目标及措施\" />\n        </el-form-item>\n        <el-form-item label=\"评价标准\" prop=\"evaluationCriteria\">\n          <el-input v-model=\"form.evaluationCriteria\" type=\"textarea\" placeholder=\"请输入评价标准\" />\n        </el-form-item>\n        <el-row>\n          <el-col :span=\"8\">\n            <el-form-item label=\"责任\" prop=\"responsibility\">\n              <el-input v-model=\"form.responsibility\" placeholder=\"请输入责任\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"责任分类\" prop=\"taskCategory\">\n              <el-input v-model=\"form.taskCategory\" placeholder=\"请输入责任分类\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"权重分值\" prop=\"weightScore\">\n              <el-input v-model=\"form.weightScore\" placeholder=\"请输入权重分值\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"8\">\n            <el-form-item label=\"完成时限\" prop=\"deadline\">\n              <el-input v-model=\"form.deadline\" placeholder=\"请输入完成时限\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属组织\" prop=\"orgName\">\n              <el-input v-model=\"form.orgName\" placeholder=\"请输入所属组织\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"计划年份\" prop=\"planYear\">\n              <el-input v-model=\"form.planYear\" placeholder=\"请输入计划年份\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  listLeader, getLeader, addLeader, updateLeader, delLeader,\n  exportLeader, batchExportLeader, downloadTemplate, downloadExcelTemplate\n} from '@/api/performance/leader'\n\nexport default {\n  name: \"LeaderPlan\",\n  data() {\n    return {\n      loading: true,\n      ids: [],\n      single: true,\n      multiple: true,\n      showSearch: true,\n      total: 0,\n      list: [],\n      title: \"\",\n      open: false,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        name: null,\n        taskType: null,\n        orgName: null,\n        planYear: null\n      },\n      form: {},\n      rules: {\n        name: [\n          { required: true, message: \"姓名不能为空\", trigger: \"blur\" },\n          { max: 100, message: \"姓名长度不能超过100个字符\", trigger: \"blur\" }\n        ],\n        taskType: [\n          { required: true, message: \"任务类型不能为空\", trigger: \"blur\" },\n          { max: 100, message: \"任务类型长度不能超过100个字符\", trigger: \"blur\" }\n        ],\n        performanceTask: [\n          { required: true, message: \"绩效任务不能为空\", trigger: \"blur\" },\n          { max: 1000, message: \"绩效任务长度不能超过1000个字符\", trigger: \"blur\" }\n        ],\n        targetMeasures: [\n          { max: 1000, message: \"目标及措施长度不能超过1000个字符\", trigger: \"blur\" }\n        ],\n        evaluationCriteria: [\n          { max: 1000, message: \"评价标准长度不能超过1000个字符\", trigger: \"blur\" }\n        ],\n        responsibility: [\n          { max: 100, message: \"责任长度不能超过100个字符\", trigger: \"blur\" }\n        ],\n        taskCategory: [\n          { max: 100, message: \"责任分类长度不能超过100个字符\", trigger: \"blur\" }\n        ],\n        weightScore: [\n          { max: 50, message: \"权重分值长度不能超过50个字符\", trigger: \"blur\" }\n        ],\n        deadline: [\n          { max: 100, message: \"完成时限长度不能超过100个字符\", trigger: \"blur\" }\n        ],\n        orgName: [\n          { max: 100, message: \"所属组织长度不能超过100个字符\", trigger: \"blur\" }\n        ],\n        planYear: [\n          { max: 10, message: \"计划年份长度不能超过10个字符\", trigger: \"blur\" }\n        ]\n      },\n      importUrl: process.env.VUE_APP_BASE_API + '/performance/leader/importExcel',\n      importParams: { planYear: '', orgName: '' },\n      uploadHeaders: {}\n    }\n  },\n  created() {\n    this.getList();\n    // 设置上传认证头\n    this.uploadHeaders = {\n      Authorization: 'Bearer ' + this.$store.getters.token\n    };\n  },\n  methods: {\n    getList() {\n      this.loading = true;\n      listLeader(this.queryParams).then(response => {\n        this.list = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    reset() {\n      this.form = {\n        id: null,\n        name: null,\n        taskType: null,\n        performanceTask: null,\n        targetMeasures: null,\n        evaluationCriteria: null,\n        responsibility: null,\n        taskCategory: null,\n        weightScore: null,\n        deadline: null,\n        orgName: null,\n        planYear: null\n      };\n      this.resetForm(\"form\");\n    },\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加班子成员绩效计划\";\n    },\n    handleEdit(row) {\n      this.reset();\n      const id = row.id;\n      getLeader(id).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改班子成员绩效计划\";\n      }).catch(() => {\n        this.$modal.msgError(\"获取数据失败\");\n      });\n    },\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id != null) {\n            updateLeader(this.form).then(() => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            }).catch(() => {\n              this.$modal.msgError(\"修改失败\");\n            });\n          } else {\n            addLeader(this.form).then(() => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            }).catch(() => {\n              this.$modal.msgError(\"新增失败\");\n            });\n          }\n        }\n      });\n    },\n    handleDelete(row) {\n      const ids = row ? [row.id] : this.ids;\n      const message = row\n        ? `是否确认删除\"${row.name}\"的绩效计划数据项？`\n        : `是否确认删除选中的${ids.length}条班子成员绩效计划数据项？`;\n\n      this.$modal.confirm(message).then(function() {\n        return delLeader(ids.join(','));\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    handleExportSelected() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要导出的数据\");\n        return;\n      }\n      // 如果只选择了一条数据，使用单个导出\n      if (this.ids.length === 1) {\n        const selectedRow = this.list.find(item => item.id === this.ids[0]);\n        this.handleExportSingle(selectedRow);\n        return;\n      }\n      // 多条数据使用批量导出\n      this.$modal.loading(\"正在导出数据，请稍候...\");\n      batchExportLeader(this.ids).then(response => {\n        const blob = new Blob([response], { \n          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' \n        });\n        const link = document.createElement('a');\n        link.href = window.URL.createObjectURL(blob);\n        link.download = '班子成员绩效计划.docx';\n        link.click();\n        window.URL.revokeObjectURL(link.href);\n        this.$modal.closeLoading();\n      }).catch(() => {\n        this.$modal.closeLoading();\n      });\n    },\n    handleExportSingle(row) {\n      this.$modal.loading(\"正在导出数据，请稍候...\");\n      exportLeader(row.id).then(response => {\n        const blob = new Blob([response], { \n          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' \n        });\n        const link = document.createElement('a');\n        link.href = window.URL.createObjectURL(blob);\n        link.download = `${row.name || '班子成员'}_绩效计划.docx`;\n        link.click();\n        window.URL.revokeObjectURL(link.href);\n        this.$modal.closeLoading();\n      }).catch(() => {\n        this.$modal.closeLoading();\n      });\n    },\n    beforeImportUpload(file) {\n      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\n                      file.type === 'application/vnd.ms-excel';\n      if (!isExcel) {\n        this.$modal.msgError('只能上传Excel文件格式(.xlsx或.xls)!');\n      }\n      return isExcel;\n    },\n    handleImportSuccess(response) {\n      if (response.code === 200) {\n        this.$modal.msgSuccess('导入成功');\n        this.getList();\n      } else {\n        this.$modal.msgError(response.msg || '导入失败');\n      }\n    },\n    handleImportError(error) {\n      console.error(\"导入失败:\", error);\n      if (error.status === 401) {\n        this.$modal.msgError(\"认证失败，请重新登录\");\n        this.$store.dispatch('LogOut').then(() => {\n          location.href = '/login';\n        });\n      } else {\n        this.$modal.msgError(\"导入失败，请检查文件格式\");\n      }\n    },\n    downloadTemplate() {\n      downloadExcelTemplate().then(response => {\n        const blob = new Blob([response], {\n          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\n        });\n        const link = document.createElement('a');\n        link.href = window.URL.createObjectURL(blob);\n        link.download = '班子成员绩效计划模板.xlsx';\n        link.click();\n        window.URL.revokeObjectURL(link.href);\n      });\n    }\n  }\n}\n</script> \n\n<style scoped>\n.upload-demo {\n  display: inline-block;\n  }\n  </style> "], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAqKA,IAAAA,OAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAKA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,GAAA;MACAC,MAAA;MACAC,QAAA;MACAC,UAAA;MACAC,KAAA;MACAC,IAAA;MACAC,KAAA;MACAC,IAAA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAb,IAAA;QACAc,QAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACAC,IAAA;MACAC,KAAA;QACAlB,IAAA,GACA;UAAAmB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACAP,QAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,eAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACAG,cAAA,GACA;UAAAF,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACAI,kBAAA,GACA;UAAAH,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACAK,cAAA,GACA;UAAAJ,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACAM,YAAA,GACA;UAAAL,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACAO,WAAA,GACA;UAAAN,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACAQ,QAAA,GACA;UAAAP,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,OAAA,GACA;UAAAO,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,QAAA,GACA;UAAAM,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAS,SAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACAC,YAAA;QAAAlB,QAAA;QAAAD,OAAA;MAAA;MACAoB,aAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA;IACA,KAAAF,aAAA;MACAG,aAAA,mBAAAC,MAAA,CAAAC,OAAA,CAAAC;IACA;EACA;EACAC,OAAA;IACAL,OAAA,WAAAA,QAAA;MAAA,IAAAM,KAAA;MACA,KAAAzC,OAAA;MACA,IAAA0C,kBAAA,OAAAjC,WAAA,EAAAkC,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAnC,IAAA,GAAAsC,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAApC,KAAA,GAAAuC,QAAA,CAAAvC,KAAA;QACAoC,KAAA,CAAAzC,OAAA;MACA;IACA;IACA8C,WAAA,WAAAA,YAAA;MACA,KAAArC,WAAA,CAAAC,OAAA;MACA,KAAAyB,OAAA;IACA;IACAY,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAF,WAAA;IACA;IACAG,MAAA,WAAAA,OAAA;MACA,KAAAzC,IAAA;MACA,KAAA0C,KAAA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAnC,IAAA;QACAoC,EAAA;QACArD,IAAA;QACAc,QAAA;QACAS,eAAA;QACAC,cAAA;QACAC,kBAAA;QACAC,cAAA;QACAC,YAAA;QACAC,WAAA;QACAC,QAAA;QACAd,OAAA;QACAC,QAAA;MACA;MACA,KAAAkC,SAAA;IACA;IACAI,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAApD,GAAA,GAAAoD,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAJ,EAAA;MAAA;MACA,KAAAjD,MAAA,GAAAmD,SAAA,CAAAG,MAAA;MACA,KAAArD,QAAA,IAAAkD,SAAA,CAAAG,MAAA;IACA;IACAC,SAAA,WAAAA,UAAA;MACA,KAAAP,KAAA;MACA,KAAA1C,IAAA;MACA,KAAAD,KAAA;IACA;IACAmD,UAAA,WAAAA,WAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAV,KAAA;MACA,IAAAC,EAAA,GAAAQ,GAAA,CAAAR,EAAA;MACA,IAAAU,iBAAA,EAAAV,EAAA,EAAAR,IAAA,WAAAC,QAAA;QACAgB,MAAA,CAAA7C,IAAA,GAAA6B,QAAA,CAAA7C,IAAA;QACA6D,MAAA,CAAApD,IAAA;QACAoD,MAAA,CAAArD,KAAA;MACA,GAAAuD,KAAA;QACAF,MAAA,CAAAG,MAAA,CAAAC,QAAA;MACA;IACA;IACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAnD,IAAA,CAAAoC,EAAA;YACA,IAAAmB,oBAAA,EAAAJ,MAAA,CAAAnD,IAAA,EAAA4B,IAAA;cACAuB,MAAA,CAAAH,MAAA,CAAAQ,UAAA;cACAL,MAAA,CAAA1D,IAAA;cACA0D,MAAA,CAAA/B,OAAA;YACA,GAAA2B,KAAA;cACAI,MAAA,CAAAH,MAAA,CAAAC,QAAA;YACA;UACA;YACA,IAAAQ,iBAAA,EAAAN,MAAA,CAAAnD,IAAA,EAAA4B,IAAA;cACAuB,MAAA,CAAAH,MAAA,CAAAQ,UAAA;cACAL,MAAA,CAAA1D,IAAA;cACA0D,MAAA,CAAA/B,OAAA;YACA,GAAA2B,KAAA;cACAI,MAAA,CAAAH,MAAA,CAAAC,QAAA;YACA;UACA;QACA;MACA;IACA;IACAS,YAAA,WAAAA,aAAAd,GAAA;MAAA,IAAAe,MAAA;MACA,IAAAzE,GAAA,GAAA0D,GAAA,IAAAA,GAAA,CAAAR,EAAA,SAAAlD,GAAA;MACA,IAAAiB,OAAA,GAAAyC,GAAA,4CAAAgB,MAAA,CACAhB,GAAA,CAAA7D,IAAA,yHAAA6E,MAAA,CACA1E,GAAA,CAAAuD,MAAA;MAEA,KAAAO,MAAA,CAAAa,OAAA,CAAA1D,OAAA,EAAAyB,IAAA;QACA,WAAAkC,iBAAA,EAAA5E,GAAA,CAAA6E,IAAA;MACA,GAAAnC,IAAA;QACA+B,MAAA,CAAAvC,OAAA;QACAuC,MAAA,CAAAX,MAAA,CAAAQ,UAAA;MACA,GAAAT,KAAA;IACA;IACAiB,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA,SAAA/E,GAAA,CAAAuD,MAAA;QACA,KAAAO,MAAA,CAAAC,QAAA;QACA;MACA;MACA;MACA,SAAA/D,GAAA,CAAAuD,MAAA;QACA,IAAAyB,WAAA,QAAA3E,IAAA,CAAA4E,IAAA,WAAA3B,IAAA;UAAA,OAAAA,IAAA,CAAAJ,EAAA,KAAA6B,MAAA,CAAA/E,GAAA;QAAA;QACA,KAAAkF,kBAAA,CAAAF,WAAA;QACA;MACA;MACA;MACA,KAAAlB,MAAA,CAAA/D,OAAA;MACA,IAAAoF,yBAAA,OAAAnF,GAAA,EAAA0C,IAAA,WAAAC,QAAA;QACA,IAAAyC,IAAA,OAAAC,IAAA,EAAA1C,QAAA;UACA2C,IAAA;QACA;QACA,IAAAC,IAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,IAAA,CAAAG,IAAA,GAAAC,MAAA,CAAAC,GAAA,CAAAC,eAAA,CAAAT,IAAA;QACAG,IAAA,CAAAO,QAAA;QACAP,IAAA,CAAAQ,KAAA;QACAJ,MAAA,CAAAC,GAAA,CAAAI,eAAA,CAAAT,IAAA,CAAAG,IAAA;QACAX,MAAA,CAAAjB,MAAA,CAAAmC,YAAA;MACA,GAAApC,KAAA;QACAkB,MAAA,CAAAjB,MAAA,CAAAmC,YAAA;MACA;IACA;IACAf,kBAAA,WAAAA,mBAAAxB,GAAA;MAAA,IAAAwC,MAAA;MACA,KAAApC,MAAA,CAAA/D,OAAA;MACA,IAAAoG,oBAAA,EAAAzC,GAAA,CAAAR,EAAA,EAAAR,IAAA,WAAAC,QAAA;QACA,IAAAyC,IAAA,OAAAC,IAAA,EAAA1C,QAAA;UACA2C,IAAA;QACA;QACA,IAAAC,IAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,IAAA,CAAAG,IAAA,GAAAC,MAAA,CAAAC,GAAA,CAAAC,eAAA,CAAAT,IAAA;QACAG,IAAA,CAAAO,QAAA,MAAApB,MAAA,CAAAhB,GAAA,CAAA7D,IAAA;QACA0F,IAAA,CAAAQ,KAAA;QACAJ,MAAA,CAAAC,GAAA,CAAAI,eAAA,CAAAT,IAAA,CAAAG,IAAA;QACAQ,MAAA,CAAApC,MAAA,CAAAmC,YAAA;MACA,GAAApC,KAAA;QACAqC,MAAA,CAAApC,MAAA,CAAAmC,YAAA;MACA;IACA;IACAG,kBAAA,WAAAA,mBAAAC,IAAA;MACA,IAAAC,OAAA,GAAAD,IAAA,CAAAf,IAAA,4EACAe,IAAA,CAAAf,IAAA;MACA,KAAAgB,OAAA;QACA,KAAAxC,MAAA,CAAAC,QAAA;MACA;MACA,OAAAuC,OAAA;IACA;IACAC,mBAAA,WAAAA,oBAAA5D,QAAA;MACA,IAAAA,QAAA,CAAA6D,IAAA;QACA,KAAA1C,MAAA,CAAAQ,UAAA;QACA,KAAApC,OAAA;MACA;QACA,KAAA4B,MAAA,CAAAC,QAAA,CAAApB,QAAA,CAAA8D,GAAA;MACA;IACA;IACAC,iBAAA,WAAAA,kBAAAC,KAAA;MACAC,OAAA,CAAAD,KAAA,UAAAA,KAAA;MACA,IAAAA,KAAA,CAAAE,MAAA;QACA,KAAA/C,MAAA,CAAAC,QAAA;QACA,KAAA3B,MAAA,CAAA0E,QAAA,WAAApE,IAAA;UACAqE,QAAA,CAAArB,IAAA;QACA;MACA;QACA,KAAA5B,MAAA,CAAAC,QAAA;MACA;IACA;IACAiD,gBAAA,WAAAA,iBAAA;MACA,IAAAC,6BAAA,IAAAvE,IAAA,WAAAC,QAAA;QACA,IAAAyC,IAAA,OAAAC,IAAA,EAAA1C,QAAA;UACA2C,IAAA;QACA;QACA,IAAAC,IAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,IAAA,CAAAG,IAAA,GAAAC,MAAA,CAAAC,GAAA,CAAAC,eAAA,CAAAT,IAAA;QACAG,IAAA,CAAAO,QAAA;QACAP,IAAA,CAAAQ,KAAA;QACAJ,MAAA,CAAAC,GAAA,CAAAI,eAAA,CAAAT,IAAA,CAAAG,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}