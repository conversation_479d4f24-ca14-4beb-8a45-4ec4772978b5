{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/HeaderSearch/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/HeaderSearch/index.vue", "mtime": 1753510684527}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_fuseMin", "_interopRequireDefault", "require", "_path", "_validate", "name", "data", "search", "options", "searchPool", "activeIndex", "show", "fuse", "undefined", "computed", "theme", "$store", "state", "settings", "routes", "getters", "defaultRoutes", "watch", "generateRoutes", "list", "initFuse", "mounted", "methods", "click", "$refs", "headerSearchSelect", "focus", "close", "blur", "change", "val", "_this", "path", "query", "isHttp", "pindex", "indexOf", "window", "open", "substr", "length", "$router", "push", "JSON", "parse", "$nextTick", "<PERSON><PERSON>", "shouldSort", "threshold", "location", "distance", "minMatchChar<PERSON>ength", "keys", "weight", "basePath", "arguments", "prefixTitle", "res", "_iterator", "_createForOfIteratorHelper2", "default", "_step", "s", "n", "done", "router", "value", "hidden", "resolve", "title", "_toConsumableArray2", "icon", "meta", "concat", "redirect", "children", "tempRoutes", "err", "e", "f", "querySearch", "_this$fuse$search$map", "map", "item", "activeStyle", "index", "navigateResult", "direction", "selectActiveResult"], "sources": ["src/components/HeaderSearch/index.vue"], "sourcesContent": ["<template>\n  <div class=\"header-search\">\n    <svg-icon class-name=\"search-icon\" icon-class=\"search\" @click.stop=\"click\" />\n    <el-dialog\n      :visible.sync=\"show\"\n      width=\"600px\"\n      @close=\"close\"\n      :show-close=\"false\"\n      append-to-body\n    >\n      <el-input\n        v-model=\"search\"\n        ref=\"headerSearchSelectRef\"\n        size=\"large\"\n        @input=\"querySearch\"\n        prefix-icon=\"el-icon-search\"\n        placeholder=\"菜单搜索，支持标题、URL模糊查询\"\n        clearable\n        @keyup.enter.native=\"selectActiveResult\"\n        @keydown.up.native=\"navigateResult('up')\"\n        @keydown.down.native=\"navigateResult('down')\"\n      >\n      </el-input>\n      <el-scrollbar wrap-class=\"right-scrollbar-wrapper\">\n        <div class=\"result-wrap\">\n          <div class=\"search-item\" v-for=\"(item, index) in options\" :key=\"item.path\" :style=\"activeStyle(index)\" @mouseenter=\"activeIndex = index\" @mouseleave=\"activeIndex = -1\">\n            <div class=\"left\">\n              <svg-icon class=\"menu-icon\" :icon-class=\"item.icon\" />\n            </div>\n            <div class=\"search-info\" @click=\"change(item)\">\n              <div class=\"menu-title\">\n                {{ item.title.join(\" / \") }}\n              </div>\n              <div class=\"menu-path\">\n                {{ item.path }}\n              </div>\n            </div>\n            <svg-icon icon-class=\"enter\" v-show=\"index === activeIndex\"/>\n          </div>\n       </div>\n      </el-scrollbar>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport Fuse from 'fuse.js/dist/fuse.min.js'\nimport path from 'path'\nimport { isHttp } from '@/utils/validate'\n\nexport default {\n  name: 'HeaderSearch',\n  data() {\n    return {\n      search: '',\n      options: [],\n      searchPool: [],\n      activeIndex: -1,\n      show: false,\n      fuse: undefined\n    }\n  },\n  computed: {\n    theme() {\n      return this.$store.state.settings.theme\n    },\n    routes() {\n      return this.$store.getters.defaultRoutes\n    }\n  },\n  watch: {\n    routes() {\n      this.searchPool = this.generateRoutes(this.routes)\n    },\n    searchPool(list) {\n      this.initFuse(list)\n    }\n  },\n  mounted() {\n    this.searchPool = this.generateRoutes(this.routes)\n  },\n  methods: {\n    click() {\n      this.show = !this.show\n      if (this.show) {\n        this.$refs.headerSearchSelect && this.$refs.headerSearchSelect.focus()\n        this.options = this.searchPool\n      }\n    },\n    close() {\n      this.$refs.headerSearchSelect && this.$refs.headerSearchSelect.blur()\n      this.search = ''\n      this.options = []\n      this.show = false\n      this.activeIndex = -1\n    },\n    change(val) {\n      const path = val.path\n      const query = val.query\n      if(isHttp(val.path)) {\n        // http(s):// 路径新窗口打开\n        const pindex = path.indexOf(\"http\")\n        window.open(path.substr(pindex, path.length), \"_blank\")\n      } else {\n        if (query) {\n          this.$router.push({ path: path, query: JSON.parse(query) })\n        } else {\n          this.$router.push(path)\n        }\n      }\n      this.search = ''\n      this.options = []\n      this.$nextTick(() => {\n        this.show = false\n      })\n    },\n    initFuse(list) {\n      this.fuse = new Fuse(list, {\n        shouldSort: true,\n        threshold: 0.4,\n        location: 0,\n        distance: 100,\n        minMatchCharLength: 1,\n        keys: [{\n          name: 'title',\n          weight: 0.7\n        }, {\n          name: 'path',\n          weight: 0.3\n        }]\n      })\n    },\n    // Filter out the routes that can be displayed in the sidebar\n    // And generate the internationalized title\n    generateRoutes(routes, basePath = '/', prefixTitle = []) {\n      let res = []\n\n      for (const router of routes) {\n        // skip hidden router\n        if (router.hidden) { continue }\n\n        const data = {\n          path: !isHttp(router.path) ? path.resolve(basePath, router.path) : router.path,\n          title: [...prefixTitle],\n          icon: ''\n        }\n\n        if (router.meta && router.meta.title) {\n          data.title = [...data.title, router.meta.title]\n          data.icon = router.meta.icon\n\n          if (router.redirect !== 'noRedirect') {\n            // only push the routes with title\n            // special case: need to exclude parent router without redirect\n            res.push(data)\n          }\n        }\n\n        if (router.query) {\n          data.query = router.query\n        }\n\n        // recursive child routes\n        if (router.children) {\n          const tempRoutes = this.generateRoutes(router.children, data.path, data.title)\n          if (tempRoutes.length >= 1) {\n            res = [...res, ...tempRoutes]\n          }\n        }\n      }\n      return res\n    },\n    querySearch(query) {\n      this.activeIndex = -1\n      if (query !== '') {\n        this.options = this.fuse.search(query).map((item) => item.item) ?? this.searchPool\n      } else {\n        this.options = this.searchPool\n      }\n    },\n    activeStyle(index) {\n      if (index !== this.activeIndex) return {}\n      return {\n        \"background-color\": this.theme,\n        \"color\": \"#fff\"\n      }\n    },\n    navigateResult(direction) {\n      if (direction === \"up\") {\n        this.activeIndex = this.activeIndex <= 0 ? this.options.length - 1 : this.activeIndex - 1\n      } else if (direction === \"down\") {\n        this.activeIndex = this.activeIndex >= this.options.length - 1 ? 0 : this.activeIndex + 1\n      }\n    },\n    selectActiveResult() {\n      if (this.options.length > 0 && this.activeIndex >= 0) {\n        this.change(this.options[this.activeIndex])\n      }\n    }\n  }\n}\n</script>\n\n<style lang='scss' scoped>\n::v-deep {\n  .el-dialog__header {\n    padding: 0 !important;\n  }\n}\n\n.header-search {\n  .search-icon {\n    cursor: pointer;\n    font-size: 18px;\n    vertical-align: middle;\n  }\n}\n\n.result-wrap {\n  height: 280px;\n  margin: 6px 0;\n\n  .search-item {\n    display: flex;\n    height: 48px;\n    align-items: center;\n    padding-right: 10px;\n\n    .left {\n      width: 60px;\n      text-align: center;\n\n      .menu-icon {\n        width: 18px;\n        height: 18px;\n      }\n    }\n\n    .search-info {\n      padding-left: 5px;\n      margin-top: 10px;\n      width: 100%;\n      display: flex;\n      flex-direction: column;\n      justify-content: flex-start;\n      flex: 1;\n\n      .menu-title,\n      .menu-path {\n        height: 20px;\n      }\n      .menu-path {\n        color: #ccc;\n        font-size: 10px;\n      }\n    }\n  }\n\n  .search-item:hover {\n    cursor: pointer;\n  }\n}\n</style>\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;AA8CA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,KAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,MAAA;MACAC,OAAA;MACAC,UAAA;MACAC,WAAA;MACAC,IAAA;MACAC,IAAA,EAAAC;IACA;EACA;EACAC,QAAA;IACAC,KAAA,WAAAA,MAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAH,KAAA;IACA;IACAI,MAAA,WAAAA,OAAA;MACA,YAAAH,MAAA,CAAAI,OAAA,CAAAC,aAAA;IACA;EACA;EACAC,KAAA;IACAH,MAAA,WAAAA,OAAA;MACA,KAAAV,UAAA,QAAAc,cAAA,MAAAJ,MAAA;IACA;IACAV,UAAA,WAAAA,WAAAe,IAAA;MACA,KAAAC,QAAA,CAAAD,IAAA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAjB,UAAA,QAAAc,cAAA,MAAAJ,MAAA;EACA;EACAQ,OAAA;IACAC,KAAA,WAAAA,MAAA;MACA,KAAAjB,IAAA,SAAAA,IAAA;MACA,SAAAA,IAAA;QACA,KAAAkB,KAAA,CAAAC,kBAAA,SAAAD,KAAA,CAAAC,kBAAA,CAAAC,KAAA;QACA,KAAAvB,OAAA,QAAAC,UAAA;MACA;IACA;IACAuB,KAAA,WAAAA,MAAA;MACA,KAAAH,KAAA,CAAAC,kBAAA,SAAAD,KAAA,CAAAC,kBAAA,CAAAG,IAAA;MACA,KAAA1B,MAAA;MACA,KAAAC,OAAA;MACA,KAAAG,IAAA;MACA,KAAAD,WAAA;IACA;IACAwB,MAAA,WAAAA,OAAAC,GAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,IAAA,GAAAF,GAAA,CAAAE,IAAA;MACA,IAAAC,KAAA,GAAAH,GAAA,CAAAG,KAAA;MACA,QAAAC,gBAAA,EAAAJ,GAAA,CAAAE,IAAA;QACA;QACA,IAAAG,MAAA,GAAAH,IAAA,CAAAI,OAAA;QACAC,MAAA,CAAAC,IAAA,CAAAN,IAAA,CAAAO,MAAA,CAAAJ,MAAA,EAAAH,IAAA,CAAAQ,MAAA;MACA;QACA,IAAAP,KAAA;UACA,KAAAQ,OAAA,CAAAC,IAAA;YAAAV,IAAA,EAAAA,IAAA;YAAAC,KAAA,EAAAU,IAAA,CAAAC,KAAA,CAAAX,KAAA;UAAA;QACA;UACA,KAAAQ,OAAA,CAAAC,IAAA,CAAAV,IAAA;QACA;MACA;MACA,KAAA9B,MAAA;MACA,KAAAC,OAAA;MACA,KAAA0C,SAAA;QACAd,KAAA,CAAAzB,IAAA;MACA;IACA;IACAc,QAAA,WAAAA,SAAAD,IAAA;MACA,KAAAZ,IAAA,OAAAuC,gBAAA,CAAA3B,IAAA;QACA4B,UAAA;QACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,kBAAA;QACAC,IAAA;UACApD,IAAA;UACAqD,MAAA;QACA;UACArD,IAAA;UACAqD,MAAA;QACA;MACA;IACA;IACA;IACA;IACAnC,cAAA,WAAAA,eAAAJ,MAAA;MAAA,IAAAwC,QAAA,GAAAC,SAAA,CAAAf,MAAA,QAAAe,SAAA,QAAA/C,SAAA,GAAA+C,SAAA;MAAA,IAAAC,WAAA,GAAAD,SAAA,CAAAf,MAAA,QAAAe,SAAA,QAAA/C,SAAA,GAAA+C,SAAA;MACA,IAAAE,GAAA;MAAA,IAAAC,SAAA,OAAAC,2BAAA,CAAAC,OAAA,EAEA9C,MAAA;QAAA+C,KAAA;MAAA;QAAA,KAAAH,SAAA,CAAAI,CAAA,MAAAD,KAAA,GAAAH,SAAA,CAAAK,CAAA,IAAAC,IAAA;UAAA,IAAAC,MAAA,GAAAJ,KAAA,CAAAK,KAAA;UACA;UACA,IAAAD,MAAA,CAAAE,MAAA;YAAA;UAAA;UAEA,IAAAlE,IAAA;YACA+B,IAAA,OAAAE,gBAAA,EAAA+B,MAAA,CAAAjC,IAAA,IAAAA,aAAA,CAAAoC,OAAA,CAAAd,QAAA,EAAAW,MAAA,CAAAjC,IAAA,IAAAiC,MAAA,CAAAjC,IAAA;YACAqC,KAAA,MAAAC,mBAAA,CAAAV,OAAA,EAAAJ,WAAA;YACAe,IAAA;UACA;UAEA,IAAAN,MAAA,CAAAO,IAAA,IAAAP,MAAA,CAAAO,IAAA,CAAAH,KAAA;YACApE,IAAA,CAAAoE,KAAA,MAAAI,MAAA,KAAAH,mBAAA,CAAAV,OAAA,EAAA3D,IAAA,CAAAoE,KAAA,IAAAJ,MAAA,CAAAO,IAAA,CAAAH,KAAA;YACApE,IAAA,CAAAsE,IAAA,GAAAN,MAAA,CAAAO,IAAA,CAAAD,IAAA;YAEA,IAAAN,MAAA,CAAAS,QAAA;cACA;cACA;cACAjB,GAAA,CAAAf,IAAA,CAAAzC,IAAA;YACA;UACA;UAEA,IAAAgE,MAAA,CAAAhC,KAAA;YACAhC,IAAA,CAAAgC,KAAA,GAAAgC,MAAA,CAAAhC,KAAA;UACA;;UAEA;UACA,IAAAgC,MAAA,CAAAU,QAAA;YACA,IAAAC,UAAA,QAAA1D,cAAA,CAAA+C,MAAA,CAAAU,QAAA,EAAA1E,IAAA,CAAA+B,IAAA,EAAA/B,IAAA,CAAAoE,KAAA;YACA,IAAAO,UAAA,CAAApC,MAAA;cACAiB,GAAA,MAAAgB,MAAA,KAAAH,mBAAA,CAAAV,OAAA,EAAAH,GAAA,OAAAa,mBAAA,CAAAV,OAAA,EAAAgB,UAAA;YACA;UACA;QACA;MAAA,SAAAC,GAAA;QAAAnB,SAAA,CAAAoB,CAAA,CAAAD,GAAA;MAAA;QAAAnB,SAAA,CAAAqB,CAAA;MAAA;MACA,OAAAtB,GAAA;IACA;IACAuB,WAAA,WAAAA,YAAA/C,KAAA;MACA,KAAA5B,WAAA;MACA,IAAA4B,KAAA;QAAA,IAAAgD,qBAAA;QACA,KAAA9E,OAAA,IAAA8E,qBAAA,QAAA1E,IAAA,CAAAL,MAAA,CAAA+B,KAAA,EAAAiD,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAA,IAAA;QAAA,gBAAAF,qBAAA,cAAAA,qBAAA,QAAA7E,UAAA;MACA;QACA,KAAAD,OAAA,QAAAC,UAAA;MACA;IACA;IACAgF,WAAA,WAAAA,YAAAC,KAAA;MACA,IAAAA,KAAA,UAAAhF,WAAA;MACA;QACA,yBAAAK,KAAA;QACA;MACA;IACA;IACA4E,cAAA,WAAAA,eAAAC,SAAA;MACA,IAAAA,SAAA;QACA,KAAAlF,WAAA,QAAAA,WAAA,aAAAF,OAAA,CAAAqC,MAAA,YAAAnC,WAAA;MACA,WAAAkF,SAAA;QACA,KAAAlF,WAAA,QAAAA,WAAA,SAAAF,OAAA,CAAAqC,MAAA,gBAAAnC,WAAA;MACA;IACA;IACAmF,kBAAA,WAAAA,mBAAA;MACA,SAAArF,OAAA,CAAAqC,MAAA,aAAAnC,WAAA;QACA,KAAAwB,MAAA,MAAA1B,OAAA,MAAAE,WAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}