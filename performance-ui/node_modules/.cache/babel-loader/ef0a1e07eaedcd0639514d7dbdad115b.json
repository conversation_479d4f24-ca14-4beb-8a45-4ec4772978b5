{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/performance/personnel.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/performance/personnel.js", "mtime": 1753510684516}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listPersonnelPlan", "query", "request", "url", "method", "params", "getPersonnelPlan", "id", "addPersonnelPlan", "data", "updatePersonnelPlan", "delPersonnelPlan", "ids", "importPersonnelPlan", "exportPersonnelPlan", "responseType", "batchExportPersonnelPlan", "downloadTemplate"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/performance/personnel.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询科级及以下人员个人绩效计划列表\nexport function listPersonnelPlan(query) {\n  return request({\n    url: '/performance/personnel/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询科级及以下人员个人绩效计划详细信息\nexport function getPersonnelPlan(id) {\n  return request({\n    url: '/performance/personnel/' + id,\n    method: 'get'\n  })\n}\n\n// 新增科级及以下人员个人绩效计划\nexport function addPersonnelPlan(data) {\n  return request({\n    url: '/performance/personnel',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改科级及以下人员个人绩效计划\nexport function updatePersonnelPlan(data) {\n  return request({\n    url: '/performance/personnel',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除科级及以下人员个人绩效计划\nexport function delPersonnelPlan(ids) {\n  return request({\n    url: '/performance/personnel/' + ids,\n    method: 'delete'\n  })\n}\n\n// 导入Word文档\nexport function importPersonnelPlan(data) {\n  return request({\n    url: '/performance/personnel/import',\n    method: 'post',\n    data: data\n  })\n}\n\n// 导出单个Word文档\nexport function exportPersonnelPlan(id) {\n  return request({\n    url: '/performance/personnel/export/' + id,\n    method: 'get',\n    responseType: 'blob'\n  })\n}\n\n// 批量导出Word文档\nexport function batchExportPersonnelPlan(ids) {\n  return request({\n    url: '/performance/personnel/batchExport',\n    method: 'post',\n    data: ids,\n    responseType: 'blob'\n  })\n}\n\n// 下载模板\nexport function downloadTemplate() {\n  return request({\n    url: '/performance/personnel/downloadTemplate',\n    method: 'get',\n    responseType: 'blob'\n  })\n} "], "mappings": ";;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,iBAAiBA,CAACC,KAAK,EAAE;EACvC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,gBAAgBA,CAACC,EAAE,EAAE;EACnC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB,GAAGI,EAAE;IACnCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,gBAAgBA,CAACC,IAAI,EAAE;EACrC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,mBAAmBA,CAACD,IAAI,EAAE;EACxC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,gBAAgBA,CAACC,GAAG,EAAE;EACpC,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB,GAAGS,GAAG;IACpCR,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,mBAAmBA,CAACJ,IAAI,EAAE;EACxC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,+BAA+B;IACpCC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,mBAAmBA,CAACP,EAAE,EAAE;EACtC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC,GAAGI,EAAE;IAC1CH,MAAM,EAAE,KAAK;IACbW,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,wBAAwBA,CAACJ,GAAG,EAAE;EAC5C,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,oCAAoC;IACzCC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEG,GAAG;IACTG,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,gBAAgBA,CAAA,EAAG;EACjC,OAAO,IAAAf,gBAAO,EAAC;IACbC,GAAG,EAAE,yCAAyC;IAC9CC,MAAM,EAAE,KAAK;IACbW,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ", "ignoreList": []}]}