{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/performance/privateAnnual.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/performance/privateAnnual.js", "mtime": 1753510684517}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listPrivateAnnual", "query", "request", "url", "method", "params", "getPrivateAnnual", "id", "addPrivateAnnual", "data", "updatePrivateAnnual", "delPrivateAnnual", "exportPrivateAnnual", "exportWordPrivateAnnual", "ids", "responseType", "downloadTemplateAnnual", "importWordPrivateAnnual", "file", "formData", "FormData", "append", "headers"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/performance/privateAnnual.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询个人年度评价列表\nexport function listPrivateAnnual(query) {\n  return request({\n    url: '/system/privateAnnual/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询个人年度评价详细\nexport function getPrivateAnnual(id) {\n  return request({\n    url: '/system/privateAnnual/' + id,\n    method: 'get'\n  })\n}\n\n// 新增个人年度评价\nexport function addPrivateAnnual(data) {\n  return request({\n    url: '/system/privateAnnual',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改个人年度评价\nexport function updatePrivateAnnual(data) {\n  return request({\n    url: '/system/privateAnnual',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除个人年度评价\nexport function delPrivateAnnual(id) {\n  return request({\n    url: '/system/privateAnnual/' + id,\n    method: 'delete'\n  })\n}\n\n// 导出Excel\nexport function exportPrivateAnnual(query) {\n  return request({\n    url: '/system/privateAnnual/export',\n    method: 'post',\n    params: query\n  })\n}\n\n// 导出Word\nexport function exportWordPrivateAnnual(ids) {\n  return request({\n    url: '/system/privateAnnual/exportWord',\n    method: 'post',\n    data: ids,\n    responseType: 'blob'\n  })\n}\n\n// 下载模板\nexport function downloadTemplateAnnual() {\n  return request({\n    url: '/system/privateAnnual/downloadTemplate',\n    method: 'get',\n    responseType: 'blob'\n  })\n}\n\n// 导入Word文件\nexport function importWordPrivateAnnual(file) {\n  const formData = new FormData()\n  formData.append('file', file)\n  return request({\n    url: '/system/privateAnnual/importWord',\n    method: 'post',\n    data: formData,\n    headers: {\n      'Content-Type': 'multipart/form-data'\n    }\n  })\n} "], "mappings": ";;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,iBAAiBA,CAACC,KAAK,EAAE;EACvC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,gBAAgBA,CAACC,EAAE,EAAE;EACnC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB,GAAGI,EAAE;IAClCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,gBAAgBA,CAACC,IAAI,EAAE;EACrC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,mBAAmBA,CAACD,IAAI,EAAE;EACxC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,gBAAgBA,CAACJ,EAAE,EAAE;EACnC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB,GAAGI,EAAE;IAClCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,mBAAmBA,CAACX,KAAK,EAAE;EACzC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE,MAAM;IACdC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASY,uBAAuBA,CAACC,GAAG,EAAE;EAC3C,OAAO,IAAAZ,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC;IACvCC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEK,GAAG;IACTC,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,sBAAsBA,CAAA,EAAG;EACvC,OAAO,IAAAd,gBAAO,EAAC;IACbC,GAAG,EAAE,wCAAwC;IAC7CC,MAAM,EAAE,KAAK;IACbW,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,uBAAuBA,CAACC,IAAI,EAAE;EAC5C,IAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;EAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC;EAC7B,OAAO,IAAAhB,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC;IACvCC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEU,QAAQ;IACdG,OAAO,EAAE;MACP,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;AACJ", "ignoreList": []}]}