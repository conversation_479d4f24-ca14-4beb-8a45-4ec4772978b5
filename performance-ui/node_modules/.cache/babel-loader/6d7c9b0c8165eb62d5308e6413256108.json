{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/performance/leader.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/performance/leader.js", "mtime": 1754318728656}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "<PERSON><PERSON><PERSON><PERSON>", "query", "request", "url", "method", "params", "<PERSON><PERSON><PERSON><PERSON>", "id", "addLeader", "data", "updateLeader", "<PERSON><PERSON><PERSON><PERSON>", "ids", "exportLeader", "responseType", "batchExportLeader", "importLeader", "importExcelLeader", "downloadTemplate", "downloadExcelTemplate"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/performance/leader.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询领导班子绩效计划列表\nexport function listLeader(query) {\n  return request({\n    url: '/performance/leader/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询领导班子绩效计划详细信息\nexport function getLeader(id) {\n  return request({\n    url: '/performance/leader/' + id,\n    method: 'get'\n  })\n}\n\n// 新增领导班子绩效计划\nexport function addLeader(data) {\n  return request({\n    url: '/performance/leader',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改领导班子绩效计划\nexport function updateLeader(data) {\n  return request({\n    url: '/performance/leader',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除领导班子绩效计划\nexport function delLeader(ids) {\n  return request({\n    url: '/performance/leader/' + ids,\n    method: 'delete'\n  })\n}\n\n// 导出单个Word文档\nexport function exportLeader(id) {\n  return request({\n    url: '/performance/leader/export/' + id,\n    method: 'get',\n    responseType: 'blob'\n  })\n}\n\n// 批量导出Word文档\nexport function batchExportLeader(ids) {\n  return request({\n    url: '/performance/leader/batchExport',\n    method: 'post',\n    data: ids,\n    responseType: 'blob'\n  })\n}\n\n// 导入Word文档\nexport function importLeader(data) {\n  return request({\n    url: '/performance/leader/import',\n    method: 'post',\n    data: data\n  })\n}\n\n// 导入Excel文档\nexport function importExcelLeader(data) {\n  return request({\n    url: '/performance/leader/importExcel',\n    method: 'post',\n    data: data\n  })\n}\n\n// 下载模板\nexport function downloadTemplate() {\n  return request({\n    url: '/performance/leader/downloadTemplate',\n    method: 'get',\n    responseType: 'blob'\n  })\n}\n\n// 下载Excel模板\nexport function downloadExcelTemplate() {\n  return request({\n    url: '/performance/leader/downloadExcelTemplate',\n    method: 'get',\n    responseType: 'blob'\n  })\n}"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,UAAUA,CAACC,KAAK,EAAE;EAChC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,SAASA,CAACC,EAAE,EAAE;EAC5B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB,GAAGI,EAAE;IAChCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,SAASA,CAACC,IAAI,EAAE;EAC9B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,YAAYA,CAACD,IAAI,EAAE;EACjC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,SAASA,CAACC,GAAG,EAAE;EAC7B,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB,GAAGS,GAAG;IACjCR,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,YAAYA,CAACN,EAAE,EAAE;EAC/B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B,GAAGI,EAAE;IACvCH,MAAM,EAAE,KAAK;IACbU,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,iBAAiBA,CAACH,GAAG,EAAE;EACrC,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEG,GAAG;IACTE,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,YAAYA,CAACP,IAAI,EAAE;EACjC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,iBAAiBA,CAACR,IAAI,EAAE;EACtC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,gBAAgBA,CAAA,EAAG;EACjC,OAAO,IAAAhB,gBAAO,EAAC;IACbC,GAAG,EAAE,sCAAsC;IAC3CC,MAAM,EAAE,KAAK;IACbU,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,qBAAqBA,CAAA,EAAG;EACtC,OAAO,IAAAjB,gBAAO,EAAC;IACbC,GAAG,EAAE,2CAA2C;IAChDC,MAAM,EAAE,KAAK;IACbU,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ", "ignoreList": []}]}