{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/utils/dict/DictOptions.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/utils/dict/DictOptions.js", "mtime": 1753510684531}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_ruoyi", "require", "_DictConverter", "_interopRequireDefault", "options", "exports", "metas", "request", "dictMeta", "console", "log", "concat", "type", "Promise", "resolve", "responseConverter", "labelField", "valueField", "DEFAULT_LABEL_FIELDS", "DEFAULT_VALUE_FIELDS", "response", "dicts", "content", "Array", "undefined", "warn", "map", "d", "dictConverter", "mergeOptions", "src", "mergeRecursive", "_default", "default"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/utils/dict/DictOptions.js"], "sourcesContent": ["import { mergeRecursive } from \"@/utils/ruoyi\"\nimport dictConverter from './DictConverter'\n\nexport const options = {\n  metas: {\n    '*': {\n      /**\n       * 字典请求，方法签名为function(dictMeta: DictMeta): Promise\n       */\n      request: (dictMeta) => {\n        console.log(`load dict ${dictMeta.type}`)\n        return Promise.resolve([])\n      },\n      /**\n       * 字典响应数据转换器，方法签名为function(response: Object, dictMeta: DictMeta): DictData\n       */\n      responseConverter,\n      labelField: 'label',\n      valueField: 'value',\n    },\n  },\n  /**\n   * 默认标签字段\n   */\n  DEFAULT_LABEL_FIELDS: ['label', 'name', 'title'],\n  /**\n   * 默认值字段\n   */\n  DEFAULT_VALUE_FIELDS: ['value', 'id', 'uid', 'key'],\n}\n\n/**\n * 映射字典\n * @param {Object} response 字典数据\n * @param {DictMeta} dictMeta 字典元数据\n * @returns {DictData}\n */\nfunction responseConverter(response, dictMeta) {\n  const dicts = response.content instanceof Array ? response.content : response\n  if (dicts === undefined) {\n    console.warn(`no dict data of \"${dictMeta.type}\" found in the response`)\n    return []\n  }\n  return dicts.map(d => dictConverter(d, dictMeta))\n}\n\nexport function mergeOptions(src) {\n  mergeRecursive(options, src)\n}\n\nexport default options\n"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAC,sBAAA,CAAAF,OAAA;AAEO,IAAMG,OAAO,GAAAC,OAAA,CAAAD,OAAA,GAAG;EACrBE,KAAK,EAAE;IACL,GAAG,EAAE;MACH;AACN;AACA;MACMC,OAAO,EAAE,SAATA,OAAOA,CAAGC,QAAQ,EAAK;QACrBC,OAAO,CAACC,GAAG,cAAAC,MAAA,CAAcH,QAAQ,CAACI,IAAI,CAAE,CAAC;QACzC,OAAOC,OAAO,CAACC,OAAO,CAAC,EAAE,CAAC;MAC5B,CAAC;MACD;AACN;AACA;MACMC,iBAAiB,EAAjBA,iBAAiB;MACjBC,UAAU,EAAE,OAAO;MACnBC,UAAU,EAAE;IACd;EACF,CAAC;EACD;AACF;AACA;EACEC,oBAAoB,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;EAChD;AACF;AACA;EACEC,oBAAoB,EAAE,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK;AACpD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,SAASJ,iBAAiBA,CAACK,QAAQ,EAAEZ,QAAQ,EAAE;EAC7C,IAAMa,KAAK,GAAGD,QAAQ,CAACE,OAAO,YAAYC,KAAK,GAAGH,QAAQ,CAACE,OAAO,GAAGF,QAAQ;EAC7E,IAAIC,KAAK,KAAKG,SAAS,EAAE;IACvBf,OAAO,CAACgB,IAAI,sBAAAd,MAAA,CAAqBH,QAAQ,CAACI,IAAI,6BAAyB,CAAC;IACxE,OAAO,EAAE;EACX;EACA,OAAOS,KAAK,CAACK,GAAG,CAAC,UAAAC,CAAC;IAAA,OAAI,IAAAC,sBAAa,EAACD,CAAC,EAAEnB,QAAQ,CAAC;EAAA,EAAC;AACnD;AAEO,SAASqB,YAAYA,CAACC,GAAG,EAAE;EAChC,IAAAC,qBAAc,EAAC3B,OAAO,EAAE0B,GAAG,CAAC;AAC9B;AAAC,IAAAE,QAAA,GAAA3B,OAAA,CAAA4B,OAAA,GAEc7B,OAAO", "ignoreList": []}]}