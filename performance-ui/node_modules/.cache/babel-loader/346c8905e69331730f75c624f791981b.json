{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/themes/snow.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/themes/snow.js", "mtime": 1753510684098}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_lodashEs", "require", "_emitter", "_interopRequireDefault", "_base", "_interopRequireWildcard", "_link", "_selection", "_icons", "_quill", "TOOLBAR_CONFIG", "header", "list", "SnowTooltip", "_BaseTooltip", "_this", "_classCallCheck2", "default", "_len", "arguments", "length", "args", "Array", "_key", "_callSuper2", "concat", "_defineProperty2", "root", "querySelector", "_inherits2", "_createClass2", "key", "value", "listen", "_this2", "_superPropGet2", "addEventListener", "event", "classList", "contains", "save", "edit", "preview", "textContent", "preventDefault", "linkRange", "range", "restoreFocus", "quill", "formatText", "Emitter", "sources", "USER", "hide", "on", "events", "SELECTION_CHANGE", "oldRange", "source", "_this2$quill$scroll$d", "scroll", "descendant", "LinkBlot", "index", "_this2$quill$scroll$d2", "_slicedToArray2", "link", "offset", "Range", "formats", "domNode", "setAttribute", "show", "bounds", "getBounds", "position", "removeAttribute", "BaseTooltip", "join", "SnowTheme", "_BaseTheme", "options", "_this3", "modules", "toolbar", "container", "add", "extendToolbar", "buildButtons", "querySelectorAll", "icons", "buildPickers", "tooltip", "keyboard", "addBinding", "<PERSON><PERSON><PERSON>", "_range", "context", "handlers", "call", "format", "BaseTheme", "DEFAULTS", "merge", "getSelection", "getText", "test", "indexOf", "theme", "<PERSON><PERSON><PERSON>", "_default", "exports"], "sources": ["../../src/themes/snow.ts"], "sourcesContent": ["import { merge } from 'lodash-es';\nimport Emitter from '../core/emitter.js';\nimport BaseTheme, { BaseTooltip } from './base.js';\nimport LinkBlot from '../formats/link.js';\nimport { Range } from '../core/selection.js';\nimport icons from '../ui/icons.js';\nimport Quill from '../core/quill.js';\nimport type { Context } from '../modules/keyboard.js';\nimport type Toolbar from '../modules/toolbar.js';\nimport type { ToolbarConfig } from '../modules/toolbar.js';\nimport type { ThemeOptions } from '../core/theme.js';\n\nconst TOOLBAR_CONFIG: ToolbarConfig = [\n  [{ header: ['1', '2', '3', false] }],\n  ['bold', 'italic', 'underline', 'link'],\n  [{ list: 'ordered' }, { list: 'bullet' }],\n  ['clean'],\n];\n\nclass SnowTooltip extends BaseTooltip {\n  static TEMPLATE = [\n    '<a class=\"ql-preview\" rel=\"noopener noreferrer\" target=\"_blank\" href=\"about:blank\"></a>',\n    '<input type=\"text\" data-formula=\"e=mc^2\" data-link=\"https://quilljs.com\" data-video=\"Embed URL\">',\n    '<a class=\"ql-action\"></a>',\n    '<a class=\"ql-remove\"></a>',\n  ].join('');\n\n  preview = this.root.querySelector('a.ql-preview');\n\n  listen() {\n    super.listen();\n    // @ts-expect-error Fix me later\n    this.root\n      .querySelector('a.ql-action')\n      .addEventListener('click', (event) => {\n        if (this.root.classList.contains('ql-editing')) {\n          this.save();\n        } else {\n          // @ts-expect-error Fix me later\n          this.edit('link', this.preview.textContent);\n        }\n        event.preventDefault();\n      });\n    // @ts-expect-error Fix me later\n    this.root\n      .querySelector('a.ql-remove')\n      .addEventListener('click', (event) => {\n        if (this.linkRange != null) {\n          const range = this.linkRange;\n          this.restoreFocus();\n          this.quill.formatText(range, 'link', false, Emitter.sources.USER);\n          delete this.linkRange;\n        }\n        event.preventDefault();\n        this.hide();\n      });\n    this.quill.on(\n      Emitter.events.SELECTION_CHANGE,\n      (range, oldRange, source) => {\n        if (range == null) return;\n        if (range.length === 0 && source === Emitter.sources.USER) {\n          const [link, offset] = this.quill.scroll.descendant(\n            LinkBlot,\n            range.index,\n          );\n          if (link != null) {\n            this.linkRange = new Range(range.index - offset, link.length());\n            const preview = LinkBlot.formats(link.domNode);\n            // @ts-expect-error Fix me later\n            this.preview.textContent = preview;\n            // @ts-expect-error Fix me later\n            this.preview.setAttribute('href', preview);\n            this.show();\n            const bounds = this.quill.getBounds(this.linkRange);\n            if (bounds != null) {\n              this.position(bounds);\n            }\n            return;\n          }\n        } else {\n          delete this.linkRange;\n        }\n        this.hide();\n      },\n    );\n  }\n\n  show() {\n    super.show();\n    this.root.removeAttribute('data-mode');\n  }\n}\n\nclass SnowTheme extends BaseTheme {\n  constructor(quill: Quill, options: ThemeOptions) {\n    if (\n      options.modules.toolbar != null &&\n      options.modules.toolbar.container == null\n    ) {\n      options.modules.toolbar.container = TOOLBAR_CONFIG;\n    }\n    super(quill, options);\n    this.quill.container.classList.add('ql-snow');\n  }\n\n  extendToolbar(toolbar: Toolbar) {\n    if (toolbar.container != null) {\n      toolbar.container.classList.add('ql-snow');\n      this.buildButtons(toolbar.container.querySelectorAll('button'), icons);\n      this.buildPickers(toolbar.container.querySelectorAll('select'), icons);\n      // @ts-expect-error\n      this.tooltip = new SnowTooltip(this.quill, this.options.bounds);\n      if (toolbar.container.querySelector('.ql-link')) {\n        this.quill.keyboard.addBinding(\n          { key: 'k', shortKey: true },\n          (_range: Range, context: Context) => {\n            toolbar.handlers.link.call(toolbar, !context.format.link);\n          },\n        );\n      }\n    }\n  }\n}\nSnowTheme.DEFAULTS = merge({}, BaseTheme.DEFAULTS, {\n  modules: {\n    toolbar: {\n      handlers: {\n        link(value: string) {\n          if (value) {\n            const range = this.quill.getSelection();\n            if (range == null || range.length === 0) return;\n            let preview = this.quill.getText(range);\n            if (\n              /^\\S+@\\S+\\.\\S+$/.test(preview) &&\n              preview.indexOf('mailto:') !== 0\n            ) {\n              preview = `mailto:${preview}`;\n            }\n            // @ts-expect-error\n            const { tooltip } = this.quill.theme;\n            tooltip.edit('link', preview);\n          } else {\n            this.quill.format('link', false, Quill.sources.USER);\n          }\n        },\n      },\n    },\n  },\n} satisfies ThemeOptions);\n\nexport default SnowTheme;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,KAAA,GAAAC,uBAAA,CAAAJ,OAAA;AACA,IAAAK,KAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,UAAA,GAAAN,OAAA;AACA,IAAAO,MAAA,GAAAL,sBAAA,CAAAF,OAAA;AACA,IAAAQ,MAAA,GAAAN,sBAAA,CAAAF,OAAA;AAMA,IAAMS,cAA6B,GAAG,CACpC,CAAC;EAAEC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK;AAAE,CAAC,CAAC,EACpC,CAAC,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,CAAC,EACvC,CAAC;EAAEC,IAAI,EAAE;AAAU,CAAC,EAAE;EAAEA,IAAI,EAAE;AAAS,CAAC,CAAC,EACzC,CAAC,OAAO,CAAC,CACV;AAAA,IAEKC,WAAW,0BAAAC,YAAA;EAAA,SAAAD,YAAA;IAAA,IAAAE,KAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAJ,WAAA;IAAA,SAAAK,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAAF,IAAA,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IAAAR,KAAA,OAAAS,WAAA,CAAAP,OAAA,QAAAJ,WAAA,KAAAY,MAAA,CAAAJ,IAAA;IAAA,IAAAK,gBAAA,CAAAT,OAAA,EAAAF,KAAA,aAQLA,KAAA,CAAKY,IAAI,CAACC,aAAa,CAAC,cAAc,CAAC;IAAA,OAAAb,KAAA;EAAA;EAAA,IAAAc,UAAA,CAAAZ,OAAA,EAAAJ,WAAA,EAAAC,YAAA;EAAA,WAAAgB,aAAA,CAAAb,OAAA,EAAAJ,WAAA;IAAAkB,GAAA;IAAAC,KAAA,EAEjD,SAAAC,MAAMA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACP,IAAAC,cAAA,CAAAlB,OAAA,EAAAJ,WAAA;MACA;MACA,IAAI,CAACc,IAAI,CACNC,aAAa,CAAC,aAAa,CAAC,CAC5BQ,gBAAgB,CAAC,OAAO,EAAG,UAAAC,KAAK,EAAK;QACpC,IAAIH,MAAI,CAACP,IAAI,CAACW,SAAS,CAACC,QAAQ,CAAC,YAAY,CAAC,EAAE;UAC9CL,MAAI,CAACM,IAAI,CAAC,CAAC;QACb,CAAC,MAAM;UACL;UACAN,MAAI,CAACO,IAAI,CAAC,MAAM,EAAEP,MAAI,CAACQ,OAAO,CAACC,WAAW,CAAC;QAC7C;QACAN,KAAK,CAACO,cAAc,CAAC,CAAC;MACxB,CAAC,CAAC;MACJ;MACA,IAAI,CAACjB,IAAI,CACNC,aAAa,CAAC,aAAa,CAAC,CAC5BQ,gBAAgB,CAAC,OAAO,EAAG,UAAAC,KAAK,EAAK;QACpC,IAAIH,MAAI,CAACW,SAAS,IAAI,IAAI,EAAE;UAC1B,IAAMC,KAAK,GAAGZ,MAAI,CAACW,SAAS;UAC5BX,MAAI,CAACa,YAAY,CAAC,CAAC;UACnBb,MAAI,CAACc,KAAK,CAACC,UAAU,CAACH,KAAK,EAAE,MAAM,EAAE,KAAK,EAAEI,gBAAO,CAACC,OAAO,CAACC,IAAI,CAAC;UACjE,OAAOlB,MAAI,CAACW,SAAS;QACvB;QACAR,KAAK,CAACO,cAAc,CAAC,CAAC;QACtBV,MAAI,CAACmB,IAAI,CAAC,CAAC;MACb,CAAC,CAAC;MACJ,IAAI,CAACL,KAAK,CAACM,EAAE,CACXJ,gBAAO,CAACK,MAAM,CAACC,gBAAgB,EAC/B,UAACV,KAAK,EAAEW,QAAQ,EAAEC,MAAM,EAAK;QAC3B,IAAIZ,KAAK,IAAI,IAAI,EAAE;QACnB,IAAIA,KAAK,CAAC1B,MAAM,KAAK,CAAC,IAAIsC,MAAM,KAAKR,gBAAO,CAACC,OAAO,CAACC,IAAI,EAAE;UACzD,IAAAO,qBAAA,GAAuBzB,MAAI,CAACc,KAAK,CAACY,MAAM,CAACC,UAAU,CACjDC,aAAQ,EACRhB,KAAK,CAACiB,KACR,CAAC;YAAAC,sBAAA,OAAAC,eAAA,CAAAhD,OAAA,EAAA0C,qBAAA;YAHMO,IAAI,GAAAF,sBAAA;YAAEG,MAAM,GAAAH,sBAAA;UAInB,IAAIE,IAAI,IAAI,IAAI,EAAE;YAChBhC,MAAI,CAACW,SAAS,GAAG,IAAIuB,gBAAK,CAACtB,KAAK,CAACiB,KAAK,GAAGI,MAAM,EAAED,IAAI,CAAC9C,MAAM,CAAC,CAAC,CAAC;YAC/D,IAAMsB,OAAO,GAAGoB,aAAQ,CAACO,OAAO,CAACH,IAAI,CAACI,OAAO,CAAC;YAC9C;YACApC,MAAI,CAACQ,OAAO,CAACC,WAAW,GAAGD,OAAO;YAClC;YACAR,MAAI,CAACQ,OAAO,CAAC6B,YAAY,CAAC,MAAM,EAAE7B,OAAO,CAAC;YAC1CR,MAAI,CAACsC,IAAI,CAAC,CAAC;YACX,IAAMC,MAAM,GAAGvC,MAAI,CAACc,KAAK,CAAC0B,SAAS,CAACxC,MAAI,CAACW,SAAS,CAAC;YACnD,IAAI4B,MAAM,IAAI,IAAI,EAAE;cAClBvC,MAAI,CAACyC,QAAQ,CAACF,MAAM,CAAC;YACvB;YACA;UACF;QACF,CAAC,MAAM;UACL,OAAOvC,MAAI,CAACW,SAAS;QACvB;QACAX,MAAI,CAACmB,IAAI,CAAC,CAAC;MACb,CACF,CAAC;IACH;EAAA;IAAAtB,GAAA;IAAAC,KAAA,EAEA,SAAAwC,IAAIA,CAAA,EAAG;MACL,IAAArC,cAAA,CAAAlB,OAAA,EAAAJ,WAAA;MACA,IAAI,CAACc,IAAI,CAACiD,eAAe,CAAC,WAAW,CAAC;IACxC;EAAA;AAAA,EAvEwBC,iBAAW;AAAA,IAAAnD,gBAAA,CAAAT,OAAA,EAA/BJ,WAAW,cACG,CAChB,yFAAyF,EACzF,kGAAkG,EAClG,2BAA2B,EAC3B,2BAA2B,CAC5B,CAACiE,IAAI,CAAC,EAAE,CAAC;AAAA,IAoENC,SAAS,0BAAAC,UAAA;EACb,SAAAD,UAAY/B,KAAY,EAAEiC,OAAqB,EAAE;IAAA,IAAAC,MAAA;IAAA,IAAAlE,gBAAA,CAAAC,OAAA,QAAA8D,SAAA;IAC/C,IACEE,OAAO,CAACE,OAAO,CAACC,OAAO,IAAI,IAAI,IAC/BH,OAAO,CAACE,OAAO,CAACC,OAAO,CAACC,SAAS,IAAI,IAAI,EACzC;MACAJ,OAAO,CAACE,OAAO,CAACC,OAAO,CAACC,SAAS,GAAG3E,cAAc;IACpD;IACAwE,MAAA,OAAA1D,WAAA,CAAAP,OAAA,QAAA8D,SAAA,GAAM/B,KAAK,EAAEiC,OAAO;IACpBC,MAAA,CAAKlC,KAAK,CAACqC,SAAS,CAAC/C,SAAS,CAACgD,GAAG,CAAC,SAAS,CAAC;IAAA,OAAAJ,MAAA;EAC/C;EAAA,IAAArD,UAAA,CAAAZ,OAAA,EAAA8D,SAAA,EAAAC,UAAA;EAAA,WAAAlD,aAAA,CAAAb,OAAA,EAAA8D,SAAA;IAAAhD,GAAA;IAAAC,KAAA,EAEA,SAAAuD,aAAaA,CAACH,OAAgB,EAAE;MAC9B,IAAIA,OAAO,CAACC,SAAS,IAAI,IAAI,EAAE;QAC7BD,OAAO,CAACC,SAAS,CAAC/C,SAAS,CAACgD,GAAG,CAAC,SAAS,CAAC;QAC1C,IAAI,CAACE,YAAY,CAACJ,OAAO,CAACC,SAAS,CAACI,gBAAgB,CAAC,QAAQ,CAAC,EAAEC,cAAK,CAAC;QACtE,IAAI,CAACC,YAAY,CAACP,OAAO,CAACC,SAAS,CAACI,gBAAgB,CAAC,QAAQ,CAAC,EAAEC,cAAK,CAAC;QACtE;QACA,IAAI,CAACE,OAAO,GAAG,IAAI/E,WAAW,CAAC,IAAI,CAACmC,KAAK,EAAE,IAAI,CAACiC,OAAO,CAACR,MAAM,CAAC;QAC/D,IAAIW,OAAO,CAACC,SAAS,CAACzD,aAAa,CAAC,UAAU,CAAC,EAAE;UAC/C,IAAI,CAACoB,KAAK,CAAC6C,QAAQ,CAACC,UAAU,CAC5B;YAAE/D,GAAG,EAAE,GAAG;YAAEgE,QAAQ,EAAE;UAAK,CAAC,EAC5B,UAACC,MAAa,EAAEC,OAAgB,EAAK;YACnCb,OAAO,CAACc,QAAQ,CAAChC,IAAI,CAACiC,IAAI,CAACf,OAAO,EAAE,CAACa,OAAO,CAACG,MAAM,CAAClC,IAAI,CAAC;UAC3D,CACF,CAAC;QACH;MACF;IACF;EAAA;AAAA,EA5BsBmC,aAAS;AA8BjCtB,SAAS,CAACuB,QAAQ,GAAG,IAAAC,eAAK,EAAC,CAAC,CAAC,EAAEF,aAAS,CAACC,QAAQ,EAAE;EACjDnB,OAAO,EAAE;IACPC,OAAO,EAAE;MACPc,QAAQ,EAAE;QACRhC,IAAI,WAAJA,IAAIA,CAAClC,KAAa,EAAE;UAClB,IAAIA,KAAK,EAAE;YACT,IAAMc,KAAK,GAAG,IAAI,CAACE,KAAK,CAACwD,YAAY,CAAC,CAAC;YACvC,IAAI1D,KAAK,IAAI,IAAI,IAAIA,KAAK,CAAC1B,MAAM,KAAK,CAAC,EAAE;YACzC,IAAIsB,OAAO,GAAG,IAAI,CAACM,KAAK,CAACyD,OAAO,CAAC3D,KAAK,CAAC;YACvC,IACE,gBAAgB,CAAC4D,IAAI,CAAChE,OAAO,CAAC,IAC9BA,OAAO,CAACiE,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,EAChC;cACAjE,OAAO,aAAAjB,MAAA,CAAaiB,OAAQ,CAAC;YAC/B;YACA;YACA,IAAQkD,OAAA,GAAY,IAAI,CAAC5C,KAAK,CAAC4D,KAAK,CAA5BhB,OAAA;YACRA,OAAO,CAACnD,IAAI,CAAC,MAAM,EAAEC,OAAO,CAAC;UAC/B,CAAC,MAAM;YACL,IAAI,CAACM,KAAK,CAACoD,MAAM,CAAC,MAAM,EAAE,KAAK,EAAES,cAAK,CAAC1D,OAAO,CAACC,IAAI,CAAC;UACtD;QACF;MACF;IACF;EACF;AACF,CAAwB,CAAC;AAAA,IAAA0D,QAAA,GAAAC,OAAA,CAAA9F,OAAA,GAEV8D,SAAS", "ignoreList": []}]}