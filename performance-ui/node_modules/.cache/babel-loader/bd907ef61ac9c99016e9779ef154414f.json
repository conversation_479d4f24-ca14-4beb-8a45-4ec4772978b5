{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/layout/components/Sidebar/Link.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/layout/components/Sidebar/Link.vue", "mtime": 1753510684529}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfdmFsaWRhdGUgPSByZXF1aXJlKCJAL3V0aWxzL3ZhbGlkYXRlIik7Ci8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBwcm9wczogewogICAgdG86IHsKICAgICAgdHlwZTogW1N0cmluZywgT2JqZWN0XSwKICAgICAgcmVxdWlyZWQ6IHRydWUKICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICBpc0V4dGVybmFsOiBmdW5jdGlvbiBpc0V4dGVybmFsKCkgewogICAgICByZXR1cm4gKDAsIF92YWxpZGF0ZS5pc0V4dGVybmFsKSh0aGlzLnRvKTsKICAgIH0sCiAgICB0eXBlOiBmdW5jdGlvbiB0eXBlKCkgewogICAgICBpZiAodGhpcy5pc0V4dGVybmFsKSB7CiAgICAgICAgcmV0dXJuICdhJzsKICAgICAgfQogICAgICByZXR1cm4gJ3JvdXRlci1saW5rJzsKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIGxpbmtQcm9wczogZnVuY3Rpb24gbGlua1Byb3BzKHRvKSB7CiAgICAgIGlmICh0aGlzLmlzRXh0ZXJuYWwpIHsKICAgICAgICByZXR1cm4gewogICAgICAgICAgaHJlZjogdG8sCiAgICAgICAgICB0YXJnZXQ6ICdfYmxhbmsnLAogICAgICAgICAgcmVsOiAnbm9vcGVuZXInCiAgICAgICAgfTsKICAgICAgfQogICAgICByZXR1cm4gewogICAgICAgIHRvOiB0bwogICAgICB9OwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["_validate", "require", "props", "to", "type", "String", "Object", "required", "computed", "isExternal", "methods", "linkProps", "href", "target", "rel"], "sources": ["src/layout/components/Sidebar/Link.vue"], "sourcesContent": ["<template>\n  <component :is=\"type\" v-bind=\"linkProps(to)\">\n    <slot />\n  </component>\n</template>\n\n<script>\nimport { isExternal } from '@/utils/validate'\n\nexport default {\n  props: {\n    to: {\n      type: [String, Object],\n      required: true\n    }\n  },\n  computed: {\n    isExternal() {\n      return isExternal(this.to)\n    },\n    type() {\n      if (this.isExternal) {\n        return 'a'\n      }\n      return 'router-link'\n    }\n  },\n  methods: {\n    linkProps(to) {\n      if (this.isExternal) {\n        return {\n          href: to,\n          target: '_blank',\n          rel: 'noopener'\n        }\n      }\n      return {\n        to: to\n      }\n    }\n  }\n}\n</script>\n"], "mappings": ";;;;;;AAOA,IAAAA,SAAA,GAAAC,OAAA;;;;;;;iCAEA;EACAC,KAAA;IACAC,EAAA;MACAC,IAAA,GAAAC,MAAA,EAAAC,MAAA;MACAC,QAAA;IACA;EACA;EACAC,QAAA;IACAC,UAAA,WAAAA,WAAA;MACA,WAAAA,oBAAA,OAAAN,EAAA;IACA;IACAC,IAAA,WAAAA,KAAA;MACA,SAAAK,UAAA;QACA;MACA;MACA;IACA;EACA;EACAC,OAAA;IACAC,SAAA,WAAAA,UAAAR,EAAA;MACA,SAAAM,UAAA;QACA;UACAG,IAAA,EAAAT,EAAA;UACAU,MAAA;UACAC,GAAA;QACA;MACA;MACA;QACAX,EAAA,EAAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}