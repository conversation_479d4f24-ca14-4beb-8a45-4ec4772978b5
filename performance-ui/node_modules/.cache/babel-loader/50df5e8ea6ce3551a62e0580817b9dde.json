{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/store/index.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/store/index.js", "mtime": 1753510684530}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCIvVXNlcnMvbWF6aWhhby9EZXNrdG9wL2Rldi9wZXJmb3JtYW5jZS9wZXJmb3JtYW5jZS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKdmFyIF92dWUgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoInZ1ZSIpKTsKdmFyIF92dWV4ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJ2dWV4IikpOwp2YXIgX2FwcCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi9tb2R1bGVzL2FwcCIpKTsKdmFyIF9kaWN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIuL21vZHVsZXMvZGljdCIpKTsKdmFyIF91c2VyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIuL21vZHVsZXMvdXNlciIpKTsKdmFyIF90YWdzVmlldyA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi9tb2R1bGVzL3RhZ3NWaWV3IikpOwp2YXIgX3Blcm1pc3Npb24gPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi4vbW9kdWxlcy9wZXJtaXNzaW9uIikpOwp2YXIgX3NldHRpbmdzID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIuL21vZHVsZXMvc2V0dGluZ3MiKSk7CnZhciBfZ2V0dGVycyA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi9nZXR0ZXJzIikpOwpfdnVlLmRlZmF1bHQudXNlKF92dWV4LmRlZmF1bHQpOwp2YXIgc3RvcmUgPSBuZXcgX3Z1ZXguZGVmYXVsdC5TdG9yZSh7CiAgbW9kdWxlczogewogICAgYXBwOiBfYXBwLmRlZmF1bHQsCiAgICBkaWN0OiBfZGljdC5kZWZhdWx0LAogICAgdXNlcjogX3VzZXIuZGVmYXVsdCwKICAgIHRhZ3NWaWV3OiBfdGFnc1ZpZXcuZGVmYXVsdCwKICAgIHBlcm1pc3Npb246IF9wZXJtaXNzaW9uLmRlZmF1bHQsCiAgICBzZXR0aW5nczogX3NldHRpbmdzLmRlZmF1bHQKICB9LAogIGdldHRlcnM6IF9nZXR0ZXJzLmRlZmF1bHQKfSk7CnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHN0b3JlOw=="}, {"version": 3, "names": ["_vue", "_interopRequireDefault", "require", "_vuex", "_app", "_dict", "_user", "_tagsView", "_permission", "_settings", "_getters", "<PERSON><PERSON>", "use", "Vuex", "store", "Store", "modules", "app", "dict", "user", "tagsView", "permission", "settings", "getters", "_default", "exports", "default"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/store/index.js"], "sourcesContent": ["import Vue from 'vue'\nimport Vuex from 'vuex'\nimport app from './modules/app'\nimport dict from './modules/dict'\nimport user from './modules/user'\nimport tagsView from './modules/tagsView'\nimport permission from './modules/permission'\nimport settings from './modules/settings'\nimport getters from './getters'\n\nVue.use(Vuex)\n\nconst store = new Vuex.Store({\n  modules: {\n    app,\n    dict,\n    user,\n    tagsView,\n    permission,\n    settings\n  },\n  getters\n})\n\nexport default store\n"], "mappings": ";;;;;;;AAAA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,KAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,IAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,KAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,KAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,SAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,WAAA,GAAAP,sBAAA,CAAAC,OAAA;AACA,IAAAO,SAAA,GAAAR,sBAAA,CAAAC,OAAA;AACA,IAAAQ,QAAA,GAAAT,sBAAA,CAAAC,OAAA;AAEAS,YAAG,CAACC,GAAG,CAACC,aAAI,CAAC;AAEb,IAAMC,KAAK,GAAG,IAAID,aAAI,CAACE,KAAK,CAAC;EAC3BC,OAAO,EAAE;IACPC,GAAG,EAAHA,YAAG;IACHC,IAAI,EAAJA,aAAI;IACJC,IAAI,EAAJA,aAAI;IACJC,QAAQ,EAARA,iBAAQ;IACRC,UAAU,EAAVA,mBAAU;IACVC,QAAQ,EAARA;EACF,CAAC;EACDC,OAAO,EAAPA;AACF,CAAC,CAAC;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEaZ,KAAK", "ignoreList": []}]}