{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/system/post/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/system/post/index.vue", "mtime": 1753510684535}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_post", "require", "name", "dicts", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "postList", "title", "open", "queryParams", "pageNum", "pageSize", "postCode", "undefined", "postName", "status", "form", "rules", "required", "message", "trigger", "postSort", "created", "getList", "methods", "_this", "listPost", "then", "response", "rows", "cancel", "reset", "postId", "remark", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "row", "_this2", "getPost", "submitForm", "_this3", "$refs", "validate", "valid", "updatePost", "$modal", "msgSuccess", "addPost", "handleDelete", "_this4", "postIds", "confirm", "delPost", "catch", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime"], "sources": ["src/views/system/post/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"岗位编码\" prop=\"postCode\">\n        <el-input\n          v-model=\"queryParams.postCode\"\n          placeholder=\"请输入岗位编码\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"岗位名称\" prop=\"postName\">\n        <el-input\n          v-model=\"queryParams.postName\"\n          placeholder=\"请输入岗位名称\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"状态\" prop=\"status\">\n        <el-select v-model=\"queryParams.status\" placeholder=\"岗位状态\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.sys_normal_disable\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['system:post:add']\"\n        >新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n          v-hasPermi=\"['system:post:edit']\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['system:post:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['system:post:export']\"\n        >导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"postList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"岗位编号\" align=\"center\" prop=\"postId\" />\n      <el-table-column label=\"岗位编码\" align=\"center\" prop=\"postCode\" />\n      <el-table-column label=\"岗位名称\" align=\"center\" prop=\"postName\" />\n      <el-table-column label=\"岗位排序\" align=\"center\" prop=\"postSort\" />\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['system:post:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['system:post:remove']\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改岗位对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n        <el-form-item label=\"岗位名称\" prop=\"postName\">\n          <el-input v-model=\"form.postName\" placeholder=\"请输入岗位名称\" />\n        </el-form-item>\n        <el-form-item label=\"岗位编码\" prop=\"postCode\">\n          <el-input v-model=\"form.postCode\" placeholder=\"请输入编码名称\" />\n        </el-form-item>\n        <el-form-item label=\"岗位顺序\" prop=\"postSort\">\n          <el-input-number v-model=\"form.postSort\" controls-position=\"right\" :min=\"0\" />\n        </el-form-item>\n        <el-form-item label=\"岗位状态\" prop=\"status\">\n          <el-radio-group v-model=\"form.status\">\n            <el-radio\n              v-for=\"dict in dict.type.sys_normal_disable\"\n              :key=\"dict.value\"\n              :label=\"dict.value\"\n            >{{dict.label}}</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listPost, getPost, delPost, addPost, updatePost } from \"@/api/system/post\"\n\nexport default {\n  name: \"Post\",\n  dicts: ['sys_normal_disable'],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 岗位表格数据\n      postList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        postCode: undefined,\n        postName: undefined,\n        status: undefined\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        postName: [\n          { required: true, message: \"岗位名称不能为空\", trigger: \"blur\" }\n        ],\n        postCode: [\n          { required: true, message: \"岗位编码不能为空\", trigger: \"blur\" }\n        ],\n        postSort: [\n          { required: true, message: \"岗位顺序不能为空\", trigger: \"blur\" }\n        ]\n      }\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    /** 查询岗位列表 */\n    getList() {\n      this.loading = true\n      listPost(this.queryParams).then(response => {\n        this.postList = response.rows\n        this.total = response.total\n        this.loading = false\n      })\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false\n      this.reset()\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        postId: undefined,\n        postCode: undefined,\n        postName: undefined,\n        postSort: 0,\n        status: \"0\",\n        remark: undefined\n      }\n      this.resetForm(\"form\")\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1\n      this.getList()\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\")\n      this.handleQuery()\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.postId)\n      this.single = selection.length!=1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset()\n      this.open = true\n      this.title = \"添加岗位\"\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset()\n      const postId = row.postId || this.ids\n      getPost(postId).then(response => {\n        this.form = response.data\n        this.open = true\n        this.title = \"修改岗位\"\n      })\n    },\n    /** 提交按钮 */\n    submitForm: function() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.postId != undefined) {\n            updatePost(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\")\n              this.open = false\n              this.getList()\n            })\n          } else {\n            addPost(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\")\n              this.open = false\n              this.getList()\n            })\n          }\n        }\n      })\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const postIds = row.postId || this.ids\n      this.$modal.confirm('是否确认删除岗位编号为\"' + postIds + '\"的数据项？').then(function() {\n        return delPost(postIds)\n      }).then(() => {\n        this.getList()\n        this.$modal.msgSuccess(\"删除成功\")\n      }).catch(() => {})\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('system/post/export', {\n        ...this.queryParams\n      }, `post_${new Date().getTime()}.xlsx`)\n    }\n  }\n}\n</script>\n"], "mappings": ";;;;;;;;;;;;AA+JA,IAAAA,KAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,QAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA,EAAAC,SAAA;QACAC,QAAA,EAAAD,SAAA;QACAE,MAAA,EAAAF;MACA;MACA;MACAG,IAAA;MACA;MACAC,KAAA;QACAH,QAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,QAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,QAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,aACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAzB,OAAA;MACA,IAAA0B,cAAA,OAAAjB,WAAA,EAAAkB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAnB,QAAA,GAAAsB,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAApB,KAAA,GAAAuB,QAAA,CAAAvB,KAAA;QACAoB,KAAA,CAAAzB,OAAA;MACA;IACA;IACA;IACA8B,MAAA,WAAAA,OAAA;MACA,KAAAtB,IAAA;MACA,KAAAuB,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAf,IAAA;QACAgB,MAAA,EAAAnB,SAAA;QACAD,QAAA,EAAAC,SAAA;QACAC,QAAA,EAAAD,SAAA;QACAQ,QAAA;QACAN,MAAA;QACAkB,MAAA,EAAApB;MACA;MACA,KAAAqB,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAA1B,WAAA,CAAAC,OAAA;MACA,KAAAa,OAAA;IACA;IACA,aACAa,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAArC,GAAA,GAAAqC,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAR,MAAA;MAAA;MACA,KAAA9B,MAAA,GAAAoC,SAAA,CAAAG,MAAA;MACA,KAAAtC,QAAA,IAAAmC,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAX,KAAA;MACA,KAAAvB,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAoC,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAd,KAAA;MACA,IAAAC,MAAA,GAAAY,GAAA,CAAAZ,MAAA,SAAA/B,GAAA;MACA,IAAA6C,aAAA,EAAAd,MAAA,EAAAL,IAAA,WAAAC,QAAA;QACAiB,MAAA,CAAA7B,IAAA,GAAAY,QAAA,CAAA7B,IAAA;QACA8C,MAAA,CAAArC,IAAA;QACAqC,MAAA,CAAAtC,KAAA;MACA;IACA;IACA;IACAwC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAhC,IAAA,CAAAgB,MAAA,IAAAnB,SAAA;YACA,IAAAuC,gBAAA,EAAAJ,MAAA,CAAAhC,IAAA,EAAAW,IAAA,WAAAC,QAAA;cACAoB,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAxC,IAAA;cACAwC,MAAA,CAAAzB,OAAA;YACA;UACA;YACA,IAAAgC,aAAA,EAAAP,MAAA,CAAAhC,IAAA,EAAAW,IAAA,WAAAC,QAAA;cACAoB,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAxC,IAAA;cACAwC,MAAA,CAAAzB,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAiC,YAAA,WAAAA,aAAAZ,GAAA;MAAA,IAAAa,MAAA;MACA,IAAAC,OAAA,GAAAd,GAAA,CAAAZ,MAAA,SAAA/B,GAAA;MACA,KAAAoD,MAAA,CAAAM,OAAA,kBAAAD,OAAA,aAAA/B,IAAA;QACA,WAAAiC,aAAA,EAAAF,OAAA;MACA,GAAA/B,IAAA;QACA8B,MAAA,CAAAlC,OAAA;QACAkC,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAO,KAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,2BAAAC,cAAA,CAAAC,OAAA,MACA,KAAAxD,WAAA,WAAAyD,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;EACA;AACA", "ignoreList": []}]}