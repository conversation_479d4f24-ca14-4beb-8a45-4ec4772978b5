{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/core/module.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/core/module.js", "mtime": 1753510684087}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCIvVXNlcnMvbWF6aWhhby9EZXNrdG9wL2Rldi9wZXJmb3JtYW5jZS9wZXJmb3JtYW5jZS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKdmFyIF9jcmVhdGVDbGFzczIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi9Vc2Vycy9tYXppaGFvL0Rlc2t0b3AvZGV2L3BlcmZvcm1hbmNlL3BlcmZvcm1hbmNlLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2NyZWF0ZUNsYXNzLmpzIikpOwp2YXIgX2NsYXNzQ2FsbENoZWNrMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiL1VzZXJzL21hemloYW8vRGVza3RvcC9kZXYvcGVyZm9ybWFuY2UvcGVyZm9ybWFuY2UtdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvY2xhc3NDYWxsQ2hlY2suanMiKSk7CnZhciBfZGVmaW5lUHJvcGVydHkyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIvVXNlcnMvbWF6aWhhby9EZXNrdG9wL2Rldi9wZXJmb3JtYW5jZS9wZXJmb3JtYW5jZS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9kZWZpbmVQcm9wZXJ0eS5qcyIpKTsKdmFyIE1vZHVsZSA9IC8qI19fUFVSRV9fKi8oMCwgX2NyZWF0ZUNsYXNzMi5kZWZhdWx0KShmdW5jdGlvbiBNb2R1bGUocXVpbGwpIHsKICAoMCwgX2NsYXNzQ2FsbENoZWNrMi5kZWZhdWx0KSh0aGlzLCBNb2R1bGUpOwogIHZhciBvcHRpb25zID0gYXJndW1lbnRzLmxlbmd0aCA+IDEgJiYgYXJndW1lbnRzWzFdICE9PSB1bmRlZmluZWQgPyBhcmd1bWVudHNbMV0gOiB7fTsKICB0aGlzLnF1aWxsID0gcXVpbGw7CiAgdGhpcy5vcHRpb25zID0gb3B0aW9uczsKfSk7CigwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKE1vZHVsZSwgIkRFRkFVTFRTIiwge30pOwp2YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSBNb2R1bGU7"}, {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "_createClass2", "default", "quill", "_classCallCheck2", "options", "arguments", "length", "undefined", "_defineProperty2", "_default", "exports"], "sources": ["../../src/core/module.ts"], "sourcesContent": ["import type Quill from './quill.js';\n\nabstract class Module<T extends {} = {}> {\n  static DEFAULTS = {};\n\n  constructor(\n    public quill: Quill,\n    protected options: Partial<T> = {},\n  ) {}\n}\n\nexport default Module;\n"], "mappings": ";;;;;;;;;;IAEeA,MAAM,oBAAAC,aAAA,CAAAC,OAAA,EAGnB,SAAAF,OACSG,KAAY,EAEnB;EAAA,IAAAC,gBAAA,CAAAF,OAAA,QAAAF,MAAA;EAAA,IADUK,OAAmB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAAA,KAD3BH,KAAY,GAAZA,KAAY;EAAA,KACTE,OAAmB,GAAnBA,OAAmB;AAC5B;AAAA,IAAAI,gBAAA,CAAAP,OAAA,EANUF,MAAM,cACD,CAAC,CAAC;AAAA,IAAAU,QAAA,GAAAC,OAAA,CAAAT,OAAA,GAQPF,MAAM", "ignoreList": []}]}