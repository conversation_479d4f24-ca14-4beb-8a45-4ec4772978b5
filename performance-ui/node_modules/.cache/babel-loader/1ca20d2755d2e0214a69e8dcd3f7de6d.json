{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/dashboard/RaddarChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/dashboard/RaddarChart.vue", "mtime": 1753510684532}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["echarts", "_interopRequireWildcard", "require", "_resize", "_interopRequireDefault", "animationDuration", "_default", "exports", "default", "mixins", "resize", "props", "className", "type", "String", "width", "height", "data", "chart", "mounted", "_this", "$nextTick", "initChart", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "methods", "init", "$el", "setOption", "tooltip", "trigger", "axisPointer", "radar", "radius", "center", "splitNumber", "splitArea", "areaStyle", "color", "opacity", "<PERSON><PERSON><PERSON><PERSON>", "shadowColor", "shadowOffsetX", "shadowOffsetY", "indicator", "name", "max", "legend", "left", "bottom", "series", "symbolSize", "normal", "value"], "sources": ["src/views/dashboard/RaddarChart.vue"], "sourcesContent": ["<template>\n  <div :class=\"className\" :style=\"{height:height,width:width}\" />\n</template>\n\n<script>\nimport * as echarts from 'echarts'\nrequire('echarts/theme/macarons') // echarts theme\nimport resize from './mixins/resize'\n\nconst animationDuration = 3000\n\nexport default {\n  mixins: [resize],\n  props: {\n    className: {\n      type: String,\n      default: 'chart'\n    },\n    width: {\n      type: String,\n      default: '100%'\n    },\n    height: {\n      type: String,\n      default: '300px'\n    }\n  },\n  data() {\n    return {\n      chart: null\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.initChart()\n    })\n  },\n  beforeDestroy() {\n    if (!this.chart) {\n      return\n    }\n    this.chart.dispose()\n    this.chart = null\n  },\n  methods: {\n    initChart() {\n      this.chart = echarts.init(this.$el, 'macarons')\n\n      this.chart.setOption({\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: { // 坐标轴指示器，坐标轴触发有效\n            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'\n          }\n        },\n        radar: {\n          radius: '66%',\n          center: ['50%', '42%'],\n          splitNumber: 8,\n          splitArea: {\n            areaStyle: {\n              color: 'rgba(127,95,132,.3)',\n              opacity: 1,\n              shadowBlur: 45,\n              shadowColor: 'rgba(0,0,0,.5)',\n              shadowOffsetX: 0,\n              shadowOffsetY: 15\n            }\n          },\n          indicator: [\n            { name: 'Sales', max: 10000 },\n            { name: 'Administration', max: 20000 },\n            { name: 'Information Techology', max: 20000 },\n            { name: 'Customer Support', max: 20000 },\n            { name: 'Development', max: 20000 },\n            { name: 'Marketing', max: 20000 }\n          ]\n        },\n        legend: {\n          left: 'center',\n          bottom: '10',\n          data: ['Allocated Budget', 'Expected Spending', 'Actual Spending']\n        },\n        series: [{\n          type: 'radar',\n          symbolSize: 0,\n          areaStyle: {\n            normal: {\n              shadowBlur: 13,\n              shadowColor: 'rgba(0,0,0,.2)',\n              shadowOffsetX: 0,\n              shadowOffsetY: 10,\n              opacity: 1\n            }\n          },\n          data: [\n            {\n              value: [5000, 7000, 12000, 11000, 15000, 14000],\n              name: 'Allocated Budget'\n            },\n            {\n              value: [4000, 9000, 15000, 15000, 13000, 11000],\n              name: 'Expected Spending'\n            },\n            {\n              value: [5500, 11000, 12000, 15000, 12000, 12000],\n              name: 'Actual Spending'\n            }\n          ],\n          animationDuration: animationDuration\n        }]\n      })\n    }\n  }\n}\n</script>\n"], "mappings": ";;;;;;;;AAKA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AAEA,IAAAC,OAAA,GAAAC,sBAAA,CAAAF,OAAA;;;;;;AADAA,OAAA;;AAGA,IAAAG,iBAAA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,MAAA,GAAAC,eAAA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAN,OAAA;IACA;IACAO,KAAA;MACAF,IAAA,EAAAC,MAAA;MACAN,OAAA;IACA;IACAQ,MAAA;MACAH,IAAA,EAAAC,MAAA;MACAN,OAAA;IACA;EACA;EACAS,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,SAAA;MACAD,KAAA,CAAAE,SAAA;IACA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA,UAAAL,KAAA;MACA;IACA;IACA,KAAAA,KAAA,CAAAM,OAAA;IACA,KAAAN,KAAA;EACA;EACAO,OAAA;IACAH,SAAA,WAAAA,UAAA;MACA,KAAAJ,KAAA,GAAAlB,OAAA,CAAA0B,IAAA,MAAAC,GAAA;MAEA,KAAAT,KAAA,CAAAU,SAAA;QACAC,OAAA;UACAC,OAAA;UACAC,WAAA;YAAA;YACAlB,IAAA;UACA;QACA;QACAmB,KAAA;UACAC,MAAA;UACAC,MAAA;UACAC,WAAA;UACAC,SAAA;YACAC,SAAA;cACAC,KAAA;cACAC,OAAA;cACAC,UAAA;cACAC,WAAA;cACAC,aAAA;cACAC,aAAA;YACA;UACA;UACAC,SAAA,GACA;YAAAC,IAAA;YAAAC,GAAA;UAAA,GACA;YAAAD,IAAA;YAAAC,GAAA;UAAA,GACA;YAAAD,IAAA;YAAAC,GAAA;UAAA,GACA;YAAAD,IAAA;YAAAC,GAAA;UAAA,GACA;YAAAD,IAAA;YAAAC,GAAA;UAAA,GACA;YAAAD,IAAA;YAAAC,GAAA;UAAA;QAEA;QACAC,MAAA;UACAC,IAAA;UACAC,MAAA;UACAhC,IAAA;QACA;QACAiC,MAAA;UACArC,IAAA;UACAsC,UAAA;UACAd,SAAA;YACAe,MAAA;cACAZ,UAAA;cACAC,WAAA;cACAC,aAAA;cACAC,aAAA;cACAJ,OAAA;YACA;UACA;UACAtB,IAAA,GACA;YACAoC,KAAA;YACAR,IAAA;UACA,GACA;YACAQ,KAAA;YACAR,IAAA;UACA,GACA;YACAQ,KAAA;YACAR,IAAA;UACA,EACA;UACAxC,iBAAA,EAAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}