{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/directive/index.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/directive/index.js", "mtime": 1753510684528}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCIvVXNlcnMvbWF6aWhhby9EZXNrdG9wL2Rldi9wZXJmb3JtYW5jZS9wZXJmb3JtYW5jZS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKdmFyIF9oYXNSb2xlID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIuL3Blcm1pc3Npb24vaGFzUm9sZSIpKTsKdmFyIF9oYXNQZXJtaSA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi9wZXJtaXNzaW9uL2hhc1Blcm1pIikpOwp2YXIgX2RyYWcgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi4vZGlhbG9nL2RyYWciKSk7CnZhciBfZHJhZ1dpZHRoID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIuL2RpYWxvZy9kcmFnV2lkdGgiKSk7CnZhciBfZHJhZ0hlaWdodCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi9kaWFsb2cvZHJhZ0hlaWdodCIpKTsKdmFyIF9jbGlwYm9hcmQgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi4vbW9kdWxlL2NsaXBib2FyZCIpKTsKdmFyIGluc3RhbGwgPSBmdW5jdGlvbiBpbnN0YWxsKFZ1ZSkgewogIFZ1ZS5kaXJlY3RpdmUoJ2hhc1JvbGUnLCBfaGFzUm9sZS5kZWZhdWx0KTsKICBWdWUuZGlyZWN0aXZlKCdoYXNQZXJtaScsIF9oYXNQZXJtaS5kZWZhdWx0KTsKICBWdWUuZGlyZWN0aXZlKCdjbGlwYm9hcmQnLCBfY2xpcGJvYXJkLmRlZmF1bHQpOwogIFZ1ZS5kaXJlY3RpdmUoJ2RpYWxvZ0RyYWcnLCBfZHJhZy5kZWZhdWx0KTsKICBWdWUuZGlyZWN0aXZlKCdkaWFsb2dEcmFnV2lkdGgnLCBfZHJhZ1dpZHRoLmRlZmF1bHQpOwogIFZ1ZS5kaXJlY3RpdmUoJ2RpYWxvZ0RyYWdIZWlnaHQnLCBfZHJhZ0hlaWdodC5kZWZhdWx0KTsKfTsKaWYgKHdpbmRvdy5WdWUpIHsKICB3aW5kb3dbJ2hhc1JvbGUnXSA9IF9oYXNSb2xlLmRlZmF1bHQ7CiAgd2luZG93WydoYXNQZXJtaSddID0gX2hhc1Blcm1pLmRlZmF1bHQ7CiAgVnVlLnVzZShpbnN0YWxsKTsKfQp2YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSBpbnN0YWxsOw=="}, {"version": 3, "names": ["_hasRole", "_interopRequireDefault", "require", "_has<PERSON>ermi", "_drag", "_dragWidth", "_dragHeight", "_clipboard", "install", "<PERSON><PERSON>", "directive", "hasRole", "<PERSON><PERSON><PERSON><PERSON>", "clipboard", "dialogDrag", "dialogDragWidth", "dialogDragHeight", "window", "use", "_default", "exports", "default"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/directive/index.js"], "sourcesContent": ["import hasRole from './permission/hasRole'\nimport hasPermi from './permission/hasPermi'\nimport dialogDrag from './dialog/drag'\nimport dialogDragWidth from './dialog/dragWidth'\nimport dialogDragHeight from './dialog/dragHeight'\nimport clipboard from './module/clipboard'\n\nconst install = function(Vue) {\n  Vue.directive('hasRole', hasRole)\n  Vue.directive('hasPermi', hasPermi)\n  Vue.directive('clipboard', clipboard)\n  Vue.directive('dialogDrag', dialogDrag)\n  Vue.directive('dialogDragWidth', dialogDragWidth)\n  Vue.directive('dialogDragHeight', dialogDragHeight)\n}\n\nif (window.Vue) {\n  window['hasRole'] = hasRole\n  window['hasPermi'] = hasPermi\n  Vue.use(install)\n}\n\nexport default install\n"], "mappings": ";;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,SAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,KAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,UAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,WAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,UAAA,GAAAN,sBAAA,CAAAC,OAAA;AAEA,IAAMM,OAAO,GAAG,SAAVA,OAAOA,CAAYC,GAAG,EAAE;EAC5BA,GAAG,CAACC,SAAS,CAAC,SAAS,EAAEC,gBAAO,CAAC;EACjCF,GAAG,CAACC,SAAS,CAAC,UAAU,EAAEE,iBAAQ,CAAC;EACnCH,GAAG,CAACC,SAAS,CAAC,WAAW,EAAEG,kBAAS,CAAC;EACrCJ,GAAG,CAACC,SAAS,CAAC,YAAY,EAAEI,aAAU,CAAC;EACvCL,GAAG,CAACC,SAAS,CAAC,iBAAiB,EAAEK,kBAAe,CAAC;EACjDN,GAAG,CAACC,SAAS,CAAC,kBAAkB,EAAEM,mBAAgB,CAAC;AACrD,CAAC;AAED,IAAIC,MAAM,CAACR,GAAG,EAAE;EACdQ,MAAM,CAAC,SAAS,CAAC,GAAGN,gBAAO;EAC3BM,MAAM,CAAC,UAAU,CAAC,GAAGL,iBAAQ;EAC7BH,GAAG,CAACS,GAAG,CAACV,OAAO,CAAC;AAClB;AAAC,IAAAW,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEcb,OAAO", "ignoreList": []}]}