{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/themes/base.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/themes/base.js", "mtime": 1753510684098}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_lodashEs", "require", "_emitter", "_interopRequireDefault", "_theme", "_colorPicker", "_iconPicker", "_picker", "_tooltip", "ALIGNS", "COLORS", "FONTS", "HEADERS", "SIZES", "BaseTheme", "exports", "default", "_Theme", "quill", "options", "_this", "_classCallCheck2", "_callSuper2", "listener", "e", "document", "body", "contains", "root", "removeEventListener", "tooltip", "target", "activeElement", "textbox", "hasFocus", "hide", "pickers", "for<PERSON>ach", "picker", "container", "close", "emitter", "listenDOM", "_inherits2", "_createClass2", "key", "value", "addModule", "name", "module", "_superPropGet2", "extendToolbar", "buildButtons", "buttons", "icons", "Array", "from", "button", "className", "getAttribute", "split", "startsWith", "slice", "length", "innerHTML", "rtl", "buildPickers", "selects", "_this2", "map", "select", "classList", "querySelector", "fillSelect", "_typeof2", "align", "IconPicker", "format", "ColorPicker", "Picker", "update", "on", "Emitter", "events", "EDITOR_CHANGE", "Theme", "DEFAULTS", "merge", "modules", "toolbar", "handlers", "formula", "theme", "edit", "image", "_this3", "fileInput", "createElement", "setAttribute", "uploader", "mimetypes", "join", "add", "addEventListener", "range", "getSelection", "upload", "files", "append<PERSON><PERSON><PERSON>", "click", "video", "BaseTooltip", "_Tooltip", "boundsContainer", "_this4", "listen", "_this5", "event", "save", "preventDefault", "cancel", "restoreFocus", "mode", "arguments", "undefined", "preview", "remove", "bounds", "getBounds", "selection", "savedRange", "position", "concat", "focus", "preventScroll", "scrollTop", "linkRange", "formatText", "sources", "USER", "extractVideoUrl", "index", "insertEmbed", "insertText", "setSelection", "<PERSON><PERSON><PERSON>", "url", "match", "values", "defaultValue", "option", "String"], "sources": ["../../src/themes/base.ts"], "sourcesContent": ["import { merge } from 'lodash-es';\nimport type Quill from '../core/quill.js';\nimport Emitter from '../core/emitter.js';\nimport Theme from '../core/theme.js';\nimport type { ThemeOptions } from '../core/theme.js';\nimport ColorPicker from '../ui/color-picker.js';\nimport IconPicker from '../ui/icon-picker.js';\nimport Picker from '../ui/picker.js';\nimport Tooltip from '../ui/tooltip.js';\nimport type { Range } from '../core/selection.js';\nimport type Clipboard from '../modules/clipboard.js';\nimport type History from '../modules/history.js';\nimport type Keyboard from '../modules/keyboard.js';\nimport type Uploader from '../modules/uploader.js';\nimport type Selection from '../core/selection.js';\n\nconst ALIGNS = [false, 'center', 'right', 'justify'];\n\nconst COLORS = [\n  '#000000',\n  '#e60000',\n  '#ff9900',\n  '#ffff00',\n  '#008a00',\n  '#0066cc',\n  '#9933ff',\n  '#ffffff',\n  '#facccc',\n  '#ffebcc',\n  '#ffffcc',\n  '#cce8cc',\n  '#cce0f5',\n  '#ebd6ff',\n  '#bbbbbb',\n  '#f06666',\n  '#ffc266',\n  '#ffff66',\n  '#66b966',\n  '#66a3e0',\n  '#c285ff',\n  '#888888',\n  '#a10000',\n  '#b26b00',\n  '#b2b200',\n  '#006100',\n  '#0047b2',\n  '#6b24b2',\n  '#444444',\n  '#5c0000',\n  '#663d00',\n  '#666600',\n  '#003700',\n  '#002966',\n  '#3d1466',\n];\n\nconst FONTS = [false, 'serif', 'monospace'];\n\nconst HEADERS = ['1', '2', '3', false];\n\nconst SIZES = ['small', false, 'large', 'huge'];\n\nclass BaseTheme extends Theme {\n  pickers: Picker[];\n  tooltip?: Tooltip;\n\n  constructor(quill: Quill, options: ThemeOptions) {\n    super(quill, options);\n    const listener = (e: MouseEvent) => {\n      if (!document.body.contains(quill.root)) {\n        document.body.removeEventListener('click', listener);\n        return;\n      }\n      if (\n        this.tooltip != null &&\n        // @ts-expect-error\n        !this.tooltip.root.contains(e.target) &&\n        // @ts-expect-error\n        document.activeElement !== this.tooltip.textbox &&\n        !this.quill.hasFocus()\n      ) {\n        this.tooltip.hide();\n      }\n      if (this.pickers != null) {\n        this.pickers.forEach((picker) => {\n          // @ts-expect-error\n          if (!picker.container.contains(e.target)) {\n            picker.close();\n          }\n        });\n      }\n    };\n    quill.emitter.listenDOM('click', document.body, listener);\n  }\n\n  addModule(name: 'clipboard'): Clipboard;\n  addModule(name: 'keyboard'): Keyboard;\n  addModule(name: 'uploader'): Uploader;\n  addModule(name: 'history'): History;\n  addModule(name: 'selection'): Selection;\n  addModule(name: string): unknown;\n  addModule(name: string) {\n    const module = super.addModule(name);\n    if (name === 'toolbar') {\n      // @ts-expect-error\n      this.extendToolbar(module);\n    }\n    return module;\n  }\n\n  buildButtons(\n    buttons: NodeListOf<HTMLElement>,\n    icons: Record<string, Record<string, string> | string>,\n  ) {\n    Array.from(buttons).forEach((button) => {\n      const className = button.getAttribute('class') || '';\n      className.split(/\\s+/).forEach((name) => {\n        if (!name.startsWith('ql-')) return;\n        name = name.slice('ql-'.length);\n        if (icons[name] == null) return;\n        if (name === 'direction') {\n          // @ts-expect-error\n          button.innerHTML = icons[name][''] + icons[name].rtl;\n        } else if (typeof icons[name] === 'string') {\n          // @ts-expect-error\n          button.innerHTML = icons[name];\n        } else {\n          // @ts-expect-error\n          const value = button.value || '';\n          // @ts-expect-error\n          if (value != null && icons[name][value]) {\n            // @ts-expect-error\n            button.innerHTML = icons[name][value];\n          }\n        }\n      });\n    });\n  }\n\n  buildPickers(\n    selects: NodeListOf<HTMLSelectElement>,\n    icons: Record<string, string | Record<string, string>>,\n  ) {\n    this.pickers = Array.from(selects).map((select) => {\n      if (select.classList.contains('ql-align')) {\n        if (select.querySelector('option') == null) {\n          fillSelect(select, ALIGNS);\n        }\n        if (typeof icons.align === 'object') {\n          return new IconPicker(select, icons.align);\n        }\n      }\n      if (\n        select.classList.contains('ql-background') ||\n        select.classList.contains('ql-color')\n      ) {\n        const format = select.classList.contains('ql-background')\n          ? 'background'\n          : 'color';\n        if (select.querySelector('option') == null) {\n          fillSelect(\n            select,\n            COLORS,\n            format === 'background' ? '#ffffff' : '#000000',\n          );\n        }\n        return new ColorPicker(select, icons[format] as string);\n      }\n      if (select.querySelector('option') == null) {\n        if (select.classList.contains('ql-font')) {\n          fillSelect(select, FONTS);\n        } else if (select.classList.contains('ql-header')) {\n          fillSelect(select, HEADERS);\n        } else if (select.classList.contains('ql-size')) {\n          fillSelect(select, SIZES);\n        }\n      }\n      return new Picker(select);\n    });\n    const update = () => {\n      this.pickers.forEach((picker) => {\n        picker.update();\n      });\n    };\n    this.quill.on(Emitter.events.EDITOR_CHANGE, update);\n  }\n}\nBaseTheme.DEFAULTS = merge({}, Theme.DEFAULTS, {\n  modules: {\n    toolbar: {\n      handlers: {\n        formula() {\n          this.quill.theme.tooltip.edit('formula');\n        },\n        image() {\n          let fileInput = this.container.querySelector(\n            'input.ql-image[type=file]',\n          );\n          if (fileInput == null) {\n            fileInput = document.createElement('input');\n            fileInput.setAttribute('type', 'file');\n            fileInput.setAttribute(\n              'accept',\n              this.quill.uploader.options.mimetypes.join(', '),\n            );\n            fileInput.classList.add('ql-image');\n            fileInput.addEventListener('change', () => {\n              const range = this.quill.getSelection(true);\n              this.quill.uploader.upload(range, fileInput.files);\n              fileInput.value = '';\n            });\n            this.container.appendChild(fileInput);\n          }\n          fileInput.click();\n        },\n        video() {\n          this.quill.theme.tooltip.edit('video');\n        },\n      },\n    },\n  },\n});\n\nclass BaseTooltip extends Tooltip {\n  textbox: HTMLInputElement | null;\n  linkRange?: Range;\n\n  constructor(quill: Quill, boundsContainer?: HTMLElement) {\n    super(quill, boundsContainer);\n    this.textbox = this.root.querySelector('input[type=\"text\"]');\n    this.listen();\n  }\n\n  listen() {\n    // @ts-expect-error Fix me later\n    this.textbox.addEventListener('keydown', (event) => {\n      if (event.key === 'Enter') {\n        this.save();\n        event.preventDefault();\n      } else if (event.key === 'Escape') {\n        this.cancel();\n        event.preventDefault();\n      }\n    });\n  }\n\n  cancel() {\n    this.hide();\n    this.restoreFocus();\n  }\n\n  edit(mode = 'link', preview: string | null = null) {\n    this.root.classList.remove('ql-hidden');\n    this.root.classList.add('ql-editing');\n    if (this.textbox == null) return;\n\n    if (preview != null) {\n      this.textbox.value = preview;\n    } else if (mode !== this.root.getAttribute('data-mode')) {\n      this.textbox.value = '';\n    }\n    const bounds = this.quill.getBounds(this.quill.selection.savedRange);\n    if (bounds != null) {\n      this.position(bounds);\n    }\n    this.textbox.select();\n    this.textbox.setAttribute(\n      'placeholder',\n      this.textbox.getAttribute(`data-${mode}`) || '',\n    );\n    this.root.setAttribute('data-mode', mode);\n  }\n\n  restoreFocus() {\n    this.quill.focus({ preventScroll: true });\n  }\n\n  save() {\n    // @ts-expect-error Fix me later\n    let { value } = this.textbox;\n    switch (this.root.getAttribute('data-mode')) {\n      case 'link': {\n        const { scrollTop } = this.quill.root;\n        if (this.linkRange) {\n          this.quill.formatText(\n            this.linkRange,\n            'link',\n            value,\n            Emitter.sources.USER,\n          );\n          delete this.linkRange;\n        } else {\n          this.restoreFocus();\n          this.quill.format('link', value, Emitter.sources.USER);\n        }\n        this.quill.root.scrollTop = scrollTop;\n        break;\n      }\n      case 'video': {\n        value = extractVideoUrl(value);\n      } // eslint-disable-next-line no-fallthrough\n      case 'formula': {\n        if (!value) break;\n        const range = this.quill.getSelection(true);\n        if (range != null) {\n          const index = range.index + range.length;\n          this.quill.insertEmbed(\n            index,\n            // @ts-expect-error Fix me later\n            this.root.getAttribute('data-mode'),\n            value,\n            Emitter.sources.USER,\n          );\n          if (this.root.getAttribute('data-mode') === 'formula') {\n            this.quill.insertText(index + 1, ' ', Emitter.sources.USER);\n          }\n          this.quill.setSelection(index + 2, Emitter.sources.USER);\n        }\n        break;\n      }\n      default:\n    }\n    // @ts-expect-error Fix me later\n    this.textbox.value = '';\n    this.hide();\n  }\n}\n\nfunction extractVideoUrl(url: string) {\n  let match =\n    url.match(\n      /^(?:(https?):\\/\\/)?(?:(?:www|m)\\.)?youtube\\.com\\/watch.*v=([a-zA-Z0-9_-]+)/,\n    ) ||\n    url.match(/^(?:(https?):\\/\\/)?(?:(?:www|m)\\.)?youtu\\.be\\/([a-zA-Z0-9_-]+)/);\n  if (match) {\n    return `${match[1] || 'https'}://www.youtube.com/embed/${\n      match[2]\n    }?showinfo=0`;\n  }\n  // eslint-disable-next-line no-cond-assign\n  if ((match = url.match(/^(?:(https?):\\/\\/)?(?:www\\.)?vimeo\\.com\\/(\\d+)/))) {\n    return `${match[1] || 'https'}://player.vimeo.com/video/${match[2]}/`;\n  }\n  return url;\n}\n\nfunction fillSelect(\n  select: HTMLSelectElement,\n  values: Array<string | boolean>,\n  defaultValue: unknown = false,\n) {\n  values.forEach((value) => {\n    const option = document.createElement('option');\n    if (value === defaultValue) {\n      option.setAttribute('selected', 'selected');\n    } else {\n      option.setAttribute('value', String(value));\n    }\n    select.appendChild(option);\n  });\n}\n\nexport { BaseTooltip, BaseTheme as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,SAAA,GAAAC,OAAA;AAEA,IAAAC,QAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,MAAA,GAAAD,sBAAA,CAAAF,OAAA;AAEA,IAAAI,YAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,WAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,OAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,QAAA,GAAAL,sBAAA,CAAAF,OAAA;AAQA,IAAMQ,MAAM,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;AAEpD,IAAMC,MAAM,GAAG,CACb,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;AAED,IAAMC,KAAK,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,WAAW,CAAC;AAE3C,IAAMC,OAAO,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC;AAEtC,IAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC;AAAA,IAEzCC,SAAS,GAAAC,OAAA,CAAAC,OAAA,0BAAAC,MAAA;EAIb,SAAAH,UAAYI,KAAY,EAAEC,OAAqB,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAAL,OAAA,QAAAF,SAAA;IAC/CM,KAAA,OAAAE,WAAA,CAAAN,OAAA,QAAAF,SAAA,GAAMI,KAAK,EAAEC,OAAO;IACpB,IAAMI,SAAQ,GAAI,SAAZA,QAAQA,CAAIC,CAAa,EAAK;MAClC,IAAI,CAACC,QAAQ,CAACC,IAAI,CAACC,QAAQ,CAACT,KAAK,CAACU,IAAI,CAAC,EAAE;QACvCH,QAAQ,CAACC,IAAI,CAACG,mBAAmB,CAAC,OAAO,EAAEN,SAAQ,CAAC;QACpD;MACF;MACA,IACEH,KAAA,CAAKU,OAAO,IAAI,IAAI;MACpB;MACA,CAACV,KAAA,CAAKU,OAAO,CAACF,IAAI,CAACD,QAAQ,CAACH,CAAC,CAACO,MAAM,CAAC;MACrC;MACAN,QAAQ,CAACO,aAAa,KAAKZ,KAAA,CAAKU,OAAO,CAACG,OAAO,IAC/C,CAACb,KAAA,CAAKF,KAAK,CAACgB,QAAQ,CAAC,CAAC,EACtB;QACAd,KAAA,CAAKU,OAAO,CAACK,IAAI,CAAC,CAAC;MACrB;MACA,IAAIf,KAAA,CAAKgB,OAAO,IAAI,IAAI,EAAE;QACxBhB,KAAA,CAAKgB,OAAO,CAACC,OAAO,CAAE,UAAAC,MAAM,EAAK;UAC/B;UACA,IAAI,CAACA,MAAM,CAACC,SAAS,CAACZ,QAAQ,CAACH,CAAC,CAACO,MAAM,CAAC,EAAE;YACxCO,MAAM,CAACE,KAAK,CAAC,CAAC;UAChB;QACF,CAAC,CAAC;MACJ;IACF,CAAC;IACDtB,KAAK,CAACuB,OAAO,CAACC,SAAS,CAAC,OAAO,EAAEjB,QAAQ,CAACC,IAAI,EAAEH,SAAQ,CAAC;IAAA,OAAAH,KAAA;EAC3D;EAAA,IAAAuB,UAAA,CAAA3B,OAAA,EAAAF,SAAA,EAAAG,MAAA;EAAA,WAAA2B,aAAA,CAAA5B,OAAA,EAAAF,SAAA;IAAA+B,GAAA;IAAAC,KAAA,EAQA,SAAAC,SAASA,CAACC,IAAY,EAAE;MACtB,IAAMC,MAAM,OAAAC,cAAA,CAAAlC,OAAA,EAAAF,SAAA,yBAAmBkC,IAAI,EAAC;MACpC,IAAIA,IAAI,KAAK,SAAS,EAAE;QACtB;QACA,IAAI,CAACG,aAAa,CAACF,MAAM,CAAC;MAC5B;MACA,OAAOA,MAAM;IACf;EAAA;IAAAJ,GAAA;IAAAC,KAAA,EAEA,SAAAM,YAAYA,CACVC,OAAgC,EAChCC,KAAsD,EACtD;MACAC,KAAK,CAACC,IAAI,CAACH,OAAO,CAAC,CAAChB,OAAO,CAAE,UAAAoB,MAAM,EAAK;QACtC,IAAMC,SAAS,GAAGD,MAAM,CAACE,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE;QACpDD,SAAS,CAACE,KAAK,CAAC,KAAK,CAAC,CAACvB,OAAO,CAAE,UAAAW,IAAI,EAAK;UACvC,IAAI,CAACA,IAAI,CAACa,UAAU,CAAC,KAAK,CAAC,EAAE;UAC7Bb,IAAI,GAAGA,IAAI,CAACc,KAAK,CAAC,KAAK,CAACC,MAAM,CAAC;UAC/B,IAAIT,KAAK,CAACN,IAAI,CAAC,IAAI,IAAI,EAAE;UACzB,IAAIA,IAAI,KAAK,WAAW,EAAE;YACxB;YACAS,MAAM,CAACO,SAAS,GAAGV,KAAK,CAACN,IAAI,CAAC,CAAC,EAAE,CAAC,GAAGM,KAAK,CAACN,IAAI,CAAC,CAACiB,GAAG;UACtD,CAAC,MAAM,IAAI,OAAOX,KAAK,CAACN,IAAI,CAAC,KAAK,QAAQ,EAAE;YAC1C;YACAS,MAAM,CAACO,SAAS,GAAGV,KAAK,CAACN,IAAI,CAAC;UAChC,CAAC,MAAM;YACL;YACA,IAAMF,KAAK,GAAGW,MAAM,CAACX,KAAK,IAAI,EAAE;YAChC;YACA,IAAIA,KAAK,IAAI,IAAI,IAAIQ,KAAK,CAACN,IAAI,CAAC,CAACF,KAAK,CAAC,EAAE;cACvC;cACAW,MAAM,CAACO,SAAS,GAAGV,KAAK,CAACN,IAAI,CAAC,CAACF,KAAK,CAAC;YACvC;UACF;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EAAA;IAAAD,GAAA;IAAAC,KAAA,EAEA,SAAAoB,YAAYA,CACVC,OAAsC,EACtCb,KAAsD,EACtD;MAAA,IAAAc,MAAA;MACA,IAAI,CAAChC,OAAO,GAAGmB,KAAK,CAACC,IAAI,CAACW,OAAO,CAAC,CAACE,GAAG,CAAE,UAAAC,MAAM,EAAK;QACjD,IAAIA,MAAM,CAACC,SAAS,CAAC5C,QAAQ,CAAC,UAAU,CAAC,EAAE;UACzC,IAAI2C,MAAM,CAACE,aAAa,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE;YAC1CC,UAAU,CAACH,MAAM,EAAE7D,MAAM,CAAC;UAC5B;UACA,IAAI,IAAAiE,QAAA,CAAA1D,OAAA,EAAOsC,KAAK,CAACqB,KAAK,MAAK,QAAQ,EAAE;YACnC,OAAO,IAAIC,mBAAU,CAACN,MAAM,EAAEhB,KAAK,CAACqB,KAAK,CAAC;UAC5C;QACF;QACA,IACEL,MAAM,CAACC,SAAS,CAAC5C,QAAQ,CAAC,eAAe,CAAC,IAC1C2C,MAAM,CAACC,SAAS,CAAC5C,QAAQ,CAAC,UAAU,CAAC,EACrC;UACA,IAAMkD,MAAM,GAAGP,MAAM,CAACC,SAAS,CAAC5C,QAAQ,CAAC,eAAe,CAAC,GACrD,YAAY,GACZ,OAAO;UACX,IAAI2C,MAAM,CAACE,aAAa,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE;YAC1CC,UAAU,CACRH,MAAM,EACN5D,MAAM,EACNmE,MAAM,KAAK,YAAY,GAAG,SAAS,GAAG,SACxC,CAAC;UACH;UACA,OAAO,IAAIC,oBAAW,CAACR,MAAM,EAAEhB,KAAK,CAACuB,MAAM,CAAW,CAAC;QACzD;QACA,IAAIP,MAAM,CAACE,aAAa,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE;UAC1C,IAAIF,MAAM,CAACC,SAAS,CAAC5C,QAAQ,CAAC,SAAS,CAAC,EAAE;YACxC8C,UAAU,CAACH,MAAM,EAAE3D,KAAK,CAAC;UAC3B,CAAC,MAAM,IAAI2D,MAAM,CAACC,SAAS,CAAC5C,QAAQ,CAAC,WAAW,CAAC,EAAE;YACjD8C,UAAU,CAACH,MAAM,EAAE1D,OAAO,CAAC;UAC7B,CAAC,MAAM,IAAI0D,MAAM,CAACC,SAAS,CAAC5C,QAAQ,CAAC,SAAS,CAAC,EAAE;YAC/C8C,UAAU,CAACH,MAAM,EAAEzD,KAAK,CAAC;UAC3B;QACF;QACA,OAAO,IAAIkE,eAAM,CAACT,MAAM,CAAC;MAC3B,CAAC,CAAC;MACF,IAAMU,MAAM,GAAG,SAATA,MAAMA,CAAA,EAAS;QACnBZ,MAAI,CAAChC,OAAO,CAACC,OAAO,CAAE,UAAAC,MAAM,EAAK;UAC/BA,MAAM,CAAC0C,MAAM,CAAC,CAAC;QACjB,CAAC,CAAC;MACJ,CAAC;MACD,IAAI,CAAC9D,KAAK,CAAC+D,EAAE,CAACC,gBAAO,CAACC,MAAM,CAACC,aAAa,EAAEJ,MAAM,CAAC;IACrD;EAAA;AAAA,EA3HsBK,cAAK;AA6H7BvE,SAAS,CAACwE,QAAQ,GAAG,IAAAC,eAAK,EAAC,CAAC,CAAC,EAAEF,cAAK,CAACC,QAAQ,EAAE;EAC7CE,OAAO,EAAE;IACPC,OAAO,EAAE;MACPC,QAAQ,EAAE;QACRC,OAAO,WAAPA,OAAOA,CAAA,EAAG;UACR,IAAI,CAACzE,KAAK,CAAC0E,KAAK,CAAC9D,OAAO,CAAC+D,IAAI,CAAC,SAAS,CAAC;QAC1C,CAAC;QACDC,KAAK,WAALA,KAAKA,CAAA,EAAG;UAAA,IAAAC,MAAA;UACN,IAAIC,SAAS,GAAG,IAAI,CAACzD,SAAS,CAACiC,aAAa,CAC1C,2BACF,CAAC;UACD,IAAIwB,SAAS,IAAI,IAAI,EAAE;YACrBA,SAAS,GAAGvE,QAAQ,CAACwE,aAAa,CAAC,OAAO,CAAC;YAC3CD,SAAS,CAACE,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC;YACtCF,SAAS,CAACE,YAAY,CACpB,QAAQ,EACR,IAAI,CAAChF,KAAK,CAACiF,QAAQ,CAAChF,OAAO,CAACiF,SAAS,CAACC,IAAI,CAAC,IAAI,CACjD,CAAC;YACDL,SAAS,CAACzB,SAAS,CAAC+B,GAAG,CAAC,UAAU,CAAC;YACnCN,SAAS,CAACO,gBAAgB,CAAC,QAAQ,EAAE,YAAM;cACzC,IAAMC,KAAK,GAAGT,MAAI,CAAC7E,KAAK,CAACuF,YAAY,CAAC,IAAI,CAAC;cAC3CV,MAAI,CAAC7E,KAAK,CAACiF,QAAQ,CAACO,MAAM,CAACF,KAAK,EAAER,SAAS,CAACW,KAAK,CAAC;cAClDX,SAAS,CAAClD,KAAK,GAAG,EAAE;YACtB,CAAC,CAAC;YACF,IAAI,CAACP,SAAS,CAACqE,WAAW,CAACZ,SAAS,CAAC;UACvC;UACAA,SAAS,CAACa,KAAK,CAAC,CAAC;QACnB,CAAC;QACDC,KAAK,WAALA,KAAKA,CAAA,EAAG;UACN,IAAI,CAAC5F,KAAK,CAAC0E,KAAK,CAAC9D,OAAO,CAAC+D,IAAI,CAAC,OAAO,CAAC;QACxC;MACF;IACF;EACF;AACF,CAAC,CAAC;AAAA,IAEIkB,WAAW,GAAAhG,OAAA,CAAAgG,WAAA,0BAAAC,QAAA;EAIf,SAAAD,YAAY7F,KAAY,EAAE+F,eAA6B,EAAE;IAAA,IAAAC,MAAA;IAAA,IAAA7F,gBAAA,CAAAL,OAAA,QAAA+F,WAAA;IACvDG,MAAA,OAAA5F,WAAA,CAAAN,OAAA,QAAA+F,WAAA,GAAM7F,KAAK,EAAE+F,eAAe;IAC5BC,MAAA,CAAKjF,OAAO,GAAGiF,MAAA,CAAKtF,IAAI,CAAC4C,aAAa,CAAC,oBAAoB,CAAC;IAC5D0C,MAAA,CAAKC,MAAM,CAAC,CAAC;IAAA,OAAAD,MAAA;EACf;EAAA,IAAAvE,UAAA,CAAA3B,OAAA,EAAA+F,WAAA,EAAAC,QAAA;EAAA,WAAApE,aAAA,CAAA5B,OAAA,EAAA+F,WAAA;IAAAlE,GAAA;IAAAC,KAAA,EAEA,SAAAqE,MAAMA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACP;MACA,IAAI,CAACnF,OAAO,CAACsE,gBAAgB,CAAC,SAAS,EAAG,UAAAc,KAAK,EAAK;QAClD,IAAIA,KAAK,CAACxE,GAAG,KAAK,OAAO,EAAE;UACzBuE,MAAI,CAACE,IAAI,CAAC,CAAC;UACXD,KAAK,CAACE,cAAc,CAAC,CAAC;QACxB,CAAC,MAAM,IAAIF,KAAK,CAACxE,GAAG,KAAK,QAAQ,EAAE;UACjCuE,MAAI,CAACI,MAAM,CAAC,CAAC;UACbH,KAAK,CAACE,cAAc,CAAC,CAAC;QACxB;MACF,CAAC,CAAC;IACJ;EAAA;IAAA1E,GAAA;IAAAC,KAAA,EAEA,SAAA0E,MAAMA,CAAA,EAAG;MACP,IAAI,CAACrF,IAAI,CAAC,CAAC;MACX,IAAI,CAACsF,YAAY,CAAC,CAAC;IACrB;EAAA;IAAA5E,GAAA;IAAAC,KAAA,EAEA,SAAA+C,IAAIA,CAAA,EAA+C;MAAA,IAA9C6B,IAAI,GAAAC,SAAA,CAAA5D,MAAA,QAAA4D,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,MAAM;MAAA,IAAEE,OAAsB,GAAAF,SAAA,CAAA5D,MAAA,QAAA4D,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,IAAI;MAC/C,IAAI,CAAC/F,IAAI,CAAC2C,SAAS,CAACuD,MAAM,CAAC,WAAW,CAAC;MACvC,IAAI,CAAClG,IAAI,CAAC2C,SAAS,CAAC+B,GAAG,CAAC,YAAY,CAAC;MACrC,IAAI,IAAI,CAACrE,OAAO,IAAI,IAAI,EAAE;MAE1B,IAAI4F,OAAO,IAAI,IAAI,EAAE;QACnB,IAAI,CAAC5F,OAAO,CAACa,KAAK,GAAG+E,OAAO;MAC9B,CAAC,MAAM,IAAIH,IAAI,KAAK,IAAI,CAAC9F,IAAI,CAAC+B,YAAY,CAAC,WAAW,CAAC,EAAE;QACvD,IAAI,CAAC1B,OAAO,CAACa,KAAK,GAAG,EAAE;MACzB;MACA,IAAMiF,MAAM,GAAG,IAAI,CAAC7G,KAAK,CAAC8G,SAAS,CAAC,IAAI,CAAC9G,KAAK,CAAC+G,SAAS,CAACC,UAAU,CAAC;MACpE,IAAIH,MAAM,IAAI,IAAI,EAAE;QAClB,IAAI,CAACI,QAAQ,CAACJ,MAAM,CAAC;MACvB;MACA,IAAI,CAAC9F,OAAO,CAACqC,MAAM,CAAC,CAAC;MACrB,IAAI,CAACrC,OAAO,CAACiE,YAAY,CACvB,aAAa,EACb,IAAI,CAACjE,OAAO,CAAC0B,YAAY,SAAAyE,MAAA,CAASV,IAAK,CAAC,CAAC,IAAI,EAC/C,CAAC;MACD,IAAI,CAAC9F,IAAI,CAACsE,YAAY,CAAC,WAAW,EAAEwB,IAAI,CAAC;IAC3C;EAAA;IAAA7E,GAAA;IAAAC,KAAA,EAEA,SAAA2E,YAAYA,CAAA,EAAG;MACb,IAAI,CAACvG,KAAK,CAACmH,KAAK,CAAC;QAAEC,aAAa,EAAE;MAAK,CAAC,CAAC;IAC3C;EAAA;IAAAzF,GAAA;IAAAC,KAAA,EAEA,SAAAwE,IAAIA,CAAA,EAAG;MACL;MACA,IAAMxE,KAAA,GAAU,IAAI,CAACb,OAAO,CAAtBa,KAAA;MACN,QAAQ,IAAI,CAAClB,IAAI,CAAC+B,YAAY,CAAC,WAAW,CAAC;QACzC,KAAK,MAAM;UAAE;YACX,IAAQ4E,SAAA,GAAc,IAAI,CAACrH,KAAK,CAACU,IAAI,CAA7B2G,SAAA;YACR,IAAI,IAAI,CAACC,SAAS,EAAE;cAClB,IAAI,CAACtH,KAAK,CAACuH,UAAU,CACnB,IAAI,CAACD,SAAS,EACd,MAAM,EACN1F,KAAK,EACLoC,gBAAO,CAACwD,OAAO,CAACC,IAClB,CAAC;cACD,OAAO,IAAI,CAACH,SAAS;YACvB,CAAC,MAAM;cACL,IAAI,CAACf,YAAY,CAAC,CAAC;cACnB,IAAI,CAACvG,KAAK,CAAC2D,MAAM,CAAC,MAAM,EAAE/B,KAAK,EAAEoC,gBAAO,CAACwD,OAAO,CAACC,IAAI,CAAC;YACxD;YACA,IAAI,CAACzH,KAAK,CAACU,IAAI,CAAC2G,SAAS,GAAGA,SAAS;YACrC;UACF;QACA,KAAK,OAAO;UAAE;YACZzF,KAAK,GAAG8F,eAAe,CAAC9F,KAAK,CAAC;UAChC;QAAE;QACF,KAAK,SAAS;UAAE;YACd,IAAI,CAACA,KAAK,EAAE;YACZ,IAAM0D,KAAK,GAAG,IAAI,CAACtF,KAAK,CAACuF,YAAY,CAAC,IAAI,CAAC;YAC3C,IAAID,KAAK,IAAI,IAAI,EAAE;cACjB,IAAMqC,KAAK,GAAGrC,KAAK,CAACqC,KAAK,GAAGrC,KAAK,CAACzC,MAAM;cACxC,IAAI,CAAC7C,KAAK,CAAC4H,WAAW,CACpBD,KAAK;cACL;cACA,IAAI,CAACjH,IAAI,CAAC+B,YAAY,CAAC,WAAW,CAAC,EACnCb,KAAK,EACLoC,gBAAO,CAACwD,OAAO,CAACC,IAClB,CAAC;cACD,IAAI,IAAI,CAAC/G,IAAI,CAAC+B,YAAY,CAAC,WAAW,CAAC,KAAK,SAAS,EAAE;gBACrD,IAAI,CAACzC,KAAK,CAAC6H,UAAU,CAACF,KAAK,GAAG,CAAC,EAAE,GAAG,EAAE3D,gBAAO,CAACwD,OAAO,CAACC,IAAI,CAAC;cAC7D;cACA,IAAI,CAACzH,KAAK,CAAC8H,YAAY,CAACH,KAAK,GAAG,CAAC,EAAE3D,gBAAO,CAACwD,OAAO,CAACC,IAAI,CAAC;YAC1D;YACA;UACF;QACA;MACF;MACA;MACA,IAAI,CAAC1G,OAAO,CAACa,KAAK,GAAG,EAAE;MACvB,IAAI,CAACX,IAAI,CAAC,CAAC;IACb;EAAA;AAAA,EAtGwB8G,gBAAO;AAyGjC,SAASL,eAAeA,CAACM,GAAW,EAAE;EACpC,IAAIC,KAAK,GACPD,GAAG,CAACC,KAAK,CACP,4EACF,CAAC,IACDD,GAAG,CAACC,KAAK,CAAC,gEAAgE,CAAC;EAC7E,IAAIA,KAAK,EAAE;IACT,UAAAf,MAAA,CAAUe,KAAK,CAAC,CAAC,CAAC,IAAI,OAAQ,+BAAAf,MAAA,CAC5Be,KAAK,CAAC,CAAC,CACR;EACH;EACA;EACA,IAAKA,KAAK,GAAGD,GAAG,CAACC,KAAK,CAAC,gDAAgD,CAAC,EAAG;IACzE,UAAAf,MAAA,CAAUe,KAAK,CAAC,CAAC,CAAC,IAAI,OAAQ,gCAAAf,MAAA,CAA4Be,KAAK,CAAC,CAAC,CAAE;EACrE;EACA,OAAOD,GAAG;AACZ;AAEA,SAASzE,UAAUA,CACjBH,MAAyB,EACzB8E,MAA+B,EAE/B;EAAA,IADAC,YAAqB,GAAA1B,SAAA,CAAA5D,MAAA,QAAA4D,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,KAAK;EAE7ByB,MAAM,CAAC/G,OAAO,CAAE,UAAAS,KAAK,EAAK;IACxB,IAAMwG,MAAM,GAAG7H,QAAQ,CAACwE,aAAa,CAAC,QAAQ,CAAC;IAC/C,IAAInD,KAAK,KAAKuG,YAAY,EAAE;MAC1BC,MAAM,CAACpD,YAAY,CAAC,UAAU,EAAE,UAAU,CAAC;IAC7C,CAAC,MAAM;MACLoD,MAAM,CAACpD,YAAY,CAAC,OAAO,EAAEqD,MAAM,CAACzG,KAAK,CAAC,CAAC;IAC7C;IACAwB,MAAM,CAACsC,WAAW,CAAC0C,MAAM,CAAC;EAC5B,CAAC,CAAC;AACJ", "ignoreList": []}]}