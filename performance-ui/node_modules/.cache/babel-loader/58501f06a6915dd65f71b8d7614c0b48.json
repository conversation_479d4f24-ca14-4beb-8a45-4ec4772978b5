{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/main.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/main.js", "mtime": 1753510684530}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_vue", "_interopRequireDefault", "require", "_js<PERSON><PERSON>ie", "_elementUi", "_App", "_store", "_router", "_directive", "_plugins", "_request", "_data", "_config", "_ruoyi2", "_Pagination", "_RightToolbar", "_Editor", "_FileUpload", "_ImageUpload", "_ImagePreview", "_DictTag", "_DictData", "<PERSON><PERSON>", "prototype", "getDicts", "getConfigKey", "parseTime", "resetForm", "addDateRange", "selectDictLabel", "selectDictLabels", "download", "handleTree", "component", "DictTag", "Pagination", "RightToolbar", "Editor", "FileUpload", "ImageUpload", "ImagePreview", "use", "directive", "plugins", "DictData", "install", "Element", "size", "Cookies", "get", "config", "productionTip", "el", "router", "store", "render", "h", "App"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/main.js"], "sourcesContent": ["import Vue from 'vue'\n\nimport Cookies from 'js-cookie'\n\nimport Element from 'element-ui'\nimport './assets/styles/element-variables.scss'\n\nimport '@/assets/styles/index.scss' // global css\nimport '@/assets/styles/ruoyi.scss' // ruoyi css\nimport App from './App'\nimport store from './store'\nimport router from './router'\nimport directive from './directive' // directive\nimport plugins from './plugins' // plugins\nimport { download } from '@/utils/request'\n\nimport './assets/icons' // icon\nimport './permission' // permission control\nimport { getDicts } from \"@/api/system/dict/data\"\nimport { getConfigKey } from \"@/api/system/config\"\nimport { parseTime, resetForm, addDateRange, selectDictLabel, selectDictLabels, handleTree } from \"@/utils/ruoyi\"\n// 分页组件\nimport Pagination from \"@/components/Pagination\"\n// 自定义表格工具组件\nimport RightToolbar from \"@/components/RightToolbar\"\n// 富文本组件\nimport Editor from \"@/components/Editor\"\n// 文件上传组件\nimport FileUpload from \"@/components/FileUpload\"\n// 图片上传组件\nimport ImageUpload from \"@/components/ImageUpload\"\n// 图片预览组件\nimport ImagePreview from \"@/components/ImagePreview\"\n// 字典标签组件\nimport DictTag from '@/components/DictTag'\n// 字典数据组件\nimport DictData from '@/components/DictData'\n\n// 全局方法挂载\nVue.prototype.getDicts = getDicts\nVue.prototype.getConfigKey = getConfigKey\nVue.prototype.parseTime = parseTime\nVue.prototype.resetForm = resetForm\nVue.prototype.addDateRange = addDateRange\nVue.prototype.selectDictLabel = selectDictLabel\nVue.prototype.selectDictLabels = selectDictLabels\nVue.prototype.download = download\nVue.prototype.handleTree = handleTree\n\n// 全局组件挂载\nVue.component('DictTag', DictTag)\nVue.component('Pagination', Pagination)\nVue.component('RightToolbar', RightToolbar)\nVue.component('Editor', Editor)\nVue.component('FileUpload', FileUpload)\nVue.component('ImageUpload', ImageUpload)\nVue.component('ImagePreview', ImagePreview)\n\nVue.use(directive)\nVue.use(plugins)\nDictData.install()\n\n/**\n * If you don't want to use mock-server\n * you want to use MockJs for mock api\n * you can execute: mockXHR()\n *\n * Currently MockJs will be used in the production environment,\n * please remove it before going online! ! !\n */\n\nVue.use(Element, {\n  size: Cookies.get('size') || 'medium' // set element-ui default size\n})\n\nVue.config.productionTip = false\n\nnew Vue({\n  el: '#app',\n  router,\n  store,\n  render: h => h(App)\n})\n"], "mappings": ";;;;;;;AAAA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,SAAA,GAAAF,sBAAA,CAAAC,OAAA;AAEA,IAAAE,UAAA,GAAAH,sBAAA,CAAAC,OAAA;AACAA,OAAA;AAEAA,OAAA;AACAA,OAAA;AACA,IAAAG,IAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,MAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,OAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,UAAA,GAAAP,sBAAA,CAAAC,OAAA;AACA,IAAAO,QAAA,GAAAR,sBAAA,CAAAC,OAAA;AACA,IAAAQ,QAAA,GAAAR,OAAA;AAEAA,OAAA;AACAA,OAAA;AACA,IAAAS,KAAA,GAAAT,OAAA;AACA,IAAAU,OAAA,GAAAV,OAAA;AACA,IAAAW,OAAA,GAAAX,OAAA;AAEA,IAAAY,WAAA,GAAAb,sBAAA,CAAAC,OAAA;AAEA,IAAAa,aAAA,GAAAd,sBAAA,CAAAC,OAAA;AAEA,IAAAc,OAAA,GAAAf,sBAAA,CAAAC,OAAA;AAEA,IAAAe,WAAA,GAAAhB,sBAAA,CAAAC,OAAA;AAEA,IAAAgB,YAAA,GAAAjB,sBAAA,CAAAC,OAAA;AAEA,IAAAiB,aAAA,GAAAlB,sBAAA,CAAAC,OAAA;AAEA,IAAAkB,QAAA,GAAAnB,sBAAA,CAAAC,OAAA;AAEA,IAAAmB,SAAA,GAAApB,sBAAA,CAAAC,OAAA;AA7BoC;AACA;;AAIA;AACJ;;AAGR;AACF;;AAItB;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAGA;AACAoB,YAAG,CAACC,SAAS,CAACC,QAAQ,GAAGA,cAAQ;AACjCF,YAAG,CAACC,SAAS,CAACE,YAAY,GAAGA,oBAAY;AACzCH,YAAG,CAACC,SAAS,CAACG,SAAS,GAAGA,iBAAS;AACnCJ,YAAG,CAACC,SAAS,CAACI,SAAS,GAAGA,iBAAS;AACnCL,YAAG,CAACC,SAAS,CAACK,YAAY,GAAGA,oBAAY;AACzCN,YAAG,CAACC,SAAS,CAACM,eAAe,GAAGA,uBAAe;AAC/CP,YAAG,CAACC,SAAS,CAACO,gBAAgB,GAAGA,wBAAgB;AACjDR,YAAG,CAACC,SAAS,CAACQ,QAAQ,GAAGA,iBAAQ;AACjCT,YAAG,CAACC,SAAS,CAACS,UAAU,GAAGA,kBAAU;;AAErC;AACAV,YAAG,CAACW,SAAS,CAAC,SAAS,EAAEC,gBAAO,CAAC;AACjCZ,YAAG,CAACW,SAAS,CAAC,YAAY,EAAEE,mBAAU,CAAC;AACvCb,YAAG,CAACW,SAAS,CAAC,cAAc,EAAEG,qBAAY,CAAC;AAC3Cd,YAAG,CAACW,SAAS,CAAC,QAAQ,EAAEI,eAAM,CAAC;AAC/Bf,YAAG,CAACW,SAAS,CAAC,YAAY,EAAEK,mBAAU,CAAC;AACvChB,YAAG,CAACW,SAAS,CAAC,aAAa,EAAEM,oBAAW,CAAC;AACzCjB,YAAG,CAACW,SAAS,CAAC,cAAc,EAAEO,qBAAY,CAAC;AAE3ClB,YAAG,CAACmB,GAAG,CAACC,kBAAS,CAAC;AAClBpB,YAAG,CAACmB,GAAG,CAACE,gBAAO,CAAC;AAChBC,iBAAQ,CAACC,OAAO,CAAC,CAAC;;AAElB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAvB,YAAG,CAACmB,GAAG,CAACK,kBAAO,EAAE;EACfC,IAAI,EAAEC,iBAAO,CAACC,GAAG,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC;AACxC,CAAC,CAAC;AAEF3B,YAAG,CAAC4B,MAAM,CAACC,aAAa,GAAG,KAAK;AAEhC,IAAI7B,YAAG,CAAC;EACN8B,EAAE,EAAE,MAAM;EACVC,MAAM,EAANA,eAAM;EACNC,KAAK,EAALA,cAAK;EACLC,MAAM,EAAE,SAARA,MAAMA,CAAEC,CAAC;IAAA,OAAIA,CAAC,CAACC,YAAG,CAAC;EAAA;AACrB,CAAC,CAAC", "ignoreList": []}]}