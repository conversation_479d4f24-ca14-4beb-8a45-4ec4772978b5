{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/tool/build/TreeNodeDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/tool/build/TreeNodeDialog.vue", "mtime": 1753510684536}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_index", "require", "components", "inheritAttrs", "props", "data", "id", "formData", "label", "undefined", "value", "rules", "required", "message", "trigger", "dataType", "dataTypeOptions", "computed", "watch", "formDataValue", "val", "isNumberStr", "created", "mounted", "methods", "onOpen", "onClose", "close", "$emit", "handleConfirm", "_this", "$refs", "elForm", "validate", "valid", "parseFloat"], "sources": ["src/views/tool/build/TreeNodeDialog.vue"], "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      v-bind=\"$attrs\"\n      :close-on-click-modal=\"false\"\n      :modal-append-to-body=\"false\"\n      v-on=\"$listeners\"\n      @open=\"onOpen\"\n      @close=\"onClose\"\n    >\n      <el-row :gutter=\"0\">\n        <el-form\n          ref=\"elForm\"\n          :model=\"formData\"\n          :rules=\"rules\"\n          size=\"small\"\n          label-width=\"100px\"\n        >\n          <el-col :span=\"24\">\n            <el-form-item\n              label=\"选项名\"\n              prop=\"label\"\n            >\n              <el-input\n                v-model=\"formData.label\"\n                placeholder=\"请输入选项名\"\n                clearable\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item\n              label=\"选项值\"\n              prop=\"value\"\n            >\n              <el-input\n                v-model=\"formData.value\"\n                placeholder=\"请输入选项值\"\n                clearable\n              >\n                <el-select\n                  slot=\"append\"\n                  v-model=\"dataType\"\n                  :style=\"{width: '100px'}\"\n                >\n                  <el-option\n                    v-for=\"(item, index) in dataTypeOptions\"\n                    :key=\"index\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                    :disabled=\"item.disabled\"\n                  />\n                </el-select>\n              </el-input>\n            </el-form-item>\n          </el-col>\n        </el-form>\n      </el-row>\n      <div slot=\"footer\">\n        <el-button\n          type=\"primary\"\n          @click=\"handleConfirm\"\n        >\n          确定\n        </el-button>\n        <el-button @click=\"close\">\n          取消\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n<script>\nimport { isNumberStr } from '@/utils/index'\n\nexport default {\n  components: {},\n  inheritAttrs: false,\n  props: [],\n  data() {\n    return {\n      id: 100,\n      formData: {\n        label: undefined,\n        value: undefined\n      },\n      rules: {\n        label: [\n          {\n            required: true,\n            message: '请输入选项名',\n            trigger: 'blur'\n          }\n        ],\n        value: [\n          {\n            required: true,\n            message: '请输入选项值',\n            trigger: 'blur'\n          }\n        ]\n      },\n      dataType: 'string',\n      dataTypeOptions: [\n        {\n          label: '字符串',\n          value: 'string'\n        },\n        {\n          label: '数字',\n          value: 'number'\n        }\n      ]\n    }\n  },\n  computed: {},\n  watch: {\n    'formData.value': function (val) {\n      this.dataType = isNumberStr(val) ? 'number' : 'string'\n    }\n  },\n  created() {},\n  mounted() {},\n  methods: {\n    onOpen() {\n      this.formData = {\n        label: undefined,\n        value: undefined\n      }\n    },\n    onClose() {},\n    close() {\n      this.$emit('update:visible', false)\n    },\n    handleConfirm() {\n      this.$refs.elForm.validate(valid => {\n        if (!valid) return\n        if (this.dataType === 'number') {\n          this.formData.value = parseFloat(this.formData.value)\n        }\n        this.formData.id = this.id++\n        this.$emit('commit', this.formData)\n        this.close()\n      })\n    }\n  }\n}\n</script>\n"], "mappings": ";;;;;;AAyEA,IAAAA,MAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,UAAA;EACAC,YAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,EAAA;MACAC,QAAA;QACAC,KAAA,EAAAC,SAAA;QACAC,KAAA,EAAAD;MACA;MACAE,KAAA;QACAH,KAAA,GACA;UACAI,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAJ,KAAA,GACA;UACAE,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;MACAC,QAAA;MACAC,eAAA,GACA;QACAR,KAAA;QACAE,KAAA;MACA,GACA;QACAF,KAAA;QACAE,KAAA;MACA;IAEA;EACA;EACAO,QAAA;EACAC,KAAA;IACA,2BAAAC,cAAAC,GAAA;MACA,KAAAL,QAAA,OAAAM,kBAAA,EAAAD,GAAA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;EACAC,OAAA,WAAAA,QAAA;EACAC,OAAA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAlB,QAAA;QACAC,KAAA,EAAAC,SAAA;QACAC,KAAA,EAAAD;MACA;IACA;IACAiB,OAAA,WAAAA,QAAA;IACAC,KAAA,WAAAA,MAAA;MACA,KAAAC,KAAA;IACA;IACAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA,CAAAC,MAAA,CAAAC,QAAA,WAAAC,KAAA;QACA,KAAAA,KAAA;QACA,IAAAJ,KAAA,CAAAf,QAAA;UACAe,KAAA,CAAAvB,QAAA,CAAAG,KAAA,GAAAyB,UAAA,CAAAL,KAAA,CAAAvB,QAAA,CAAAG,KAAA;QACA;QACAoB,KAAA,CAAAvB,QAAA,CAAAD,EAAA,GAAAwB,KAAA,CAAAxB,EAAA;QACAwB,KAAA,CAAAF,KAAA,WAAAE,KAAA,CAAAvB,QAAA;QACAuB,KAAA,CAAAH,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}