{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/tool/build/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/tool/build/index.vue", "mtime": 1753510684536}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_vuedraggable", "_interopRequireDefault", "require", "_jsBeautify", "_clipboard", "_render", "_RightPanel", "_config", "_index", "_html", "_js", "_css", "_drawingDefault", "_logo", "_CodeTypeDialog", "_DraggableItem", "oldActiveId", "tempActiveData", "_default", "exports", "default", "components", "draggable", "render", "RightPanel", "CodeTypeDialog", "DraggableItem", "data", "logo", "idGlobal", "formConf", "inputComponents", "selectComponents", "layoutComponents", "labelWidth", "drawingList", "<PERSON><PERSON><PERSON><PERSON>", "drawingData", "activeId", "formId", "drawerVisible", "formData", "dialogVisible", "generateConf", "showFileName", "activeData", "created", "document", "body", "ondrop", "event", "preventDefault", "stopPropagation", "watch", "activeDataLabel", "val", "oldVal", "placeholder", "undefined", "tag", "replace", "handler", "immediate", "mounted", "_this", "clipboard", "ClipboardJS", "text", "trigger", "codeStr", "generateCode", "$notify", "title", "message", "type", "on", "e", "$message", "error", "methods", "activeFormItem", "element", "onEnd", "obj", "a", "from", "to", "addComponent", "item", "clone", "cloneComponent", "push", "origin", "JSON", "parse", "stringify", "span", "<PERSON><PERSON><PERSON>", "Date", "layout", "vModel", "concat", "label", "componentName", "gutter", "AssembleFormData", "_objectSpread2", "fields", "generate", "func", "titleCase", "operationType", "execRun", "execDownload", "blob", "Blob", "$download", "saveAs", "fileName", "execCopy", "getElementById", "click", "empty", "_this2", "$confirm", "then", "drawingItemCopy", "parent", "createIdAndKey", "_this3", "Array", "isArray", "children", "map", "childItem", "drawingItemDelete", "index", "_this4", "splice", "$nextTick", "len", "length", "script", "vueScript", "makeUpJs", "html", "vueTemplate", "makeUpHtml", "css", "cssStyle", "makeUpCss", "beautifier", "beautifierConf", "download", "run", "copy", "tagChange", "newTag", "_this5", "tagIcon", "Object", "keys", "for<PERSON>ach", "key", "_typeof2", "updateDrawingList", "list", "_this6", "findIndex"], "sources": ["src/views/tool/build/index.vue"], "sourcesContent": ["<template>\n  <div class=\"container\">\n    <div class=\"left-board\">\n      <div class=\"logo-wrapper\">\n        <div class=\"logo\">\n          <img :src=\"logo\" alt=\"logo\"> Form Generator\n        </div>\n      </div>\n      <el-scrollbar class=\"left-scrollbar\">\n        <div class=\"components-list\">\n          <div class=\"components-title\">\n            <svg-icon icon-class=\"component\" />输入型组件\n          </div>\n          <draggable\n            class=\"components-draggable\"\n            :list=\"inputComponents\"\n            :group=\"{ name: 'componentsGroup', pull: 'clone', put: false }\"\n            :clone=\"cloneComponent\"\n            draggable=\".components-item\"\n            :sort=\"false\"\n            @end=\"onEnd\"\n          >\n            <div\n              v-for=\"(element, index) in inputComponents\" :key=\"index\" class=\"components-item\"\n              @click=\"addComponent(element)\"\n            >\n              <div class=\"components-body\">\n                <svg-icon :icon-class=\"element.tagIcon\" />\n                {{ element.label }}\n              </div>\n            </div>\n          </draggable>\n          <div class=\"components-title\">\n            <svg-icon icon-class=\"component\" />选择型组件\n          </div>\n          <draggable\n            class=\"components-draggable\"\n            :list=\"selectComponents\"\n            :group=\"{ name: 'componentsGroup', pull: 'clone', put: false }\"\n            :clone=\"cloneComponent\"\n            draggable=\".components-item\"\n            :sort=\"false\"\n            @end=\"onEnd\"\n          >\n            <div\n              v-for=\"(element, index) in selectComponents\"\n              :key=\"index\"\n              class=\"components-item\"\n              @click=\"addComponent(element)\"\n            >\n              <div class=\"components-body\">\n                <svg-icon :icon-class=\"element.tagIcon\" />\n                {{ element.label }}\n              </div>\n            </div>\n          </draggable>\n          <div class=\"components-title\">\n            <svg-icon icon-class=\"component\" /> 布局型组件\n          </div>\n          <draggable\n            class=\"components-draggable\" :list=\"layoutComponents\"\n            :group=\"{ name: 'componentsGroup', pull: 'clone', put: false }\" :clone=\"cloneComponent\"\n            draggable=\".components-item\" :sort=\"false\" @end=\"onEnd\"\n          >\n            <div\n              v-for=\"(element, index) in layoutComponents\" :key=\"index\" class=\"components-item\"\n              @click=\"addComponent(element)\"\n            >\n              <div class=\"components-body\">\n                <svg-icon :icon-class=\"element.tagIcon\" />\n                {{ element.label }}\n              </div>\n            </div>\n          </draggable>\n        </div>\n      </el-scrollbar>\n    </div>\n\n    <div class=\"center-board\">\n      <div class=\"action-bar\">\n        <el-button icon=\"el-icon-download\" type=\"text\" @click=\"download\">\n          导出vue文件\n        </el-button>\n        <el-button class=\"copy-btn-main\" icon=\"el-icon-document-copy\" type=\"text\" @click=\"copy\">\n          复制代码\n        </el-button>\n        <el-button class=\"delete-btn\" icon=\"el-icon-delete\" type=\"text\" @click=\"empty\">\n          清空\n        </el-button>\n      </div>\n      <el-scrollbar class=\"center-scrollbar\">\n        <el-row class=\"center-board-row\" :gutter=\"formConf.gutter\">\n          <el-form\n            :size=\"formConf.size\"\n            :label-position=\"formConf.labelPosition\"\n            :disabled=\"formConf.disabled\"\n            :label-width=\"formConf.labelWidth + 'px'\"\n          >\n            <draggable class=\"drawing-board\" :list=\"drawingList\" :animation=\"340\" group=\"componentsGroup\">\n              <draggable-item\n                v-for=\"(element, index) in drawingList\"\n                :key=\"element.renderKey\"\n                :drawing-list=\"drawingList\"\n                :element=\"element\"\n                :index=\"index\"\n                :active-id=\"activeId\"\n                :form-conf=\"formConf\"\n                @activeItem=\"activeFormItem\"\n                @copyItem=\"drawingItemCopy\"\n                @deleteItem=\"drawingItemDelete\"\n              />\n            </draggable>\n            <div v-show=\"!drawingList.length\" class=\"empty-info\">\n              从左侧拖入或点选组件进行表单设计\n            </div>\n          </el-form>\n        </el-row>\n      </el-scrollbar>\n    </div>\n\n    <right-panel\n      :active-data=\"activeData\"\n      :form-conf=\"formConf\"\n      :show-field=\"!!drawingList.length\"\n      @tag-change=\"tagChange\"\n    />\n\n    <code-type-dialog\n      :visible.sync=\"dialogVisible\"\n      title=\"选择生成类型\"\n      :show-file-name=\"showFileName\"\n      @confirm=\"generate\"\n    />\n    <input id=\"copyNode\" type=\"hidden\">\n  </div>\n</template>\n\n<script>\nimport draggable from 'vuedraggable'\nimport beautifier from 'js-beautify'\nimport ClipboardJS from 'clipboard'\nimport render from '@/utils/generator/render'\nimport RightPanel from './RightPanel'\nimport { inputComponents, selectComponents, layoutComponents, formConf } from '@/utils/generator/config'\nimport { beautifierConf, titleCase } from '@/utils/index'\nimport { makeUpHtml, vueTemplate, vueScript, cssStyle } from '@/utils/generator/html'\nimport { makeUpJs } from '@/utils/generator/js'\nimport { makeUpCss } from '@/utils/generator/css'\nimport drawingDefault from '@/utils/generator/drawingDefault'\nimport logo from '@/assets/logo/logo.png'\nimport CodeTypeDialog from './CodeTypeDialog'\nimport DraggableItem from './DraggableItem'\n\nlet oldActiveId\nlet tempActiveData\n\nexport default {\n  components: {\n    draggable,\n    render,\n    RightPanel,\n    CodeTypeDialog,\n    DraggableItem\n  },\n  data() {\n    return {\n      logo,\n      idGlobal: 100,\n      formConf,\n      inputComponents,\n      selectComponents,\n      layoutComponents,\n      labelWidth: 100,\n      drawingList: drawingDefault,\n      drawingData: {},\n      activeId: drawingDefault[0].formId,\n      drawerVisible: false,\n      formData: {},\n      dialogVisible: false,\n      generateConf: null,\n      showFileName: false,\n      activeData: drawingDefault[0]\n    }\n  },\n  created() {\n    // 防止 firefox 下 拖拽 会新打卡一个选项卡\n    document.body.ondrop = event => {\n      event.preventDefault()\n      event.stopPropagation()\n    }\n  },\n  watch: {\n    'activeData.label': function (val, oldVal) {\n      if (\n        this.activeData.placeholder === undefined\n        || !this.activeData.tag\n        || oldActiveId !== this.activeId\n      ) {\n        return\n      }\n      this.activeData.placeholder = this.activeData.placeholder.replace(oldVal, '') + val\n    },\n    activeId: {\n      handler(val) {\n        oldActiveId = val\n      },\n      immediate: true\n    }\n  },\n  mounted() {\n    const clipboard = new ClipboardJS('#copyNode', {\n      text: trigger => {\n        const codeStr = this.generateCode()\n        this.$notify({\n          title: '成功',\n          message: '代码已复制到剪切板，可粘贴。',\n          type: 'success'\n        })\n        return codeStr\n      }\n    })\n    clipboard.on('error', e => {\n      this.$message.error('代码复制失败')\n    })\n  },\n  methods: {\n    activeFormItem(element) {\n      this.activeData = element\n      this.activeId = element.formId\n    },\n    onEnd(obj, a) {\n      if (obj.from !== obj.to) {\n        this.activeData = tempActiveData\n        this.activeId = this.idGlobal\n      }\n    },\n    addComponent(item) {\n      const clone = this.cloneComponent(item)\n      this.drawingList.push(clone)\n      this.activeFormItem(clone)\n    },\n    cloneComponent(origin) {\n      const clone = JSON.parse(JSON.stringify(origin))\n      clone.formId = ++this.idGlobal\n      clone.span = formConf.span\n      clone.renderKey = +new Date() // 改变renderKey后可以实现强制更新组件\n      if (!clone.layout) clone.layout = 'colFormItem'\n      if (clone.layout === 'colFormItem') {\n        clone.vModel = `field${this.idGlobal}`\n        clone.placeholder !== undefined && (clone.placeholder += clone.label)\n        tempActiveData = clone\n      } else if (clone.layout === 'rowFormItem') {\n        delete clone.label\n        clone.componentName = `row${this.idGlobal}`\n        clone.gutter = this.formConf.gutter\n        tempActiveData = clone\n      }\n      return tempActiveData\n    },\n    AssembleFormData() {\n      this.formData = {\n        fields: JSON.parse(JSON.stringify(this.drawingList)),\n        ...this.formConf\n      }\n    },\n    generate(data) {\n      const func = this[`exec${titleCase(this.operationType)}`]\n      this.generateConf = data\n      func && func(data)\n    },\n    execRun(data) {\n      this.AssembleFormData()\n      this.drawerVisible = true\n    },\n    execDownload(data) {\n      const codeStr = this.generateCode()\n      const blob = new Blob([codeStr], { type: 'text/plain;charset=utf-8' })\n      this.$download.saveAs(blob, data.fileName)\n    },\n    execCopy(data) {\n      document.getElementById('copyNode').click()\n    },\n    empty() {\n      this.$confirm('确定要清空所有组件吗？', '提示', { type: 'warning' }).then(\n        () => {\n          this.drawingList = []\n        }\n      )\n    },\n    drawingItemCopy(item, parent) {\n      let clone = JSON.parse(JSON.stringify(item))\n      clone = this.createIdAndKey(clone)\n      parent.push(clone)\n      this.activeFormItem(clone)\n    },\n    createIdAndKey(item) {\n      item.formId = ++this.idGlobal\n      item.renderKey = +new Date()\n      if (item.layout === 'colFormItem') {\n        item.vModel = `field${this.idGlobal}`\n      } else if (item.layout === 'rowFormItem') {\n        item.componentName = `row${this.idGlobal}`\n      }\n      if (Array.isArray(item.children)) {\n        item.children = item.children.map(childItem => this.createIdAndKey(childItem))\n      }\n      return item\n    },\n    drawingItemDelete(index, parent) {\n      parent.splice(index, 1)\n      this.$nextTick(() => {\n        const len = this.drawingList.length\n        if (len) {\n          this.activeFormItem(this.drawingList[len - 1])\n        }\n      })\n    },\n    generateCode() {\n      const { type } = this.generateConf\n      this.AssembleFormData()\n      const script = vueScript(makeUpJs(this.formData, type))\n      const html = vueTemplate(makeUpHtml(this.formData, type))\n      const css = cssStyle(makeUpCss(this.formData))\n      return beautifier.html(html + script + css, beautifierConf.html)\n    },\n    download() {\n      this.dialogVisible = true\n      this.showFileName = true\n      this.operationType = 'download'\n    },\n    run() {\n      this.dialogVisible = true\n      this.showFileName = false\n      this.operationType = 'run'\n    },\n    copy() {\n      this.dialogVisible = true\n      this.showFileName = false\n      this.operationType = 'copy'\n    },\n    tagChange(newTag) {\n      newTag = this.cloneComponent(newTag)\n      newTag.vModel = this.activeData.vModel\n      newTag.formId = this.activeId\n      newTag.span = this.activeData.span\n      delete this.activeData.tag\n      delete this.activeData.tagIcon\n      delete this.activeData.document\n      Object.keys(newTag).forEach(key => {\n        if (this.activeData[key] !== undefined\n          && typeof this.activeData[key] === typeof newTag[key]) {\n          newTag[key] = this.activeData[key]\n        }\n      })\n      this.activeData = newTag\n      this.updateDrawingList(newTag, this.drawingList)\n    },\n    updateDrawingList(newTag, list) {\n      const index = list.findIndex(item => item.formId === this.activeId)\n      if (index > -1) {\n        list.splice(index, 1, newTag)\n      } else {\n        list.forEach(item => {\n          if (Array.isArray(item.children)) this.updateDrawingList(newTag, item.children)\n        })\n      }\n    }\n  }\n}\n</script>\n\n<style lang='scss'>\n.editor-tabs{\n  background: #121315;\n  .el-tabs__header{\n    margin: 0;\n    border-bottom-color: #121315;\n    .el-tabs__nav{\n      border-color: #121315;\n    }\n  }\n  .el-tabs__item{\n    height: 32px;\n    line-height: 32px;\n    color: #888a8e;\n    border-left: 1px solid #121315 !important;\n    background: #363636;\n    margin-right: 5px;\n    user-select: none;\n  }\n  .el-tabs__item.is-active{\n    background: #1e1e1e;\n    border-bottom-color: #1e1e1e!important;\n    color: #fff;\n  }\n  .el-icon-edit{\n    color: #f1fa8c;\n  }\n  .el-icon-document{\n    color: #a95812;\n  }\n}\n\n// home\n.right-scrollbar {\n  .el-scrollbar__view {\n    padding: 12px 18px 15px 15px;\n  }\n}\n.left-scrollbar .el-scrollbar__wrap {\n  box-sizing: border-box;\n  overflow-x: hidden !important;\n  margin-bottom: 0 !important;\n}\n.center-tabs{\n  .el-tabs__header{\n    margin-bottom: 0!important;\n  }\n  .el-tabs__item{\n    width: 50%;\n    text-align: center;\n  }\n  .el-tabs__nav{\n    width: 100%;\n  }\n}\n.reg-item{\n  padding: 12px 6px;\n  background: #f8f8f8;\n  position: relative;\n  border-radius: 4px;\n  .close-btn{\n    position: absolute;\n    right: -6px;\n    top: -6px;\n    display: block;\n    width: 16px;\n    height: 16px;\n    line-height: 16px;\n    background: rgba(0, 0, 0, 0.2);\n    border-radius: 50%;\n    color: #fff;\n    text-align: center;\n    z-index: 1;\n    cursor: pointer;\n    font-size: 12px;\n    &:hover{\n      background: rgba(210, 23, 23, 0.5)\n    }\n  }\n  & + .reg-item{\n    margin-top: 18px;\n  }\n}\n.action-bar{\n  & .el-button+.el-button {\n    margin-left: 15px;\n  }\n  & i {\n    font-size: 20px;\n    vertical-align: middle;\n    position: relative;\n    top: -1px;\n  }\n}\n\n.custom-tree-node{\n  width: 100%;\n  font-size: 14px;\n  .node-operation{\n    float: right;\n  }\n  i[class*=\"el-icon\"] + i[class*=\"el-icon\"]{\n    margin-left: 6px;\n  }\n  .el-icon-plus{\n    color: #409EFF;\n  }\n  .el-icon-delete{\n    color: #157a0c;\n  }\n}\n\n.left-scrollbar .el-scrollbar__view{\n  overflow-x: hidden;\n}\n\n.el-rate{\n  display: inline-block;\n  vertical-align: text-top;\n}\n.el-upload__tip{\n  line-height: 1.2;\n}\n\n$selectedColor: #f6f7ff;\n$lighterBlue: #409EFF;\n\n.container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n\n.components-list {\n  padding: 8px;\n  box-sizing: border-box;\n  height: 100%;\n  .components-item {\n    display: inline-block;\n    width: 48%;\n    margin: 1%;\n    transition: transform 0ms !important;\n  }\n}\n.components-draggable{\n  padding-bottom: 20px;\n}\n.components-title{\n  font-size: 14px;\n  color: #222;\n  margin: 6px 2px;\n  .svg-icon{\n    color: #666;\n    font-size: 18px;\n  }\n}\n\n.components-body {\n  padding: 8px 10px;\n  background: $selectedColor;\n  font-size: 12px;\n  cursor: move;\n  border: 1px dashed $selectedColor;\n  border-radius: 3px;\n  .svg-icon{\n    color: #777;\n    font-size: 15px;\n  }\n  &:hover {\n    border: 1px dashed #787be8;\n    color: #787be8;\n    .svg-icon {\n      color: #787be8;\n    }\n  }\n}\n\n.left-board {\n  width: 260px;\n  position: absolute;\n  left: 0;\n  top: 0;\n  height: 100vh;\n}\n.left-scrollbar{\n  height: calc(100vh - 42px);\n  overflow: hidden;\n}\n.center-scrollbar {\n  height: calc(100vh - 42px);\n  overflow: hidden;\n  border-left: 1px solid #f1e8e8;\n  border-right: 1px solid #f1e8e8;\n  box-sizing: border-box;\n}\n.center-board {\n  height: 100vh;\n  width: auto;\n  margin: 0 350px 0 260px;\n  box-sizing: border-box;\n}\n.empty-info{\n  position: absolute;\n  top: 46%;\n  left: 0;\n  right: 0;\n  text-align: center;\n  font-size: 18px;\n  color: #ccb1ea;\n  letter-spacing: 4px;\n}\n.action-bar{\n  position: relative;\n  height: 42px;\n  text-align: right;\n  padding: 0 15px;\n  box-sizing: border-box;;\n  border: 1px solid #f1e8e8;\n  border-top: none;\n  border-left: none;\n  .delete-btn{\n    color: #F56C6C;\n  }\n}\n.logo-wrapper{\n  position: relative;\n  height: 42px;\n  background: #fff;\n  border-bottom: 1px solid #f1e8e8;\n  box-sizing: border-box;\n}\n.logo{\n  position: absolute;\n  left: 12px;\n  top: 6px;\n  line-height: 30px;\n  color: #00afff;\n  font-weight: 600;\n  font-size: 17px;\n  white-space: nowrap;\n  > img{\n    width: 30px;\n    height: 30px;\n    vertical-align: top;\n  }\n  .github{\n    display: inline-block;\n    vertical-align: sub;\n    margin-left: 15px;\n    > img{\n      height: 22px;\n    }\n  }\n}\n\n.center-board-row {\n  padding: 12px 12px 15px 12px;\n  box-sizing: border-box;\n  & > .el-form {\n    // 69 = 12+15+42\n    height: calc(100vh - 69px);\n  }\n}\n.drawing-board {\n  height: 100%;\n  position: relative;\n  .components-body {\n    padding: 0;\n    margin: 0;\n    font-size: 0;\n  }\n  .sortable-ghost {\n    position: relative;\n    display: block;\n    overflow: hidden;\n    &::before {\n      content: \" \";\n      position: absolute;\n      left: 0;\n      right: 0;\n      top: 0;\n      height: 3px;\n      background: rgb(89, 89, 223);\n      z-index: 2;\n    }\n  }\n  .components-item.sortable-ghost {\n    width: 100%;\n    height: 60px;\n    background-color: $selectedColor;\n  }\n  .active-from-item {\n    & > .el-form-item{\n      background: $selectedColor;\n      border-radius: 6px;\n    }\n    & > .drawing-item-copy, & > .drawing-item-delete{\n      display: initial;\n    }\n    & > .component-name{\n      color: $lighterBlue;\n    }\n  }\n  .el-form-item{\n    margin-bottom: 15px;\n  }\n}\n.drawing-item{\n  position: relative;\n  cursor: move;\n  &.unfocus-bordered:not(.activeFromItem) > div:first-child  {\n    border: 1px dashed #ccc;\n  }\n  .el-form-item{\n    padding: 12px 10px;\n  }\n}\n.drawing-row-item{\n  position: relative;\n  cursor: move;\n  box-sizing: border-box;\n  border: 1px dashed #ccc;\n  border-radius: 3px;\n  padding: 0 2px;\n  margin-bottom: 15px;\n  .drawing-row-item {\n    margin-bottom: 2px;\n  }\n  .el-col{\n    margin-top: 22px;\n  }\n  .el-form-item{\n    margin-bottom: 0;\n  }\n  .drag-wrapper{\n    min-height: 80px;\n  }\n  &.active-from-item{\n    border: 1px dashed $lighterBlue;\n  }\n  .component-name{\n    position: absolute;\n    top: 0;\n    left: 0;\n    font-size: 12px;\n    color: #bbb;\n    display: inline-block;\n    padding: 0 6px;\n  }\n}\n.drawing-item, .drawing-row-item{\n  &:hover {\n    & > .el-form-item{\n      background: $selectedColor;\n      border-radius: 6px;\n    }\n    & > .drawing-item-copy, & > .drawing-item-delete{\n      display: initial;\n    }\n  }\n  & > .drawing-item-copy, & > .drawing-item-delete{\n    display: none;\n    position: absolute;\n    top: -10px;\n    width: 22px;\n    height: 22px;\n    line-height: 22px;\n    text-align: center;\n    border-radius: 50%;\n    font-size: 12px;\n    border: 1px solid;\n    cursor: pointer;\n    z-index: 1;\n  }\n  & > .drawing-item-copy{\n    right: 56px;\n    border-color: $lighterBlue;\n    color: $lighterBlue;\n    background: #fff;\n    &:hover{\n      background: $lighterBlue;\n      color: #fff;\n    }\n  }\n  & > .drawing-item-delete{\n    right: 24px;\n    border-color: #F56C6C;\n    color: #F56C6C;\n    background: #fff;\n    &:hover{\n      background: #F56C6C;\n      color: #fff;\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AA0IA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,WAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,UAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,OAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,WAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,OAAA,GAAAL,OAAA;AACA,IAAAM,MAAA,GAAAN,OAAA;AACA,IAAAO,KAAA,GAAAP,OAAA;AACA,IAAAQ,GAAA,GAAAR,OAAA;AACA,IAAAS,IAAA,GAAAT,OAAA;AACA,IAAAU,eAAA,GAAAX,sBAAA,CAAAC,OAAA;AACA,IAAAW,KAAA,GAAAZ,sBAAA,CAAAC,OAAA;AACA,IAAAY,eAAA,GAAAb,sBAAA,CAAAC,OAAA;AACA,IAAAa,cAAA,GAAAd,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAAc,WAAA;AACA,IAAAC,cAAA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,UAAA;IACAC,SAAA,EAAAA,qBAAA;IACAC,MAAA,EAAAA,eAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,cAAA,EAAAA,uBAAA;IACAC,aAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA,EAAAA,aAAA;MACAC,QAAA;MACAC,QAAA,EAAAA,gBAAA;MACAC,eAAA,EAAAA,uBAAA;MACAC,gBAAA,EAAAA,wBAAA;MACAC,gBAAA,EAAAA,wBAAA;MACAC,UAAA;MACAC,WAAA,EAAAC,uBAAA;MACAC,WAAA;MACAC,QAAA,EAAAF,uBAAA,IAAAG,MAAA;MACAC,aAAA;MACAC,QAAA;MACAC,aAAA;MACAC,YAAA;MACAC,YAAA;MACAC,UAAA,EAAAT,uBAAA;IACA;EACA;EACAU,OAAA,WAAAA,QAAA;IACA;IACAC,QAAA,CAAAC,IAAA,CAAAC,MAAA,aAAAC,KAAA;MACAA,KAAA,CAAAC,cAAA;MACAD,KAAA,CAAAE,eAAA;IACA;EACA;EACAC,KAAA;IACA,6BAAAC,gBAAAC,GAAA,EAAAC,MAAA;MACA,IACA,KAAAX,UAAA,CAAAY,WAAA,KAAAC,SAAA,IACA,MAAAb,UAAA,CAAAc,GAAA,IACA3C,WAAA,UAAAsB,QAAA,EACA;QACA;MACA;MACA,KAAAO,UAAA,CAAAY,WAAA,QAAAZ,UAAA,CAAAY,WAAA,CAAAG,OAAA,CAAAJ,MAAA,QAAAD,GAAA;IACA;IACAjB,QAAA;MACAuB,OAAA,WAAAA,QAAAN,GAAA;QACAvC,WAAA,GAAAuC,GAAA;MACA;MACAO,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,IAAAC,SAAA,OAAAC,kBAAA;MACAC,IAAA,WAAAA,KAAAC,OAAA;QACA,IAAAC,OAAA,GAAAL,KAAA,CAAAM,YAAA;QACAN,KAAA,CAAAO,OAAA;UACAC,KAAA;UACAC,OAAA;UACAC,IAAA;QACA;QACA,OAAAL,OAAA;MACA;IACA;IACAJ,SAAA,CAAAU,EAAA,oBAAAC,CAAA;MACAZ,KAAA,CAAAa,QAAA,CAAAC,KAAA;IACA;EACA;EACAC,OAAA;IACAC,cAAA,WAAAA,eAAAC,OAAA;MACA,KAAApC,UAAA,GAAAoC,OAAA;MACA,KAAA3C,QAAA,GAAA2C,OAAA,CAAA1C,MAAA;IACA;IACA2C,KAAA,WAAAA,MAAAC,GAAA,EAAAC,CAAA;MACA,IAAAD,GAAA,CAAAE,IAAA,KAAAF,GAAA,CAAAG,EAAA;QACA,KAAAzC,UAAA,GAAA5B,cAAA;QACA,KAAAqB,QAAA,QAAAT,QAAA;MACA;IACA;IACA0D,YAAA,WAAAA,aAAAC,IAAA;MACA,IAAAC,KAAA,QAAAC,cAAA,CAAAF,IAAA;MACA,KAAArD,WAAA,CAAAwD,IAAA,CAAAF,KAAA;MACA,KAAAT,cAAA,CAAAS,KAAA;IACA;IACAC,cAAA,WAAAA,eAAAE,MAAA;MACA,IAAAH,KAAA,GAAAI,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAH,MAAA;MACAH,KAAA,CAAAlD,MAAA,UAAAV,QAAA;MACA4D,KAAA,CAAAO,IAAA,GAAAlE,gBAAA,CAAAkE,IAAA;MACAP,KAAA,CAAAQ,SAAA,QAAAC,IAAA;MACA,KAAAT,KAAA,CAAAU,MAAA,EAAAV,KAAA,CAAAU,MAAA;MACA,IAAAV,KAAA,CAAAU,MAAA;QACAV,KAAA,CAAAW,MAAA,WAAAC,MAAA,MAAAxE,QAAA;QACA4D,KAAA,CAAAhC,WAAA,KAAAC,SAAA,KAAA+B,KAAA,CAAAhC,WAAA,IAAAgC,KAAA,CAAAa,KAAA;QACArF,cAAA,GAAAwE,KAAA;MACA,WAAAA,KAAA,CAAAU,MAAA;QACA,OAAAV,KAAA,CAAAa,KAAA;QACAb,KAAA,CAAAc,aAAA,SAAAF,MAAA,MAAAxE,QAAA;QACA4D,KAAA,CAAAe,MAAA,QAAA1E,QAAA,CAAA0E,MAAA;QACAvF,cAAA,GAAAwE,KAAA;MACA;MACA,OAAAxE,cAAA;IACA;IACAwF,gBAAA,WAAAA,iBAAA;MACA,KAAAhE,QAAA,OAAAiE,cAAA,CAAAtF,OAAA;QACAuF,MAAA,EAAAd,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAA5D,WAAA;MAAA,GACA,KAAAL,QAAA,CACA;IACA;IACA8E,QAAA,WAAAA,SAAAjF,IAAA;MACA,IAAAkF,IAAA,eAAAR,MAAA,KAAAS,gBAAA,OAAAC,aAAA;MACA,KAAApE,YAAA,GAAAhB,IAAA;MACAkF,IAAA,IAAAA,IAAA,CAAAlF,IAAA;IACA;IACAqF,OAAA,WAAAA,QAAArF,IAAA;MACA,KAAA8E,gBAAA;MACA,KAAAjE,aAAA;IACA;IACAyE,YAAA,WAAAA,aAAAtF,IAAA;MACA,IAAA0C,OAAA,QAAAC,YAAA;MACA,IAAA4C,IAAA,OAAAC,IAAA,EAAA9C,OAAA;QAAAK,IAAA;MAAA;MACA,KAAA0C,SAAA,CAAAC,MAAA,CAAAH,IAAA,EAAAvF,IAAA,CAAA2F,QAAA;IACA;IACAC,QAAA,WAAAA,SAAA5F,IAAA;MACAoB,QAAA,CAAAyE,cAAA,aAAAC,KAAA;IACA;IACAC,KAAA,WAAAA,MAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QAAAlD,IAAA;MAAA,GAAAmD,IAAA,CACA;QACAF,MAAA,CAAAxF,WAAA;MACA,CACA;IACA;IACA2F,eAAA,WAAAA,gBAAAtC,IAAA,EAAAuC,MAAA;MACA,IAAAtC,KAAA,GAAAI,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAP,IAAA;MACAC,KAAA,QAAAuC,cAAA,CAAAvC,KAAA;MACAsC,MAAA,CAAApC,IAAA,CAAAF,KAAA;MACA,KAAAT,cAAA,CAAAS,KAAA;IACA;IACAuC,cAAA,WAAAA,eAAAxC,IAAA;MAAA,IAAAyC,MAAA;MACAzC,IAAA,CAAAjD,MAAA,UAAAV,QAAA;MACA2D,IAAA,CAAAS,SAAA,QAAAC,IAAA;MACA,IAAAV,IAAA,CAAAW,MAAA;QACAX,IAAA,CAAAY,MAAA,WAAAC,MAAA,MAAAxE,QAAA;MACA,WAAA2D,IAAA,CAAAW,MAAA;QACAX,IAAA,CAAAe,aAAA,SAAAF,MAAA,MAAAxE,QAAA;MACA;MACA,IAAAqG,KAAA,CAAAC,OAAA,CAAA3C,IAAA,CAAA4C,QAAA;QACA5C,IAAA,CAAA4C,QAAA,GAAA5C,IAAA,CAAA4C,QAAA,CAAAC,GAAA,WAAAC,SAAA;UAAA,OAAAL,MAAA,CAAAD,cAAA,CAAAM,SAAA;QAAA;MACA;MACA,OAAA9C,IAAA;IACA;IACA+C,iBAAA,WAAAA,kBAAAC,KAAA,EAAAT,MAAA;MAAA,IAAAU,MAAA;MACAV,MAAA,CAAAW,MAAA,CAAAF,KAAA;MACA,KAAAG,SAAA;QACA,IAAAC,GAAA,GAAAH,MAAA,CAAAtG,WAAA,CAAA0G,MAAA;QACA,IAAAD,GAAA;UACAH,MAAA,CAAAzD,cAAA,CAAAyD,MAAA,CAAAtG,WAAA,CAAAyG,GAAA;QACA;MACA;IACA;IACAtE,YAAA,WAAAA,aAAA;MACA,IAAAI,IAAA,QAAA/B,YAAA,CAAA+B,IAAA;MACA,KAAA+B,gBAAA;MACA,IAAAqC,MAAA,OAAAC,eAAA,MAAAC,YAAA,OAAAvG,QAAA,EAAAiC,IAAA;MACA,IAAAuE,IAAA,OAAAC,iBAAA,MAAAC,gBAAA,OAAA1G,QAAA,EAAAiC,IAAA;MACA,IAAA0E,GAAA,OAAAC,cAAA,MAAAC,cAAA,OAAA7G,QAAA;MACA,OAAA8G,mBAAA,CAAAN,IAAA,CAAAA,IAAA,GAAAH,MAAA,GAAAM,GAAA,EAAAI,qBAAA,CAAAP,IAAA;IACA;IACAQ,QAAA,WAAAA,SAAA;MACA,KAAA/G,aAAA;MACA,KAAAE,YAAA;MACA,KAAAmE,aAAA;IACA;IACA2C,GAAA,WAAAA,IAAA;MACA,KAAAhH,aAAA;MACA,KAAAE,YAAA;MACA,KAAAmE,aAAA;IACA;IACA4C,IAAA,WAAAA,KAAA;MACA,KAAAjH,aAAA;MACA,KAAAE,YAAA;MACA,KAAAmE,aAAA;IACA;IACA6C,SAAA,WAAAA,UAAAC,MAAA;MAAA,IAAAC,MAAA;MACAD,MAAA,QAAAnE,cAAA,CAAAmE,MAAA;MACAA,MAAA,CAAAzD,MAAA,QAAAvD,UAAA,CAAAuD,MAAA;MACAyD,MAAA,CAAAtH,MAAA,QAAAD,QAAA;MACAuH,MAAA,CAAA7D,IAAA,QAAAnD,UAAA,CAAAmD,IAAA;MACA,YAAAnD,UAAA,CAAAc,GAAA;MACA,YAAAd,UAAA,CAAAkH,OAAA;MACA,YAAAlH,UAAA,CAAAE,QAAA;MACAiH,MAAA,CAAAC,IAAA,CAAAJ,MAAA,EAAAK,OAAA,WAAAC,GAAA;QACA,IAAAL,MAAA,CAAAjH,UAAA,CAAAsH,GAAA,MAAAzG,SAAA,IACA,IAAA0G,QAAA,CAAAhJ,OAAA,EAAA0I,MAAA,CAAAjH,UAAA,CAAAsH,GAAA,WAAAC,QAAA,CAAAhJ,OAAA,EAAAyI,MAAA,CAAAM,GAAA;UACAN,MAAA,CAAAM,GAAA,IAAAL,MAAA,CAAAjH,UAAA,CAAAsH,GAAA;QACA;MACA;MACA,KAAAtH,UAAA,GAAAgH,MAAA;MACA,KAAAQ,iBAAA,CAAAR,MAAA,OAAA1H,WAAA;IACA;IACAkI,iBAAA,WAAAA,kBAAAR,MAAA,EAAAS,IAAA;MAAA,IAAAC,MAAA;MACA,IAAA/B,KAAA,GAAA8B,IAAA,CAAAE,SAAA,WAAAhF,IAAA;QAAA,OAAAA,IAAA,CAAAjD,MAAA,KAAAgI,MAAA,CAAAjI,QAAA;MAAA;MACA,IAAAkG,KAAA;QACA8B,IAAA,CAAA5B,MAAA,CAAAF,KAAA,KAAAqB,MAAA;MACA;QACAS,IAAA,CAAAJ,OAAA,WAAA1E,IAAA;UACA,IAAA0C,KAAA,CAAAC,OAAA,CAAA3C,IAAA,CAAA4C,QAAA,GAAAmC,MAAA,CAAAF,iBAAA,CAAAR,MAAA,EAAArE,IAAA,CAAA4C,QAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}