{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/Editor/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/components/Editor/index.vue", "mtime": 1753510684527}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_axios", "_interopRequireDefault", "require", "_quill", "_auth", "name", "props", "value", "type", "String", "default", "height", "Number", "minHeight", "readOnly", "Boolean", "fileSize", "data", "uploadUrl", "process", "env", "VUE_APP_BASE_API", "headers", "Authorization", "getToken", "<PERSON><PERSON><PERSON>", "currentValue", "options", "theme", "bounds", "document", "body", "debug", "modules", "toolbar", "list", "indent", "size", "header", "color", "background", "align", "placeholder", "computed", "styles", "style", "concat", "watch", "handler", "val", "clipboard", "dangerouslyPasteHTML", "immediate", "mounted", "init", "<PERSON><PERSON><PERSON><PERSON>", "methods", "_this", "editor", "$refs", "getModule", "add<PERSON><PERSON><PERSON>", "upload", "$children", "input", "click", "quill", "format", "root", "addEventListener", "handlePasteCapture", "on", "delta", "<PERSON><PERSON><PERSON><PERSON>", "source", "html", "children", "innerHTML", "text", "getText", "$emit", "range", "oldRange", "eventName", "_len", "arguments", "length", "args", "Array", "_key", "apply", "handleBeforeUpload", "file", "isJPG", "includes", "$message", "error", "isLt", "handleUploadSuccess", "res", "code", "getSelection", "index", "insertEmbed", "fileName", "setSelection", "handleUploadError", "e", "clipboardData", "window", "items", "i", "item", "indexOf", "preventDefault", "getAsFile", "insertImage", "_this2", "formData", "FormData", "append", "axios", "post", "then"], "sources": ["src/components/Editor/index.vue"], "sourcesContent": ["<template>\n  <div>\n    <el-upload\n      :action=\"uploadUrl\"\n      :before-upload=\"handleBeforeUpload\"\n      :on-success=\"handleUploadSuccess\"\n      :on-error=\"handleUploadError\"\n      name=\"file\"\n      :show-file-list=\"false\"\n      :headers=\"headers\"\n      style=\"display: none\"\n      ref=\"upload\"\n      v-if=\"this.type == 'url'\"\n    >\n    </el-upload>\n    <div class=\"editor\" ref=\"editor\" :style=\"styles\"></div>\n  </div>\n</template>\n\n<script>\nimport axios from \"axios\"\nimport Quill from \"quill\"\nimport \"quill/dist/quill.core.css\"\nimport \"quill/dist/quill.snow.css\"\nimport \"quill/dist/quill.bubble.css\"\nimport { getToken } from \"@/utils/auth\"\n\nexport default {\n  name: \"Editor\",\n  props: {\n    /* 编辑器的内容 */\n    value: {\n      type: String,\n      default: \"\",\n    },\n    /* 高度 */\n    height: {\n      type: Number,\n      default: null,\n    },\n    /* 最小高度 */\n    minHeight: {\n      type: Number,\n      default: null,\n    },\n    /* 只读 */\n    readOnly: {\n      type: Boolean,\n      default: false,\n    },\n    /* 上传文件大小限制(MB) */\n    fileSize: {\n      type: Number,\n      default: 5,\n    },\n    /* 类型（base64格式、url格式） */\n    type: {\n      type: String,\n      default: \"url\",\n    }\n  },\n  data() {\n    return {\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/common/upload\", // 上传的图片服务器地址\n      headers: {\n        Authorization: \"Bearer \" + getToken()\n      },\n      Quill: null,\n      currentValue: \"\",\n      options: {\n        theme: \"snow\",\n        bounds: document.body,\n        debug: \"warn\",\n        modules: {\n          // 工具栏配置\n          toolbar: [\n            [\"bold\", \"italic\", \"underline\", \"strike\"],       // 加粗 斜体 下划线 删除线\n            [\"blockquote\", \"code-block\"],                    // 引用  代码块\n            [{ list: \"ordered\" }, { list: \"bullet\" }],       // 有序、无序列表\n            [{ indent: \"-1\" }, { indent: \"+1\" }],            // 缩进\n            [{ size: [\"small\", false, \"large\", \"huge\"] }],   // 字体大小\n            [{ header: [1, 2, 3, 4, 5, 6, false] }],         // 标题\n            [{ color: [] }, { background: [] }],             // 字体颜色、字体背景颜色\n            [{ align: [] }],                                 // 对齐方式\n            [\"clean\"],                                       // 清除文本格式\n            [\"link\", \"image\", \"video\"]                       // 链接、图片、视频\n          ],\n        },\n        placeholder: \"请输入内容\",\n        readOnly: this.readOnly,\n      },\n    }\n  },\n  computed: {\n    styles() {\n      let style = {}\n      if (this.minHeight) {\n        style.minHeight = `${this.minHeight}px`\n      }\n      if (this.height) {\n        style.height = `${this.height}px`\n      }\n      return style\n    }\n  },\n  watch: {\n    value: {\n      handler(val) {\n        if (val !== this.currentValue) {\n          this.currentValue = val === null ? \"\" : val\n          if (this.Quill) {\n            this.Quill.clipboard.dangerouslyPasteHTML(this.currentValue)\n          }\n        }\n      },\n      immediate: true,\n    },\n  },\n  mounted() {\n    this.init()\n  },\n  beforeDestroy() {\n    this.Quill = null\n  },\n  methods: {\n    init() {\n      const editor = this.$refs.editor\n      this.Quill = new Quill(editor, this.options)\n      // 如果设置了上传地址则自定义图片上传事件\n      if (this.type == 'url') {\n        let toolbar = this.Quill.getModule(\"toolbar\")\n        toolbar.addHandler(\"image\", (value) => {\n          if (value) {\n            this.$refs.upload.$children[0].$refs.input.click()\n          } else {\n            this.quill.format(\"image\", false)\n          }\n        })\n        this.Quill.root.addEventListener('paste', this.handlePasteCapture, true)\n      }\n      this.Quill.clipboard.dangerouslyPasteHTML(this.currentValue)\n      this.Quill.on(\"text-change\", (delta, oldDelta, source) => {\n        const html = this.$refs.editor.children[0].innerHTML\n        const text = this.Quill.getText()\n        const quill = this.Quill\n        this.currentValue = html\n        this.$emit(\"input\", html)\n        this.$emit(\"on-change\", { html, text, quill })\n      })\n      this.Quill.on(\"text-change\", (delta, oldDelta, source) => {\n        this.$emit(\"on-text-change\", delta, oldDelta, source)\n      })\n      this.Quill.on(\"selection-change\", (range, oldRange, source) => {\n        this.$emit(\"on-selection-change\", range, oldRange, source)\n      })\n      this.Quill.on(\"editor-change\", (eventName, ...args) => {\n        this.$emit(\"on-editor-change\", eventName, ...args)\n      })\n    },\n    // 上传前校检格式和大小\n    handleBeforeUpload(file) {\n      const type = [\"image/jpeg\", \"image/jpg\", \"image/png\", \"image/svg\"]\n      const isJPG = type.includes(file.type)\n      // 检验文件格式\n      if (!isJPG) {\n        this.$message.error(`图片格式错误!`)\n        return false\n      }\n      // 校检文件大小\n      if (this.fileSize) {\n        const isLt = file.size / 1024 / 1024 < this.fileSize\n        if (!isLt) {\n          this.$message.error(`上传文件大小不能超过 ${this.fileSize} MB!`)\n          return false\n        }\n      }\n      return true\n    },\n    handleUploadSuccess(res, file) {\n      // 如果上传成功\n      if (res.code == 200) {\n        // 获取富文本组件实例\n        let quill = this.Quill\n        // 获取光标所在位置\n        let length = quill.getSelection().index\n        // 插入图片  res.url为服务器返回的图片地址\n        quill.insertEmbed(length, \"image\", process.env.VUE_APP_BASE_API + res.fileName)\n        // 调整光标到最后\n        quill.setSelection(length + 1)\n      } else {\n        this.$message.error(\"图片插入失败\")\n      }\n    },\n    handleUploadError() {\n      this.$message.error(\"图片插入失败\")\n    },\n    // 复制粘贴图片处理\n    handlePasteCapture(e) {\n      const clipboard = e.clipboardData || window.clipboardData\n      if (clipboard && clipboard.items) {\n        for (let i = 0; i < clipboard.items.length; i++) {\n          const item = clipboard.items[i]\n          if (item.type.indexOf('image') !== -1) {\n            e.preventDefault()\n            const file = item.getAsFile()\n            this.insertImage(file)\n          }\n        }\n      }\n    },\n    insertImage(file) {\n      const formData = new FormData()\n      formData.append(\"file\", file)\n      axios.post(this.uploadUrl, formData, { headers: { \"Content-Type\": \"multipart/form-data\", Authorization: this.headers.Authorization } }).then(res => {\n        this.handleUploadSuccess(res.data)\n      })\n    }\n  }\n}\n</script>\n\n<style>\n.editor, .ql-toolbar {\n  white-space: pre-wrap !important;\n  line-height: normal !important;\n}\n.quill-img {\n  display: none;\n}\n.ql-snow .ql-tooltip[data-mode=\"link\"]::before {\n  content: \"请输入链接地址:\";\n}\n.ql-snow .ql-tooltip.ql-editing a.ql-action::after {\n  border-right: 0px;\n  content: \"保存\";\n  padding-right: 0px;\n}\n.ql-snow .ql-tooltip[data-mode=\"video\"]::before {\n  content: \"请输入视频地址:\";\n}\n.ql-snow .ql-picker.ql-size .ql-picker-label::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item::before {\n  content: \"14px\";\n}\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"small\"]::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"small\"]::before {\n  content: \"10px\";\n}\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"large\"]::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"large\"]::before {\n  content: \"18px\";\n}\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"huge\"]::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"huge\"]::before {\n  content: \"32px\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item::before {\n  content: \"文本\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"1\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"1\"]::before {\n  content: \"标题1\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"2\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"2\"]::before {\n  content: \"标题2\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"3\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"3\"]::before {\n  content: \"标题3\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"4\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"4\"]::before {\n  content: \"标题4\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"5\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"5\"]::before {\n  content: \"标题5\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"6\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"6\"]::before {\n  content: \"标题6\";\n}\n.ql-snow .ql-picker.ql-font .ql-picker-label::before,\n.ql-snow .ql-picker.ql-font .ql-picker-item::before {\n  content: \"标准字体\";\n}\n.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=\"serif\"]::before,\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=\"serif\"]::before {\n  content: \"衬线字体\";\n}\n.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=\"monospace\"]::before,\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=\"monospace\"]::before {\n  content: \"等宽字体\";\n}\n</style>\n"], "mappings": ";;;;;;;;;;AAoBA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAF,sBAAA,CAAAC,OAAA;AACAA,OAAA;AACAA,OAAA;AACAA,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,KAAA;IACA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACA;IACAC,MAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;IACA;IACAG,SAAA;MACAL,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;IACA;IACAI,QAAA;MACAN,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACA;IACAM,QAAA;MACAR,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;IACA;IACAF,IAAA;MACAA,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAO,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MAAA;MACAC,OAAA;QACAC,aAAA,kBAAAC,cAAA;MACA;MACAC,KAAA;MACAC,YAAA;MACAC,OAAA;QACAC,KAAA;QACAC,MAAA,EAAAC,QAAA,CAAAC,IAAA;QACAC,KAAA;QACAC,OAAA;UACA;UACAC,OAAA,GACA;UAAA;UACA;UAAA;UACA;YAAAC,IAAA;UAAA;YAAAA,IAAA;UAAA;UAAA;UACA;YAAAC,MAAA;UAAA;YAAAA,MAAA;UAAA;UAAA;UACA;YAAAC,IAAA;UAAA;UAAA;UACA;YAAAC,MAAA;UAAA;UAAA;UACA;YAAAC,KAAA;UAAA;YAAAC,UAAA;UAAA;UAAA;UACA;YAAAC,KAAA;UAAA;UAAA;UACA;UAAA;UACA;UAAA;QAEA;QACAC,WAAA;QACA5B,QAAA,OAAAA;MACA;IACA;EACA;EACA6B,QAAA;IACAC,MAAA,WAAAA,OAAA;MACA,IAAAC,KAAA;MACA,SAAAhC,SAAA;QACAgC,KAAA,CAAAhC,SAAA,MAAAiC,MAAA,MAAAjC,SAAA;MACA;MACA,SAAAF,MAAA;QACAkC,KAAA,CAAAlC,MAAA,MAAAmC,MAAA,MAAAnC,MAAA;MACA;MACA,OAAAkC,KAAA;IACA;EACA;EACAE,KAAA;IACAxC,KAAA;MACAyC,OAAA,WAAAA,QAAAC,GAAA;QACA,IAAAA,GAAA,UAAAvB,YAAA;UACA,KAAAA,YAAA,GAAAuB,GAAA,iBAAAA,GAAA;UACA,SAAAxB,KAAA;YACA,KAAAA,KAAA,CAAAyB,SAAA,CAAAC,oBAAA,MAAAzB,YAAA;UACA;QACA;MACA;MACA0B,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA,KAAA9B,KAAA;EACA;EACA+B,OAAA;IACAF,IAAA,WAAAA,KAAA;MAAA,IAAAG,KAAA;MACA,IAAAC,MAAA,QAAAC,KAAA,CAAAD,MAAA;MACA,KAAAjC,KAAA,OAAAA,cAAA,CAAAiC,MAAA,OAAA/B,OAAA;MACA;MACA,SAAAnB,IAAA;QACA,IAAA0B,OAAA,QAAAT,KAAA,CAAAmC,SAAA;QACA1B,OAAA,CAAA2B,UAAA,oBAAAtD,KAAA;UACA,IAAAA,KAAA;YACAkD,KAAA,CAAAE,KAAA,CAAAG,MAAA,CAAAC,SAAA,IAAAJ,KAAA,CAAAK,KAAA,CAAAC,KAAA;UACA;YACAR,KAAA,CAAAS,KAAA,CAAAC,MAAA;UACA;QACA;QACA,KAAA1C,KAAA,CAAA2C,IAAA,CAAAC,gBAAA,eAAAC,kBAAA;MACA;MACA,KAAA7C,KAAA,CAAAyB,SAAA,CAAAC,oBAAA,MAAAzB,YAAA;MACA,KAAAD,KAAA,CAAA8C,EAAA,0BAAAC,KAAA,EAAAC,QAAA,EAAAC,MAAA;QACA,IAAAC,IAAA,GAAAlB,KAAA,CAAAE,KAAA,CAAAD,MAAA,CAAAkB,QAAA,IAAAC,SAAA;QACA,IAAAC,IAAA,GAAArB,KAAA,CAAAhC,KAAA,CAAAsD,OAAA;QACA,IAAAb,KAAA,GAAAT,KAAA,CAAAhC,KAAA;QACAgC,KAAA,CAAA/B,YAAA,GAAAiD,IAAA;QACAlB,KAAA,CAAAuB,KAAA,UAAAL,IAAA;QACAlB,KAAA,CAAAuB,KAAA;UAAAL,IAAA,EAAAA,IAAA;UAAAG,IAAA,EAAAA,IAAA;UAAAZ,KAAA,EAAAA;QAAA;MACA;MACA,KAAAzC,KAAA,CAAA8C,EAAA,0BAAAC,KAAA,EAAAC,QAAA,EAAAC,MAAA;QACAjB,KAAA,CAAAuB,KAAA,mBAAAR,KAAA,EAAAC,QAAA,EAAAC,MAAA;MACA;MACA,KAAAjD,KAAA,CAAA8C,EAAA,+BAAAU,KAAA,EAAAC,QAAA,EAAAR,MAAA;QACAjB,KAAA,CAAAuB,KAAA,wBAAAC,KAAA,EAAAC,QAAA,EAAAR,MAAA;MACA;MACA,KAAAjD,KAAA,CAAA8C,EAAA,4BAAAY,SAAA;QAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;UAAAF,IAAA,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;QAAA;QACAhC,KAAA,CAAAuB,KAAA,CAAAU,KAAA,CAAAjC,KAAA,uBAAA0B,SAAA,EAAArC,MAAA,CAAAyC,IAAA;MACA;IACA;IACA;IACAI,kBAAA,WAAAA,mBAAAC,IAAA;MACA,IAAApF,IAAA;MACA,IAAAqF,KAAA,GAAArF,IAAA,CAAAsF,QAAA,CAAAF,IAAA,CAAApF,IAAA;MACA;MACA,KAAAqF,KAAA;QACA,KAAAE,QAAA,CAAAC,KAAA;QACA;MACA;MACA;MACA,SAAAhF,QAAA;QACA,IAAAiF,IAAA,GAAAL,IAAA,CAAAvD,IAAA,sBAAArB,QAAA;QACA,KAAAiF,IAAA;UACA,KAAAF,QAAA,CAAAC,KAAA,iEAAAlD,MAAA,MAAA9B,QAAA;UACA;QACA;MACA;MACA;IACA;IACAkF,mBAAA,WAAAA,oBAAAC,GAAA,EAAAP,IAAA;MACA;MACA,IAAAO,GAAA,CAAAC,IAAA;QACA;QACA,IAAAlC,KAAA,QAAAzC,KAAA;QACA;QACA,IAAA6D,MAAA,GAAApB,KAAA,CAAAmC,YAAA,GAAAC,KAAA;QACA;QACApC,KAAA,CAAAqC,WAAA,CAAAjB,MAAA,WAAAnE,OAAA,CAAAC,GAAA,CAAAC,gBAAA,GAAA8E,GAAA,CAAAK,QAAA;QACA;QACAtC,KAAA,CAAAuC,YAAA,CAAAnB,MAAA;MACA;QACA,KAAAS,QAAA,CAAAC,KAAA;MACA;IACA;IACAU,iBAAA,WAAAA,kBAAA;MACA,KAAAX,QAAA,CAAAC,KAAA;IACA;IACA;IACA1B,kBAAA,WAAAA,mBAAAqC,CAAA;MACA,IAAAzD,SAAA,GAAAyD,CAAA,CAAAC,aAAA,IAAAC,MAAA,CAAAD,aAAA;MACA,IAAA1D,SAAA,IAAAA,SAAA,CAAA4D,KAAA;QACA,SAAAC,CAAA,MAAAA,CAAA,GAAA7D,SAAA,CAAA4D,KAAA,CAAAxB,MAAA,EAAAyB,CAAA;UACA,IAAAC,IAAA,GAAA9D,SAAA,CAAA4D,KAAA,CAAAC,CAAA;UACA,IAAAC,IAAA,CAAAxG,IAAA,CAAAyG,OAAA;YACAN,CAAA,CAAAO,cAAA;YACA,IAAAtB,IAAA,GAAAoB,IAAA,CAAAG,SAAA;YACA,KAAAC,WAAA,CAAAxB,IAAA;UACA;QACA;MACA;IACA;IACAwB,WAAA,WAAAA,YAAAxB,IAAA;MAAA,IAAAyB,MAAA;MACA,IAAAC,QAAA,OAAAC,QAAA;MACAD,QAAA,CAAAE,MAAA,SAAA5B,IAAA;MACA6B,cAAA,CAAAC,IAAA,MAAAxG,SAAA,EAAAoG,QAAA;QAAAhG,OAAA;UAAA;UAAAC,aAAA,OAAAD,OAAA,CAAAC;QAAA;MAAA,GAAAoG,IAAA,WAAAxB,GAAA;QACAkB,MAAA,CAAAnB,mBAAA,CAAAC,GAAA,CAAAlF,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}