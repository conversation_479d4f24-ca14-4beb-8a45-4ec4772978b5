{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/system/user.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/system/user.js", "mtime": 1753510684517}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "_ruoyi", "listUser", "query", "request", "url", "method", "params", "getUser", "userId", "parseStrEmpty", "addUser", "data", "updateUser", "<PERSON><PERSON><PERSON>", "resetUserPwd", "password", "changeUserStatus", "status", "getUserProfile", "updateUserProfile", "updateUserPwd", "oldPassword", "newPassword", "uploadAvatar", "headers", "getAuthRole", "updateAuthRole", "deptTreeSelect"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/system/user.js"], "sourcesContent": ["import request from '@/utils/request'\nimport { parseStrEmpty } from \"@/utils/ruoyi\";\n\n// 查询用户列表\nexport function listUser(query) {\n  return request({\n    url: '/system/user/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询用户详细\nexport function getUser(userId) {\n  return request({\n    url: '/system/user/' + parseStrEmpty(userId),\n    method: 'get'\n  })\n}\n\n// 新增用户\nexport function addUser(data) {\n  return request({\n    url: '/system/user',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改用户\nexport function updateUser(data) {\n  return request({\n    url: '/system/user',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除用户\nexport function delUser(userId) {\n  return request({\n    url: '/system/user/' + userId,\n    method: 'delete'\n  })\n}\n\n// 用户密码重置\nexport function resetUserPwd(userId, password) {\n  const data = {\n    userId,\n    password\n  }\n  return request({\n    url: '/system/user/resetPwd',\n    method: 'put',\n    data: data\n  })\n}\n\n// 用户状态修改\nexport function changeUserStatus(userId, status) {\n  const data = {\n    userId,\n    status\n  }\n  return request({\n    url: '/system/user/changeStatus',\n    method: 'put',\n    data: data\n  })\n}\n\n// 查询用户个人信息\nexport function getUserProfile() {\n  return request({\n    url: '/system/user/profile',\n    method: 'get'\n  })\n}\n\n// 修改用户个人信息\nexport function updateUserProfile(data) {\n  return request({\n    url: '/system/user/profile',\n    method: 'put',\n    data: data\n  })\n}\n\n// 用户密码重置\nexport function updateUserPwd(oldPassword, newPassword) {\n  const data = {\n    oldPassword,\n    newPassword\n  }\n  return request({\n    url: '/system/user/profile/updatePwd',\n    method: 'put',\n    data: data\n  })\n}\n\n// 用户头像上传\nexport function uploadAvatar(data) {\n  return request({\n    url: '/system/user/profile/avatar',\n    method: 'post',\n    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },\n    data: data\n  })\n}\n\n// 查询授权角色\nexport function getAuthRole(userId) {\n  return request({\n    url: '/system/user/authRole/' + userId,\n    method: 'get'\n  })\n}\n\n// 保存授权角色\nexport function updateAuthRole(data) {\n  return request({\n    url: '/system/user/authRole',\n    method: 'put',\n    params: data\n  })\n}\n\n// 查询部门下拉树结构\nexport function deptTreeSelect() {\n  return request({\n    url: '/system/user/deptTree',\n    method: 'get'\n  })\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AAEA;AACO,SAASE,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,OAAOA,CAACC,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAG,IAAAK,oBAAa,EAACD,MAAM,CAAC;IAC5CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,OAAOA,CAACL,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,YAAYA,CAACN,MAAM,EAAEO,QAAQ,EAAE;EAC7C,IAAMJ,IAAI,GAAG;IACXH,MAAM,EAANA,MAAM;IACNO,QAAQ,EAARA;EACF,CAAC;EACD,OAAO,IAAAZ,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,gBAAgBA,CAACR,MAAM,EAAES,MAAM,EAAE;EAC/C,IAAMN,IAAI,GAAG;IACXH,MAAM,EAANA,MAAM;IACNS,MAAM,EAANA;EACF,CAAC;EACD,OAAO,IAAAd,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASO,cAAcA,CAAA,EAAG;EAC/B,OAAO,IAAAf,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASc,iBAAiBA,CAACR,IAAI,EAAE;EACtC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,aAAaA,CAACC,WAAW,EAAEC,WAAW,EAAE;EACtD,IAAMX,IAAI,GAAG;IACXU,WAAW,EAAXA,WAAW;IACXC,WAAW,EAAXA;EACF,CAAC;EACD,OAAO,IAAAnB,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASY,YAAYA,CAACZ,IAAI,EAAE;EACjC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,MAAM;IACdmB,OAAO,EAAE;MAAE,cAAc,EAAE;IAAoC,CAAC;IAChEb,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASc,WAAWA,CAACjB,MAAM,EAAE;EAClC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB,GAAGI,MAAM;IACtCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASqB,cAAcA,CAACf,IAAI,EAAE;EACnC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEK;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASgB,cAAcA,CAAA,EAAG;EAC/B,OAAO,IAAAxB,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}