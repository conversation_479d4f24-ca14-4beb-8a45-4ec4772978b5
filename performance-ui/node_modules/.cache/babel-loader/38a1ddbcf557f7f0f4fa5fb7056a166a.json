{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/utils/jsencrypt.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/utils/jsencrypt.js", "mtime": 1753510684532}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_jsencrypt", "_interopRequireDefault", "require", "public<PERSON>ey", "privateKey", "encrypt", "txt", "encryptor", "JSEncrypt", "setPublicKey", "decrypt", "setPrivateKey"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/utils/jsencrypt.js"], "sourcesContent": ["import JSEncrypt from 'jsencrypt/bin/jsencrypt.min'\n\n// 密钥对生成 http://web.chacuo.net/netrsakeypair\n\nconst publicKey = 'MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdH\\n' +\n  'nzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ=='\n\nconst privateKey = 'MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAqhHyZfSsYourNxaY\\n' +\n  '7Nt+PrgrxkiA50efORdI5U5lsW79MmFnusUA355oaSXcLhu5xxB38SMSyP2KvuKN\\n' +\n  'PuH3owIDAQABAkAfoiLyL+Z4lf4Myxk6xUDgLaWGximj20CUf+5BKKnlrK+Ed8gA\\n' +\n  'kM0HqoTt2UZwA5E2MzS4EI2gjfQhz5X28uqxAiEA3wNFxfrCZlSZHb0gn2zDpWow\\n' +\n  'cSxQAgiCstxGUoOqlW8CIQDDOerGKH5OmCJ4Z21v+F25WaHYPxCFMvwxpcw99Ecv\\n' +\n  'DQIgIdhDTIqD2jfYjPTY8Jj3EDGPbH2HHuffvflECt3Ek60CIQCFRlCkHpi7hthh\\n' +\n  'YhovyloRYsM+IS9h/0BzlEAuO0ktMQIgSPT3aFAgJYwKpqRYKlLDVcflZFCKY7u3\\n' +\n  'UP8iWi1Qw0Y='\n\n// 加密\nexport function encrypt(txt) {\n  const encryptor = new JSEncrypt()\n  encryptor.setPublicKey(publicKey) // 设置公钥\n  return encryptor.encrypt(txt) // 对数据进行加密\n}\n\n// 解密\nexport function decrypt(txt) {\n  const encryptor = new JSEncrypt()\n  encryptor.setPrivateKey(privateKey) // 设置私钥\n  return encryptor.decrypt(txt) // 对数据进行解密\n}\n\n"], "mappings": ";;;;;;;;AAAA,IAAAA,UAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;;AAEA,IAAMC,SAAS,GAAG,oEAAoE,GACpF,kEAAkE;AAEpE,IAAMC,UAAU,GAAG,oEAAoE,GACrF,oEAAoE,GACpE,oEAAoE,GACpE,oEAAoE,GACpE,oEAAoE,GACpE,oEAAoE,GACpE,oEAAoE,GACpE,cAAc;;AAEhB;AACO,SAASC,OAAOA,CAACC,GAAG,EAAE;EAC3B,IAAMC,SAAS,GAAG,IAAIC,kBAAS,CAAC,CAAC;EACjCD,SAAS,CAACE,YAAY,CAACN,SAAS,CAAC,EAAC;EAClC,OAAOI,SAAS,CAACF,OAAO,CAACC,GAAG,CAAC,EAAC;AAChC;;AAEA;AACO,SAASI,OAAOA,CAACJ,GAAG,EAAE;EAC3B,IAAMC,SAAS,GAAG,IAAIC,kBAAS,CAAC,CAAC;EACjCD,SAAS,CAACI,aAAa,CAACP,UAAU,CAAC,EAAC;EACpC,OAAOG,SAAS,CAACG,OAAO,CAACJ,GAAG,CAAC,EAAC;AAChC", "ignoreList": []}]}