{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/execute/quarter/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/execute/quarter/index.vue", "mtime": 1753510684533}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_quarterly", "require", "_auth", "data", "tableData", "total", "pageNum", "pageSize", "dialogVisible", "dialogTitle", "form", "selectedIds", "uploadUrl", "process", "env", "VUE_APP_BASE_API", "uploadExcelUrl", "uploadHeaders", "Authorization", "getToken", "created", "loadData", "methods", "_this", "listQuarterly", "then", "res", "code", "rows", "handleAdd", "handleEdit", "row", "Object", "assign", "handleDelete", "_this2", "$confirm", "type", "delQuarterly", "id", "$message", "success", "submitForm", "_this3", "updateQuarterly", "addQuarterly", "handleSelectionChange", "val", "map", "item", "handleImportSuccess", "msg", "error", "handleImportError", "handleExport", "length", "warning", "exportQuarterly", "blob", "Blob", "url", "window", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleDownloadTemplate", "downloadTemplateQuarterly", "handleDownloadExcelTemplate", "downloadExcelTemplateQuarterly"], "sources": ["src/views/execute/quarter/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"20\" style=\"margin-bottom: 20px;\">\n      <el-col :span=\"12\">\n        <el-button type=\"primary\" @click=\"handleAdd\">新增</el-button>\n        <el-upload\n          class=\"upload-demo\"\n          :action=\"uploadExcelUrl\"\n          :headers=\"uploadHeaders\"\n          :show-file-list=\"false\"\n          :on-success=\"handleImportSuccess\"\n          :on-error=\"handleImportError\"\n          accept=\".xls,.xlsx\"\n          style=\"display:inline-block;margin-left:10px;\"\n        >\n          <el-button type=\"success\">导入Excel</el-button>\n        </el-upload>\n        <el-button @click=\"handleExport\" style=\"margin-left:10px;\">导出Word</el-button>\n        <el-button @click=\"handleDownloadExcelTemplate\" style=\"margin-left:10px;\">下载Excel模板</el-button>\n      </el-col>\n    </el-row>\n    <el-table :data=\"tableData\" border style=\"width: 100%\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" />\n      <el-table-column prop=\"id\" label=\"序号\" width=\"60\" />\n      <el-table-column prop=\"deptName\" label=\"科室\" />\n      <el-table-column prop=\"position\" label=\"职务\" />\n      <el-table-column prop=\"category\" label=\"分类\" />\n      <el-table-column prop=\"userName\" label=\"姓名\" />\n      <el-table-column prop=\"planScore\" label=\"计划分值\" />\n      <el-table-column prop=\"selfScore\" label=\"自评分值\" />\n      <el-table-column prop=\"sectionLeaderComment\" label=\"科长点评\" />\n      <el-table-column prop=\"divisionLeaderComment\" label=\"分管领导点评\" />\n      <el-table-column prop=\"groupLeaderScore\" label=\"领导小组评鉴\" />\n      <el-table-column prop=\"taskSubtotalScore\" label=\"任务小计得分\" />\n      <el-table-column prop=\"incentiveIndex\" label=\"激励指标\" />\n      <el-table-column prop=\"constraintIndex\" label=\"约束指标\" />\n      <el-table-column prop=\"quarterScore\" label=\"季度得分\" />\n      <el-table-column prop=\"sameLevelOrder\" label=\"同层级排序\" />\n      <el-table-column prop=\"grade\" label=\"等次\" />\n      <el-table-column prop=\"remark\" label=\"备注\" />\n      <el-table-column prop=\"year\" label=\"年\" width=\"80\" />\n      <el-table-column prop=\"quarter\" label=\"季度\" width=\"60\" />\n      <el-table-column label=\"操作\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <el-button size=\"mini\" @click=\"handleEdit(scope.row)\">编辑</el-button>\n          <el-button size=\"mini\" type=\"danger\" @click=\"handleDelete(scope.row)\">删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <el-pagination\n      style=\"margin-top: 20px;\"\n      background\n      layout=\"prev, pager, next, jumper\"\n      :total=\"total\"\n      :page-size=\"pageSize\"\n      :current-page.sync=\"pageNum\"\n      @current-change=\"loadData\"\n    />\n    <el-dialog :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\n      <el-form :model=\"form\" label-width=\"100px\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\"><el-form-item label=\"科室\"><el-input v-model=\"form.deptName\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"职务\"><el-input v-model=\"form.position\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"分类\"><el-input v-model=\"form.category\" /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\"><el-form-item label=\"姓名\"><el-input v-model=\"form.userName\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"计划分值\"><el-input v-model=\"form.planScore\" type=\"number\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"自评分值\"><el-input v-model=\"form.selfScore\" type=\"number\" /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\"><el-form-item label=\"科长点评\"><el-input v-model=\"form.sectionLeaderComment\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"分管领导点评\"><el-input v-model=\"form.divisionLeaderComment\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"领导小组评鉴\"><el-input v-model=\"form.groupLeaderScore\" type=\"number\" /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\"><el-form-item label=\"任务小计得分\"><el-input v-model=\"form.taskSubtotalScore\" type=\"number\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"激励指标\"><el-input v-model=\"form.incentiveIndex\" type=\"number\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"约束指标\"><el-input v-model=\"form.constraintIndex\" type=\"number\" /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\"><el-form-item label=\"季度得分\"><el-input v-model=\"form.quarterScore\" type=\"number\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"同层级排序\"><el-input v-model=\"form.sameLevelOrder\" type=\"number\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"等次\"><el-input v-model=\"form.grade\" /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\"><el-form-item label=\"备注\"><el-input v-model=\"form.remark\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"年\"><el-input v-model=\"form.year\" type=\"number\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"季度\"><el-input v-model=\"form.quarter\" type=\"number\" /></el-form-item></el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"submitForm\">确定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  listQuarterly,\n  addQuarterly,\n  updateQuarterly,\n  delQuarterly,\n  exportQuarterly,\n  downloadTemplateQuarterly,\n  downloadExcelTemplateQuarterly\n} from '@/api/performance/quarterly'\nimport { getToken } from '@/utils/auth'\n\nexport default {\n  data() {\n    return {\n      tableData: [],\n      total: 0,\n      pageNum: 1,\n      pageSize: 10,\n      dialogVisible: false,\n      dialogTitle: '新增',\n      form: {},\n      selectedIds: [],\n      uploadUrl: process.env.VUE_APP_BASE_API + '/performance/quarterly/importData',\n      uploadExcelUrl: process.env.VUE_APP_BASE_API + '/performance/quarterly/importExcel',\n      uploadHeaders: {\n        Authorization: 'Bearer ' + getToken()\n      }\n    }\n  },\n  created() {\n    this.loadData()\n  },\n  methods: {\n    loadData() {\n      listQuarterly({ pageNum: this.pageNum, pageSize: this.pageSize }).then(res => {\n        if (res.code === 200) {\n          this.tableData = res.rows\n          this.total = res.total\n        }\n      })\n    },\n    handleAdd() {\n      this.dialogTitle = '新增'\n      this.form = {}\n      this.dialogVisible = true\n    },\n    handleEdit(row) {\n      this.dialogTitle = '编辑'\n      this.form = Object.assign({}, row)\n      this.dialogVisible = true\n    },\n    handleDelete(row) {\n      this.$confirm('确定删除该条记录吗？', '提示', { type: 'warning' }).then(() => {\n        delQuarterly(row.id).then(() => {\n          this.$message.success('删除成功')\n          this.loadData()\n        })\n      })\n    },\n    submitForm() {\n      if (this.form.id) {\n        updateQuarterly(this.form).then(() => {\n          this.$message.success('修改成功')\n          this.dialogVisible = false\n          this.loadData()\n        })\n      } else {\n        addQuarterly(this.form).then(() => {\n          this.$message.success('新增成功')\n          this.dialogVisible = false\n          this.loadData()\n        })\n      }\n    },\n    handleSelectionChange(val) {\n      this.selectedIds = val.map(item => item.id)\n    },\n    handleImportSuccess(res) {\n      if (res.code === 200) {\n        this.$message.success(res.msg || '导入成功')\n        this.loadData()\n      } else {\n        this.$message.error(res.msg || '导入失败')\n      }\n    },\n    handleImportError() {\n      this.$message.error('导入失败，请检查文件或网络')\n    },\n    handleExport() {\n      if (this.selectedIds.length === 0) {\n        this.$message.warning('请先选择要导出的数据')\n        return\n      }\n      exportQuarterly(this.selectedIds).then(data => {\n        const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })\n        const url = window.URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.href = url\n        a.download = '季度总结导出.docx'\n        document.body.appendChild(a)\n        a.click()\n        document.body.removeChild(a)\n        window.URL.revokeObjectURL(url)\n      })\n    },\n    handleDownloadTemplate() {\n      downloadTemplateQuarterly().then(data => {\n        const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })\n        const url = window.URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.href = url\n        a.download = '季度总结导入模板.docx'\n        document.body.appendChild(a)\n        a.click()\n        document.body.removeChild(a)\n        window.URL.revokeObjectURL(url)\n      })\n    },\n    handleDownloadExcelTemplate() {\n      downloadExcelTemplateQuarterly().then(data => {\n        const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })\n        const url = window.URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.href = url\n        a.download = '季度总结Excel导入模板.xlsx'\n        document.body.appendChild(a)\n        a.click()\n        document.body.removeChild(a)\n        window.URL.revokeObjectURL(url)\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.app-container {\n  padding: 20px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAoGA,IAAAA,UAAA,GAAAC,OAAA;AASA,IAAAC,KAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACAC,KAAA;MACAC,OAAA;MACAC,QAAA;MACAC,aAAA;MACAC,WAAA;MACAC,IAAA;MACAC,WAAA;MACAC,SAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACAC,cAAA,EAAAH,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACAE,aAAA;QACAC,aAAA,kBAAAC,cAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,QAAA;EACA;EACAC,OAAA;IACAD,QAAA,WAAAA,SAAA;MAAA,IAAAE,KAAA;MACA,IAAAC,wBAAA;QAAAlB,OAAA,OAAAA,OAAA;QAAAC,QAAA,OAAAA;MAAA,GAAAkB,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAJ,KAAA,CAAAnB,SAAA,GAAAsB,GAAA,CAAAE,IAAA;UACAL,KAAA,CAAAlB,KAAA,GAAAqB,GAAA,CAAArB,KAAA;QACA;MACA;IACA;IACAwB,SAAA,WAAAA,UAAA;MACA,KAAApB,WAAA;MACA,KAAAC,IAAA;MACA,KAAAF,aAAA;IACA;IACAsB,UAAA,WAAAA,WAAAC,GAAA;MACA,KAAAtB,WAAA;MACA,KAAAC,IAAA,GAAAsB,MAAA,CAAAC,MAAA,KAAAF,GAAA;MACA,KAAAvB,aAAA;IACA;IACA0B,YAAA,WAAAA,aAAAH,GAAA;MAAA,IAAAI,MAAA;MACA,KAAAC,QAAA;QAAAC,IAAA;MAAA,GAAAZ,IAAA;QACA,IAAAa,uBAAA,EAAAP,GAAA,CAAAQ,EAAA,EAAAd,IAAA;UACAU,MAAA,CAAAK,QAAA,CAAAC,OAAA;UACAN,MAAA,CAAAd,QAAA;QACA;MACA;IACA;IACAqB,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,SAAAjC,IAAA,CAAA6B,EAAA;QACA,IAAAK,0BAAA,OAAAlC,IAAA,EAAAe,IAAA;UACAkB,MAAA,CAAAH,QAAA,CAAAC,OAAA;UACAE,MAAA,CAAAnC,aAAA;UACAmC,MAAA,CAAAtB,QAAA;QACA;MACA;QACA,IAAAwB,uBAAA,OAAAnC,IAAA,EAAAe,IAAA;UACAkB,MAAA,CAAAH,QAAA,CAAAC,OAAA;UACAE,MAAA,CAAAnC,aAAA;UACAmC,MAAA,CAAAtB,QAAA;QACA;MACA;IACA;IACAyB,qBAAA,WAAAA,sBAAAC,GAAA;MACA,KAAApC,WAAA,GAAAoC,GAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAV,EAAA;MAAA;IACA;IACAW,mBAAA,WAAAA,oBAAAxB,GAAA;MACA,IAAAA,GAAA,CAAAC,IAAA;QACA,KAAAa,QAAA,CAAAC,OAAA,CAAAf,GAAA,CAAAyB,GAAA;QACA,KAAA9B,QAAA;MACA;QACA,KAAAmB,QAAA,CAAAY,KAAA,CAAA1B,GAAA,CAAAyB,GAAA;MACA;IACA;IACAE,iBAAA,WAAAA,kBAAA;MACA,KAAAb,QAAA,CAAAY,KAAA;IACA;IACAE,YAAA,WAAAA,aAAA;MACA,SAAA3C,WAAA,CAAA4C,MAAA;QACA,KAAAf,QAAA,CAAAgB,OAAA;QACA;MACA;MACA,IAAAC,0BAAA,OAAA9C,WAAA,EAAAc,IAAA,WAAAtB,IAAA;QACA,IAAAuD,IAAA,OAAAC,IAAA,EAAAxD,IAAA;UAAAkC,IAAA;QAAA;QACA,IAAAuB,GAAA,GAAAC,MAAA,CAAAC,GAAA,CAAAC,eAAA,CAAAL,IAAA;QACA,IAAAM,CAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,CAAA,CAAAG,IAAA,GAAAP,GAAA;QACAI,CAAA,CAAAI,QAAA;QACAH,QAAA,CAAAI,IAAA,CAAAC,WAAA,CAAAN,CAAA;QACAA,CAAA,CAAAO,KAAA;QACAN,QAAA,CAAAI,IAAA,CAAAG,WAAA,CAAAR,CAAA;QACAH,MAAA,CAAAC,GAAA,CAAAW,eAAA,CAAAb,GAAA;MACA;IACA;IACAc,sBAAA,WAAAA,uBAAA;MACA,IAAAC,oCAAA,IAAAlD,IAAA,WAAAtB,IAAA;QACA,IAAAuD,IAAA,OAAAC,IAAA,EAAAxD,IAAA;UAAAkC,IAAA;QAAA;QACA,IAAAuB,GAAA,GAAAC,MAAA,CAAAC,GAAA,CAAAC,eAAA,CAAAL,IAAA;QACA,IAAAM,CAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,CAAA,CAAAG,IAAA,GAAAP,GAAA;QACAI,CAAA,CAAAI,QAAA;QACAH,QAAA,CAAAI,IAAA,CAAAC,WAAA,CAAAN,CAAA;QACAA,CAAA,CAAAO,KAAA;QACAN,QAAA,CAAAI,IAAA,CAAAG,WAAA,CAAAR,CAAA;QACAH,MAAA,CAAAC,GAAA,CAAAW,eAAA,CAAAb,GAAA;MACA;IACA;IACAgB,2BAAA,WAAAA,4BAAA;MACA,IAAAC,yCAAA,IAAApD,IAAA,WAAAtB,IAAA;QACA,IAAAuD,IAAA,OAAAC,IAAA,EAAAxD,IAAA;UAAAkC,IAAA;QAAA;QACA,IAAAuB,GAAA,GAAAC,MAAA,CAAAC,GAAA,CAAAC,eAAA,CAAAL,IAAA;QACA,IAAAM,CAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,CAAA,CAAAG,IAAA,GAAAP,GAAA;QACAI,CAAA,CAAAI,QAAA;QACAH,QAAA,CAAAI,IAAA,CAAAC,WAAA,CAAAN,CAAA;QACAA,CAAA,CAAAO,KAAA;QACAN,QAAA,CAAAI,IAAA,CAAAG,WAAA,CAAAR,CAAA;QACAH,MAAA,CAAAC,GAAA,CAAAW,eAAA,CAAAb,GAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}