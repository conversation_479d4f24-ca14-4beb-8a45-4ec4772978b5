{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/formats/indent.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/formats/indent.js", "mtime": 1753510684093}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_parchment", "require", "IndentAttributor", "_ClassAttributor", "_classCallCheck2", "default", "_callSuper2", "arguments", "_inherits2", "_createClass2", "key", "value", "add", "node", "normalizedValue", "indent", "remove", "_superPropGet2", "toString", "canAdd", "parseInt", "undefined", "ClassAttributor", "IndentClass", "scope", "<PERSON><PERSON>", "BLOCK", "whitelist", "_default", "exports"], "sources": ["../../src/formats/indent.ts"], "sourcesContent": ["import { ClassAttributor, Scope } from 'parchment';\n\nclass IndentAttributor extends ClassAttributor {\n  add(node: HTMLElement, value: string | number) {\n    let normalizedValue = 0;\n    if (value === '+1' || value === '-1') {\n      const indent = this.value(node) || 0;\n      normalizedValue = value === '+1' ? indent + 1 : indent - 1;\n    } else if (typeof value === 'number') {\n      normalizedValue = value;\n    }\n    if (normalizedValue === 0) {\n      this.remove(node);\n      return true;\n    }\n    return super.add(node, normalizedValue.toString());\n  }\n\n  canAdd(node: HTMLElement, value: string) {\n    return super.canAdd(node, value) || super.canAdd(node, parseInt(value, 10));\n  }\n\n  value(node: HTMLElement) {\n    return parseInt(super.value(node), 10) || undefined; // Don't return NaN\n  }\n}\n\nconst IndentClass = new IndentAttributor('indent', 'ql-indent', {\n  scope: Scope.BLOCK,\n  // @ts-expect-error\n  whitelist: [1, 2, 3, 4, 5, 6, 7, 8],\n});\n\nexport default IndentClass;\n"], "mappings": ";;;;;;;;;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAAkD,IAE5CC,gBAAgB,0BAAAC,gBAAA;EAAA,SAAAD,iBAAA;IAAA,IAAAE,gBAAA,CAAAC,OAAA,QAAAH,gBAAA;IAAA,WAAAI,WAAA,CAAAD,OAAA,QAAAH,gBAAA,EAAAK,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAH,gBAAA,EAAAC,gBAAA;EAAA,WAAAM,aAAA,CAAAJ,OAAA,EAAAH,gBAAA;IAAAQ,GAAA;IAAAC,KAAA,EACpB,SAAAC,GAAGA,CAACC,IAAiB,EAAEF,KAAsB,EAAE;MAC7C,IAAIG,eAAe,GAAG,CAAC;MACvB,IAAIH,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,IAAI,EAAE;QACpC,IAAMI,MAAM,GAAG,IAAI,CAACJ,KAAK,CAACE,IAAI,CAAC,IAAI,CAAC;QACpCC,eAAe,GAAGH,KAAK,KAAK,IAAI,GAAGI,MAAM,GAAG,CAAC,GAAGA,MAAM,GAAG,CAAC;MAC5D,CAAC,MAAM,IAAI,OAAOJ,KAAK,KAAK,QAAQ,EAAE;QACpCG,eAAe,GAAGH,KAAK;MACzB;MACA,IAAIG,eAAe,KAAK,CAAC,EAAE;QACzB,IAAI,CAACE,MAAM,CAACH,IAAI,CAAC;QACjB,OAAO,IAAI;MACb;MACA,WAAAI,cAAA,CAAAZ,OAAA,EAAAH,gBAAA,mBAAiBW,IAAI,EAAEC,eAAe,CAACI,QAAQ,CAAC,CAAC;IACnD;EAAA;IAAAR,GAAA;IAAAC,KAAA,EAEA,SAAAQ,MAAMA,CAACN,IAAiB,EAAEF,KAAa,EAAE;MACvC,OAAO,IAAAM,cAAA,CAAAZ,OAAA,EAAAH,gBAAA,sBAAaW,IAAI,EAAEF,KAAK,UAAAM,cAAA,CAAAZ,OAAA,EAAAH,gBAAA,sBAAkBW,IAAI,EAAEO,QAAQ,CAACT,KAAK,EAAE,EAAE,CAAC,EAAC;IAC7E;EAAA;IAAAD,GAAA;IAAAC,KAAA,EAEA,SAAAA,KAAKA,CAACE,IAAiB,EAAE;MACvB,OAAOO,QAAQ,KAAAH,cAAA,CAAAZ,OAAA,EAAAH,gBAAA,qBAAaW,IAAI,IAAG,EAAE,CAAC,IAAIQ,SAAS,CAAC,CAAC;IACvD;EAAA;AAAA,EAtB6BC,0BAAe;AAyB9C,IAAMC,WAAW,GAAG,IAAIrB,gBAAgB,CAAC,QAAQ,EAAE,WAAW,EAAE;EAC9DsB,KAAK,EAAEC,gBAAK,CAACC,KAAK;EAClB;EACAC,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACpC,CAAC,CAAC;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAxB,OAAA,GAEakB,WAAW", "ignoreList": []}]}