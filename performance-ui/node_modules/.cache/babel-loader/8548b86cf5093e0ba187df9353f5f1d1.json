{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/monitor/server/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/monitor/server/index.vue", "mtime": 1753510684534}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfc2VydmVyID0gcmVxdWlyZSgiQC9hcGkvbW9uaXRvci9zZXJ2ZXIiKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG5hbWU6ICJTZXJ2ZXIiLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDmnI3liqHlmajkv6Hmga8KICAgICAgc2VydmVyOiBbXQogICAgfTsKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKTsKICAgIHRoaXMub3BlbkxvYWRpbmcoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDmn6Xor6LmnI3liqHlmajkv6Hmga8gKi9nZXRMaXN0OiBmdW5jdGlvbiBnZXRMaXN0KCkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgICAoMCwgX3NlcnZlci5nZXRTZXJ2ZXIpKCkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpcy5zZXJ2ZXIgPSByZXNwb25zZS5kYXRhOwogICAgICAgIF90aGlzLiRtb2RhbC5jbG9zZUxvYWRpbmcoKTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5omT5byA5Yqg6L295bGCCiAgICBvcGVuTG9hZGluZzogZnVuY3Rpb24gb3BlbkxvYWRpbmcoKSB7CiAgICAgIHRoaXMuJG1vZGFsLmxvYWRpbmcoIuato+WcqOWKoOi9veacjeWKoeebkeaOp+aVsOaNru+8jOivt+eojeWAme+8gSIpOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["_server", "require", "name", "data", "server", "created", "getList", "openLoading", "methods", "_this", "getServer", "then", "response", "$modal", "closeLoading", "loading"], "sources": ["src/views/monitor/server/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"10\">\n      <el-col :span=\"12\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\"><span><i class=\"el-icon-cpu\"></i> CPU</span></div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <table cellspacing=\"0\" style=\"width: 100%;\">\n              <thead>\n                <tr>\n                  <th class=\"el-table__cell is-leaf\"><div class=\"cell\">属性</div></th>\n                  <th class=\"el-table__cell is-leaf\"><div class=\"cell\">值</div></th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">核心数</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.cpu\">{{ server.cpu.cpuNum }}</div></td>\n                </tr>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">用户使用率</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.cpu\">{{ server.cpu.used }}%</div></td>\n                </tr>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">系统使用率</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.cpu\">{{ server.cpu.sys }}%</div></td>\n                </tr>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">当前空闲率</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.cpu\">{{ server.cpu.free }}%</div></td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"12\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\"><span><i class=\"el-icon-tickets\"></i> 内存</span></div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <table cellspacing=\"0\" style=\"width: 100%;\">\n              <thead>\n                <tr>\n                  <th class=\"el-table__cell is-leaf\"><div class=\"cell\">属性</div></th>\n                  <th class=\"el-table__cell is-leaf\"><div class=\"cell\">内存</div></th>\n                  <th class=\"el-table__cell is-leaf\"><div class=\"cell\">JVM</div></th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">总内存</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.mem\">{{ server.mem.total }}G</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.jvm\">{{ server.jvm.total }}M</div></td>\n                </tr>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">已用内存</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.mem\">{{ server.mem.used}}G</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.jvm\">{{ server.jvm.used}}M</div></td>\n                </tr>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">剩余内存</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.mem\">{{ server.mem.free }}G</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.jvm\">{{ server.jvm.free }}M</div></td>\n                </tr>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">使用率</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.mem\" :class=\"{'text-danger': server.mem.usage > 80}\">{{ server.mem.usage }}%</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.jvm\" :class=\"{'text-danger': server.jvm.usage > 80}\">{{ server.jvm.usage }}%</div></td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"24\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\">\n            <span><i class=\"el-icon-monitor\"></i> 服务器信息</span>\n          </div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <table cellspacing=\"0\" style=\"width: 100%;\">\n              <tbody>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">服务器名称</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.sys\">{{ server.sys.computerName }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">操作系统</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.sys\">{{ server.sys.osName }}</div></td>\n                </tr>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">服务器IP</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.sys\">{{ server.sys.computerIp }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">系统架构</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.sys\">{{ server.sys.osArch }}</div></td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"24\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\">\n            <span><i class=\"el-icon-coffee-cup\"></i> Java虚拟机信息</span>\n          </div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <table cellspacing=\"0\" style=\"width: 100%;table-layout:fixed;\">\n              <tbody>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">Java名称</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.jvm\">{{ server.jvm.name }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">Java版本</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.jvm\">{{ server.jvm.version }}</div></td>\n                </tr>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">启动时间</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.jvm\">{{ server.jvm.startTime }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">运行时长</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.jvm\">{{ server.jvm.runTime }}</div></td>\n                </tr>\n                <tr>\n                  <td colspan=\"1\" class=\"el-table__cell is-leaf\"><div class=\"cell\">安装路径</div></td>\n                  <td colspan=\"3\" class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.jvm\">{{ server.jvm.home }}</div></td>\n                </tr>\n                <tr>\n                  <td colspan=\"1\" class=\"el-table__cell is-leaf\"><div class=\"cell\">项目路径</div></td>\n                  <td colspan=\"3\" class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.sys\">{{ server.sys.userDir }}</div></td>\n                </tr>\n                <tr>\n                  <td colspan=\"1\" class=\"el-table__cell is-leaf\"><div class=\"cell\">运行参数</div></td>\n                  <td colspan=\"3\" class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.jvm\">{{ server.jvm.inputArgs }}</div></td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"24\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\">\n            <span><i class=\"el-icon-receiving\"></i> 磁盘状态</span>\n          </div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <table cellspacing=\"0\" style=\"width: 100%;\">\n              <thead>\n                <tr>\n                  <th class=\"el-table__cell el-table__cell is-leaf\"><div class=\"cell\">盘符路径</div></th>\n                  <th class=\"el-table__cell is-leaf\"><div class=\"cell\">文件系统</div></th>\n                  <th class=\"el-table__cell is-leaf\"><div class=\"cell\">盘符类型</div></th>\n                  <th class=\"el-table__cell is-leaf\"><div class=\"cell\">总大小</div></th>\n                  <th class=\"el-table__cell is-leaf\"><div class=\"cell\">可用大小</div></th>\n                  <th class=\"el-table__cell is-leaf\"><div class=\"cell\">已用大小</div></th>\n                  <th class=\"el-table__cell is-leaf\"><div class=\"cell\">已用百分比</div></th>\n                </tr>\n              </thead>\n              <tbody v-if=\"server.sysFiles\">\n                <tr v-for=\"(sysFile, index) in server.sysFiles\" :key=\"index\">\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">{{ sysFile.dirName }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">{{ sysFile.sysTypeName }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">{{ sysFile.typeName }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">{{ sysFile.total }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">{{ sysFile.free }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">{{ sysFile.used }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" :class=\"{'text-danger': sysFile.usage > 80}\">{{ sysFile.usage }}%</div></td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport { getServer } from \"@/api/monitor/server\"\n\nexport default {\n  name: \"Server\",\n  data() {\n    return {\n      // 服务器信息\n      server: []\n    }\n  },\n  created() {\n    this.getList()\n    this.openLoading()\n  },\n  methods: {\n    /** 查询服务器信息 */\n    getList() {\n      getServer().then(response => {\n        this.server = response.data\n        this.$modal.closeLoading()\n      })\n    },\n    // 打开加载层\n    openLoading() {\n      this.$modal.loading(\"正在加载服务监控数据，请稍候！\")\n    }\n  }\n}\n</script>\n"], "mappings": ";;;;;;AAkLA,IAAAA,OAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,MAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACA,cACAF,OAAA,WAAAA,QAAA;MAAA,IAAAG,KAAA;MACA,IAAAC,iBAAA,IAAAC,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAL,MAAA,GAAAQ,QAAA,CAAAT,IAAA;QACAM,KAAA,CAAAI,MAAA,CAAAC,YAAA;MACA;IACA;IACA;IACAP,WAAA,WAAAA,YAAA;MACA,KAAAM,MAAA,CAAAE,OAAA;IACA;EACA;AACA", "ignoreList": []}]}