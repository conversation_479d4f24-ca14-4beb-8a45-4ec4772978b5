{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/plugins/cache.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/plugins/cache.js", "mtime": 1753510684530}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["sessionCache", "set", "key", "value", "sessionStorage", "setItem", "get", "getItem", "setJSON", "jsonValue", "JSON", "stringify", "getJSON", "parse", "remove", "removeItem", "localCache", "localStorage", "_default", "exports", "default", "session", "local"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/plugins/cache.js"], "sourcesContent": ["const sessionCache = {\n  set (key, value) {\n    if (!sessionStorage) {\n      return\n    }\n    if (key != null && value != null) {\n      sessionStorage.setItem(key, value)\n    }\n  },\n  get (key) {\n    if (!sessionStorage) {\n      return null\n    }\n    if (key == null) {\n      return null\n    }\n    return sessionStorage.getItem(key)\n  },\n  setJSON (key, jsonValue) {\n    if (jsonValue != null) {\n      this.set(key, JSON.stringify(jsonValue))\n    }\n  },\n  getJSON (key) {\n    const value = this.get(key)\n    if (value != null) {\n      return JSON.parse(value)\n    }\n    return null\n  },\n  remove (key) {\n    sessionStorage.removeItem(key)\n  }\n}\nconst localCache = {\n  set (key, value) {\n    if (!localStorage) {\n      return\n    }\n    if (key != null && value != null) {\n      localStorage.setItem(key, value)\n    }\n  },\n  get (key) {\n    if (!localStorage) {\n      return null\n    }\n    if (key == null) {\n      return null\n    }\n    return localStorage.getItem(key)\n  },\n  setJSO<PERSON> (key, jsonValue) {\n    if (jsonValue != null) {\n      this.set(key, JSON.stringify(jsonValue))\n    }\n  },\n  getJ<PERSON><PERSON> (key) {\n    const value = this.get(key)\n    if (value != null) {\n      return JSON.parse(value)\n    }\n    return null\n  },\n  remove (key) {\n    localStorage.removeItem(key)\n  }\n}\n\nexport default {\n  /**\n   * 会话级缓存\n   */\n  session: sessionCache,\n  /**\n   * 本地缓存\n   */\n  local: localCache\n}\n"], "mappings": ";;;;;;;;AAAA,IAAMA,YAAY,GAAG;EACnBC,GAAG,WAAHA,GAAGA,CAAEC,GAAG,EAAEC,KAAK,EAAE;IACf,IAAI,CAACC,cAAc,EAAE;MACnB;IACF;IACA,IAAIF,GAAG,IAAI,IAAI,IAAIC,KAAK,IAAI,IAAI,EAAE;MAChCC,cAAc,CAACC,OAAO,CAACH,GAAG,EAAEC,KAAK,CAAC;IACpC;EACF,CAAC;EACDG,GAAG,WAAHA,GAAGA,CAAEJ,GAAG,EAAE;IACR,IAAI,CAACE,cAAc,EAAE;MACnB,OAAO,IAAI;IACb;IACA,IAAIF,GAAG,IAAI,IAAI,EAAE;MACf,OAAO,IAAI;IACb;IACA,OAAOE,cAAc,CAACG,OAAO,CAACL,GAAG,CAAC;EACpC,CAAC;EACDM,OAAO,WAAPA,OAAOA,CAAEN,GAAG,EAAEO,SAAS,EAAE;IACvB,IAAIA,SAAS,IAAI,IAAI,EAAE;MACrB,IAAI,CAACR,GAAG,CAACC,GAAG,EAAEQ,IAAI,CAACC,SAAS,CAACF,SAAS,CAAC,CAAC;IAC1C;EACF,CAAC;EACDG,OAAO,WAAPA,OAAOA,CAAEV,GAAG,EAAE;IACZ,IAAMC,KAAK,GAAG,IAAI,CAACG,GAAG,CAACJ,GAAG,CAAC;IAC3B,IAAIC,KAAK,IAAI,IAAI,EAAE;MACjB,OAAOO,IAAI,CAACG,KAAK,CAACV,KAAK,CAAC;IAC1B;IACA,OAAO,IAAI;EACb,CAAC;EACDW,MAAM,WAANA,MAAMA,CAAEZ,GAAG,EAAE;IACXE,cAAc,CAACW,UAAU,CAACb,GAAG,CAAC;EAChC;AACF,CAAC;AACD,IAAMc,UAAU,GAAG;EACjBf,GAAG,WAAHA,GAAGA,CAAEC,GAAG,EAAEC,KAAK,EAAE;IACf,IAAI,CAACc,YAAY,EAAE;MACjB;IACF;IACA,IAAIf,GAAG,IAAI,IAAI,IAAIC,KAAK,IAAI,IAAI,EAAE;MAChCc,YAAY,CAACZ,OAAO,CAACH,GAAG,EAAEC,KAAK,CAAC;IAClC;EACF,CAAC;EACDG,GAAG,WAAHA,GAAGA,CAAEJ,GAAG,EAAE;IACR,IAAI,CAACe,YAAY,EAAE;MACjB,OAAO,IAAI;IACb;IACA,IAAIf,GAAG,IAAI,IAAI,EAAE;MACf,OAAO,IAAI;IACb;IACA,OAAOe,YAAY,CAACV,OAAO,CAACL,GAAG,CAAC;EAClC,CAAC;EACDM,OAAO,WAAPA,OAAOA,CAAEN,GAAG,EAAEO,SAAS,EAAE;IACvB,IAAIA,SAAS,IAAI,IAAI,EAAE;MACrB,IAAI,CAACR,GAAG,CAACC,GAAG,EAAEQ,IAAI,CAACC,SAAS,CAACF,SAAS,CAAC,CAAC;IAC1C;EACF,CAAC;EACDG,OAAO,WAAPA,OAAOA,CAAEV,GAAG,EAAE;IACZ,IAAMC,KAAK,GAAG,IAAI,CAACG,GAAG,CAACJ,GAAG,CAAC;IAC3B,IAAIC,KAAK,IAAI,IAAI,EAAE;MACjB,OAAOO,IAAI,CAACG,KAAK,CAACV,KAAK,CAAC;IAC1B;IACA,OAAO,IAAI;EACb,CAAC;EACDW,MAAM,WAANA,MAAMA,CAAEZ,GAAG,EAAE;IACXe,YAAY,CAACF,UAAU,CAACb,GAAG,CAAC;EAC9B;AACF,CAAC;AAAA,IAAAgB,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEc;EACb;AACF;AACA;EACEC,OAAO,EAAErB,YAAY;EACrB;AACF;AACA;EACEsB,KAAK,EAAEN;AACT,CAAC", "ignoreList": []}]}