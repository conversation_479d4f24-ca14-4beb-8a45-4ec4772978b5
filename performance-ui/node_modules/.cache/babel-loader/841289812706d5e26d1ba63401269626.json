{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/monitor/cache/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/monitor/cache/index.vue", "mtime": 1753510684534}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_cache", "require", "echarts", "_interopRequireWildcard", "name", "data", "commandstats", "usedmemory", "cache", "created", "getList", "openLoading", "methods", "_this", "getCache", "then", "response", "$modal", "closeLoading", "init", "$refs", "setOption", "tooltip", "trigger", "formatter", "series", "type", "roseType", "radius", "center", "commandStats", "animationEasing", "animationDuration", "info", "used_memory_human", "min", "max", "detail", "value", "parseFloat", "window", "addEventListener", "resize", "loading"], "sources": ["src/views/monitor/cache/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"10\">\n      <el-col :span=\"24\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\"><span><i class=\"el-icon-monitor\"></i> 基本信息</span></div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <table cellspacing=\"0\" style=\"width: 100%\">\n              <tbody>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">Redis版本</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.redis_version }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">运行模式</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.redis_mode == \"standalone\" ? \"单机\" : \"集群\" }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">端口</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.tcp_port }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">客户端数</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.connected_clients }}</div></td>\n                </tr>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">运行时间(天)</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.uptime_in_days }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">使用内存</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.used_memory_human }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">使用CPU</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ parseFloat(cache.info.used_cpu_user_children).toFixed(2) }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">内存配置</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.maxmemory_human }}</div></td>\n                </tr>\n                <tr>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">AOF是否开启</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.aof_enabled == \"0\" ? \"否\" : \"是\" }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">RDB是否成功</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.rdb_last_bgsave_status }}</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">Key数量</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.dbSize\">{{ cache.dbSize }} </div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">网络入口/出口</div></td>\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"cache.info\">{{ cache.info.instantaneous_input_kbps }}kps/{{cache.info.instantaneous_output_kbps}}kps</div></td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"12\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\"><span><i class=\"el-icon-pie-chart\"></i> 命令统计</span></div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <div ref=\"commandstats\" style=\"height: 420px\" />\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"12\" class=\"card-box\">\n        <el-card>\n          <div slot=\"header\"><span><i class=\"el-icon-odometer\"></i> 内存信息</span></div>\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\n            <div ref=\"usedmemory\" style=\"height: 420px\" />\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport { getCache } from \"@/api/monitor/cache\"\nimport * as echarts from \"echarts\"\n\nexport default {\n  name: \"Cache\",\n  data() {\n    return {\n      // 统计命令信息\n      commandstats: null,\n      // 使用内存\n      usedmemory: null,\n      // cache信息\n      cache: []\n    }\n  },\n  created() {\n    this.getList()\n    this.openLoading()\n  },\n  methods: {\n    /** 查缓存询信息 */\n    getList() {\n      getCache().then((response) => {\n        this.cache = response.data\n        this.$modal.closeLoading()\n\n        this.commandstats = echarts.init(this.$refs.commandstats, \"macarons\")\n        this.commandstats.setOption({\n          tooltip: {\n            trigger: \"item\",\n            formatter: \"{a} <br/>{b} : {c} ({d}%)\",\n          },\n          series: [\n            {\n              name: \"命令\",\n              type: \"pie\",\n              roseType: \"radius\",\n              radius: [15, 95],\n              center: [\"50%\", \"38%\"],\n              data: response.data.commandStats,\n              animationEasing: \"cubicInOut\",\n              animationDuration: 1000,\n            }\n          ]\n        })\n        this.usedmemory = echarts.init(this.$refs.usedmemory, \"macarons\")\n        this.usedmemory.setOption({\n          tooltip: {\n            formatter: \"{b} <br/>{a} : \" + this.cache.info.used_memory_human,\n          },\n          series: [\n            {\n              name: \"峰值\",\n              type: \"gauge\",\n              min: 0,\n              max: 1000,\n              detail: {\n                formatter: this.cache.info.used_memory_human,\n              },\n              data: [\n                {\n                  value: parseFloat(this.cache.info.used_memory_human),\n                  name: \"内存消耗\",\n                }\n              ]\n            }\n          ]\n        })\n        window.addEventListener(\"resize\", () => {\n          this.commandstats.resize()\n          this.usedmemory.resize()\n        })\n      })\n    },\n    // 打开加载层\n    openLoading() {\n      this.$modal.loading(\"正在加载缓存监控数据，请稍候！\")\n    }\n  }\n}\n</script>\n"], "mappings": ";;;;;;;AAmEA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAC,uBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,YAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACA,aACAF,OAAA,WAAAA,QAAA;MAAA,IAAAG,KAAA;MACA,IAAAC,eAAA,IAAAC,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAL,KAAA,GAAAQ,QAAA,CAAAX,IAAA;QACAQ,KAAA,CAAAI,MAAA,CAAAC,YAAA;QAEAL,KAAA,CAAAP,YAAA,GAAAJ,OAAA,CAAAiB,IAAA,CAAAN,KAAA,CAAAO,KAAA,CAAAd,YAAA;QACAO,KAAA,CAAAP,YAAA,CAAAe,SAAA;UACAC,OAAA;YACAC,OAAA;YACAC,SAAA;UACA;UACAC,MAAA,GACA;YACArB,IAAA;YACAsB,IAAA;YACAC,QAAA;YACAC,MAAA;YACAC,MAAA;YACAxB,IAAA,EAAAW,QAAA,CAAAX,IAAA,CAAAyB,YAAA;YACAC,eAAA;YACAC,iBAAA;UACA;QAEA;QACAnB,KAAA,CAAAN,UAAA,GAAAL,OAAA,CAAAiB,IAAA,CAAAN,KAAA,CAAAO,KAAA,CAAAb,UAAA;QACAM,KAAA,CAAAN,UAAA,CAAAc,SAAA;UACAC,OAAA;YACAE,SAAA,sBAAAX,KAAA,CAAAL,KAAA,CAAAyB,IAAA,CAAAC;UACA;UACAT,MAAA,GACA;YACArB,IAAA;YACAsB,IAAA;YACAS,GAAA;YACAC,GAAA;YACAC,MAAA;cACAb,SAAA,EAAAX,KAAA,CAAAL,KAAA,CAAAyB,IAAA,CAAAC;YACA;YACA7B,IAAA,GACA;cACAiC,KAAA,EAAAC,UAAA,CAAA1B,KAAA,CAAAL,KAAA,CAAAyB,IAAA,CAAAC,iBAAA;cACA9B,IAAA;YACA;UAEA;QAEA;QACAoC,MAAA,CAAAC,gBAAA;UACA5B,KAAA,CAAAP,YAAA,CAAAoC,MAAA;UACA7B,KAAA,CAAAN,UAAA,CAAAmC,MAAA;QACA;MACA;IACA;IACA;IACA/B,WAAA,WAAAA,YAAA;MACA,KAAAM,MAAA,CAAA0B,OAAA;IACA;EACA;AACA", "ignoreList": []}]}