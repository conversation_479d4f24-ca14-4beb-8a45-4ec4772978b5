{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/system/post.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/system/post.js", "mtime": 1753510684517}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCIvVXNlcnMvbWF6aWhhby9EZXNrdG9wL2Rldi9wZXJmb3JtYW5jZS9wZXJmb3JtYW5jZS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuYWRkUG9zdCA9IGFkZFBvc3Q7CmV4cG9ydHMuZGVsUG9zdCA9IGRlbFBvc3Q7CmV4cG9ydHMuZ2V0UG9zdCA9IGdldFBvc3Q7CmV4cG9ydHMubGlzdFBvc3QgPSBsaXN0UG9zdDsKZXhwb3J0cy51cGRhdGVQb3N0ID0gdXBkYXRlUG9zdDsKdmFyIF9yZXF1ZXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3V0aWxzL3JlcXVlc3QiKSk7Ci8vIOafpeivouWyl+S9jeWIl+ihqApmdW5jdGlvbiBsaXN0UG9zdChxdWVyeSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS9wb3N0L2xpc3QnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfQoKLy8g5p+l6K+i5bKX5L2N6K+m57uGCmZ1bmN0aW9uIGdldFBvc3QocG9zdElkKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvc3lzdGVtL3Bvc3QvJyArIHBvc3RJZCwKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQoKLy8g5paw5aKe5bKX5L2NCmZ1bmN0aW9uIGFkZFBvc3QoZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS9wb3N0JywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDkv67mlLnlspfkvY0KZnVuY3Rpb24gdXBkYXRlUG9zdChkYXRhKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvc3lzdGVtL3Bvc3QnLAogICAgbWV0aG9kOiAncHV0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5Yig6Zmk5bKX5L2NCmZ1bmN0aW9uIGRlbFBvc3QocG9zdElkKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvc3lzdGVtL3Bvc3QvJyArIHBvc3RJZCwKICAgIG1ldGhvZDogJ2RlbGV0ZScKICB9KTsKfQ=="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listPost", "query", "request", "url", "method", "params", "getPost", "postId", "addPost", "data", "updatePost", "delPost"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/system/post.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询岗位列表\nexport function listPost(query) {\n  return request({\n    url: '/system/post/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询岗位详细\nexport function getPost(postId) {\n  return request({\n    url: '/system/post/' + postId,\n    method: 'get'\n  })\n}\n\n// 新增岗位\nexport function addPost(data) {\n  return request({\n    url: '/system/post',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改岗位\nexport function updatePost(data) {\n  return request({\n    url: '/system/post',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除岗位\nexport function delPost(postId) {\n  return request({\n    url: '/system/post/' + postId,\n    method: 'delete'\n  })\n}\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,OAAOA,CAACC,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,OAAOA,CAACJ,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}