{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/execute/year/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/execute/year/index.vue", "mtime": 1753510684533}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_yearly", "require", "_auth", "name", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "tableData", "dialogTitle", "dialogVisible", "form", "queryParams", "pageNum", "pageSize", "year", "upload", "open", "title", "isUploading", "url", "process", "env", "VUE_APP_BASE_API", "headers", "Authorization", "getToken", "uploadExcelUrl", "created", "getList", "methods", "_this", "listYearly", "then", "response", "rows", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleSelectionChange", "selection", "map", "item", "id", "length", "handleAdd", "handleUpdate", "row", "Object", "assign", "submitForm", "_this2", "update<PERSON>early", "$modal", "msgSuccess", "addYearly", "handleDelete", "_this3", "confirm", "join", "<PERSON><PERSON><PERSON><PERSON>", "catch", "handleImport", "handleImportError", "$message", "error", "handleExport", "_this4", "msgWarning", "exportYearly", "blob", "Blob", "type", "window", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "downloadExcelTemplate", "downloadExcelTemplateYearly", "handleFileUploadProgress", "event", "file", "fileList", "handleFileSuccess", "code", "success", "msg"], "sources": ["src/views/execute/year/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"20\" style=\"margin-bottom: 20px;\">\n      <el-col :span=\"12\">\n        <el-button type=\"primary\" @click=\"handleAdd\">新增</el-button>\n        <el-upload\n          class=\"upload-demo\"\n          :action=\"uploadExcelUrl\"\n          :headers=\"upload.headers\"\n          :show-file-list=\"false\"\n          :on-success=\"handleFileSuccess\"\n          :on-error=\"handleImportError\"\n          accept=\".xls,.xlsx\"\n          style=\"display:inline-block;margin-left:10px;\"\n        >\n          <el-button type=\"success\">导入Excel</el-button>\n        </el-upload>\n        <el-button @click=\"handleExport\" style=\"margin-left:10px;\">导出Excel</el-button>\n        <el-button @click=\"downloadExcelTemplate\" style=\"margin-left:10px;\">下载Excel模板</el-button>\n      </el-col>\n    </el-row>\n    <el-table :data=\"tableData\" border style=\"width: 100%\" @selection-change=\"handleSelectionChange\" v-loading=\"loading\">\n      <el-table-column type=\"selection\" width=\"55\" />\n\n      <el-table-column prop=\"name\" label=\"姓名\" width=\"50\" />\n      <el-table-column prop=\"gender\" label=\"性别\" width=\"50\"/>\n      <el-table-column prop=\"birthday\" label=\"出生年月\" width=\"100\" />\n      <el-table-column prop=\"politicalStatus\" label=\"政治面貌\" width=\"80\" />\n      <el-table-column prop=\"currentPosition\" label=\"任现职时间\" width=\"100\" />\n      <el-table-column prop=\"unitandPosition\" label=\"单位及职务职级\" width=\"150\" />\n      <el-table-column prop=\"workContent\" label=\"从事或分管工作\" width=\"150\" />\n      <el-table-column prop=\"selfSummary\" label=\"个人总结\" width=\"300\" />\n      <el-table-column prop=\"training\" label=\"参加脱产培训情况\" width=\"300\" />\n      <el-table-column prop=\"advice\" label=\"主管领导评语和考核等次建议\" width=\"300\" />\n      <el-table-column prop=\"attitude\" label=\"机关负责人或考核委员会意见\" width=\"300\" />\n      <el-table-column prop=\"remark\" label=\"需要说明的情况\" width=\"300\" />\n\n      <el-table-column label=\"操作\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <el-button size=\"mini\" @click=\"handleUpdate(scope.row)\">编辑</el-button>\n          <el-button size=\"mini\" type=\"danger\" @click=\"handleDelete(scope.row)\">删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <el-pagination\n      style=\"margin-top: 20px;\"\n      background\n      layout=\"prev, pager, next, jumper\"\n      :total=\"total\"\n      :page-size=\"queryParams.pageSize\"\n      :current-page.sync=\"queryParams.pageNum\"\n      @current-change=\"getList\"\n    />\n    <el-dialog :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\n      <el-form :model=\"form\" label-width=\"100px\">\n        <el-row :gutter=\"20\">\n          <!-- <el-col :span=\"8\"><el-form-item label=\"序号\"><el-input v-model=\"form.id\" /></el-form-item></el-col> -->\n          <el-col :span=\"8\"><el-form-item label=\"姓名\"><el-input v-model=\"form.name\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"性别\"><el-input v-model=\"form.gender\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"出生年月\"><el-input v-model=\"form.birthday\" /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\"><el-form-item label=\"政治面貌\"><el-input v-model=\"form.politicalStatus\" /></el-form-item></el-col>\n          <el-col :span=\"8\"><el-form-item label=\"任现职时间\"><el-input v-model=\"form.currentPosition\" /></el-form-item></el-col>\n\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"10\"><el-form-item label=\"单位及职务职级\"><el-input v-model=\"form.unitandPosition\" /></el-form-item></el-col>\n          <el-col :span=\"10\"><el-form-item label=\"从事或分管工作\"><el-input v-model=\"form.workContent\" /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"20\"><el-form-item label=\"个人总结\"><el-input v-model=\"form.selfSummary\" /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"20\"><el-form-item label=\"参加脱产培训情况\"><el-input v-model=\"form.training\" /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"20\"><el-form-item label=\"主管领导评语和考核等次建议\"><el-input v-model=\"form.advice\" /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"20\"><el-form-item label=\"机关负责人或考核委员会意见\"><el-input v-model=\"form.attitude\" /></el-form-item></el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"20\"><el-form-item label=\"需要说明的情况\"><el-input v-model=\"form.remark\" /></el-form-item></el-col>\n        </el-row>\n\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"submitForm\">确定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listYearly, addYearly, updateYearly, delYearly, exportYearly, downloadTemplateYearly, downloadExcelTemplateYearly } from \"@/api/performance/yearly\";\nimport { getToken } from \"@/utils/auth\";\n\nexport default {\n  name: \"Yearly\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 表格数据\n      tableData: [],\n      // 弹出层标题\n      dialogTitle: \"\",\n      // 是否显示弹出层\n      dialogVisible: false,\n      // 表单参数\n      form: {},\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        year: null,\n      },\n      // Word导入参数\n      upload: {\n        open: false,\n        title: \"\",\n        isUploading: false,\n        url: process.env.VUE_APP_BASE_API + \"/performance/yearly/importData\",\n        headers: { Authorization: \"Bearer \" + getToken() }\n      },\n      // Excel导入URL\n      uploadExcelUrl: process.env.VUE_APP_BASE_API + \"/performance/yearly/importExcel\"\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询列表 */\n    getList() {\n      this.loading = true;\n      listYearly(this.queryParams).then(response => {\n        this.tableData = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    /** 多选框选中数据 */\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.form = {};\n      this.dialogVisible = true;\n      this.dialogTitle = \"新增年度总结\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      const id = row.id || this.ids[0];\n      // 在实际应用中，推荐通过ID查询最新数据\n      // getYearly(id).then(response => { ... });\n      this.form = Object.assign({}, row);\n      this.dialogVisible = true;\n      this.dialogTitle = \"修改年度总结\";\n    },\n    /** 提交按钮 */\n    submitForm() {\n      if (this.form.id != null) {\n        updateYearly(this.form).then(response => {\n          this.$modal.msgSuccess(\"修改成功\");\n          this.dialogVisible = false;\n          this.getList();\n        });\n      } else {\n        addYearly(this.form).then(response => {\n          this.$modal.msgSuccess(\"新增成功\");\n          this.dialogVisible = false;\n          this.getList();\n        });\n      }\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row.id ? [row.id] : this.ids;\n      this.$modal.confirm('是否确认删除编号为\"' + ids.join(',') + '\"的数据项？').then(() => {\n        return delYearly(ids);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 导入按钮操作 */\n    handleImport() {\n      this.upload.title = \"年度绩效Word导入\";\n      this.upload.open = true;\n    },\n    /** 导入错误处理 */\n    handleImportError() {\n      this.$message.error('导入失败，请检查文件或网络');\n    },\n\n    /** 导出按钮操作 */\n    handleExport() {\n      if (this.ids.length === 0) {\n        this.$modal.msgWarning(\"请选择要导出的数据\");\n        return;\n      }\n      this.$modal.confirm('是否确认导出选中的数据项？').then(() => {\n        return exportYearly(this.ids);\n      }).then(data => {\n        const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })\n        const url = window.URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.href = url\n        a.download = '年度总结导出.docx'\n        document.body.appendChild(a)\n        a.click()\n        document.body.removeChild(a)\n        window.URL.revokeObjectURL(url)\n      }).catch(() => {});\n    },\n\n    // /** 下载Word模板操作 */\n    // downloadTemplate() {\n    //   downloadTemplateYearly().then(data => {\n    //     const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })\n    //     const url = window.URL.createObjectURL(blob)\n    //     const a = document.createElement('a')\n    //     a.href = url\n    //     a.download = '年度总结导入模板.docx'\n    //     document.body.appendChild(a)\n    //     a.click()\n    //     document.body.removeChild(a)\n    //     window.URL.revokeObjectURL(url)\n    //   });\n    // },\n\n    /** 下载Excel模板操作 */\n    downloadExcelTemplate() {\n      downloadExcelTemplateYearly().then(data => {\n        const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })\n        const url = window.URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.href = url\n        a.download = '年度总结Excel导入模板.xlsx'\n        document.body.appendChild(a)\n        a.click()\n        document.body.removeChild(a)\n        window.URL.revokeObjectURL(url)\n      });\n    },\n    /** 文件上传中处理 */\n    handleFileUploadProgress(event, file, fileList) {\n      this.upload.isUploading = true;\n    },\n    /** 文件上传成功处理 */\n    handleFileSuccess(response, file, fileList) {\n      if (response.code === 200) {\n        this.$message.success(response.msg || '导入成功');\n        this.getList();\n      } else {\n        this.$message.error(response.msg || '导入失败');\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.app-container {\n  padding: 20px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAgGA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,SAAA;MACA;MACAC,WAAA;MACA;MACAC,aAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,IAAA;MACA;MACA;MACAC,MAAA;QACAC,IAAA;QACAC,KAAA;QACAC,WAAA;QACAC,GAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;QACAC,OAAA;UAAAC,aAAA,kBAAAC,cAAA;QAAA;MACA;MACA;MACAC,cAAA,EAAAN,OAAA,CAAAC,GAAA,CAAAC,gBAAA;IACA;EACA;EACAK,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,WACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAA7B,OAAA;MACA,IAAA8B,kBAAA,OAAApB,WAAA,EAAAqB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAvB,SAAA,GAAA0B,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAxB,KAAA,GAAA2B,QAAA,CAAA3B,KAAA;QACAwB,KAAA,CAAA7B,OAAA;MACA;IACA;IACA,aACAkC,WAAA,WAAAA,YAAA;MACA,KAAAxB,WAAA,CAAAC,OAAA;MACA,KAAAgB,OAAA;IACA;IACA,aACAQ,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAF,WAAA;IACA;IACA,cACAG,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAArC,GAAA,GAAAqC,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,EAAA;MAAA;MACA,KAAAvC,MAAA,GAAAoC,SAAA,CAAAI,MAAA;MACA,KAAAvC,QAAA,IAAAmC,SAAA,CAAAI,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAlC,IAAA;MACA,KAAAD,aAAA;MACA,KAAAD,WAAA;IACA;IACA,aACAqC,YAAA,WAAAA,aAAAC,GAAA;MACA,IAAAJ,EAAA,GAAAI,GAAA,CAAAJ,EAAA,SAAAxC,GAAA;MACA;MACA;MACA,KAAAQ,IAAA,GAAAqC,MAAA,CAAAC,MAAA,KAAAF,GAAA;MACA,KAAArC,aAAA;MACA,KAAAD,WAAA;IACA;IACA,WACAyC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,SAAAxC,IAAA,CAAAgC,EAAA;QACA,IAAAS,oBAAA,OAAAzC,IAAA,EAAAsB,IAAA,WAAAC,QAAA;UACAiB,MAAA,CAAAE,MAAA,CAAAC,UAAA;UACAH,MAAA,CAAAzC,aAAA;UACAyC,MAAA,CAAAtB,OAAA;QACA;MACA;QACA,IAAA0B,iBAAA,OAAA5C,IAAA,EAAAsB,IAAA,WAAAC,QAAA;UACAiB,MAAA,CAAAE,MAAA,CAAAC,UAAA;UACAH,MAAA,CAAAzC,aAAA;UACAyC,MAAA,CAAAtB,OAAA;QACA;MACA;IACA;IACA,aACA2B,YAAA,WAAAA,aAAAT,GAAA;MAAA,IAAAU,MAAA;MACA,IAAAtD,GAAA,GAAA4C,GAAA,CAAAJ,EAAA,IAAAI,GAAA,CAAAJ,EAAA,SAAAxC,GAAA;MACA,KAAAkD,MAAA,CAAAK,OAAA,gBAAAvD,GAAA,CAAAwD,IAAA,kBAAA1B,IAAA;QACA,WAAA2B,iBAAA,EAAAzD,GAAA;MACA,GAAA8B,IAAA;QACAwB,MAAA,CAAA5B,OAAA;QACA4B,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAO,KAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAA9C,MAAA,CAAAE,KAAA;MACA,KAAAF,MAAA,CAAAC,IAAA;IACA;IACA,aACA8C,iBAAA,WAAAA,kBAAA;MACA,KAAAC,QAAA,CAAAC,KAAA;IACA;IAEA,aACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,SAAAhE,GAAA,CAAAyC,MAAA;QACA,KAAAS,MAAA,CAAAe,UAAA;QACA;MACA;MACA,KAAAf,MAAA,CAAAK,OAAA,kBAAAzB,IAAA;QACA,WAAAoC,oBAAA,EAAAF,MAAA,CAAAhE,GAAA;MACA,GAAA8B,IAAA,WAAAhC,IAAA;QACA,IAAAqE,IAAA,OAAAC,IAAA,EAAAtE,IAAA;UAAAuE,IAAA;QAAA;QACA,IAAApD,GAAA,GAAAqD,MAAA,CAAAC,GAAA,CAAAC,eAAA,CAAAL,IAAA;QACA,IAAAM,CAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,CAAA,CAAAG,IAAA,GAAA3D,GAAA;QACAwD,CAAA,CAAAI,QAAA;QACAH,QAAA,CAAAI,IAAA,CAAAC,WAAA,CAAAN,CAAA;QACAA,CAAA,CAAAO,KAAA;QACAN,QAAA,CAAAI,IAAA,CAAAG,WAAA,CAAAR,CAAA;QACAH,MAAA,CAAAC,GAAA,CAAAW,eAAA,CAAAjE,GAAA;MACA,GAAAyC,KAAA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACAyB,qBAAA,WAAAA,sBAAA;MACA,IAAAC,mCAAA,IAAAtD,IAAA,WAAAhC,IAAA;QACA,IAAAqE,IAAA,OAAAC,IAAA,EAAAtE,IAAA;UAAAuE,IAAA;QAAA;QACA,IAAApD,GAAA,GAAAqD,MAAA,CAAAC,GAAA,CAAAC,eAAA,CAAAL,IAAA;QACA,IAAAM,CAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,CAAA,CAAAG,IAAA,GAAA3D,GAAA;QACAwD,CAAA,CAAAI,QAAA;QACAH,QAAA,CAAAI,IAAA,CAAAC,WAAA,CAAAN,CAAA;QACAA,CAAA,CAAAO,KAAA;QACAN,QAAA,CAAAI,IAAA,CAAAG,WAAA,CAAAR,CAAA;QACAH,MAAA,CAAAC,GAAA,CAAAW,eAAA,CAAAjE,GAAA;MACA;IACA;IACA,cACAoE,wBAAA,WAAAA,yBAAAC,KAAA,EAAAC,IAAA,EAAAC,QAAA;MACA,KAAA3E,MAAA,CAAAG,WAAA;IACA;IACA,eACAyE,iBAAA,WAAAA,kBAAA1D,QAAA,EAAAwD,IAAA,EAAAC,QAAA;MACA,IAAAzD,QAAA,CAAA2D,IAAA;QACA,KAAA7B,QAAA,CAAA8B,OAAA,CAAA5D,QAAA,CAAA6D,GAAA;QACA,KAAAlE,OAAA;MACA;QACA,KAAAmC,QAAA,CAAAC,KAAA,CAAA/B,QAAA,CAAA6D,GAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}