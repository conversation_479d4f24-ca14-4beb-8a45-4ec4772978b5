{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/performance/org.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/performance/org.js", "mtime": 1753510684516}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listOrgPlan", "query", "request", "url", "method", "params", "addOrgPlan", "data", "updateOrgPlan", "delOrgPlan", "id", "exportOrgPlan", "concat", "responseType", "batchExportOrgPlan", "ids", "importOrgPlan", "downloadOrgTemplate"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/performance/org.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询组织绩效计划列表\nexport function listOrgPlan(query) {\n  return request({\n    url: '/performance/plan/org/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 新增组织绩效计划\nexport function addOrgPlan(data) {\n  return request({\n    url: '/performance/plan/org',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改组织绩效计划\nexport function updateOrgPlan(data) {\n  return request({\n    url: '/performance/plan/org',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除组织绩效计划\nexport function delOrgPlan(id) {\n  return request({\n    url: '/performance/plan/org/' + id,\n    method: 'delete'\n  })\n}\n\n// 单条导出Word\nexport function exportOrgPlan(id) {\n  return request({\n    url: `/performance/plan/org/export/${id}`,\n    method: 'get',\n    responseType: 'blob'\n  })\n}\n\n// 批量导出Word\nexport function batchExportOrgPlan(ids) {\n  return request({\n    url: '/performance/plan/org/batchExport',\n    method: 'post',\n    data: ids,\n    responseType: 'blob'\n  })\n}\n\n// 导入Word\nexport function importOrgPlan(data) {\n  return request({\n    url: '/performance/plan/org/import',\n    method: 'post',\n    data: data\n  })\n}\n\n// 下载模板\nexport function downloadOrgTemplate() {\n  return request({\n    url: '/performance/plan/org/downloadTemplate',\n    method: 'get',\n    responseType: 'blob'\n  })\n} "], "mappings": ";;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,WAAWA,CAACC,KAAK,EAAE;EACjC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,UAAUA,CAACC,IAAI,EAAE;EAC/B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,MAAM;IACdG,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,aAAaA,CAACD,IAAI,EAAE;EAClC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,KAAK;IACbG,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,UAAUA,CAACC,EAAE,EAAE;EAC7B,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB,GAAGO,EAAE;IAClCN,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASO,aAAaA,CAACD,EAAE,EAAE;EAChC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,kCAAAS,MAAA,CAAkCF,EAAE,CAAE;IACzCN,MAAM,EAAE,KAAK;IACbS,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,kBAAkBA,CAACC,GAAG,EAAE;EACtC,OAAO,IAAAb,gBAAO,EAAC;IACbC,GAAG,EAAE,mCAAmC;IACxCC,MAAM,EAAE,MAAM;IACdG,IAAI,EAAEQ,GAAG;IACTF,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ;;AAEA;AACO,SAASG,aAAaA,CAACT,IAAI,EAAE;EAClC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE,MAAM;IACdG,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,mBAAmBA,CAAA,EAAG;EACpC,OAAO,IAAAf,gBAAO,EAAC;IACbC,GAAG,EAAE,wCAAwC;IAC7CC,MAAM,EAAE,KAAK;IACbS,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ", "ignoreList": []}]}