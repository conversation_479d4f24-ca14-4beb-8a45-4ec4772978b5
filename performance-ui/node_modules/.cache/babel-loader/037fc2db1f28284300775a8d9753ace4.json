{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/formats/font.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/quill/formats/font.js", "mtime": 1753510684093}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_parchment", "require", "config", "scope", "<PERSON><PERSON>", "INLINE", "whitelist", "FontClass", "exports", "ClassAttributor", "FontStyleAttributor", "_StyleAttributor", "_classCallCheck2", "default", "_callSuper2", "arguments", "_inherits2", "_createClass2", "key", "value", "node", "_superPropGet2", "replace", "StyleAttributor", "FontStyle"], "sources": ["../../src/formats/font.ts"], "sourcesContent": ["import { ClassAttributor, Scope, StyleAttributor } from 'parchment';\n\nconst config = {\n  scope: Scope.INLINE,\n  whitelist: ['serif', 'monospace'],\n};\n\nconst FontClass = new ClassAttributor('font', 'ql-font', config);\n\nclass FontStyleAttributor extends StyleAttributor {\n  value(node: HTMLElement) {\n    return super.value(node).replace(/[\"']/g, '');\n  }\n}\n\nconst FontStyle = new FontStyleAttributor('font', 'font-family', config);\n\nexport { FontStyle, FontClass };\n"], "mappings": ";;;;;;;;;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAEA,IAAMC,MAAM,GAAG;EACbC,KAAK,EAAEC,gBAAK,CAACC,MAAM;EACnBC,SAAS,EAAE,CAAC,OAAO,EAAE,WAAW;AAClC,CAAC;AAED,IAAMC,SAAS,GAAAC,OAAA,CAAAD,SAAA,GAAG,IAAIE,0BAAe,CAAC,MAAM,EAAE,SAAS,EAAEP,MAAM,CAAC;AAAA,IAE1DQ,mBAAmB,0BAAAC,gBAAA;EAAA,SAAAD,oBAAA;IAAA,IAAAE,gBAAA,CAAAC,OAAA,QAAAH,mBAAA;IAAA,WAAAI,WAAA,CAAAD,OAAA,QAAAH,mBAAA,EAAAK,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAH,mBAAA,EAAAC,gBAAA;EAAA,WAAAM,aAAA,CAAAJ,OAAA,EAAAH,mBAAA;IAAAQ,GAAA;IAAAC,KAAA,EACvB,SAAAA,KAAKA,CAACC,IAAiB,EAAE;MACvB,OAAO,IAAAC,cAAA,CAAAR,OAAA,EAAAH,mBAAA,qBAAYU,IAAI,GAAEE,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;IAC/C;EAAA;AAAA,EAHgCC,0BAAe;AAMjD,IAAMC,SAAS,GAAAhB,OAAA,CAAAgB,SAAA,GAAG,IAAId,mBAAmB,CAAC,MAAM,EAAE,aAAa,EAAER,MAAM,CAAC", "ignoreList": []}]}