{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/layout/components/Sidebar/FixiOSBug.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/layout/components/Sidebar/FixiOSBug.js", "mtime": 1753510684529}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBjb21wdXRlZDogewogICAgZGV2aWNlOiBmdW5jdGlvbiBkZXZpY2UoKSB7CiAgICAgIHJldHVybiB0aGlzLiRzdG9yZS5zdGF0ZS5hcHAuZGV2aWNlOwogICAgfQogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIC8vIEluIG9yZGVyIHRvIGZpeCB0aGUgY2xpY2sgb24gbWVudSBvbiB0aGUgaW9zIGRldmljZSB3aWxsIHRyaWdnZXIgdGhlIG1vdXNlbGVhdmUgYnVnCiAgICB0aGlzLmZpeEJ1Z0luaU9TKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICBmaXhCdWdJbmlPUzogZnVuY3Rpb24gZml4QnVnSW5pT1MoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHZhciAkc3ViTWVudSA9IHRoaXMuJHJlZnMuc3ViTWVudTsKICAgICAgaWYgKCRzdWJNZW51KSB7CiAgICAgICAgdmFyIGhhbmRsZU1vdXNlbGVhdmUgPSAkc3ViTWVudS5oYW5kbGVNb3VzZWxlYXZlOwogICAgICAgICRzdWJNZW51LmhhbmRsZU1vdXNlbGVhdmUgPSBmdW5jdGlvbiAoZSkgewogICAgICAgICAgaWYgKF90aGlzLmRldmljZSA9PT0gJ21vYmlsZScpIHsKICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgfQogICAgICAgICAgaGFuZGxlTW91c2VsZWF2ZShlKTsKICAgICAgICB9OwogICAgICB9CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["computed", "device", "$store", "state", "app", "mounted", "fixBugIniOS", "methods", "_this", "$subMenu", "$refs", "subMenu", "handleMouseleave", "e"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/layout/components/Sidebar/FixiOSBug.js"], "sourcesContent": ["export default {\n  computed: {\n    device() {\n      return this.$store.state.app.device\n    }\n  },\n  mounted() {\n    // In order to fix the click on menu on the ios device will trigger the mouseleave bug\n    this.fixBugIniOS()\n  },\n  methods: {\n    fixBugIniOS() {\n      const $subMenu = this.$refs.subMenu\n      if ($subMenu) {\n        const handleMouseleave = $subMenu.handleMouseleave\n        $subMenu.handleMouseleave = (e) => {\n          if (this.device === 'mobile') {\n            return\n          }\n          handleMouseleave(e)\n        }\n      }\n    }\n  }\n}\n"], "mappings": ";;;;;;iCAAe;EACbA,QAAQ,EAAE;IACRC,MAAM,WAANA,MAAMA,CAAA,EAAG;MACP,OAAO,IAAI,CAACC,MAAM,CAACC,KAAK,CAACC,GAAG,CAACH,MAAM;IACrC;EACF,CAAC;EACDI,OAAO,WAAPA,OAAOA,CAAA,EAAG;IACR;IACA,IAAI,CAACC,WAAW,CAAC,CAAC;EACpB,CAAC;EACDC,OAAO,EAAE;IACPD,WAAW,WAAXA,WAAWA,CAAA,EAAG;MAAA,IAAAE,KAAA;MACZ,IAAMC,QAAQ,GAAG,IAAI,CAACC,KAAK,CAACC,OAAO;MACnC,IAAIF,QAAQ,EAAE;QACZ,IAAMG,gBAAgB,GAAGH,QAAQ,CAACG,gBAAgB;QAClDH,QAAQ,CAACG,gBAAgB,GAAG,UAACC,CAAC,EAAK;UACjC,IAAIL,KAAI,CAACP,MAAM,KAAK,QAAQ,EAAE;YAC5B;UACF;UACAW,gBAAgB,CAACC,CAAC,CAAC;QACrB,CAAC;MACH;IACF;EACF;AACF,CAAC", "ignoreList": []}]}