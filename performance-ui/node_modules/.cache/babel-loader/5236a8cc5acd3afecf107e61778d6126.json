{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/directive/dialog/drag.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/directive/dialog/drag.js", "mtime": 1753510684528}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_default", "exports", "default", "bind", "el", "binding", "vnode", "oldVnode", "value", "dialogHeaderEl", "querySelector", "dragDom", "style", "cursor", "sty", "currentStyle", "window", "getComputedStyle", "position", "marginTop", "width", "includes", "document", "body", "clientWidth", "replace", "left", "concat", "onmousedown", "e", "disX", "clientX", "offsetLeft", "disY", "clientY", "offsetTop", "styL", "styT", "clientHeight", "top", "<PERSON><PERSON><PERSON><PERSON>", "l", "t", "finallyL", "finallyT", "onmouseup"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/directive/dialog/drag.js"], "sourcesContent": ["/**\n* v-dialogDrag 弹窗拖拽\n* Copyright (c) 2019 ruoyi\n*/\n\nexport default {\n  bind(el, binding, vnode, oldVnode) {\n    const value = binding.value\n    if (value == false) return\n    // 获取拖拽内容头部\n    const dialogHeaderEl = el.querySelector('.el-dialog__header')\n    const dragDom = el.querySelector('.el-dialog')\n    dialogHeaderEl.style.cursor = 'move'\n    // 获取原有属性 ie dom元素.currentStyle 火狐谷歌 window.getComputedStyle(dom元素, null)\n    const sty = dragDom.currentStyle || window.getComputedStyle(dragDom, null)\n    dragDom.style.position = 'absolute'\n    dragDom.style.marginTop = 0\n    let width = dragDom.style.width\n    if (width.includes('%')) {\n      width = +document.body.clientWidth * (+width.replace(/\\%/g, '') / 100)\n    } else {\n      width = +width.replace(/\\px/g, '')\n    }\n    dragDom.style.left = `${(document.body.clientWidth - width) / 2}px`\n    // 鼠标按下事件\n    dialogHeaderEl.onmousedown = (e) => {\n      // 鼠标按下，计算当前元素距离可视区的距离 (鼠标点击位置距离可视窗口的距离)\n      const disX = e.clientX - dialogHeaderEl.offsetLeft\n      const disY = e.clientY - dialogHeaderEl.offsetTop\n\n      // 获取到的值带px 正则匹配替换\n      let styL, styT\n\n      // 注意在ie中 第一次获取到的值为组件自带50% 移动之后赋值为px\n      if (sty.left.includes('%')) {\n        styL = +document.body.clientWidth * (+sty.left.replace(/\\%/g, '') / 100)\n        styT = +document.body.clientHeight * (+sty.top.replace(/\\%/g, '') / 100)\n      } else {\n        styL = +sty.left.replace(/\\px/g, '')\n        styT = +sty.top.replace(/\\px/g, '')\n      }\n\n      // 鼠标拖拽事件\n      document.onmousemove = function (e) {\n        // 通过事件委托，计算移动的距离 （开始拖拽至结束拖拽的距离）\n        const l = e.clientX - disX\n        const t = e.clientY - disY\n\n        let finallyL = l + styL\n        let finallyT = t + styT\n\n        // 移动当前元素\n        dragDom.style.left = `${finallyL}px`\n        dragDom.style.top = `${finallyT}px`\n\n      }\n\n      document.onmouseup = function (e) {\n        document.onmousemove = null\n        document.onmouseup = null\n      }\n    }\n  }\n}"], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AACA;AAHA,IAAAA,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAKe;EACbC,IAAI,WAAJA,IAAIA,CAACC,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IACjC,IAAMC,KAAK,GAAGH,OAAO,CAACG,KAAK;IAC3B,IAAIA,KAAK,IAAI,KAAK,EAAE;IACpB;IACA,IAAMC,cAAc,GAAGL,EAAE,CAACM,aAAa,CAAC,oBAAoB,CAAC;IAC7D,IAAMC,OAAO,GAAGP,EAAE,CAACM,aAAa,CAAC,YAAY,CAAC;IAC9CD,cAAc,CAACG,KAAK,CAACC,MAAM,GAAG,MAAM;IACpC;IACA,IAAMC,GAAG,GAAGH,OAAO,CAACI,YAAY,IAAIC,MAAM,CAACC,gBAAgB,CAACN,OAAO,EAAE,IAAI,CAAC;IAC1EA,OAAO,CAACC,KAAK,CAACM,QAAQ,GAAG,UAAU;IACnCP,OAAO,CAACC,KAAK,CAACO,SAAS,GAAG,CAAC;IAC3B,IAAIC,KAAK,GAAGT,OAAO,CAACC,KAAK,CAACQ,KAAK;IAC/B,IAAIA,KAAK,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;MACvBD,KAAK,GAAG,CAACE,QAAQ,CAACC,IAAI,CAACC,WAAW,IAAI,CAACJ,KAAK,CAACK,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC;IACxE,CAAC,MAAM;MACLL,KAAK,GAAG,CAACA,KAAK,CAACK,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;IACpC;IACAd,OAAO,CAACC,KAAK,CAACc,IAAI,MAAAC,MAAA,CAAM,CAACL,QAAQ,CAACC,IAAI,CAACC,WAAW,GAAGJ,KAAK,IAAI,CAAC,OAAI;IACnE;IACAX,cAAc,CAACmB,WAAW,GAAG,UAACC,CAAC,EAAK;MAClC;MACA,IAAMC,IAAI,GAAGD,CAAC,CAACE,OAAO,GAAGtB,cAAc,CAACuB,UAAU;MAClD,IAAMC,IAAI,GAAGJ,CAAC,CAACK,OAAO,GAAGzB,cAAc,CAAC0B,SAAS;;MAEjD;MACA,IAAIC,IAAI,EAAEC,IAAI;;MAEd;MACA,IAAIvB,GAAG,CAACY,IAAI,CAACL,QAAQ,CAAC,GAAG,CAAC,EAAE;QAC1Be,IAAI,GAAG,CAACd,QAAQ,CAACC,IAAI,CAACC,WAAW,IAAI,CAACV,GAAG,CAACY,IAAI,CAACD,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC;QACxEY,IAAI,GAAG,CAACf,QAAQ,CAACC,IAAI,CAACe,YAAY,IAAI,CAACxB,GAAG,CAACyB,GAAG,CAACd,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC;MAC1E,CAAC,MAAM;QACLW,IAAI,GAAG,CAACtB,GAAG,CAACY,IAAI,CAACD,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;QACpCY,IAAI,GAAG,CAACvB,GAAG,CAACyB,GAAG,CAACd,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;MACrC;;MAEA;MACAH,QAAQ,CAACkB,WAAW,GAAG,UAAUX,CAAC,EAAE;QAClC;QACA,IAAMY,CAAC,GAAGZ,CAAC,CAACE,OAAO,GAAGD,IAAI;QAC1B,IAAMY,CAAC,GAAGb,CAAC,CAACK,OAAO,GAAGD,IAAI;QAE1B,IAAIU,QAAQ,GAAGF,CAAC,GAAGL,IAAI;QACvB,IAAIQ,QAAQ,GAAGF,CAAC,GAAGL,IAAI;;QAEvB;QACA1B,OAAO,CAACC,KAAK,CAACc,IAAI,MAAAC,MAAA,CAAMgB,QAAQ,OAAI;QACpChC,OAAO,CAACC,KAAK,CAAC2B,GAAG,MAAAZ,MAAA,CAAMiB,QAAQ,OAAI;MAErC,CAAC;MAEDtB,QAAQ,CAACuB,SAAS,GAAG,UAAUhB,CAAC,EAAE;QAChCP,QAAQ,CAACkB,WAAW,GAAG,IAAI;QAC3BlB,QAAQ,CAACuB,SAAS,GAAG,IAAI;MAC3B,CAAC;IACH,CAAC;EACH;AACF,CAAC", "ignoreList": []}]}