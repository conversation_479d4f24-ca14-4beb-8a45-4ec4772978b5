{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js??ref--0-0!/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/evaluation/privateYear/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/evaluation/privateYear/index.vue", "mtime": 1753510684533}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5tYXAuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnB1c2guanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnNwbGljZS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmNvbnN0cnVjdG9yLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IuZm9yLWVhY2guanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5tYXAuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuZm9yLWVhY2guanMiKTsKdmFyIF9wcml2YXRlQW5udWFsID0gcmVxdWlyZSgiQC9hcGkvcGVyZm9ybWFuY2UvcHJpdmF0ZUFubnVhbCIpOwp2YXIgX2F1dGggPSByZXF1aXJlKCJAL3V0aWxzL2F1dGgiKTsKdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG5hbWU6ICJQcml2YXRlQW5udWFsIiwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g6YGu572p5bGCCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIC8vIOmAieS4reaVsOe7hAogICAgICBpZHM6IFtdLAogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgKICAgICAgc2luZ2xlOiB0cnVlLAogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgKICAgICAgbXVsdGlwbGU6IHRydWUsCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tgogICAgICBzaG93U2VhcmNoOiB0cnVlLAogICAgICAvLyDmgLvmnaHmlbAKICAgICAgdG90YWw6IDAsCiAgICAgIC8vIOS4quS6uuW5tOW6puivhOS7t+ihqOagvOaVsOaNrgogICAgICBwcml2YXRlQW5udWFsTGlzdDogW10sCiAgICAgIC8vIOW8ueWHuuWxguagh+mimAogICAgICB0aXRsZTogIiIsCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxggogICAgICBvcGVuOiBmYWxzZSwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgbmFtZTogdW5kZWZpbmVkLAogICAgICAgIHllYXI6IHVuZGVmaW5lZAogICAgICB9LAogICAgICAvLyDooajljZXlj4LmlbAKICAgICAgZm9ybToge30sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICBydWxlczogewogICAgICAgIG5hbWU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLlp5PlkI3kuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgeWVhcjogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuW5tOS7veS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICB0ZXN0VGltZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIua1i+ivhOaXtumXtOS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XQogICAgICB9LAogICAgICAvLyDkuIrkvKDlj4LmlbAKICAgICAgdXBsb2FkOiB7CiAgICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGC77yI5a+85YWl77yJCiAgICAgICAgb3BlbjogZmFsc2UsCiAgICAgICAgLy8g5by55Ye65bGC5qCH6aKY77yI5a+85YWl77yJCiAgICAgICAgdGl0bGU6ICIiLAogICAgICAgIC8vIOaYr+WQpuemgeeUqOS4iuS8oAogICAgICAgIGlzVXBsb2FkaW5nOiBmYWxzZSwKICAgICAgICAvLyDorr7nva7kuIrkvKDnmoTor7fmsYLlpLTpg6gKICAgICAgICBoZWFkZXJzOiB7CiAgICAgICAgICBBdXRob3JpemF0aW9uOiAiQmVhcmVyICIgKyAoMCwgX2F1dGguZ2V0VG9rZW4pKCkKICAgICAgICB9LAogICAgICAgIC8vIOS4iuS8oOeahOWcsOWdgAogICAgICAgIHVybDogcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArICIvc3lzdGVtL3ByaXZhdGVBbm51YWwvaW1wb3J0RGF0YSIsCiAgICAgICAgLy8g5piv5ZCm5pu05paw5bey57uP5a2Y5Zyo55qE5pWw5o2uCiAgICAgICAgdXBkYXRlU3VwcG9ydDogZmFsc2UsCiAgICAgICAgLy8g5b2T5YmN5r+A5rS755qE5qCH562+6aG1CiAgICAgICAgYWN0aXZlVGFiOiAiZXhjZWwiCiAgICAgIH0sCiAgICAgIC8vIOWFseaAp+aMh+agh+exu+Wei+mAiemhuQogICAgICBjb21tb25UeXBlT3B0aW9uczogW3sKICAgICAgICB2YWx1ZTogIuaUv+ayu+ihqOeOsCIsCiAgICAgICAgbGFiZWw6ICLmlL/msrvooajnjrAiCiAgICAgIH0sIHsKICAgICAgICB2YWx1ZTogIuiDveWKm+e0oOi0qCIsCiAgICAgICAgbGFiZWw6ICLog73lipvntKDotKgiCiAgICAgIH0sIHsKICAgICAgICB2YWx1ZTogIueyvuelnueKtuaAgeOAgeW3peS9nOS9nOmjjiIsCiAgICAgICAgbGFiZWw6ICLnsr7npZ7nirbmgIHjgIHlt6XkvZzkvZzpo44iCiAgICAgIH0sIHsKICAgICAgICB2YWx1ZTogIuW7iea0geiHquW+iyIsCiAgICAgICAgbGFiZWw6ICLlu4nmtIHoh6rlvosiCiAgICAgIH1dLAogICAgICAvLyDmv4DlirHnuqbmnZ/mjIfmoIfnsbvlnovpgInpobkKICAgICAgaW5jZW50aXZlVHlwZU9wdGlvbnM6IFt7CiAgICAgICAgdmFsdWU6ICLmv4DlirHmjIfmoIciLAogICAgICAgIGxhYmVsOiAi5r+A5Yqx5oyH5qCHIgogICAgICB9LCB7CiAgICAgICAgdmFsdWU6ICLnuqbmnZ/mjIfmoIciLAogICAgICAgIGxhYmVsOiAi57qm5p2f5oyH5qCHIgogICAgICB9XQogICAgfTsKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDmn6Xor6LkuKrkurrlubTluqbor4Tku7fliJfooaggKi9nZXRMaXN0OiBmdW5jdGlvbiBnZXRMaXN0KCkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICAoMCwgX3ByaXZhdGVBbm51YWwubGlzdFByaXZhdGVBbm51YWwpKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMucHJpdmF0ZUFubnVhbExpc3QgPSByZXNwb25zZS5yb3dzOwogICAgICAgIF90aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgX3RoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDlj5bmtojmjInpkq4KICAgIGNhbmNlbDogZnVuY3Rpb24gY2FuY2VsKCkgewogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgdGhpcy5yZXNldCgpOwogICAgfSwKICAgIC8vIOihqOWNlemHjee9rgogICAgcmVzZXQ6IGZ1bmN0aW9uIHJlc2V0KCkgewogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgaWQ6IHVuZGVmaW5lZCwKICAgICAgICBuYW1lOiB1bmRlZmluZWQsCiAgICAgICAgcG9zaXRpb246IHVuZGVmaW5lZCwKICAgICAgICB5ZWFyOiBuZXcgRGF0ZSgpLmdldEZ1bGxZZWFyKCksCiAgICAgICAgdGVzdFRpbWU6IHVuZGVmaW5lZCwKICAgICAgICB0b3RhbFNjb3JlOiB1bmRlZmluZWQsCiAgICAgICAgc2VsZkV2YWx1YXRpb246IHVuZGVmaW5lZCwKICAgICAgICBsZWFkZXJFdmFsdWF0aW9uOiB1bmRlZmluZWQsCiAgICAgICAgc3RhdHVzOiAiMCIsCiAgICAgICAgY29tbW9uSW5kaWNhdG9yczogW3sKICAgICAgICAgIGluZGljYXRvclR5cGU6ICLmlL/msrvooajnjrAiLAogICAgICAgICAgaW5kaWNhdG9yQ29udGVudDogIiIsCiAgICAgICAgICBldmFsdWF0aW9uU3RhbmRhcmQ6ICIiLAogICAgICAgICAgc2NvcmU6ICIiLAogICAgICAgICAgc2VsZlNjb3JlOiAiIiwKICAgICAgICAgIGxlYWRlclNjb3JlOiAiIiwKICAgICAgICAgIGRlcGFydG1lbnRTY29yZTogIiIsCiAgICAgICAgICBncm91cFNjb3JlOiAiIiwKICAgICAgICAgIHN1YnRvdGFsU2NvcmU6ICIiLAogICAgICAgICAgYW5udWFsU2NvcmU6ICIiCiAgICAgICAgfSwgewogICAgICAgICAgaW5kaWNhdG9yVHlwZTogIuiDveWKm+e0oOi0qCIsCiAgICAgICAgICBpbmRpY2F0b3JDb250ZW50OiAiIiwKICAgICAgICAgIGV2YWx1YXRpb25TdGFuZGFyZDogIiIsCiAgICAgICAgICBzY29yZTogIiIsCiAgICAgICAgICBzZWxmU2NvcmU6ICIiLAogICAgICAgICAgbGVhZGVyU2NvcmU6ICIiLAogICAgICAgICAgZGVwYXJ0bWVudFNjb3JlOiAiIiwKICAgICAgICAgIGdyb3VwU2NvcmU6ICIiLAogICAgICAgICAgc3VidG90YWxTY29yZTogIiIsCiAgICAgICAgICBhbm51YWxTY29yZTogIiIKICAgICAgICB9LCB7CiAgICAgICAgICBpbmRpY2F0b3JUeXBlOiAi57K+56We54q25oCB44CB5bel5L2c5L2c6aOOIiwKICAgICAgICAgIGluZGljYXRvckNvbnRlbnQ6ICIiLAogICAgICAgICAgZXZhbHVhdGlvblN0YW5kYXJkOiAiIiwKICAgICAgICAgIHNjb3JlOiAiIiwKICAgICAgICAgIHNlbGZTY29yZTogIiIsCiAgICAgICAgICBsZWFkZXJTY29yZTogIiIsCiAgICAgICAgICBkZXBhcnRtZW50U2NvcmU6ICIiLAogICAgICAgICAgZ3JvdXBTY29yZTogIiIsCiAgICAgICAgICBzdWJ0b3RhbFNjb3JlOiAiIiwKICAgICAgICAgIGFubnVhbFNjb3JlOiAiIgogICAgICAgIH0sIHsKICAgICAgICAgIGluZGljYXRvclR5cGU6ICLlu4nmtIHoh6rlvosiLAogICAgICAgICAgaW5kaWNhdG9yQ29udGVudDogIiIsCiAgICAgICAgICBldmFsdWF0aW9uU3RhbmRhcmQ6ICIiLAogICAgICAgICAgc2NvcmU6ICIiLAogICAgICAgICAgc2VsZlNjb3JlOiAiIiwKICAgICAgICAgIGxlYWRlclNjb3JlOiAiIiwKICAgICAgICAgIGRlcGFydG1lbnRTY29yZTogIiIsCiAgICAgICAgICBncm91cFNjb3JlOiAiIiwKICAgICAgICAgIHN1YnRvdGFsU2NvcmU6ICIiLAogICAgICAgICAgYW5udWFsU2NvcmU6ICIiCiAgICAgICAgfV0sCiAgICAgICAgaW5kaXZpZHVhbEluZGljYXRvcnM6IFtdLAogICAgICAgIGluY2VudGl2ZUluZGljYXRvcnM6IFt7CiAgICAgICAgICBpbmRpY2F0b3JUeXBlOiAi5r+A5Yqx5oyH5qCHIiwKICAgICAgICAgIGluZGljYXRvckNvbnRlbnQ6ICIiLAogICAgICAgICAgZXZhbHVhdGlvblN0YW5kYXJkOiAiIiwKICAgICAgICAgIHNjb3JlOiAiIiwKICAgICAgICAgIHNlbGZTY29yZTogIiIsCiAgICAgICAgICBsZWFkZXJTY29yZTogIiIsCiAgICAgICAgICBkZXBhcnRtZW50U2NvcmU6ICIiLAogICAgICAgICAgZ3JvdXBTY29yZTogIiIsCiAgICAgICAgICBzdWJ0b3RhbFNjb3JlOiAiIiwKICAgICAgICAgIGluY2VudGl2ZVNjb3JlOiAiIgogICAgICAgIH0sIHsKICAgICAgICAgIGluZGljYXRvclR5cGU6ICLnuqbmnZ/mjIfmoIciLAogICAgICAgICAgaW5kaWNhdG9yQ29udGVudDogIiIsCiAgICAgICAgICBldmFsdWF0aW9uU3RhbmRhcmQ6ICIiLAogICAgICAgICAgc2NvcmU6ICIiLAogICAgICAgICAgc2VsZlNjb3JlOiAiIiwKICAgICAgICAgIGxlYWRlclNjb3JlOiAiIiwKICAgICAgICAgIGRlcGFydG1lbnRTY29yZTogIiIsCiAgICAgICAgICBncm91cFNjb3JlOiAiIiwKICAgICAgICAgIHN1YnRvdGFsU2NvcmU6ICIiLAogICAgICAgICAgaW5jZW50aXZlU2NvcmU6ICIiCiAgICAgICAgfV0KICAgICAgfTsKICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsKICAgIH0sCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovaGFuZGxlUXVlcnk6IGZ1bmN0aW9uIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovcmVzZXRRdWVyeTogZnVuY3Rpb24gcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgcmV0dXJuIGl0ZW0uaWQ7CiAgICAgIH0pOwogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDE7CiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsKICAgIH0sCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovaGFuZGxlQWRkOiBmdW5jdGlvbiBoYW5kbGVBZGQoKSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDkuKrkurrlubTluqbor4Tku7ciOwogICAgfSwKICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi9oYW5kbGVVcGRhdGU6IGZ1bmN0aW9uIGhhbmRsZVVwZGF0ZShyb3cpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgdmFyIGlkID0gcm93LmlkIHx8IHRoaXMuaWRzWzBdOwogICAgICAoMCwgX3ByaXZhdGVBbm51YWwuZ2V0UHJpdmF0ZUFubnVhbCkoaWQpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMyLmZvcm0gPSByZXNwb25zZS5kYXRhOwogICAgICAgIC8vIOehruS/neWFseaAp+aMh+agh+WSjOa/gOWKsee6puadn+aMh+agh+WtmOWcqAogICAgICAgIGlmICghX3RoaXMyLmZvcm0uY29tbW9uSW5kaWNhdG9ycyB8fCBfdGhpczIuZm9ybS5jb21tb25JbmRpY2F0b3JzLmxlbmd0aCA9PT0gMCkgewogICAgICAgICAgX3RoaXMyLmZvcm0uY29tbW9uSW5kaWNhdG9ycyA9IFt7CiAgICAgICAgICAgIGluZGljYXRvclR5cGU6ICLmlL/msrvooajnjrAiLAogICAgICAgICAgICBpbmRpY2F0b3JDb250ZW50OiAiIiwKICAgICAgICAgICAgZXZhbHVhdGlvblN0YW5kYXJkOiAiIiwKICAgICAgICAgICAgc2NvcmU6ICIiLAogICAgICAgICAgICBzZWxmU2NvcmU6ICIiLAogICAgICAgICAgICBsZWFkZXJTY29yZTogIiIsCiAgICAgICAgICAgIGRlcGFydG1lbnRTY29yZTogIiIsCiAgICAgICAgICAgIGdyb3VwU2NvcmU6ICIiLAogICAgICAgICAgICBzdWJ0b3RhbFNjb3JlOiAiIiwKICAgICAgICAgICAgYW5udWFsU2NvcmU6ICIiCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIGluZGljYXRvclR5cGU6ICLog73lipvntKDotKgiLAogICAgICAgICAgICBpbmRpY2F0b3JDb250ZW50OiAiIiwKICAgICAgICAgICAgZXZhbHVhdGlvblN0YW5kYXJkOiAiIiwKICAgICAgICAgICAgc2NvcmU6ICIiLAogICAgICAgICAgICBzZWxmU2NvcmU6ICIiLAogICAgICAgICAgICBsZWFkZXJTY29yZTogIiIsCiAgICAgICAgICAgIGRlcGFydG1lbnRTY29yZTogIiIsCiAgICAgICAgICAgIGdyb3VwU2NvcmU6ICIiLAogICAgICAgICAgICBzdWJ0b3RhbFNjb3JlOiAiIiwKICAgICAgICAgICAgYW5udWFsU2NvcmU6ICIiCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIGluZGljYXRvclR5cGU6ICLnsr7npZ7nirbmgIHjgIHlt6XkvZzkvZzpo44iLAogICAgICAgICAgICBpbmRpY2F0b3JDb250ZW50OiAiIiwKICAgICAgICAgICAgZXZhbHVhdGlvblN0YW5kYXJkOiAiIiwKICAgICAgICAgICAgc2NvcmU6ICIiLAogICAgICAgICAgICBzZWxmU2NvcmU6ICIiLAogICAgICAgICAgICBsZWFkZXJTY29yZTogIiIsCiAgICAgICAgICAgIGRlcGFydG1lbnRTY29yZTogIiIsCiAgICAgICAgICAgIGdyb3VwU2NvcmU6ICIiLAogICAgICAgICAgICBzdWJ0b3RhbFNjb3JlOiAiIiwKICAgICAgICAgICAgYW5udWFsU2NvcmU6ICIiCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIGluZGljYXRvclR5cGU6ICLlu4nmtIHoh6rlvosiLAogICAgICAgICAgICBpbmRpY2F0b3JDb250ZW50OiAiIiwKICAgICAgICAgICAgZXZhbHVhdGlvblN0YW5kYXJkOiAiIiwKICAgICAgICAgICAgc2NvcmU6ICIiLAogICAgICAgICAgICBzZWxmU2NvcmU6ICIiLAogICAgICAgICAgICBsZWFkZXJTY29yZTogIiIsCiAgICAgICAgICAgIGRlcGFydG1lbnRTY29yZTogIiIsCiAgICAgICAgICAgIGdyb3VwU2NvcmU6ICIiLAogICAgICAgICAgICBzdWJ0b3RhbFNjb3JlOiAiIiwKICAgICAgICAgICAgYW5udWFsU2NvcmU6ICIiCiAgICAgICAgICB9XTsKICAgICAgICB9CiAgICAgICAgaWYgKCFfdGhpczIuZm9ybS5pbmNlbnRpdmVJbmRpY2F0b3JzIHx8IF90aGlzMi5mb3JtLmluY2VudGl2ZUluZGljYXRvcnMubGVuZ3RoID09PSAwKSB7CiAgICAgICAgICBfdGhpczIuZm9ybS5pbmNlbnRpdmVJbmRpY2F0b3JzID0gW3sKICAgICAgICAgICAgaW5kaWNhdG9yVHlwZTogIua/gOWKseaMh+aghyIsCiAgICAgICAgICAgIGluZGljYXRvckNvbnRlbnQ6ICIiLAogICAgICAgICAgICBldmFsdWF0aW9uU3RhbmRhcmQ6ICIiLAogICAgICAgICAgICBzY29yZTogIiIsCiAgICAgICAgICAgIHNlbGZTY29yZTogIiIsCiAgICAgICAgICAgIGxlYWRlclNjb3JlOiAiIiwKICAgICAgICAgICAgZGVwYXJ0bWVudFNjb3JlOiAiIiwKICAgICAgICAgICAgZ3JvdXBTY29yZTogIiIsCiAgICAgICAgICAgIHN1YnRvdGFsU2NvcmU6ICIiLAogICAgICAgICAgICBpbmNlbnRpdmVTY29yZTogIiIKICAgICAgICAgIH0sIHsKICAgICAgICAgICAgaW5kaWNhdG9yVHlwZTogIue6puadn+aMh+aghyIsCiAgICAgICAgICAgIGluZGljYXRvckNvbnRlbnQ6ICIiLAogICAgICAgICAgICBldmFsdWF0aW9uU3RhbmRhcmQ6ICIiLAogICAgICAgICAgICBzY29yZTogIiIsCiAgICAgICAgICAgIHNlbGZTY29yZTogIiIsCiAgICAgICAgICAgIGxlYWRlclNjb3JlOiAiIiwKICAgICAgICAgICAgZGVwYXJ0bWVudFNjb3JlOiAiIiwKICAgICAgICAgICAgZ3JvdXBTY29yZTogIiIsCiAgICAgICAgICAgIHN1YnRvdGFsU2NvcmU6ICIiLAogICAgICAgICAgICBpbmNlbnRpdmVTY29yZTogIiIKICAgICAgICAgIH1dOwogICAgICAgIH0KICAgICAgICAvLyDnoa7kv53kuKrmgKfmjIfmoIflrZjlnKgKICAgICAgICBpZiAoIV90aGlzMi5mb3JtLmluZGl2aWR1YWxJbmRpY2F0b3JzKSB7CiAgICAgICAgICBfdGhpczIuZm9ybS5pbmRpdmlkdWFsSW5kaWNhdG9ycyA9IFtdOwogICAgICAgIH0KICAgICAgICBfdGhpczIub3BlbiA9IHRydWU7CiAgICAgICAgX3RoaXMyLnRpdGxlID0gIuS/ruaUueS4quS6uuW5tOW6puivhOS7tyI7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmj5DkuqTmjInpkq4gKi9zdWJtaXRGb3JtOiBmdW5jdGlvbiBzdWJtaXRGb3JtKCkgewogICAgICB2YXIgX3RoaXMzID0gdGhpczsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKGZ1bmN0aW9uICh2YWxpZCkgewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgaWYgKF90aGlzMy5mb3JtLmlkICE9IG51bGwpIHsKICAgICAgICAgICAgKDAsIF9wcml2YXRlQW5udWFsLnVwZGF0ZVByaXZhdGVBbm51YWwpKF90aGlzMy5mb3JtKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgICAgIF90aGlzMy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7CiAgICAgICAgICAgICAgX3RoaXMzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICBfdGhpczMuZ2V0TGlzdCgpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICgwLCBfcHJpdmF0ZUFubnVhbC5hZGRQcml2YXRlQW5udWFsKShfdGhpczMuZm9ybSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgICAgICBfdGhpczMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOwogICAgICAgICAgICAgIF90aGlzMy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgX3RoaXMzLmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovaGFuZGxlRGVsZXRlOiBmdW5jdGlvbiBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICB2YXIgaWRzID0gcm93LmlkIHx8IHRoaXMuaWRzOwogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTkuKrkurrlubTluqbor4Tku7fnvJblj7fkuLoiJyArIGlkcyArICci55qE5pWw5o2u6aG577yfJykudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgcmV0dXJuICgwLCBfcHJpdmF0ZUFubnVhbC5kZWxQcml2YXRlQW5udWFsKShpZHMpOwogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczQuZ2V0TGlzdCgpOwogICAgICAgIF90aGlzNC4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHt9KTsKICAgIH0sCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovaGFuZGxlRXhwb3J0OiBmdW5jdGlvbiBoYW5kbGVFeHBvcnQoKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTlr7zlh7rmiYDmnInkuKrkurrlubTluqbor4Tku7fmlbDmja7pobnvvJ8nKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczUuJG1vZGFsLmxvYWRpbmcoIuato+WcqOWvvOWHuuaVsOaNru+8jOivt+eojeWQji4uLiIpOwogICAgICAgICgwLCBfcHJpdmF0ZUFubnVhbC5leHBvcnRQcml2YXRlQW5udWFsKShfdGhpczUucXVlcnlQYXJhbXMpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICBfdGhpczUuJGRvd25sb2FkLmV4Y2VsKHJlc3BvbnNlLCAn5Liq5Lq65bm05bqm6K+E5Lu35pWw5o2uLnhsc3gnKTsKICAgICAgICAgIF90aGlzNS4kbW9kYWwuY2xvc2VMb2FkaW5nKCk7CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDlr7zlhaXmjInpkq7mk43kvZwgKi9oYW5kbGVJbXBvcnQ6IGZ1bmN0aW9uIGhhbmRsZUltcG9ydCgpIHsKICAgICAgdGhpcy51cGxvYWQudGl0bGUgPSAi5Liq5Lq65bm05bqm6K+E5Lu35pWw5o2u5a+85YWlIjsKICAgICAgdGhpcy51cGxvYWQub3BlbiA9IHRydWU7CiAgICB9LAogICAgLyoqIOS4i+i9veaooeadv+aTjeS9nCAqL2ltcG9ydFRlbXBsYXRlOiBmdW5jdGlvbiBpbXBvcnRUZW1wbGF0ZSgpIHsKICAgICAgdmFyIF90aGlzNiA9IHRoaXM7CiAgICAgIHRoaXMuJG1vZGFsLmxvYWRpbmcoIuato+WcqOS4i+i9veaooeadv++8jOivt+eojeWQji4uLiIpOwogICAgICAoMCwgX3ByaXZhdGVBbm51YWwuZG93bmxvYWRUZW1wbGF0ZUFubnVhbCkoKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzNi4kZG93bmxvYWQuc2F2ZUFzKHJlc3BvbnNlLCAn5Liq5Lq65bm05bqm6K+E5Lu35qih5p2/LmRvY3gnKTsKICAgICAgICBfdGhpczYuJG1vZGFsLmNsb3NlTG9hZGluZygpOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDmlofku7bkuIrkvKDkuK3lpITnkIYKICAgIGhhbmRsZUZpbGVVcGxvYWRQcm9ncmVzczogZnVuY3Rpb24gaGFuZGxlRmlsZVVwbG9hZFByb2dyZXNzKGV2ZW50LCBmaWxlLCBmaWxlTGlzdCkgewogICAgICB0aGlzLnVwbG9hZC5pc1VwbG9hZGluZyA9IHRydWU7CiAgICB9LAogICAgLy8g5paH5Lu25LiK5Lyg5oiQ5Yqf5aSE55CGCiAgICBoYW5kbGVGaWxlU3VjY2VzczogZnVuY3Rpb24gaGFuZGxlRmlsZVN1Y2Nlc3MocmVzcG9uc2UsIGZpbGUsIGZpbGVMaXN0KSB7CiAgICAgIHRoaXMudXBsb2FkLm9wZW4gPSBmYWxzZTsKICAgICAgdGhpcy51cGxvYWQuaXNVcGxvYWRpbmcgPSBmYWxzZTsKICAgICAgdGhpcy4kcmVmcy51cGxvYWQuY2xlYXJGaWxlcygpOwogICAgICB0aGlzLiRhbGVydChyZXNwb25zZS5tc2csICLlr7zlhaXnu5PmnpwiLCB7CiAgICAgICAgZGFuZ2Vyb3VzbHlVc2VIVE1MU3RyaW5nOiB0cnVlCiAgICAgIH0pOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvLyDmj5DkuqTkuIrkvKDmlofku7YKICAgIHN1Ym1pdEZpbGVGb3JtOiBmdW5jdGlvbiBzdWJtaXRGaWxlRm9ybSgpIHsKICAgICAgdGhpcy4kcmVmcy51cGxvYWQuc3VibWl0KCk7CiAgICB9LAogICAgLy8gV29yZOaWh+S7tuS4iuS8oOWJjeWkhOeQhgogICAgYmVmb3JlV29yZFVwbG9hZDogZnVuY3Rpb24gYmVmb3JlV29yZFVwbG9hZChmaWxlKSB7CiAgICAgIHZhciBpc0RvY3ggPSBmaWxlLnR5cGUgPT09ICdhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQud29yZHByb2Nlc3NpbmdtbC5kb2N1bWVudCc7CiAgICAgIHZhciBpc0RvYyA9IGZpbGUudHlwZSA9PT0gJ2FwcGxpY2F0aW9uL21zd29yZCc7CiAgICAgIGlmICghaXNEb2N4ICYmICFpc0RvYykgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+S4iuS8oOaWh+S7tuWPquiDveaYryBXb3JkIOagvOW8jyEnKTsKICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KICAgICAgcmV0dXJuIHRydWU7CiAgICB9LAogICAgLy8gV29yZOaWh+S7tuS4iuS8oOWkhOeQhgogICAgdXBsb2FkV29yZFJlcXVlc3Q6IGZ1bmN0aW9uIHVwbG9hZFdvcmRSZXF1ZXN0KG9wdGlvbnMpIHsKICAgICAgdmFyIF90aGlzNyA9IHRoaXM7CiAgICAgIHRoaXMuJG1vZGFsLmxvYWRpbmcoIuato+WcqOWvvOWFpeaVsOaNru+8jOivt+eojeWQji4uLiIpOwogICAgICAoMCwgX3ByaXZhdGVBbm51YWwuaW1wb3J0V29yZFByaXZhdGVBbm51YWwpKG9wdGlvbnMuZmlsZSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczcuJG1vZGFsLmNsb3NlTG9hZGluZygpOwogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsKICAgICAgICAgIF90aGlzNy4kbW9kYWwubXNnU3VjY2VzcyhyZXNwb25zZS5tc2cpOwogICAgICAgICAgX3RoaXM3LnVwbG9hZC5vcGVuID0gZmFsc2U7CiAgICAgICAgICBfdGhpczcuZ2V0TGlzdCgpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBfdGhpczcuJG1vZGFsLm1zZ0Vycm9yKHJlc3BvbnNlLm1zZyk7CiAgICAgICAgfQogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoZXJyb3IpIHsKICAgICAgICBfdGhpczcuJG1vZGFsLmNsb3NlTG9hZGluZygpOwogICAgICAgIF90aGlzNy4kbW9kYWwubXNnRXJyb3IoIuWvvOWFpeWksei0pe+8miIgKyBlcnJvcik7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIFdvcmTmlofku7bkuIrkvKDmiJDlip/lpITnkIYKICAgIGhhbmRsZVdvcmRTdWNjZXNzOiBmdW5jdGlvbiBoYW5kbGVXb3JkU3VjY2VzcyhyZXNwb25zZSwgZmlsZSwgZmlsZUxpc3QpIHsKICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MocmVzcG9uc2UubXNnKTsKICAgICAgICB0aGlzLnVwbG9hZC5vcGVuID0gZmFsc2U7CiAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IocmVzcG9uc2UubXNnKTsKICAgICAgfQogICAgfSwKICAgIC8vIFdvcmTmlofku7bkuIrkvKDlpLHotKXlpITnkIYKICAgIGhhbmRsZVdvcmRFcnJvcjogZnVuY3Rpb24gaGFuZGxlV29yZEVycm9yKGVycikgewogICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi5a+85YWl5aSx6LSl77yM6K+35qOA5p+l5paH5Lu25qC85byP5oiW572R57uc6L+e5o6lIik7CiAgICB9LAogICAgLyoqIOWvvOWHuldvcmTmjInpkq7mk43kvZwgKi9oYW5kbGVFeHBvcnRXb3JkOiBmdW5jdGlvbiBoYW5kbGVFeHBvcnRXb3JkKCkgewogICAgICB2YXIgX3RoaXM4ID0gdGhpczsKICAgICAgaWYgKHRoaXMuaWRzLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLor7fpgInmi6nopoHlr7zlh7rnmoTmlbDmja4iKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5a+85Ye66YCJ5Lit55qE5Liq5Lq65bm05bqm6K+E5Lu35pWw5o2u6aG577yfJykudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXM4LiRtb2RhbC5sb2FkaW5nKCLmraPlnKjlr7zlh7rmlbDmja7vvIzor7fnqI3lkI4uLi4iKTsKICAgICAgICAoMCwgX3ByaXZhdGVBbm51YWwuZXhwb3J0V29yZFByaXZhdGVBbm51YWwpKF90aGlzOC5pZHMpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICBfdGhpczguJGRvd25sb2FkLnNhdmVBcyhyZXNwb25zZSwgJ+S4quS6uuW5tOW6puivhOS7ty5kb2N4Jyk7CiAgICAgICAgICBfdGhpczguJG1vZGFsLmNsb3NlTG9hZGluZygpOwogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5re75Yqg5Liq5oCn5oyH5qCH6KGMICovaGFuZGxlQWRkSW5kaXZpZHVhbFJvdzogZnVuY3Rpb24gaGFuZGxlQWRkSW5kaXZpZHVhbFJvdygpIHsKICAgICAgaWYgKCF0aGlzLmZvcm0uaW5kaXZpZHVhbEluZGljYXRvcnMpIHsKICAgICAgICB0aGlzLmZvcm0uaW5kaXZpZHVhbEluZGljYXRvcnMgPSBbXTsKICAgICAgfQogICAgICB0aGlzLmZvcm0uaW5kaXZpZHVhbEluZGljYXRvcnMucHVzaCh7CiAgICAgICAgc2VyaWFsTnVtYmVyOiB0aGlzLmZvcm0uaW5kaXZpZHVhbEluZGljYXRvcnMubGVuZ3RoICsgMSwKICAgICAgICBpbmRpY2F0b3JUeXBlOiAi5Liq5oCn5oyH5qCHIiwKICAgICAgICBpbmRpY2F0b3JDb250ZW50OiAiIiwKICAgICAgICBldmFsdWF0aW9uU3RhbmRhcmQ6ICIiLAogICAgICAgIHNjb3JlOiAwLAogICAgICAgIHNlbGZTY29yZTogIiIsCiAgICAgICAgbGVhZGVyU2NvcmU6ICIiLAogICAgICAgIGRlcGFydG1lbnRTY29yZTogIiIsCiAgICAgICAgZ3JvdXBTY29yZTogIiIsCiAgICAgICAgc3VidG90YWxTY29yZTogIiIsCiAgICAgICAgYW5udWFsU2NvcmU6ICIiCiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDliKDpmaTkuKrmgKfmjIfmoIfooYwgKi9oYW5kbGVEZWxldGVJbmRpdmlkdWFsUm93OiBmdW5jdGlvbiBoYW5kbGVEZWxldGVJbmRpdmlkdWFsUm93KGluZGV4KSB7CiAgICAgIHRoaXMuZm9ybS5pbmRpdmlkdWFsSW5kaWNhdG9ycy5zcGxpY2UoaW5kZXgsIDEpOwogICAgICAvLyDph43mlrDmjpLluo8KICAgICAgdGhpcy5mb3JtLmluZGl2aWR1YWxJbmRpY2F0b3JzLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0sIGkpIHsKICAgICAgICBpdGVtLnNlcmlhbE51bWJlciA9IGkgKyAxOwogICAgICB9KTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["_privateAnnual", "require", "_auth", "_default", "exports", "default", "name", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "privateAnnualList", "title", "open", "queryParams", "undefined", "year", "form", "rules", "required", "message", "trigger", "testTime", "upload", "isUploading", "headers", "Authorization", "getToken", "url", "process", "env", "VUE_APP_BASE_API", "updateSupport", "activeTab", "commonTypeOptions", "value", "label", "incentiveTypeOptions", "created", "getList", "methods", "_this", "listPrivateAnnual", "then", "response", "rows", "cancel", "reset", "id", "position", "Date", "getFullYear", "totalScore", "selfEvaluation", "leaderEvaluation", "status", "commonIndicators", "indicatorType", "indicatorContent", "evaluationStandard", "score", "selfScore", "leaderScore", "departmentScore", "groupScore", "subtotalScore", "annualScore", "individualIndicators", "incentiveIndicators", "incentiveScore", "resetForm", "handleQuery", "pageNum", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "row", "_this2", "getPrivateAnnual", "submitForm", "_this3", "$refs", "validate", "valid", "updatePrivateAnnual", "$modal", "msgSuccess", "addPrivateAnnual", "handleDelete", "_this4", "confirm", "delPrivateAnnual", "catch", "handleExport", "_this5", "exportPrivateAnnual", "$download", "excel", "closeLoading", "handleImport", "importTemplate", "_this6", "downloadTemplateAnnual", "saveAs", "handleFileUploadProgress", "event", "file", "fileList", "handleFileSuccess", "clearFiles", "$alert", "msg", "dangerouslyUseHTMLString", "submitFileForm", "submit", "beforeWordUpload", "isDocx", "type", "isDoc", "$message", "error", "uploadWordRequest", "options", "_this7", "importWordPrivateAnnual", "code", "msgError", "handleWordSuccess", "handleWordError", "err", "handleExportWord", "_this8", "exportWordPrivateAnnual", "handleAddIndividualRow", "push", "serialNumber", "handleDeleteIndividualRow", "index", "splice", "for<PERSON>ach", "i"], "sources": ["src/views/evaluation/privateYear/index.vue"], "sourcesContent": ["<script>\nimport { listPrivateAnnual, getPrivateAnnual, delPrivateAnnual, addPrivateAnnual, updatePrivateAnnual, exportPrivateAnnual, exportWordPrivateAnnual, downloadTemplateAnnual, importWordPrivateAnnual } from \"@/api/performance/privateAnnual\";\nimport { getToken } from \"@/utils/auth\";\n\nexport default {\n  name: \"PrivateAnnual\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 个人年度评价表格数据\n      privateAnnualList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 查询参数\n      queryParams: {\n        name: undefined,\n        year: undefined\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        name: [\n          { required: true, message: \"姓名不能为空\", trigger: \"blur\" }\n        ],\n        year: [\n          { required: true, message: \"年份不能为空\", trigger: \"blur\" }\n        ],\n        testTime: [\n          { required: true, message: \"测评时间不能为空\", trigger: \"blur\" }\n        ]\n      },\n      // 上传参数\n      upload: {\n        // 是否显示弹出层（导入）\n        open: false,\n        // 弹出层标题（导入）\n        title: \"\",\n        // 是否禁用上传\n        isUploading: false,\n        // 设置上传的请求头部\n        headers: { Authorization: \"Bearer \" + getToken() },\n        // 上传的地址\n        url: process.env.VUE_APP_BASE_API + \"/system/privateAnnual/importData\",\n        // 是否更新已经存在的数据\n        updateSupport: false,\n        // 当前激活的标签页\n        activeTab: \"excel\"\n      },\n      // 共性指标类型选项\n      commonTypeOptions: [\n        { value: \"政治表现\", label: \"政治表现\" },\n        { value: \"能力素质\", label: \"能力素质\" },\n        { value: \"精神状态、工作作风\", label: \"精神状态、工作作风\" },\n        { value: \"廉洁自律\", label: \"廉洁自律\" }\n      ],\n      // 激励约束指标类型选项\n      incentiveTypeOptions: [\n        { value: \"激励指标\", label: \"激励指标\" },\n        { value: \"约束指标\", label: \"约束指标\" }\n      ]\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询个人年度评价列表 */\n    getList() {\n      this.loading = true;\n      listPrivateAnnual(this.queryParams).then(response => {\n        this.privateAnnualList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: undefined,\n        name: undefined,\n        position: undefined,\n        year: new Date().getFullYear(),\n        testTime: undefined,\n        totalScore: undefined,\n        selfEvaluation: undefined,\n        leaderEvaluation: undefined,\n        status: \"0\",\n        commonIndicators: [\n          { indicatorType: \"政治表现\", indicatorContent: \"\", evaluationStandard: \"\", score: \"\", selfScore: \"\", leaderScore: \"\", departmentScore: \"\", groupScore: \"\", subtotalScore: \"\", annualScore: \"\" },\n          { indicatorType: \"能力素质\", indicatorContent: \"\", evaluationStandard: \"\", score: \"\", selfScore: \"\", leaderScore: \"\", departmentScore: \"\", groupScore: \"\", subtotalScore: \"\", annualScore: \"\" },\n          { indicatorType: \"精神状态、工作作风\", indicatorContent: \"\", evaluationStandard: \"\", score: \"\", selfScore: \"\", leaderScore: \"\", departmentScore: \"\", groupScore: \"\", subtotalScore: \"\", annualScore: \"\" },\n          { indicatorType: \"廉洁自律\", indicatorContent: \"\", evaluationStandard: \"\", score: \"\", selfScore: \"\", leaderScore: \"\", departmentScore: \"\", groupScore: \"\", subtotalScore: \"\", annualScore: \"\" }\n        ],\n        individualIndicators: [],\n        incentiveIndicators: [\n          { indicatorType: \"激励指标\", indicatorContent: \"\", evaluationStandard: \"\", score: \"\", selfScore: \"\", leaderScore: \"\", departmentScore: \"\", groupScore: \"\", subtotalScore: \"\", incentiveScore: \"\" },\n          { indicatorType: \"约束指标\", indicatorContent: \"\", evaluationStandard: \"\", score: \"\", selfScore: \"\", leaderScore: \"\", departmentScore: \"\", groupScore: \"\", subtotalScore: \"\", incentiveScore: \"\" }\n        ]\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加个人年度评价\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids[0];\n      getPrivateAnnual(id).then(response => {\n        this.form = response.data;\n        // 确保共性指标和激励约束指标存在\n        if (!this.form.commonIndicators || this.form.commonIndicators.length === 0) {\n          this.form.commonIndicators = [\n            { indicatorType: \"政治表现\", indicatorContent: \"\", evaluationStandard: \"\", score: \"\", selfScore: \"\", leaderScore: \"\", departmentScore: \"\", groupScore: \"\", subtotalScore: \"\", annualScore: \"\" },\n            { indicatorType: \"能力素质\", indicatorContent: \"\", evaluationStandard: \"\", score: \"\", selfScore: \"\", leaderScore: \"\", departmentScore: \"\", groupScore: \"\", subtotalScore: \"\", annualScore: \"\" },\n            { indicatorType: \"精神状态、工作作风\", indicatorContent: \"\", evaluationStandard: \"\", score: \"\", selfScore: \"\", leaderScore: \"\", departmentScore: \"\", groupScore: \"\", subtotalScore: \"\", annualScore: \"\" },\n            { indicatorType: \"廉洁自律\", indicatorContent: \"\", evaluationStandard: \"\", score: \"\", selfScore: \"\", leaderScore: \"\", departmentScore: \"\", groupScore: \"\", subtotalScore: \"\", annualScore: \"\" }\n          ];\n        }\n        if (!this.form.incentiveIndicators || this.form.incentiveIndicators.length === 0) {\n          this.form.incentiveIndicators = [\n            { indicatorType: \"激励指标\", indicatorContent: \"\", evaluationStandard: \"\", score: \"\", selfScore: \"\", leaderScore: \"\", departmentScore: \"\", groupScore: \"\", subtotalScore: \"\", incentiveScore: \"\" },\n            { indicatorType: \"约束指标\", indicatorContent: \"\", evaluationStandard: \"\", score: \"\", selfScore: \"\", leaderScore: \"\", departmentScore: \"\", groupScore: \"\", subtotalScore: \"\", incentiveScore: \"\" }\n          ];\n        }\n        // 确保个性指标存在\n        if (!this.form.individualIndicators) {\n          this.form.individualIndicators = [];\n        }\n        this.open = true;\n        this.title = \"修改个人年度评价\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id != null) {\n            updatePrivateAnnual(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addPrivateAnnual(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row.id || this.ids;\n      this.$modal.confirm('是否确认删除个人年度评价编号为\"' + ids + '\"的数据项？').then(function() {\n        return delPrivateAnnual(ids);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.$modal.confirm('是否确认导出所有个人年度评价数据项？').then(() => {\n        this.$modal.loading(\"正在导出数据，请稍后...\");\n        exportPrivateAnnual(this.queryParams).then(response => {\n          this.$download.excel(response, '个人年度评价数据.xlsx');\n          this.$modal.closeLoading();\n        });\n      });\n    },\n    /** 导入按钮操作 */\n    handleImport() {\n      this.upload.title = \"个人年度评价数据导入\";\n      this.upload.open = true;\n    },\n    /** 下载模板操作 */\n    importTemplate() {\n      this.$modal.loading(\"正在下载模板，请稍后...\");\n      downloadTemplateAnnual().then(response => {\n        this.$download.saveAs(response, '个人年度评价模板.docx');\n        this.$modal.closeLoading();\n      });\n    },\n    // 文件上传中处理\n    handleFileUploadProgress(event, file, fileList) {\n      this.upload.isUploading = true;\n    },\n    // 文件上传成功处理\n    handleFileSuccess(response, file, fileList) {\n      this.upload.open = false;\n      this.upload.isUploading = false;\n      this.$refs.upload.clearFiles();\n      this.$alert(response.msg, \"导入结果\", { dangerouslyUseHTMLString: true });\n      this.getList();\n    },\n    // 提交上传文件\n    submitFileForm() {\n      this.$refs.upload.submit();\n    },\n    // Word文件上传前处理\n    beforeWordUpload(file) {\n      const isDocx = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';\n      const isDoc = file.type === 'application/msword';\n      if (!isDocx && !isDoc) {\n        this.$message.error('上传文件只能是 Word 格式!');\n        return false;\n      }\n      return true;\n    },\n    // Word文件上传处理\n    uploadWordRequest(options) {\n      this.$modal.loading(\"正在导入数据，请稍后...\");\n      importWordPrivateAnnual(options.file).then(response => {\n        this.$modal.closeLoading();\n        if (response.code === 200) {\n          this.$modal.msgSuccess(response.msg);\n          this.upload.open = false;\n          this.getList();\n        } else {\n          this.$modal.msgError(response.msg);\n        }\n      }).catch(error => {\n        this.$modal.closeLoading();\n        this.$modal.msgError(\"导入失败：\" + error);\n      });\n    },\n    // Word文件上传成功处理\n    handleWordSuccess(response, file, fileList) {\n      if (response.code === 200) {\n        this.$modal.msgSuccess(response.msg);\n        this.upload.open = false;\n        this.getList();\n      } else {\n        this.$modal.msgError(response.msg);\n      }\n    },\n    // Word文件上传失败处理\n    handleWordError(err) {\n      this.$modal.msgError(\"导入失败，请检查文件格式或网络连接\");\n    },\n    /** 导出Word按钮操作 */\n    handleExportWord() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要导出的数据\");\n        return;\n      }\n      this.$modal.confirm('是否确认导出选中的个人年度评价数据项？').then(() => {\n        this.$modal.loading(\"正在导出数据，请稍后...\");\n        exportWordPrivateAnnual(this.ids).then(response => {\n          this.$download.saveAs(response, '个人年度评价.docx');\n          this.$modal.closeLoading();\n        });\n      });\n    },\n    /** 添加个性指标行 */\n    handleAddIndividualRow() {\n      if (!this.form.individualIndicators) {\n        this.form.individualIndicators = [];\n      }\n      this.form.individualIndicators.push({\n        serialNumber: this.form.individualIndicators.length + 1,\n        indicatorType: \"个性指标\",\n        indicatorContent: \"\",\n        evaluationStandard: \"\",\n        score: 0,\n        selfScore: \"\",\n        leaderScore: \"\",\n        departmentScore: \"\",\n        groupScore: \"\",\n        subtotalScore: \"\",\n        annualScore: \"\"\n      });\n    },\n    /** 删除个性指标行 */\n    handleDeleteIndividualRow(index) {\n      this.form.individualIndicators.splice(index, 1);\n      // 重新排序\n      this.form.individualIndicators.forEach((item, i) => {\n        item.serialNumber = i + 1;\n      });\n    }\n  }\n};\n</script>\n\n<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"姓名\" prop=\"name\">\n        <el-input\n          v-model=\"queryParams.name\"\n          placeholder=\"请输入姓名\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"年份\" prop=\"year\">\n        <el-input\n          v-model=\"queryParams.year\"\n          placeholder=\"请输入年份\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['system:privateQuarterly:add']\"\n        >新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n          v-hasPermi=\"['system:privateQuarterly:edit']\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['system:privateQuarterly:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['system:privateQuarterly:export']\"\n        >导出Excel</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleExportWord\"\n          v-hasPermi=\"['system:privateQuarterly:export']\"\n        >导出Word</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"info\"\n          plain\n          icon=\"el-icon-upload2\"\n          size=\"mini\"\n          @click=\"handleImport\"\n          v-hasPermi=\"['system:privateQuarterly:import']\"\n        >导入</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"info\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"importTemplate\"\n          v-hasPermi=\"['system:privateQuarterly:export']\"\n        >下载模板</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"privateAnnualList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"ID\" align=\"center\" prop=\"id\" width=\"80\" />\n      <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" />\n      <el-table-column label=\"职务\" align=\"center\" prop=\"position\" />\n      <el-table-column label=\"年份\" align=\"center\" prop=\"year\" />\n      <el-table-column label=\"测评时间\" align=\"center\" prop=\"testTime\" width=\"180\" />\n<!--      <el-table-column label=\"总分\" align=\"center\" prop=\"totalScore\" />-->\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['system:privateQuarterly:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['system:privateQuarterly:remove']\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改个人年度评价对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"80vw\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n        <el-row>\n          <el-col :span=\"8\">\n            <el-form-item label=\"姓名\" prop=\"name\">\n              <el-input v-model=\"form.name\" placeholder=\"请输入姓名\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"职务\" prop=\"position\">\n              <el-input v-model=\"form.position\" placeholder=\"请输入职务\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"测评时间\" prop=\"testTime\">\n              <el-date-picker clearable\n                v-model=\"form.testTime\"\n                type=\"date\"\n                value-format=\"yyyy-MM-dd\"\n                placeholder=\"请选择测评时间\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"年份\" prop=\"year\">\n              <el-input v-model=\"form.year\" placeholder=\"请输入年份\" />\n            </el-form-item>\n          </el-col>\n<!--          <el-col :span=\"12\">-->\n<!--            <el-form-item label=\"总分\" prop=\"totalScore\">-->\n<!--              <el-input v-model=\"form.totalScore\" placeholder=\"请输入总分\" />-->\n<!--            </el-form-item>-->\n<!--          </el-col>-->\n        </el-row>\n\n        <el-tabs type=\"border-card\">\n          <el-tab-pane label=\"共性指标\">\n            <el-table :data=\"form.commonIndicators\" border>\n              <el-table-column label=\"指标类型\" align=\"center\" width=\"120\">\n                <template slot-scope=\"scope\">\n                  <el-select v-model=\"scope.row.indicatorType\" placeholder=\"请选择指标类型\">\n                    <el-option\n                      v-for=\"dict in commonTypeOptions\"\n                      :key=\"dict.value\"\n                      :label=\"dict.label\"\n                      :value=\"dict.value\"\n                    ></el-option>\n                  </el-select>\n                </template>\n              </el-table-column>\n<!--              <el-table-column label=\"指标内容\" align=\"center\">-->\n<!--                <template slot-scope=\"scope\">-->\n<!--                  <el-input v-model=\"scope.row.indicatorContent\" type=\"textarea\" placeholder=\"请输入指标内容\" />-->\n<!--                </template>-->\n<!--              </el-table-column>-->\n              <el-table-column label=\"评价标准\" align=\"center\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.evaluationStandard\" type=\"textarea\" placeholder=\"请输入评价标准\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"分值\" align=\"center\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.score\" placeholder=\"分值\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"自评分值\" align=\"center\" width=\"80\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.selfScore\" placeholder=\"自评\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"科长点评\" align=\"center\" width=\"80\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.leaderScore\" placeholder=\"科长点评\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"分管领导点评\" align=\"center\" width=\"90\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.departmentScore\" placeholder=\"分管领导点评\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"领导小组评鉴\" align=\"center\" width=\"90\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.groupScore\" placeholder=\"领导小组评鉴\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"小计得分\" align=\"center\" width=\"80\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.subtotalScore\" placeholder=\"小计得分\" />\n                </template>\n              </el-table-column>\n<!--              <el-table-column label=\"年度得分\" align=\"center\" width=\"80\">-->\n<!--                <template slot-scope=\"scope\">-->\n<!--                  <el-input v-model=\"scope.row.annualScore\" placeholder=\"年度得分\" />-->\n<!--                </template>-->\n<!--              </el-table-column>-->\n            </el-table>\n          </el-tab-pane>\n\n          <el-tab-pane label=\"个性指标\">\n            <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAddIndividualRow\">添加个性指标</el-button>\n            <el-table :data=\"form.individualIndicators\" border>\n              <el-table-column label=\"序号\" align=\"center\" width=\"60\">\n                <template slot-scope=\"scope\">\n                  <span>{{ scope.row.serialNumber }}</span>\n                </template>\n              </el-table-column>\n<!--              <el-table-column label=\"指标内容\" align=\"center\">-->\n<!--                <template slot-scope=\"scope\">-->\n<!--                  <el-input v-model=\"scope.row.indicatorContent\" type=\"textarea\" placeholder=\"请输入指标内容\" />-->\n<!--                </template>-->\n<!--              </el-table-column>-->\n              <el-table-column label=\"评价标准\" align=\"center\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.evaluationStandard\" type=\"textarea\" placeholder=\"请输入评价标准\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"分值\" align=\"center\" width=\"80\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.score\" placeholder=\"分值\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"自评分值\" align=\"center\" width=\"80\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.selfScore\" placeholder=\"自评\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"科长点评\" align=\"center\" width=\"80\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.leaderScore\" placeholder=\"科长点评\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"分管领导点评\" align=\"center\" width=\"90\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.departmentScore\" placeholder=\"分管领导点评\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"领导小组评鉴\" align=\"center\" width=\"90\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.groupScore\" placeholder=\"领导小组评鉴\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"小计得分\" align=\"center\" width=\"80\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.subtotalScore\" placeholder=\"小计得分\" />\n                </template>\n              </el-table-column>\n<!--              <el-table-column label=\"年度得分\" align=\"center\" width=\"80\">-->\n<!--                <template slot-scope=\"scope\">-->\n<!--                  <el-input v-model=\"scope.row.annualScore\" placeholder=\"年度得分\" />-->\n<!--                </template>-->\n<!--              </el-table-column>-->\n              <el-table-column label=\"操作\" align=\"center\" width=\"60\">\n                <template slot-scope=\"scope\">\n                  <el-button type=\"danger\" icon=\"el-icon-delete\" size=\"mini\" @click=\"handleDeleteIndividualRow(scope.$index)\"></el-button>\n                </template>\n              </el-table-column>\n            </el-table>\n          </el-tab-pane>\n\n          <el-tab-pane label=\"激励约束指标\">\n            <el-table :data=\"form.incentiveIndicators\" border>\n              <el-table-column label=\"指标类型\" align=\"center\" width=\"120\">\n                <template slot-scope=\"scope\">\n                  <el-select v-model=\"scope.row.indicatorType\" placeholder=\"请选择指标类型\">\n                    <el-option\n                      v-for=\"dict in incentiveTypeOptions\"\n                      :key=\"dict.value\"\n                      :label=\"dict.label\"\n                      :value=\"dict.value\"\n                    ></el-option>\n                  </el-select>\n                </template>\n              </el-table-column>\n              <el-table-column label=\"具体事项描述\" align=\"center\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.indicatorContent\" type=\"textarea\" placeholder=\"请输入具体事项描述\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"评分细则对应标准\" align=\"center\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.evaluationStandard\" type=\"textarea\" placeholder=\"请输入评分细则对应标准\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"分值\" align=\"center\" width=\"80\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.score\" placeholder=\"分值\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"自评\" align=\"center\" width=\"80\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.selfScore\" placeholder=\"自评\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"科长点评\" align=\"center\" width=\"80\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.leaderScore\" placeholder=\"科长点评\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"分管领导点评\" align=\"center\" width=\"90\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.departmentScore\" placeholder=\"分管领导点评\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"领导小组评鉴\" align=\"center\" width=\"90\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.groupScore\" placeholder=\"领导小组评鉴\" />\n                </template>\n              </el-table-column>\n              <el-table-column label=\"小计得分\" align=\"center\" width=\"80\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.subtotalScore\" placeholder=\"小计得分\" />\n                </template>\n              </el-table-column>\n            \n            </el-table>\n          </el-tab-pane>\n        </el-tabs>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 导入对话框 -->\n    <el-dialog :title=\"upload.title\" :visible.sync=\"upload.open\" width=\"400px\" append-to-body>\n      <el-tabs v-model=\"upload.activeTab\">\n        <el-tab-pane label=\"Excel导入\" name=\"excel\">\n          <el-upload\n            ref=\"upload\"\n            :limit=\"1\"\n            accept=\".xlsx, .xls\"\n            :headers=\"upload.headers\"\n            :action=\"upload.url\"\n            :disabled=\"upload.isUploading\"\n            :on-progress=\"handleFileUploadProgress\"\n            :on-success=\"handleFileSuccess\"\n            :auto-upload=\"false\"\n            drag\n          >\n            <i class=\"el-icon-upload\"></i>\n            <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\n            <div class=\"el-upload__tip text-center\" slot=\"tip\">\n              <span>仅允许导入xls、xlsx格式文件。</span>\n              <el-checkbox v-model=\"upload.updateSupport\" />是否更新已经存在的数据\n            </div>\n          </el-upload>\n          <div slot=\"footer\" class=\"dialog-footer\">\n            <el-button type=\"primary\" @click=\"submitFileForm\">确 定</el-button>\n            <el-button @click=\"upload.open = false\">取 消</el-button>\n          </div>\n        </el-tab-pane>\n\n        <el-tab-pane label=\"Word导入\" name=\"word\">\n          <el-upload\n            ref=\"uploadWord\"\n            :limit=\"1\"\n            accept=\".doc, .docx\"\n            action=\"#\"\n            :on-success=\"handleWordSuccess\"\n            :on-error=\"handleWordError\"\n            :before-upload=\"beforeWordUpload\"\n            :http-request=\"uploadWordRequest\"\n            :auto-upload=\"true\"\n            drag\n          >\n            <i class=\"el-icon-upload\"></i>\n            <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\n            <div class=\"el-upload__tip text-center\" slot=\"tip\">\n              <span>仅允许导入doc、docx格式文件。</span>\n            </div>\n          </el-upload>\n        </el-tab-pane>\n      </el-tabs>\n    </el-dialog>\n  </div>\n</template>\n\n<style scoped lang=\"scss\">\n.el-tag + .el-tag {\n  margin-left: 10px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;AACA,IAAAA,cAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAAA,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,iBAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAX,IAAA,EAAAY,SAAA;QACAC,IAAA,EAAAD;MACA;MACA;MACAE,IAAA;MACA;MACAC,KAAA;QACAf,IAAA,GACA;UAAAgB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,IAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,QAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAE,MAAA;QACA;QACAV,IAAA;QACA;QACAD,KAAA;QACA;QACAY,WAAA;QACA;QACAC,OAAA;UAAAC,aAAA,kBAAAC,cAAA;QAAA;QACA;QACAC,GAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;QACA;QACAC,aAAA;QACA;QACAC,SAAA;MACA;MACA;MACAC,iBAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACA;MACAC,oBAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA;IAEA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,iBACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAApC,OAAA;MACA,IAAAqC,gCAAA,OAAA5B,WAAA,EAAA6B,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAA9B,iBAAA,GAAAiC,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAA/B,KAAA,GAAAkC,QAAA,CAAAlC,KAAA;QACA+B,KAAA,CAAApC,OAAA;MACA;IACA;IACA;IACAyC,MAAA,WAAAA,OAAA;MACA,KAAAjC,IAAA;MACA,KAAAkC,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAA9B,IAAA;QACA+B,EAAA,EAAAjC,SAAA;QACAZ,IAAA,EAAAY,SAAA;QACAkC,QAAA,EAAAlC,SAAA;QACAC,IAAA,MAAAkC,IAAA,GAAAC,WAAA;QACA7B,QAAA,EAAAP,SAAA;QACAqC,UAAA,EAAArC,SAAA;QACAsC,cAAA,EAAAtC,SAAA;QACAuC,gBAAA,EAAAvC,SAAA;QACAwC,MAAA;QACAC,gBAAA,GACA;UAAAC,aAAA;UAAAC,gBAAA;UAAAC,kBAAA;UAAAC,KAAA;UAAAC,SAAA;UAAAC,WAAA;UAAAC,eAAA;UAAAC,UAAA;UAAAC,aAAA;UAAAC,WAAA;QAAA,GACA;UAAAT,aAAA;UAAAC,gBAAA;UAAAC,kBAAA;UAAAC,KAAA;UAAAC,SAAA;UAAAC,WAAA;UAAAC,eAAA;UAAAC,UAAA;UAAAC,aAAA;UAAAC,WAAA;QAAA,GACA;UAAAT,aAAA;UAAAC,gBAAA;UAAAC,kBAAA;UAAAC,KAAA;UAAAC,SAAA;UAAAC,WAAA;UAAAC,eAAA;UAAAC,UAAA;UAAAC,aAAA;UAAAC,WAAA;QAAA,GACA;UAAAT,aAAA;UAAAC,gBAAA;UAAAC,kBAAA;UAAAC,KAAA;UAAAC,SAAA;UAAAC,WAAA;UAAAC,eAAA;UAAAC,UAAA;UAAAC,aAAA;UAAAC,WAAA;QAAA,EACA;QACAC,oBAAA;QACAC,mBAAA,GACA;UAAAX,aAAA;UAAAC,gBAAA;UAAAC,kBAAA;UAAAC,KAAA;UAAAC,SAAA;UAAAC,WAAA;UAAAC,eAAA;UAAAC,UAAA;UAAAC,aAAA;UAAAI,cAAA;QAAA,GACA;UAAAZ,aAAA;UAAAC,gBAAA;UAAAC,kBAAA;UAAAC,KAAA;UAAAC,SAAA;UAAAC,WAAA;UAAAC,eAAA;UAAAC,UAAA;UAAAC,aAAA;UAAAI,cAAA;QAAA;MAEA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAzD,WAAA,CAAA0D,OAAA;MACA,KAAAjC,OAAA;IACA;IACA,aACAkC,UAAA,WAAAA,WAAA;MACA,KAAAH,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAG,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAArE,GAAA,GAAAqE,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA7B,EAAA;MAAA;MACA,KAAAzC,MAAA,GAAAoE,SAAA,CAAAG,MAAA;MACA,KAAAtE,QAAA,IAAAmE,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAhC,KAAA;MACA,KAAAlC,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAoE,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAnC,KAAA;MACA,IAAAC,EAAA,GAAAiC,GAAA,CAAAjC,EAAA,SAAA1C,GAAA;MACA,IAAA6E,+BAAA,EAAAnC,EAAA,EAAAL,IAAA,WAAAC,QAAA;QACAsC,MAAA,CAAAjE,IAAA,GAAA2B,QAAA,CAAAxC,IAAA;QACA;QACA,KAAA8E,MAAA,CAAAjE,IAAA,CAAAuC,gBAAA,IAAA0B,MAAA,CAAAjE,IAAA,CAAAuC,gBAAA,CAAAsB,MAAA;UACAI,MAAA,CAAAjE,IAAA,CAAAuC,gBAAA,IACA;YAAAC,aAAA;YAAAC,gBAAA;YAAAC,kBAAA;YAAAC,KAAA;YAAAC,SAAA;YAAAC,WAAA;YAAAC,eAAA;YAAAC,UAAA;YAAAC,aAAA;YAAAC,WAAA;UAAA,GACA;YAAAT,aAAA;YAAAC,gBAAA;YAAAC,kBAAA;YAAAC,KAAA;YAAAC,SAAA;YAAAC,WAAA;YAAAC,eAAA;YAAAC,UAAA;YAAAC,aAAA;YAAAC,WAAA;UAAA,GACA;YAAAT,aAAA;YAAAC,gBAAA;YAAAC,kBAAA;YAAAC,KAAA;YAAAC,SAAA;YAAAC,WAAA;YAAAC,eAAA;YAAAC,UAAA;YAAAC,aAAA;YAAAC,WAAA;UAAA,GACA;YAAAT,aAAA;YAAAC,gBAAA;YAAAC,kBAAA;YAAAC,KAAA;YAAAC,SAAA;YAAAC,WAAA;YAAAC,eAAA;YAAAC,UAAA;YAAAC,aAAA;YAAAC,WAAA;UAAA,EACA;QACA;QACA,KAAAgB,MAAA,CAAAjE,IAAA,CAAAmD,mBAAA,IAAAc,MAAA,CAAAjE,IAAA,CAAAmD,mBAAA,CAAAU,MAAA;UACAI,MAAA,CAAAjE,IAAA,CAAAmD,mBAAA,IACA;YAAAX,aAAA;YAAAC,gBAAA;YAAAC,kBAAA;YAAAC,KAAA;YAAAC,SAAA;YAAAC,WAAA;YAAAC,eAAA;YAAAC,UAAA;YAAAC,aAAA;YAAAI,cAAA;UAAA,GACA;YAAAZ,aAAA;YAAAC,gBAAA;YAAAC,kBAAA;YAAAC,KAAA;YAAAC,SAAA;YAAAC,WAAA;YAAAC,eAAA;YAAAC,UAAA;YAAAC,aAAA;YAAAI,cAAA;UAAA,EACA;QACA;QACA;QACA,KAAAa,MAAA,CAAAjE,IAAA,CAAAkD,oBAAA;UACAe,MAAA,CAAAjE,IAAA,CAAAkD,oBAAA;QACA;QACAe,MAAA,CAAArE,IAAA;QACAqE,MAAA,CAAAtE,KAAA;MACA;IACA;IACA,WACAwE,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAApE,IAAA,CAAA+B,EAAA;YACA,IAAAyC,kCAAA,EAAAJ,MAAA,CAAApE,IAAA,EAAA0B,IAAA,WAAAC,QAAA;cACAyC,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAxE,IAAA;cACAwE,MAAA,CAAA9C,OAAA;YACA;UACA;YACA,IAAAqD,+BAAA,EAAAP,MAAA,CAAApE,IAAA,EAAA0B,IAAA,WAAAC,QAAA;cACAyC,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAxE,IAAA;cACAwE,MAAA,CAAA9C,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAsD,YAAA,WAAAA,aAAAZ,GAAA;MAAA,IAAAa,MAAA;MACA,IAAAxF,GAAA,GAAA2E,GAAA,CAAAjC,EAAA,SAAA1C,GAAA;MACA,KAAAoF,MAAA,CAAAK,OAAA,sBAAAzF,GAAA,aAAAqC,IAAA;QACA,WAAAqD,+BAAA,EAAA1F,GAAA;MACA,GAAAqC,IAAA;QACAmD,MAAA,CAAAvD,OAAA;QACAuD,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAM,KAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAT,MAAA,CAAAK,OAAA,uBAAApD,IAAA;QACAwD,MAAA,CAAAT,MAAA,CAAArF,OAAA;QACA,IAAA+F,kCAAA,EAAAD,MAAA,CAAArF,WAAA,EAAA6B,IAAA,WAAAC,QAAA;UACAuD,MAAA,CAAAE,SAAA,CAAAC,KAAA,CAAA1D,QAAA;UACAuD,MAAA,CAAAT,MAAA,CAAAa,YAAA;QACA;MACA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAjF,MAAA,CAAAX,KAAA;MACA,KAAAW,MAAA,CAAAV,IAAA;IACA;IACA,aACA4F,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAAhB,MAAA,CAAArF,OAAA;MACA,IAAAsG,qCAAA,IAAAhE,IAAA,WAAAC,QAAA;QACA8D,MAAA,CAAAL,SAAA,CAAAO,MAAA,CAAAhE,QAAA;QACA8D,MAAA,CAAAhB,MAAA,CAAAa,YAAA;MACA;IACA;IACA;IACAM,wBAAA,WAAAA,yBAAAC,KAAA,EAAAC,IAAA,EAAAC,QAAA;MACA,KAAAzF,MAAA,CAAAC,WAAA;IACA;IACA;IACAyF,iBAAA,WAAAA,kBAAArE,QAAA,EAAAmE,IAAA,EAAAC,QAAA;MACA,KAAAzF,MAAA,CAAAV,IAAA;MACA,KAAAU,MAAA,CAAAC,WAAA;MACA,KAAA8D,KAAA,CAAA/D,MAAA,CAAA2F,UAAA;MACA,KAAAC,MAAA,CAAAvE,QAAA,CAAAwE,GAAA;QAAAC,wBAAA;MAAA;MACA,KAAA9E,OAAA;IACA;IACA;IACA+E,cAAA,WAAAA,eAAA;MACA,KAAAhC,KAAA,CAAA/D,MAAA,CAAAgG,MAAA;IACA;IACA;IACAC,gBAAA,WAAAA,iBAAAT,IAAA;MACA,IAAAU,MAAA,GAAAV,IAAA,CAAAW,IAAA;MACA,IAAAC,KAAA,GAAAZ,IAAA,CAAAW,IAAA;MACA,KAAAD,MAAA,KAAAE,KAAA;QACA,KAAAC,QAAA,CAAAC,KAAA;QACA;MACA;MACA;IACA;IACA;IACAC,iBAAA,WAAAA,kBAAAC,OAAA;MAAA,IAAAC,MAAA;MACA,KAAAtC,MAAA,CAAArF,OAAA;MACA,IAAA4H,sCAAA,EAAAF,OAAA,CAAAhB,IAAA,EAAApE,IAAA,WAAAC,QAAA;QACAoF,MAAA,CAAAtC,MAAA,CAAAa,YAAA;QACA,IAAA3D,QAAA,CAAAsF,IAAA;UACAF,MAAA,CAAAtC,MAAA,CAAAC,UAAA,CAAA/C,QAAA,CAAAwE,GAAA;UACAY,MAAA,CAAAzG,MAAA,CAAAV,IAAA;UACAmH,MAAA,CAAAzF,OAAA;QACA;UACAyF,MAAA,CAAAtC,MAAA,CAAAyC,QAAA,CAAAvF,QAAA,CAAAwE,GAAA;QACA;MACA,GAAAnB,KAAA,WAAA4B,KAAA;QACAG,MAAA,CAAAtC,MAAA,CAAAa,YAAA;QACAyB,MAAA,CAAAtC,MAAA,CAAAyC,QAAA,WAAAN,KAAA;MACA;IACA;IACA;IACAO,iBAAA,WAAAA,kBAAAxF,QAAA,EAAAmE,IAAA,EAAAC,QAAA;MACA,IAAApE,QAAA,CAAAsF,IAAA;QACA,KAAAxC,MAAA,CAAAC,UAAA,CAAA/C,QAAA,CAAAwE,GAAA;QACA,KAAA7F,MAAA,CAAAV,IAAA;QACA,KAAA0B,OAAA;MACA;QACA,KAAAmD,MAAA,CAAAyC,QAAA,CAAAvF,QAAA,CAAAwE,GAAA;MACA;IACA;IACA;IACAiB,eAAA,WAAAA,gBAAAC,GAAA;MACA,KAAA5C,MAAA,CAAAyC,QAAA;IACA;IACA,iBACAI,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MACA,SAAAlI,GAAA,CAAAwE,MAAA;QACA,KAAAY,MAAA,CAAAyC,QAAA;QACA;MACA;MACA,KAAAzC,MAAA,CAAAK,OAAA,wBAAApD,IAAA;QACA6F,MAAA,CAAA9C,MAAA,CAAArF,OAAA;QACA,IAAAoI,sCAAA,EAAAD,MAAA,CAAAlI,GAAA,EAAAqC,IAAA,WAAAC,QAAA;UACA4F,MAAA,CAAAnC,SAAA,CAAAO,MAAA,CAAAhE,QAAA;UACA4F,MAAA,CAAA9C,MAAA,CAAAa,YAAA;QACA;MACA;IACA;IACA,cACAmC,sBAAA,WAAAA,uBAAA;MACA,UAAAzH,IAAA,CAAAkD,oBAAA;QACA,KAAAlD,IAAA,CAAAkD,oBAAA;MACA;MACA,KAAAlD,IAAA,CAAAkD,oBAAA,CAAAwE,IAAA;QACAC,YAAA,OAAA3H,IAAA,CAAAkD,oBAAA,CAAAW,MAAA;QACArB,aAAA;QACAC,gBAAA;QACAC,kBAAA;QACAC,KAAA;QACAC,SAAA;QACAC,WAAA;QACAC,eAAA;QACAC,UAAA;QACAC,aAAA;QACAC,WAAA;MACA;IACA;IACA,cACA2E,yBAAA,WAAAA,0BAAAC,KAAA;MACA,KAAA7H,IAAA,CAAAkD,oBAAA,CAAA4E,MAAA,CAAAD,KAAA;MACA;MACA,KAAA7H,IAAA,CAAAkD,oBAAA,CAAA6E,OAAA,WAAAnE,IAAA,EAAAoE,CAAA;QACApE,IAAA,CAAA+D,YAAA,GAAAK,CAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}