{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/login.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/login.js", "mtime": 1753510684516}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCIvVXNlcnMvbWF6aWhhby9EZXNrdG9wL2Rldi9wZXJmb3JtYW5jZS9wZXJmb3JtYW5jZS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZ2V0Q29kZUltZyA9IGdldENvZGVJbWc7CmV4cG9ydHMuZ2V0SW5mbyA9IGdldEluZm87CmV4cG9ydHMubG9naW4gPSBsb2dpbjsKZXhwb3J0cy5sb2dvdXQgPSBsb2dvdXQ7CmV4cG9ydHMucmVnaXN0ZXIgPSByZWdpc3RlcjsKdmFyIF9yZXF1ZXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3V0aWxzL3JlcXVlc3QiKSk7Ci8vIOeZu+W9leaWueazlQpmdW5jdGlvbiBsb2dpbih1c2VybmFtZSwgcGFzc3dvcmQsIGNvZGUsIHV1aWQpIHsKICB2YXIgZGF0YSA9IHsKICAgIHVzZXJuYW1lOiB1c2VybmFtZSwKICAgIHBhc3N3b3JkOiBwYXNzd29yZCwKICAgIGNvZGU6IGNvZGUsCiAgICB1dWlkOiB1dWlkCiAgfTsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9sb2dpbicsCiAgICBoZWFkZXJzOiB7CiAgICAgIGlzVG9rZW46IGZhbHNlLAogICAgICByZXBlYXRTdWJtaXQ6IGZhbHNlCiAgICB9LAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOazqOWGjOaWueazlQpmdW5jdGlvbiByZWdpc3RlcihkYXRhKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvcmVnaXN0ZXInLAogICAgaGVhZGVyczogewogICAgICBpc1Rva2VuOiBmYWxzZQogICAgfSwKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDojrflj5bnlKjmiLfor6bnu4bkv6Hmga8KZnVuY3Rpb24gZ2V0SW5mbygpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9nZXRJbmZvJywKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQoKLy8g6YCA5Ye65pa55rOVCmZ1bmN0aW9uIGxvZ291dCgpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9sb2dvdXQnLAogICAgbWV0aG9kOiAncG9zdCcKICB9KTsKfQoKLy8g6I635Y+W6aqM6K+B56CBCmZ1bmN0aW9uIGdldENvZGVJbWcoKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvY2FwdGNoYUltYWdlJywKICAgIGhlYWRlcnM6IHsKICAgICAgaXNUb2tlbjogZmFsc2UsCiAgICAgIGlzQXV0aDogZmFsc2UKICAgIH0sCiAgICBtZXRob2Q6ICdnZXQnLAogICAgdGltZW91dDogMjAwMDAKICB9KTsKfQ=="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "login", "username", "password", "code", "uuid", "data", "request", "url", "headers", "isToken", "repeatSubmit", "method", "register", "getInfo", "logout", "getCodeImg", "isAuth", "timeout"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/login.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 登录方法\nexport function login(username, password, code, uuid) {\n  const data = {\n    username,\n    password,\n    code,\n    uuid\n  }\n  return request({\n    url: '/login',\n    headers: {\n      isToken: false,\n      repeatSubmit: false\n    },\n    method: 'post',\n    data: data\n  })\n}\n\n// 注册方法\nexport function register(data) {\n  return request({\n    url: '/register',\n    headers: {\n      isToken: false\n    },\n    method: 'post',\n    data: data\n  })\n}\n\n// 获取用户详细信息\nexport function getInfo() {\n  return request({\n    url: '/getInfo',\n    method: 'get'\n  })\n}\n\n// 退出方法\nexport function logout() {\n  return request({\n    url: '/logout',\n    method: 'post'\n  })\n}\n\n// 获取验证码\nexport function getCodeImg() {\n  return request({\n    url: '/captchaImage',\n    headers: {\n      isToken: false,\n      isAuth: false\n    },\n    method: 'get',\n    timeout: 20000\n  })\n}"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,KAAKA,CAACC,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,IAAI,EAAE;EACpD,IAAMC,IAAI,GAAG;IACXJ,QAAQ,EAARA,QAAQ;IACRC,QAAQ,EAARA,QAAQ;IACRC,IAAI,EAAJA,IAAI;IACJC,IAAI,EAAJA;EACF,CAAC;EACD,OAAO,IAAAE,gBAAO,EAAC;IACbC,GAAG,EAAE,QAAQ;IACbC,OAAO,EAAE;MACPC,OAAO,EAAE,KAAK;MACdC,YAAY,EAAE;IAChB,CAAC;IACDC,MAAM,EAAE,MAAM;IACdN,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASO,QAAQA,CAACP,IAAI,EAAE;EAC7B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,WAAW;IAChBC,OAAO,EAAE;MACPC,OAAO,EAAE;IACX,CAAC;IACDE,MAAM,EAAE,MAAM;IACdN,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,OAAOA,CAAA,EAAG;EACxB,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,UAAU;IACfI,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASG,MAAMA,CAAA,EAAG;EACvB,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,SAAS;IACdI,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,UAAUA,CAAA,EAAG;EAC3B,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe;IACpBC,OAAO,EAAE;MACPC,OAAO,EAAE,KAAK;MACdO,MAAM,EAAE;IACV,CAAC;IACDL,MAAM,EAAE,KAAK;IACbM,OAAO,EAAE;EACX,CAAC,CAAC;AACJ", "ignoreList": []}]}