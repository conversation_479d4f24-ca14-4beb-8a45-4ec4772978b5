{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/system/dict/type.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/system/dict/type.js", "mtime": 1753510684517}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listType", "query", "request", "url", "method", "params", "getType", "dictId", "addType", "data", "updateType", "delType", "refreshCache", "optionselect"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/system/dict/type.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询字典类型列表\nexport function listType(query) {\n  return request({\n    url: '/system/dict/type/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询字典类型详细\nexport function getType(dictId) {\n  return request({\n    url: '/system/dict/type/' + dictId,\n    method: 'get'\n  })\n}\n\n// 新增字典类型\nexport function addType(data) {\n  return request({\n    url: '/system/dict/type',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改字典类型\nexport function updateType(data) {\n  return request({\n    url: '/system/dict/type',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除字典类型\nexport function delType(dictId) {\n  return request({\n    url: '/system/dict/type/' + dictId,\n    method: 'delete'\n  })\n}\n\n// 刷新字典缓存\nexport function refreshCache() {\n  return request({\n    url: '/system/dict/type/refreshCache',\n    method: 'delete'\n  })\n}\n\n// 获取字典选择框列表\nexport function optionselect() {\n  return request({\n    url: '/system/dict/type/optionselect',\n    method: 'get'\n  })\n}"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,OAAOA,CAACC,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGI,MAAM;IAClCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,OAAOA,CAACJ,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGI,MAAM;IAClCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,YAAYA,CAAA,EAAG;EAC7B,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,YAAYA,CAAA,EAAG;EAC7B,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}