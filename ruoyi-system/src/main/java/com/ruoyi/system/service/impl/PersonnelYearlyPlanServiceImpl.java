package com.ruoyi.system.service.impl;

import com.ruoyi.system.domain.PersonnelYearlyPlan;
import com.ruoyi.system.domain.PersonnelYearlyPersonalIndicator;
import com.ruoyi.system.domain.PersonnelYearlyCommonIndicator;
import com.ruoyi.system.mapper.PersonnelYearlyPlanMapper;
import com.ruoyi.system.mapper.PersonnelYearlyPersonalIndicatorMapper;
import com.ruoyi.system.mapper.PersonnelYearlyCommonIndicatorMapper;
import com.ruoyi.system.service.IPersonnelYearlyPlanService;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * 个人年度绩效计划Service业务层处理
 */
@Service
public class PersonnelYearlyPlanServiceImpl implements IPersonnelYearlyPlanService {
    
    @Autowired
    private PersonnelYearlyPlanMapper personnelYearlyPlanMapper;
    
    @Autowired
    private PersonnelYearlyPersonalIndicatorMapper personalIndicatorMapper;
    
    @Autowired
    private PersonnelYearlyCommonIndicatorMapper commonIndicatorMapper;

    @Override
    public PersonnelYearlyPlan selectPersonnelYearlyPlanById(Long id) {
        return personnelYearlyPlanMapper.selectPersonnelYearlyPlanById(id);
    }

    @Override
    public List<PersonnelYearlyPlan> selectPersonnelYearlyPlanList(PersonnelYearlyPlan personnelYearlyPlan) {
        return personnelYearlyPlanMapper.selectPersonnelYearlyPlanList(personnelYearlyPlan);
    }

    @Override
    public int insertPersonnelYearlyPlan(PersonnelYearlyPlan personnelYearlyPlan) {
        return personnelYearlyPlanMapper.insertPersonnelYearlyPlan(personnelYearlyPlan);
    }

    @Override
    public int updatePersonnelYearlyPlan(PersonnelYearlyPlan personnelYearlyPlan) {
        return personnelYearlyPlanMapper.updatePersonnelYearlyPlan(personnelYearlyPlan);
    }

    @Override
    public int deletePersonnelYearlyPlanByIds(Long[] ids) {
        // 先删除关联的指标数据
        for (Long id : ids) {
            personalIndicatorMapper.deleteByPlanId(id);
            commonIndicatorMapper.deleteByPlanId(id);
        }
        return personnelYearlyPlanMapper.deletePersonnelYearlyPlanByIds(ids);
    }

    @Override
    public int deletePersonnelYearlyPlanById(Long id) {
        // 先删除关联的指标数据
        personalIndicatorMapper.deleteByPlanId(id);
        commonIndicatorMapper.deleteByPlanId(id);
        return personnelYearlyPlanMapper.deletePersonnelYearlyPlanById(id);
    }

    @Override
    public List<PersonnelYearlyPersonalIndicator> selectPersonalIndicatorsByPlanId(Long planId) {
        return personalIndicatorMapper.selectByPlanId(planId);
    }

    @Override
    public List<PersonnelYearlyCommonIndicator> selectCommonIndicatorsByPlanId(Long planId) {
        return commonIndicatorMapper.selectByPlanId(planId);
    }

    @Override
    public void importExcelData(MultipartFile file) throws Exception {
        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);

            // 从第二行提取姓名和科室信息
            String[] nameAndDept = extractNameAndDepartment(sheet);
            String name = nameAndDept[0];
            String department = nameAndDept[1];
            
            // 获取当前年份
            String currentYear = String.valueOf(java.time.Year.now().getValue());

            // 创建或获取年度计划
            PersonnelYearlyPlan plan = new PersonnelYearlyPlan();
            plan.setName(name);
            plan.setDepartment(department);
            plan.setYear(currentYear);
            
            // 插入主计划记录
            personnelYearlyPlanMapper.insertPersonnelYearlyPlan(plan);
            Long planId = plan.getId();

            // 解析个性指标（从第4行开始，直到遇到"共性指标"）
            List<PersonnelYearlyPersonalIndicator> personalIndicators = parsePersonalIndicators(sheet, planId);
            if (!personalIndicators.isEmpty()) {
                personalIndicatorMapper.insertBatch(personalIndicators);
            }

            // 解析共性指标
            List<PersonnelYearlyCommonIndicator> commonIndicators = parseCommonIndicators(sheet, planId);
            if (!commonIndicators.isEmpty()) {
                commonIndicatorMapper.insertBatch(commonIndicators);
            }
        }
    }

    @Override
    public void downloadExcelTemplate(HttpServletResponse response) throws Exception {
        // 读取模板文件
        InputStream templateStream = getClass().getResourceAsStream("/template/year_plan.xlsx");
        if (templateStream == null) {
            throw new FileNotFoundException("Excel模板文件不存在: /template/year_plan.xlsx");
        }

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=year_plan_template.xlsx");

        // 直接将模板文件内容写入响应
        try (InputStream inputStream = templateStream;
             OutputStream outputStream = response.getOutputStream()) {

            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            outputStream.flush();
        }
    }

    /**
     * 从Excel第二行提取姓名和科室信息
     * 格式：姓名: ${name} 科室: ${className}
     */
    private String[] extractNameAndDepartment(Sheet sheet) {
        Row nameRow = sheet.getRow(1); // 第二行
        String name = "未知";
        String department = "未知";

        if (nameRow != null) {
            // 遍历第二行的所有单元格，查找姓名和科室信息
            for (int i = 0; i < nameRow.getLastCellNum(); i++) {
                Cell cell = nameRow.getCell(i);
                if (cell != null) {
                    String cellValue = getCellValue(cell);

                    // 查找姓名信息 (格式: 姓名: xxx)
                    if (cellValue.contains("姓名:") || cellValue.contains("姓名：")) {
                        String[] parts = cellValue.split("[：:]");
                        if (parts.length > 1) {
                            name = parts[1].trim();
                        }
                    }

                    // 查找科室信息 (格式: 科室: xxx)
                    if (cellValue.contains("科室:") || cellValue.contains("科室：")) {
                        String[] parts = cellValue.split("[：:]");
                        if (parts.length > 1) {
                            department = parts[1].trim();
                        }
                    }
                }
            }
        }

        return new String[]{name, department};
    }

    /**
     * 解析个性指标
     * 表头：序号、任务类型、绩效任务、目标及措施、评价标准、分值或权重、责任分类、完成时限
     */
    private List<PersonnelYearlyPersonalIndicator> parsePersonalIndicators(Sheet sheet, Long planId) {
        List<PersonnelYearlyPersonalIndicator> indicators = new ArrayList<>();

        // 从第4行开始解析个性指标，直到遇到共性指标部分或空行
        for (int i = 3; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) continue;

            // 检查第一列序号
            Cell firstCell = row.getCell(0);
            String firstCellValue = getCellValue(firstCell);


            // 检查序号列是否为空或者是否为数字
            if (firstCell == null || firstCellValue.trim().isEmpty()) {
                continue;
            }

            // 只处理序号为数字的行（个性指标行）
            Integer seqNoInt = parseIntOrNull(firstCellValue);
            if (seqNoInt == null) {
                continue;
            }

            // 检查任务类型是否为空，如果为空则跳过该行
            String taskCategory = getCellValue(row.getCell(2));
            if (taskCategory == null || taskCategory.trim().isEmpty()) {
                continue;
            }

            PersonnelYearlyPersonalIndicator indicator = new PersonnelYearlyPersonalIndicator();
            indicator.setPlanId(planId);
            indicator.setSeqNo(firstCellValue); // 直接使用字符串值
            indicator.setTaskCategory(taskCategory); // 任务类型
            indicator.setPerformanceTask(getCellValue(row.getCell(3))); // 绩效任务
            indicator.setTargetMeasures(getCellValue(row.getCell(4))); // 目标及措施
            indicator.setEvaluationCriteria(getCellValue(row.getCell(5))); // 评价标准
            indicator.setScoreWeight(getCellValue(row.getCell(7))); // 分值或权重
            indicator.setResponsibilityCategory(getCellValue(row.getCell(8))); // 责任分类
            indicator.setCompletionTime(getCellValue(row.getCell(9))); // 完成时限

            indicators.add(indicator);
        }

        return indicators;
    }

    /**
     * 解析共性指标
     * 共性指标包括：政治表现、能力素质、精神状态、工作作风、廉洁自律
     */
    private List<PersonnelYearlyCommonIndicator> parseCommonIndicators(Sheet sheet, Long planId) {
        List<PersonnelYearlyCommonIndicator> indicators = new ArrayList<>();

        // 查找共性指标部分
        for (int i = 3; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) continue;

            // 检查评价标准列（第5列，索引4）是否包含共性指标关键词
            Cell evaluationCell = row.getCell(5);
            String evaluationValue = getCellValue(evaluationCell);

            // 判断是否为共性指标行
            if (evaluationValue.contains("政治表现") || evaluationValue.contains("能力素质") ||
                evaluationValue.contains("精神状态") || evaluationValue.contains("工作作风") ||
                evaluationValue.contains("廉洁自律")) {

                PersonnelYearlyCommonIndicator indicator = new PersonnelYearlyCommonIndicator();
                indicator.setPlanId(planId);

                // 获取序号
                Cell seqCell = row.getCell(0);
                indicator.setSeqNo(getCellValue(seqCell)); // 直接使用字符串值

                // 责任分类就是评价标准列的内容
                indicator.setResponsibilityCategory(String.valueOf(row.getCell(6)));

                // 分值或权重
                indicator.setScoreWeight(getCellValue(row.getCell(7)));

                // 完成时限
                indicator.setCompletionTime(getCellValue(row.getCell(9)));

                indicators.add(indicator);
            }
        }

        return indicators;
    }

    private String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf((long) cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    private Integer parseIntOrNull(String s) {
        try {
            return Integer.parseInt(s);
        } catch (Exception e) {
            return null;
        }
    }
}
