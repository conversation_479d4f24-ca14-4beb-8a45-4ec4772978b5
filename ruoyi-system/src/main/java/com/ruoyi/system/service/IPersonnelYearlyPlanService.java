package com.ruoyi.system.service;

import com.ruoyi.system.domain.PersonnelYearlyPlan;
import com.ruoyi.system.domain.PersonnelYearlyPersonalIndicator;
import com.ruoyi.system.domain.PersonnelYearlyCommonIndicator;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 个人年度绩效计划Service接口
 */
public interface IPersonnelYearlyPlanService {
    
    /**
     * 查询个人年度绩效计划
     */
    PersonnelYearlyPlan selectPersonnelYearlyPlanById(Long id);

    /**
     * 查询个人年度绩效计划列表
     */
    List<PersonnelYearlyPlan> selectPersonnelYearlyPlanList(PersonnelYearlyPlan personnelYearlyPlan);

    /**
     * 新增个人年度绩效计划
     */
    int insertPersonnelYearlyPlan(PersonnelYearlyPlan personnelYearlyPlan);

    /**
     * 修改个人年度绩效计划
     */
    int updatePersonnelYearlyPlan(PersonnelYearlyPlan personnelYearlyPlan);

    /**
     * 批量删除个人年度绩效计划
     */
    int deletePersonnelYearlyPlanByIds(Long[] ids);

    /**
     * 删除个人年度绩效计划信息
     */
    int deletePersonnelYearlyPlanById(Long id);
    
    /**
     * 根据计划ID查询个性指标
     */
    List<PersonnelYearlyPersonalIndicator> selectPersonalIndicatorsByPlanId(Long planId);
    
    /**
     * 根据计划ID查询共性指标
     */
    List<PersonnelYearlyCommonIndicator> selectCommonIndicatorsByPlanId(Long planId);
    
    /**
     * Excel导入
     */
    void importExcelData(MultipartFile file) throws Exception;
    
    /**
     * 下载Excel模板
     */
    void downloadExcelTemplate(HttpServletResponse response) throws Exception;
}
