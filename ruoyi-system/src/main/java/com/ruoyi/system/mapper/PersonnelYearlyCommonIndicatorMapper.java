package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.PersonnelYearlyCommonIndicator;
import java.util.List;

/**
 * 个人年度绩效计划共性指标Mapper接口
 */
public interface PersonnelYearlyCommonIndicatorMapper {
    
    /**
     * 查询共性指标
     */
    PersonnelYearlyCommonIndicator selectPersonnelYearlyCommonIndicatorById(Long id);

    /**
     * 查询共性指标列表
     */
    List<PersonnelYearlyCommonIndicator> selectPersonnelYearlyCommonIndicatorList(PersonnelYearlyCommonIndicator indicator);

    /**
     * 根据计划ID查询共性指标列表
     */
    List<PersonnelYearlyCommonIndicator> selectByPlanId(Long planId);

    /**
     * 新增共性指标
     */
    int insertPersonnelYearlyCommonIndicator(PersonnelYearlyCommonIndicator indicator);

    /**
     * 修改共性指标
     */
    int updatePersonnelYearlyCommonIndicator(PersonnelYearlyCommonIndicator indicator);

    /**
     * 删除共性指标
     */
    int deletePersonnelYearlyCommonIndicatorById(Long id);

    /**
     * 批量删除共性指标
     */
    int deletePersonnelYearlyCommonIndicatorByIds(Long[] ids);
    
    /**
     * 根据计划ID删除共性指标
     */
    int deleteByPlanId(Long planId);
    
    /**
     * 批量插入共性指标
     */
    int insertBatch(List<PersonnelYearlyCommonIndicator> indicators);
}
