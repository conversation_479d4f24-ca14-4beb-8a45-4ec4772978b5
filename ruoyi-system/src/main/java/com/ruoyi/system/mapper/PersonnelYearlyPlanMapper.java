package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.PersonnelYearlyPlan;
import java.util.List;

/**
 * 个人年度绩效计划Mapper接口
 */
public interface PersonnelYearlyPlanMapper {
    
    /**
     * 查询个人年度绩效计划
     */
    PersonnelYearlyPlan selectPersonnelYearlyPlanById(Long id);

    /**
     * 查询个人年度绩效计划列表
     */
    List<PersonnelYearlyPlan> selectPersonnelYearlyPlanList(PersonnelYearlyPlan personnelYearlyPlan);

    /**
     * 新增个人年度绩效计划
     */
    int insertPersonnelYearlyPlan(PersonnelYearlyPlan personnelYearlyPlan);

    /**
     * 修改个人年度绩效计划
     */
    int updatePersonnelYearlyPlan(PersonnelYearlyPlan personnelYearlyPlan);

    /**
     * 删除个人年度绩效计划
     */
    int deletePersonnelYearlyPlanById(Long id);

    /**
     * 批量删除个人年度绩效计划
     */
    int deletePersonnelYearlyPlanByIds(Long[] ids);
    
    /**
     * 批量插入个人年度绩效计划
     */
    int insertBatch(List<PersonnelYearlyPlan> plans);
}
