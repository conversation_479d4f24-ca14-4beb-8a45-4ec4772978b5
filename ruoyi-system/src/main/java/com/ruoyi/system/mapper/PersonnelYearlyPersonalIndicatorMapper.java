package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.PersonnelYearlyPersonalIndicator;
import java.util.List;

/**
 * 个人年度绩效计划个性指标Mapper接口
 */
public interface PersonnelYearlyPersonalIndicatorMapper {
    
    /**
     * 查询个性指标
     */
    PersonnelYearlyPersonalIndicator selectPersonnelYearlyPersonalIndicatorById(Long id);

    /**
     * 查询个性指标列表
     */
    List<PersonnelYearlyPersonalIndicator> selectPersonnelYearlyPersonalIndicatorList(PersonnelYearlyPersonalIndicator indicator);

    /**
     * 根据计划ID查询个性指标列表
     */
    List<PersonnelYearlyPersonalIndicator> selectByPlanId(Long planId);

    /**
     * 新增个性指标
     */
    int insertPersonnelYearlyPersonalIndicator(PersonnelYearlyPersonalIndicator indicator);

    /**
     * 修改个性指标
     */
    int updatePersonnelYearlyPersonalIndicator(PersonnelYearlyPersonalIndicator indicator);

    /**
     * 删除个性指标
     */
    int deletePersonnelYearlyPersonalIndicatorById(Long id);

    /**
     * 批量删除个性指标
     */
    int deletePersonnelYearlyPersonalIndicatorByIds(Long[] ids);
    
    /**
     * 根据计划ID删除个性指标
     */
    int deleteByPlanId(Long planId);
    
    /**
     * 批量插入个性指标
     */
    int insertBatch(List<PersonnelYearlyPersonalIndicator> indicators);
}
