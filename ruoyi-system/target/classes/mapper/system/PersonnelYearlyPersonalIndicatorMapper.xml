<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.PersonnelYearlyPersonalIndicatorMapper">
    
    <resultMap id="PersonnelYearlyPersonalIndicatorResult" type="com.ruoyi.system.domain.PersonnelYearlyPersonalIndicator">
        <id property="id" column="id" />
        <result property="planId" column="plan_id" />
        <result property="seqNo" column="seq_no" />
        <result property="taskCategory" column="task_category" />
        <result property="performanceTask" column="performance_task" />
        <result property="targetMeasures" column="target_measures" />
        <result property="evaluationCriteria" column="evaluation_criteria" />
        <result property="scoreWeight" column="score_weight" />
        <result property="responsibilityCategory" column="responsibility_category" />
        <result property="completionTime" column="completion_time" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="selectPersonnelYearlyPersonalIndicatorVo">
        select id, plan_id, seq_no, task_category, performance_task, target_measures, evaluation_criteria, score_weight, responsibility_category, completion_time, create_time, update_time
        from personnel_yearly_personal_indicator
    </sql>

    <select id="selectPersonnelYearlyPersonalIndicatorList" parameterType="com.ruoyi.system.domain.PersonnelYearlyPersonalIndicator" resultMap="PersonnelYearlyPersonalIndicatorResult">
        <include refid="selectPersonnelYearlyPersonalIndicatorVo"/>
        <where>  
            <if test="planId != null"> and plan_id = #{planId}</if>
            <if test="taskCategory != null and taskCategory != ''"> and task_category like concat('%', #{taskCategory}, '%')</if>
        </where>
        order by seq_no
    </select>
    
    <select id="selectPersonnelYearlyPersonalIndicatorById" parameterType="Long" resultMap="PersonnelYearlyPersonalIndicatorResult">
        <include refid="selectPersonnelYearlyPersonalIndicatorVo"/>
        where id = #{id}
    </select>
    
    <select id="selectByPlanId" parameterType="Long" resultMap="PersonnelYearlyPersonalIndicatorResult">
        <include refid="selectPersonnelYearlyPersonalIndicatorVo"/>
        where plan_id = #{planId}
        order by seq_no
    </select>
        
    <insert id="insertPersonnelYearlyPersonalIndicator" parameterType="com.ruoyi.system.domain.PersonnelYearlyPersonalIndicator" useGeneratedKeys="true" keyProperty="id">
        insert into personnel_yearly_personal_indicator
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="planId != null">plan_id,</if>
            <if test="seqNo != null">seq_no,</if>
            <if test="taskCategory != null and taskCategory != ''">task_category,</if>
            <if test="performanceTask != null and performanceTask != ''">performance_task,</if>
            <if test="targetMeasures != null and targetMeasures != ''">target_measures,</if>
            <if test="evaluationCriteria != null and evaluationCriteria != ''">evaluation_criteria,</if>
            <if test="scoreWeight != null and scoreWeight != ''">score_weight,</if>
            <if test="responsibilityCategory != null and responsibilityCategory != ''">responsibility_category,</if>
            <if test="completionTime != null and completionTime != ''">completion_time,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="planId != null">#{planId},</if>
            <if test="seqNo != null">#{seqNo},</if>
            <if test="taskCategory != null and taskCategory != ''">#{taskCategory},</if>
            <if test="performanceTask != null and performanceTask != ''">#{performanceTask},</if>
            <if test="targetMeasures != null and targetMeasures != ''">#{targetMeasures},</if>
            <if test="evaluationCriteria != null and evaluationCriteria != ''">#{evaluationCriteria},</if>
            <if test="scoreWeight != null and scoreWeight != ''">#{scoreWeight},</if>
            <if test="responsibilityCategory != null and responsibilityCategory != ''">#{responsibilityCategory},</if>
            <if test="completionTime != null and completionTime != ''">#{completionTime},</if>
            sysdate()
        </trim>
    </insert>

    <update id="updatePersonnelYearlyPersonalIndicator" parameterType="com.ruoyi.system.domain.PersonnelYearlyPersonalIndicator">
        update personnel_yearly_personal_indicator
        <trim prefix="SET" suffixOverrides=",">
            <if test="planId != null">plan_id = #{planId},</if>
            <if test="seqNo != null">seq_no = #{seqNo},</if>
            <if test="taskCategory != null and taskCategory != ''">task_category = #{taskCategory},</if>
            <if test="performanceTask != null and performanceTask != ''">performance_task = #{performanceTask},</if>
            <if test="targetMeasures != null and targetMeasures != ''">target_measures = #{targetMeasures},</if>
            <if test="evaluationCriteria != null and evaluationCriteria != ''">evaluation_criteria = #{evaluationCriteria},</if>
            <if test="scoreWeight != null and scoreWeight != ''">score_weight = #{scoreWeight},</if>
            <if test="responsibilityCategory != null and responsibilityCategory != ''">responsibility_category = #{responsibilityCategory},</if>
            <if test="completionTime != null and completionTime != ''">completion_time = #{completionTime},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePersonnelYearlyPersonalIndicatorById" parameterType="Long">
        delete from personnel_yearly_personal_indicator where id = #{id}
    </delete>

    <delete id="deletePersonnelYearlyPersonalIndicatorByIds" parameterType="String">
        delete from personnel_yearly_personal_indicator where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <delete id="deleteByPlanId" parameterType="Long">
        delete from personnel_yearly_personal_indicator where plan_id = #{planId}
    </delete>
    
    <insert id="insertBatch" parameterType="java.util.List">
        insert into personnel_yearly_personal_indicator (plan_id, seq_no, task_category, performance_task, target_measures, evaluation_criteria, score_weight, responsibility_category, completion_time, create_time, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.planId}, #{item.seqNo}, #{item.taskCategory}, #{item.performanceTask}, #{item.targetMeasures}, #{item.evaluationCriteria}, #{item.scoreWeight}, #{item.responsibilityCategory}, #{item.completionTime}, NOW(), NOW())
        </foreach>
    </insert>
</mapper>
