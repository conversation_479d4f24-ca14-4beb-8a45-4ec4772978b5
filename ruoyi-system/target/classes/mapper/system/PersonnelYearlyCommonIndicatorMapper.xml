<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.PersonnelYearlyCommonIndicatorMapper">
    
    <resultMap id="PersonnelYearlyCommonIndicatorResult" type="com.ruoyi.system.domain.PersonnelYearlyCommonIndicator">
        <id property="id" column="id" />
        <result property="planId" column="plan_id" />
        <result property="seqNo" column="seq_no" />
        <result property="evaluationStandard" column="evaluation_standard" />
        <result property="responsibilityCategory" column="responsibility_category" />
        <result property="scoreWeight" column="score_weight" />
        <result property="completionTime" column="completion_time" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="selectPersonnelYearlyCommonIndicatorVo">
        select id, plan_id, seq_no, responsibility_category, evaluation_standard, score_weight, completion_time, create_time, update_time
        from personnel_yearly_common_indicator
    </sql>

    <select id="selectPersonnelYearlyCommonIndicatorList" parameterType="com.ruoyi.system.domain.PersonnelYearlyCommonIndicator" resultMap="PersonnelYearlyCommonIndicatorResult">
        <include refid="selectPersonnelYearlyCommonIndicatorVo"/>
        <where>  
            <if test="planId != null"> and plan_id = #{planId}</if>
            <if test="responsibilityCategory != null and responsibilityCategory != ''"> and responsibility_category like concat('%', #{responsibilityCategory}, '%')</if>
        </where>
        order by seq_no
    </select>
    
    <select id="selectPersonnelYearlyCommonIndicatorById" parameterType="Long" resultMap="PersonnelYearlyCommonIndicatorResult">
        <include refid="selectPersonnelYearlyCommonIndicatorVo"/>
        where id = #{id}
    </select>
    
    <select id="selectByPlanId" parameterType="Long" resultMap="PersonnelYearlyCommonIndicatorResult">
        <include refid="selectPersonnelYearlyCommonIndicatorVo"/>
        where plan_id = #{planId}
        order by seq_no
    </select>
        
    <insert id="insertPersonnelYearlyCommonIndicator" parameterType="com.ruoyi.system.domain.PersonnelYearlyCommonIndicator" useGeneratedKeys="true" keyProperty="id">
        insert into personnel_yearly_common_indicator
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="planId != null">plan_id,</if>
            <if test="seqNo != null">seq_no,</if>
            <if test="responsibilityCategory != null and responsibilityCategory != ''">responsibility_category,</if>
            <if test="scoreWeight != null and scoreWeight != ''">score_weight,</if>
            <if test="completionTime != null and completionTime != ''">completion_time,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="planId != null">#{planId},</if>
            <if test="seqNo != null">#{seqNo},</if>
            <if test="responsibilityCategory != null and responsibilityCategory != ''">#{responsibilityCategory},</if>
            <if test="scoreWeight != null and scoreWeight != ''">#{scoreWeight},</if>
            <if test="completionTime != null and completionTime != ''">#{completionTime},</if>
            sysdate()
        </trim>
    </insert>

    <update id="updatePersonnelYearlyCommonIndicator" parameterType="com.ruoyi.system.domain.PersonnelYearlyCommonIndicator">
        update personnel_yearly_common_indicator
        <trim prefix="SET" suffixOverrides=",">
            <if test="planId != null">plan_id = #{planId},</if>
            <if test="seqNo != null">seq_no = #{seqNo},</if>
            <if test="responsibilityCategory != null and responsibilityCategory != ''">responsibility_category = #{responsibilityCategory},</if>
            <if test="scoreWeight != null and scoreWeight != ''">score_weight = #{scoreWeight},</if>
            <if test="completionTime != null and completionTime != ''">completion_time = #{completionTime},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePersonnelYearlyCommonIndicatorById" parameterType="Long">
        delete from personnel_yearly_common_indicator where id = #{id}
    </delete>

    <delete id="deletePersonnelYearlyCommonIndicatorByIds" parameterType="String">
        delete from personnel_yearly_common_indicator where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <delete id="deleteByPlanId" parameterType="Long">
        delete from personnel_yearly_common_indicator where plan_id = #{planId}
    </delete>
    
    <insert id="insertBatch" parameterType="java.util.List">
        insert into personnel_yearly_common_indicator (plan_id, seq_no, responsibility_category, evaluation_standard, score_weight, completion_time, create_time, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.planId}, #{item.seqNo}, #{item.responsibilityCategory},#{item.evaluationStandard}, #{item.scoreWeight}, #{item.completionTime}, NOW(), NOW())
        </foreach>
    </insert>
</mapper>
