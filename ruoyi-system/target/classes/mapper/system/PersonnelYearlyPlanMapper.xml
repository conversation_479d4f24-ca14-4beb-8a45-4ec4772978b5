<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.PersonnelYearlyPlanMapper">
    
    <resultMap id="PersonnelYearlyPlanResult" type="com.ruoyi.system.domain.PersonnelYearlyPlan">
        <id property="id" column="id" />
        <result property="name" column="name" />
        <result property="department" column="department" />
        <result property="year" column="year" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
        <result property="createBy" column="create_by" />
        <result property="updateBy" column="update_by" />
    </resultMap>

    <sql id="selectPersonnelYearlyPlanVo">
        select id, name, department, year, create_time, update_time, create_by, update_by from personnel_yearly_plan
    </sql>

    <select id="selectPersonnelYearlyPlanList" parameterType="com.ruoyi.system.domain.PersonnelYearlyPlan" resultMap="PersonnelYearlyPlanResult">
        <include refid="selectPersonnelYearlyPlanVo"/>
        <where>  
            <if test="name != null and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="department != null and department != ''"> and department like concat('%', #{department}, '%')</if>
            <if test="year != null"> and year = #{year}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectPersonnelYearlyPlanById" parameterType="Long" resultMap="PersonnelYearlyPlanResult">
        <include refid="selectPersonnelYearlyPlanVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertPersonnelYearlyPlan" parameterType="com.ruoyi.system.domain.PersonnelYearlyPlan" useGeneratedKeys="true" keyProperty="id">
        insert into personnel_yearly_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="department != null and department != ''">department,</if>
            <if test="year != null">year,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="department != null and department != ''">#{department},</if>
            <if test="year != null">#{year},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            sysdate()
        </trim>
    </insert>

    <update id="updatePersonnelYearlyPlan" parameterType="com.ruoyi.system.domain.PersonnelYearlyPlan">
        update personnel_yearly_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="department != null and department != ''">department = #{department},</if>
            <if test="year != null">year = #{year},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePersonnelYearlyPlanById" parameterType="Long">
        delete from personnel_yearly_plan where id = #{id}
    </delete>

    <delete id="deletePersonnelYearlyPlanByIds" parameterType="String">
        delete from personnel_yearly_plan where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <insert id="insertBatch" parameterType="java.util.List">
        insert into personnel_yearly_plan (name, department, year, create_time, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.name}, #{item.department}, #{item.year}, NOW(), NOW())
        </foreach>
    </insert>
</mapper>
