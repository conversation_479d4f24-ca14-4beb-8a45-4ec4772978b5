22:06:00.343 [Thread-44] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:06:00.352 [Thread-44] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-8} closing ...
22:06:00.353 [Thread-44] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-8} closed
22:06:00.963 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 47255 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazi<PERSON> in /Users/<USER>/Desktop/dev/performance)
22:06:00.964 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:06:01.652 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8080"]
22:06:01.652 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:06:01.652 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:06:01.656 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:06:02.231 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-9} inited
22:06:02.236 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Stopping service [Tomcat]
22:06:02.254 [Druid-ConnectionPool-Create-875838376] INFO  c.a.d.p.DruidAbstractDataSource - [setFailContinuous,1900] - {dataSource-9} failContinuous is true
22:07:28.592 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 47255 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:07:28.592 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:07:31.514 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8080"]
22:07:31.514 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:07:31.514 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:07:31.525 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
