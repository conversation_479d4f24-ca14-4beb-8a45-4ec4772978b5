21:20:31.750 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
21:20:56.466 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
21:20:56.468 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 24584 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by ma<PERSON><PERSON> in /Users/<USER>/Desktop/dev/performance)
21:20:56.468 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:20:58.538 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8080"]
21:20:58.539 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:20:58.539 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:20:58.579 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:20:59.246 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
21:21:01.338 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8080"]
21:21:01.344 [restartedMain] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:21:01.346 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
21:21:01.350 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
21:21:01.465 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Pausing ProtocolHandler ["http-nio-8080"]
21:21:01.465 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Stopping service [Tomcat]
21:21:01.467 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Stopping ProtocolHandler ["http-nio-8080"]
21:21:01.467 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Destroying ProtocolHandler ["http-nio-8080"]
21:22:01.644 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25238 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:22:01.644 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
21:22:01.646 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:22:02.658 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8080"]
21:22:02.659 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:22:02.659 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:22:02.688 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:22:03.271 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
21:22:05.176 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8080"]
21:22:05.181 [restartedMain] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:22:05.182 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
21:22:05.185 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
21:22:05.296 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Pausing ProtocolHandler ["http-nio-8080"]
21:22:05.296 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Stopping service [Tomcat]
21:22:05.297 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Stopping ProtocolHandler ["http-nio-8080"]
21:22:05.298 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Destroying ProtocolHandler ["http-nio-8080"]
21:22:53.168 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
21:22:53.168 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:22:53.169 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:22:54.036 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8080"]
21:22:54.037 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:22:54.037 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:22:54.064 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:22:54.564 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
21:22:56.294 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8080"]
21:22:56.420 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 3.397 seconds (JVM running for 4.192)
21:23:07.234 [http-nio-8080-exec-1] INFO  o.a.t.u.h.p.Cookie - [log,168] - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1750346069,1750383323,1750484999,1750832074] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
21:23:07.240 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
21:23:25.161 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[admin][Success][登录成功]
21:41:13.497 [Thread-11] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:41:13.500 [Thread-11] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
21:41:13.523 [Thread-11] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
21:41:16.457 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:41:16.457 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:41:17.089 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:41:17.089 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:41:17.094 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:41:17.525 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
21:41:18.972 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.579 seconds (JVM running for 1106.719)
21:42:10.139 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
21:46:47.875 [Thread-19] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:46:47.877 [Thread-19] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-2} closing ...
21:46:47.880 [Thread-19] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-2} closed
21:46:48.237 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:46:48.237 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:46:48.570 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:46:48.570 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:46:48.573 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:46:48.872 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-3} inited
21:46:50.040 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.824 seconds (JVM running for 1437.78)
21:47:02.766 [Thread-26] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:47:02.770 [Thread-26] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-3} closing ...
21:47:02.773 [Thread-26] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-3} closed
21:47:03.183 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:47:03.184 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:47:03.498 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:47:03.498 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:47:03.500 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:47:03.703 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-4} inited
21:47:04.702 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.537 seconds (JVM running for 1452.441)
21:47:15.347 [Thread-30] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:47:15.353 [Thread-30] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-4} closing ...
21:47:15.362 [Thread-30] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-4} closed
21:47:15.762 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:47:15.762 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:47:16.084 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:47:16.084 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:47:16.087 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:47:16.294 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-5} inited
21:47:17.848 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.104 seconds (JVM running for 1465.587)
21:47:26.448 [Thread-34] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:47:26.451 [Thread-34] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-5} closing ...
21:47:26.454 [Thread-34] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-5} closed
21:47:26.879 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:47:26.879 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:47:27.159 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:47:27.159 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:47:27.161 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:47:27.406 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-6} inited
21:47:28.589 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.726 seconds (JVM running for 1476.328)
21:47:38.240 [Thread-38] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:47:38.254 [Thread-38] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-6} closing ...
21:47:38.258 [Thread-38] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-6} closed
21:47:38.666 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:47:38.666 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:47:38.999 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:47:38.999 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:47:39.002 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:47:39.223 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-7} inited
21:47:40.317 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.668 seconds (JVM running for 1488.056)
21:47:56.148 [Thread-42] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:47:56.153 [Thread-42] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-7} closing ...
21:47:56.157 [Thread-42] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-7} closed
21:47:56.655 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:47:56.655 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:47:56.989 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:47:56.989 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:47:56.991 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:47:57.205 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-8} inited
21:47:58.274 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.636 seconds (JVM running for 1506.012)
21:48:17.211 [Thread-46] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:48:17.215 [Thread-46] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-8} closing ...
21:48:17.217 [Thread-46] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-8} closed
21:48:17.663 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:48:17.663 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:48:17.975 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:48:17.975 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:48:17.985 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:48:18.224 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-9} inited
21:48:19.525 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.878 seconds (JVM running for 1527.263)
21:48:48.773 [Thread-50] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:48:48.777 [Thread-50] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-9} closing ...
21:48:48.781 [Thread-50] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-9} closed
21:48:49.170 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:48:49.170 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:48:49.482 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:48:49.482 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:48:49.484 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:48:49.711 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-10} inited
21:48:50.779 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.625 seconds (JVM running for 1558.516)
21:49:10.763 [Thread-54] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:49:10.768 [Thread-54] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-10} closing ...
21:49:10.771 [Thread-54] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-10} closed
21:49:11.330 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:49:11.330 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:49:11.901 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:49:11.901 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:49:11.904 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:49:12.161 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-11} inited
21:49:12.802 [restartedMain] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:49:12.802 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-11} closing ...
21:49:12.805 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-11} closed
21:49:12.913 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Stopping service [Tomcat]
21:49:42.219 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:49:42.219 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:49:42.547 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:49:42.547 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:49:42.550 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:49:42.755 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-12} inited
21:49:43.862 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.674 seconds (JVM running for 1611.598)
21:50:09.934 [Thread-58] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:50:09.939 [Thread-58] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-12} closing ...
21:50:09.944 [Thread-58] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-12} closed
21:50:10.595 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:50:10.595 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:50:11.172 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:50:11.172 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:50:11.176 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:50:11.453 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-13} inited
21:50:13.070 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.499 seconds (JVM running for 1640.806)
21:50:28.487 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
21:50:39.163 [Thread-65] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:50:39.166 [Thread-65] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-13} closing ...
21:50:39.169 [Thread-65] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-13} closed
21:50:39.916 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:50:39.916 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:50:40.413 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:50:40.414 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:50:40.419 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:50:40.675 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-14} inited
21:50:41.938 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.043 seconds (JVM running for 1669.673)
21:51:04.937 [Thread-69] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:51:04.941 [Thread-69] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-14} closing ...
21:51:04.944 [Thread-69] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-14} closed
21:51:05.503 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:51:05.503 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:51:05.845 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:51:05.845 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:51:05.854 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:51:06.099 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-15} inited
21:51:07.532 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.046 seconds (JVM running for 1695.266)
21:51:37.042 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:15:53.272 [Thread-11] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:15:53.275 [Thread-11] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
22:15:53.286 [Thread-11] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
22:15:53.666 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 43390 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:15:53.666 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:15:54.625 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8080"]
22:15:54.626 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:15:54.626 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:15:54.634 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:15:55.250 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
22:15:57.167 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8080"]
22:15:57.231 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 3.615 seconds (JVM running for 112.589)
22:16:00.890 [Thread-20] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:16:00.985 [Thread-20] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-2} closing ...
22:16:01.015 [Thread-20] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-2} closed
22:16:02.640 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 43390 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:16:02.640 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:16:04.393 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8080"]
22:16:04.394 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:16:04.395 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:16:04.403 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:16:05.081 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-3} inited
22:16:05.827 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-3} closing ...
22:16:05.829 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-3} closed
22:16:05.938 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Stopping service [Tomcat]
22:16:07.452 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 43390 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:16:07.452 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:16:07.934 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8080"]
22:16:07.934 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:16:07.934 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:16:07.937 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:16:08.161 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-4} inited
22:16:08.585 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-4} closing ...
22:16:08.586 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-4} closed
22:16:08.694 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Stopping service [Tomcat]
22:21:01.832 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 44979 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:21:01.833 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
22:21:01.833 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:21:02.835 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8080"]
22:21:02.836 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:21:02.836 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:21:02.865 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:21:03.417 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
22:21:05.177 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8080"]
22:21:05.303 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 3.641 seconds (JVM running for 4.414)
22:21:09.872 [http-nio-8080-exec-1] INFO  o.a.t.u.h.p.Cookie - [log,168] - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1750346069,1750383323,1750484999,1750832074;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
22:21:09.877 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:22:26.249 [Thread-11] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:22:26.259 [Thread-11] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
22:22:26.282 [Thread-11] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
22:22:26.830 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 44979 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:22:26.830 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:22:27.255 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8080"]
22:22:27.255 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:22:27.255 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:22:27.261 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:22:27.498 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
22:22:27.824 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-2} closing ...
22:22:27.825 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-2} closed
22:22:27.933 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Stopping service [Tomcat]
