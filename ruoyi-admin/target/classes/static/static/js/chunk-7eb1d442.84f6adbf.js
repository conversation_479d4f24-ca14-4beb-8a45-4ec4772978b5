(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7eb1d442"],{"5d2d":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"姓名",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入姓名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.name,callback:function(t){e.$set(e.queryParams,"name",t)},expression:"queryParams.name"}})],1),a("el-form-item",{attrs:{label:"任务类型",prop:"taskType"}},[a("el-input",{attrs:{placeholder:"请输入任务类型",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.taskType,callback:function(t){e.$set(e.queryParams,"taskType",t)},expression:"queryParams.taskType"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini",disabled:e.multiple},on:{click:e.handleExportSelected}},[e._v("导出Word")])],1),a("el-col",{attrs:{span:1.5}},[a("el-upload",{staticClass:"upload-demo",staticStyle:{display:"inline-block"},attrs:{action:e.importUrl,headers:e.uploadHeaders,"on-success":e.handleImportSuccess,"on-error":e.handleImportError,"before-upload":e.beforeImportUpload,"show-file-list":!1,data:e.importParams}},[a("el-button",{attrs:{type:"info",plain:"",icon:"el-icon-upload2",size:"mini"}},[e._v("导入Word")])],1)],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{attrs:{type:"info",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.downloadTemplate}},[e._v("下载模板")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.list},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{prop:"taskType",label:"任务类型",width:"120"}}),a("el-table-column",{attrs:{prop:"performanceTask",label:"绩效任务","min-width":"200","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"targetMeasures",label:"目标及措施","min-width":"200","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"evaluationCriteria",label:"评价标准","min-width":"200","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"taskCategory",label:"责任分类",width:"100"}}),a("el-table-column",{attrs:{prop:"weightScore",label:"权重分值",width:"100"}}),a("el-table-column",{attrs:{prop:"deadline",label:"完成时限",width:"120"}})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"900px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"姓名",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入姓名"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"任务类型",prop:"taskType"}},[a("el-input",{attrs:{placeholder:"请输入任务类型/类别"},model:{value:e.form.taskType,callback:function(t){e.$set(e.form,"taskType",t)},expression:"form.taskType"}})],1)],1)],1),a("el-form-item",{attrs:{label:"绩效任务",prop:"performanceTask"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入绩效任务"},model:{value:e.form.performanceTask,callback:function(t){e.$set(e.form,"performanceTask",t)},expression:"form.performanceTask"}})],1),a("el-form-item",{attrs:{label:"目标及措施",prop:"targetMeasures"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入目标及措施"},model:{value:e.form.targetMeasures,callback:function(t){e.$set(e.form,"targetMeasures",t)},expression:"form.targetMeasures"}})],1),a("el-form-item",{attrs:{label:"评价标准",prop:"evaluationCriteria"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入评价标准"},model:{value:e.form.evaluationCriteria,callback:function(t){e.$set(e.form,"evaluationCriteria",t)},expression:"form.evaluationCriteria"}})],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"责任",prop:"responsibility"}},[a("el-input",{attrs:{placeholder:"请输入责任"},model:{value:e.form.responsibility,callback:function(t){e.$set(e.form,"responsibility",t)},expression:"form.responsibility"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"责任分类",prop:"taskCategory"}},[a("el-input",{attrs:{placeholder:"请输入责任分类"},model:{value:e.form.taskCategory,callback:function(t){e.$set(e.form,"taskCategory",t)},expression:"form.taskCategory"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"权重分值",prop:"weightScore"}},[a("el-input",{attrs:{placeholder:"请输入权重分值"},model:{value:e.form.weightScore,callback:function(t){e.$set(e.form,"weightScore",t)},expression:"form.weightScore"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"完成时限",prop:"deadline"}},[a("el-input",{attrs:{placeholder:"请输入完成时限"},model:{value:e.form.deadline,callback:function(t){e.$set(e.form,"deadline",t)},expression:"form.deadline"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"所属组织",prop:"orgName"}},[a("el-input",{attrs:{placeholder:"请输入所属组织"},model:{value:e.form.orgName,callback:function(t){e.$set(e.form,"orgName",t)},expression:"form.orgName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"计划年份",prop:"planYear"}},[a("el-input",{attrs:{placeholder:"请输入计划年份"},model:{value:e.form.planYear,callback:function(t){e.$set(e.form,"planYear",t)},expression:"form.planYear"}})],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},o=[],l=(a("7db0"),a("d81d"),a("b0c0"),a("d3b7"),a("3ca3"),a("0643"),a("fffc"),a("a573"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("88a7"),a("271a"),a("5494"),a("b775"));function n(e){return Object(l["a"])({url:"/performance/leader/list",method:"get",params:e})}function i(e){return Object(l["a"])({url:"/performance/leader/"+e,method:"get"})}function s(e){return Object(l["a"])({url:"/performance/leader",method:"post",data:e})}function c(e){return Object(l["a"])({url:"/performance/leader",method:"put",data:e})}function m(e){return Object(l["a"])({url:"/performance/leader/"+e,method:"delete"})}function p(e){return Object(l["a"])({url:"/performance/leader/export/"+e,method:"get",responseType:"blob"})}function d(e){return Object(l["a"])({url:"/performance/leader/batchExport",method:"post",data:e,responseType:"blob"})}function u(){return Object(l["a"])({url:"/performance/leader/downloadTemplate",method:"get",responseType:"blob"})}var f={name:"LeaderPlan",data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,list:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,name:null,taskType:null,orgName:null,planYear:null},form:{},rules:{name:[{required:!0,message:"姓名不能为空",trigger:"blur"}],taskType:[{required:!0,message:"任务类型不能为空",trigger:"blur"}],performanceTask:[{required:!0,message:"绩效任务不能为空",trigger:"blur"}]},importUrl:"/prod-api/performance/leader/import",importParams:{planYear:"",orgName:""},uploadHeaders:{}}},created:function(){this.getList(),this.uploadHeaders={Authorization:"Bearer "+this.$store.getters.token}},methods:{getList:function(){var e=this;this.loading=!0,n(this.queryParams).then((function(t){e.list=t.rows,e.total=t.total,e.loading=!1}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,name:null,taskType:null,performanceTask:null,targetMeasures:null,evaluationCriteria:null,responsibility:null,taskCategory:null,weightScore:null,deadline:null,orgName:null,planYear:null},this.resetForm("form")},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加班子成员绩效计划"},handleEdit:function(e){var t=this;this.reset();var a=e.id;i(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改班子成员绩效计划"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?c(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):s(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认删除班子成员绩效计划编号为"'+a+'"的数据项？').then((function(){return m(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExportSelected:function(){var e=this;if(0!==this.ids.length)if(1!==this.ids.length)this.$modal.loading("正在导出数据，请稍候..."),d(this.ids).then((function(t){var a=new Blob([t],{type:"application/vnd.openxmlformats-officedocument.wordprocessingml.document"}),r=document.createElement("a");r.href=window.URL.createObjectURL(a),r.download="班子成员绩效计划.docx",r.click(),window.URL.revokeObjectURL(r.href),e.$modal.closeLoading()})).catch((function(){e.$modal.closeLoading()}));else{var t=this.list.find((function(t){return t.id===e.ids[0]}));this.handleExportSingle(t)}else this.$modal.msgError("请选择要导出的数据")},handleExportSingle:function(e){var t=this;this.$modal.loading("正在导出数据，请稍候..."),p(e.id).then((function(a){var r=new Blob([a],{type:"application/vnd.openxmlformats-officedocument.wordprocessingml.document"}),o=document.createElement("a");o.href=window.URL.createObjectURL(r),o.download="".concat(e.name||"班子成员","_绩效计划.docx"),o.click(),window.URL.revokeObjectURL(o.href),t.$modal.closeLoading()})).catch((function(){t.$modal.closeLoading()}))},beforeImportUpload:function(e){var t="application/vnd.openxmlformats-officedocument.wordprocessingml.document"===e.type;return t||this.$modal.msgError("只能上传Word文档格式文件!"),t},handleImportSuccess:function(e){200===e.code?(this.$modal.msgSuccess("导入成功"),this.getList()):this.$modal.msgError(e.msg||"导入失败")},handleImportError:function(e){console.error("导入失败:",e),401===e.status?(this.$modal.msgError("认证失败，请重新登录"),this.$store.dispatch("LogOut").then((function(){location.href="/login"}))):this.$modal.msgError("导入失败，请检查文件格式")},downloadTemplate:function(){u().then((function(e){var t=new Blob([e],{type:"application/vnd.openxmlformats-officedocument.wordprocessingml.document"}),a=document.createElement("a");a.href=window.URL.createObjectURL(t),a.download="班子成员绩效计划模板.docx",a.click(),window.URL.revokeObjectURL(a.href)}))}}},h=f,b=(a("dcb5"),a("2877")),g=Object(b["a"])(h,r,o,!1,null,"a668485e",null);t["default"]=g.exports},"7adc":function(e,t,a){},dcb5:function(e,t,a){"use strict";a("7adc")}}]);