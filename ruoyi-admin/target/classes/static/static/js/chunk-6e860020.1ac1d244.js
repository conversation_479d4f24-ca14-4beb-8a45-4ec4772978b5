(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6e860020"],{"27fb":function(e,t,a){},"2c50":function(e,t,a){"use strict";a("27fb")},ee4b:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"姓名",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入姓名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.name,callback:function(t){e.$set(e.queryParams,"name",t)},expression:"queryParams.name"}})],1),a("el-form-item",{attrs:{label:"科室",prop:"department"}},[a("el-input",{attrs:{placeholder:"请输入科室",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.department,callback:function(t){e.$set(e.queryParams,"department",t)},expression:"queryParams.department"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini",disabled:e.multiple},on:{click:e.handleExportSelected}},[e._v("导出Word")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),a("el-col",{attrs:{span:1.5}},[a("el-upload",{staticClass:"upload-demo",staticStyle:{display:"inline-block"},attrs:{action:e.importUrl,headers:e.uploadHeaders,"on-success":e.handleImportSuccess,"on-error":e.handleImportError,"before-upload":e.beforeImportUpload,"show-file-list":!1}},[a("el-button",{attrs:{type:"info",plain:"",icon:"el-icon-upload2",size:"mini"}},[e._v("导入Word")])],1)],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{attrs:{type:"info",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleDownloadTemplate}},[e._v("下载模板")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-card",{staticClass:"box-card",staticStyle:{"margin-bottom":"20px"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",{staticStyle:{"font-weight":"bold"}},[e._v("个人绩效计划列表")])]),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.planList,"highlight-current-row":"",height:300},on:{"selection-change":e.handleSelectionChange,"current-change":e.handleCurrentChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"姓名",align:"center",prop:"name",width:"100"}}),a("el-table-column",{attrs:{label:"科室",align:"center",prop:"department",width:"150"}}),a("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center",width:"200","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-s-data"},on:{click:function(a){return e.handleViewPersonalIndicators(t.row)}}},[e._v("个性指标")]),a("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-data-analysis"},on:{click:function(a){return e.handleViewCommonIndicators(t.row)}}},[e._v("共性指标")]),a("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"姓名",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入姓名"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),a("el-form-item",{attrs:{label:"科室",prop:"department"}},[a("el-input",{attrs:{placeholder:"请输入科室"},model:{value:e.form.department,callback:function(t){e.$set(e.form,"department",t)},expression:"form.department"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1),a("el-dialog",{attrs:{title:e.taskTitle,visible:e.taskOpen,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.taskOpen=t}}},[a("el-form",{ref:"taskForm",attrs:{model:e.taskForm,rules:e.taskRules,"label-width":"100px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"任务类型",prop:"taskType"}},[a("el-input",{attrs:{placeholder:"请输入任务类型"},model:{value:e.taskForm.taskType,callback:function(t){e.$set(e.taskForm,"taskType",t)},expression:"taskForm.taskType"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"分值",prop:"score"}},[a("el-input-number",{attrs:{min:0,max:100},model:{value:e.taskForm.score,callback:function(t){e.$set(e.taskForm,"score",t)},expression:"taskForm.score"}})],1)],1)],1),a("el-form-item",{attrs:{label:"任务内容",prop:"taskContent"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入任务内容"},model:{value:e.taskForm.taskContent,callback:function(t){e.$set(e.taskForm,"taskContent",t)},expression:"taskForm.taskContent"}})],1),a("el-form-item",{attrs:{label:"目标及措施",prop:"targetMeasures"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入目标及措施"},model:{value:e.taskForm.targetMeasures,callback:function(t){e.$set(e.taskForm,"targetMeasures",t)},expression:"taskForm.targetMeasures"}})],1),a("el-form-item",{attrs:{label:"评价标准",prop:"evaluationStandard"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入评价标准"},model:{value:e.taskForm.evaluationStandard,callback:function(t){e.$set(e.taskForm,"evaluationStandard",t)},expression:"taskForm.evaluationStandard"}})],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"责任人",prop:"responsible"}},[a("el-input",{attrs:{placeholder:"请输入责任人"},model:{value:e.taskForm.responsible,callback:function(t){e.$set(e.taskForm,"responsible",t)},expression:"taskForm.responsible"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"完成时限",prop:"deadline"}},[a("el-date-picker",{attrs:{type:"date",placeholder:"选择完成时限",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},model:{value:e.taskForm.deadline,callback:function(t){e.$set(e.taskForm,"deadline",t)},expression:"taskForm.deadline"}})],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitTaskForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancelTask}},[e._v("取 消")])],1)],1),a("el-dialog",{attrs:{title:"个性指标",visible:e.personalIndicatorOpen,width:"80%","append-to-body":""},on:{"update:visible":function(t){e.personalIndicatorOpen=t}}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.personalIndicatorList,border:""}},[a("el-table-column",{attrs:{label:"序号",type:"index",width:"60",align:"center"}}),a("el-table-column",{attrs:{label:"任务类型",prop:"taskType",width:"120",align:"center"}}),a("el-table-column",{attrs:{label:"绩效任务",prop:"taskContent","min-width":"200","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{label:"目标及措施",prop:"targetMeasures","min-width":"200","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{label:"评价标准",prop:"evaluationStandard","min-width":"200","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{label:"分值",prop:"score",width:"80",align:"center"}}),a("el-table-column",{attrs:{label:"责任人",prop:"responsible",width:"100",align:"center"}}),a("el-table-column",{attrs:{label:"完成时限",prop:"deadline",width:"120",align:"center"}})],1)],1),a("el-dialog",{attrs:{title:"共性指标",visible:e.commonIndicatorOpen,width:"60%","append-to-body":""},on:{"update:visible":function(t){e.commonIndicatorOpen=t}}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.commonIndicatorList,border:""}},[a("el-table-column",{attrs:{label:"序号",type:"index",width:"60",align:"center"}}),a("el-table-column",{attrs:{label:"责任分类",prop:"responsibilityCategory","min-width":"250","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{label:"分值",prop:"score",width:"80",align:"center"}}),a("el-table-column",{attrs:{label:"完成时限",prop:"deadline",width:"120",align:"center"}})],1)],1)],1)},o=[],r=(a("d81d"),a("d3b7"),a("3ca3"),a("0643"),a("a573"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("88a7"),a("271a"),a("5494"),a("b775"));function l(e){return Object(r["a"])({url:"/performance/personnel/list",method:"get",params:e})}function s(e){return Object(r["a"])({url:"/performance/personnel/"+e,method:"get"})}function i(e){return Object(r["a"])({url:"/performance/personnel",method:"post",data:e})}function c(e){return Object(r["a"])({url:"/performance/personnel",method:"put",data:e})}function d(e){return Object(r["a"])({url:"/performance/personnel/"+e,method:"delete"})}function m(e){return Object(r["a"])({url:"/performance/personnel/batchExport",method:"post",data:e,responseType:"blob"})}function u(){return Object(r["a"])({url:"/performance/personnel/downloadTemplate",method:"get",responseType:"blob"})}function p(e){return Object(r["a"])({url:"/performance/personnel/task/list",method:"get",params:e})}function h(e){return Object(r["a"])({url:"/performance/personnel/task/"+e,method:"get"})}function f(e){return Object(r["a"])({url:"/performance/personnel/task",method:"post",data:e})}function b(e){return Object(r["a"])({url:"/performance/personnel/task",method:"put",data:e})}function g(e){return Object(r["a"])({url:"/performance/personnel/task/"+e,method:"delete"})}var k={name:"PersonnelPlan",dicts:["performance_status"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,planList:[],taskList:[],personalIndicatorList:[],commonIndicatorList:[],currentPlan:null,title:"",taskTitle:"",open:!1,taskOpen:!1,personalIndicatorOpen:!1,commonIndicatorOpen:!1,queryParams:{pageNum:1,pageSize:10,name:null,department:null,status:null},form:{},taskForm:{},rules:{name:[{required:!0,message:"姓名不能为空",trigger:"blur"}],department:[{required:!0,message:"科室不能为空",trigger:"blur"}]},taskRules:{taskType:[{required:!0,message:"任务类型不能为空",trigger:"blur"}],taskContent:[{required:!0,message:"任务内容不能为空",trigger:"blur"}],score:[{required:!0,message:"分值不能为空",trigger:"blur"}]},importUrl:"/prod-api/performance/personnel/import",uploadHeaders:{}}},created:function(){this.getList(),this.uploadHeaders={Authorization:"Bearer "+this.$store.getters.token}},methods:{getList:function(){var e=this;this.loading=!0,l(this.queryParams).then((function(t){e.planList=t.rows,e.total=t.total,e.loading=!1}))},getTaskList:function(e){var t=this;e&&p({planId:e}).then((function(e){t.taskList=e.rows||[]}))},cancel:function(){this.open=!1,this.reset()},cancelTask:function(){this.taskOpen=!1,this.resetTask()},reset:function(){this.form={id:null,name:null,department:null,status:"DRAFT"},this.resetForm("form")},resetTask:function(){this.taskForm={id:null,planId:null,taskType:null,taskContent:null,targetMeasures:null,evaluationStandard:null,score:null,responsible:null,deadline:null},this.resetForm("taskForm")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleCurrentChange:function(e){this.currentPlan=e,e?this.getTaskList(e.id):this.taskList=[]},handleAdd:function(){this.reset(),this.open=!0,this.title="添加绩效计划"},handleUpdate:function(e){var t=this;this.reset();var a=e.id||this.ids;s(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改绩效计划"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?c(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):i(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认删除绩效计划编号为"'+a+'"的数据项？').then((function(){return d(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleAddTask:function(){this.currentPlan?(this.resetTask(),this.taskForm.planId=this.currentPlan.id,this.taskOpen=!0,this.taskTitle="添加绩效任务"):this.$modal.msgError("请先选择一个绩效计划")},handleUpdateTask:function(e){var t=this;this.resetTask();var a=e.id;h(a).then((function(e){t.taskForm=e.data,t.taskOpen=!0,t.taskTitle="修改绩效任务"}))},submitTaskForm:function(){var e=this;this.$refs["taskForm"].validate((function(t){t&&(null!=e.taskForm.id?b(e.taskForm).then((function(t){e.$modal.msgSuccess("修改成功"),e.taskOpen=!1,e.getTaskList(e.currentPlan.id)})):f(e.taskForm).then((function(t){e.$modal.msgSuccess("新增成功"),e.taskOpen=!1,e.getTaskList(e.currentPlan.id)})))}))},handleDeleteTask:function(e){var t=this,a=e.id;this.$modal.confirm("是否确认删除该任务数据项？").then((function(){return g(a)})).then((function(){t.getTaskList(t.currentPlan.id),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExportSelected:function(){var e=this;0!==this.ids.length?this.$modal.confirm("是否确认导出选中的"+this.ids.length+"条数据？").then((function(){e.$modal.loading("正在导出数据，请稍候..."),m(e.ids).then((function(t){var a=new Blob([t],{type:"application/vnd.openxmlformats-officedocument.wordprocessingml.document"}),n=document.createElement("a");n.href=window.URL.createObjectURL(a),n.download="个人绩效计划_"+(new Date).getTime()+".docx",n.click(),window.URL.revokeObjectURL(n.href),e.$modal.closeLoading(),e.$modal.msgSuccess("导出成功")})).catch((function(){e.$modal.closeLoading(),e.$modal.msgError("导出失败")}))})).catch((function(){})):this.$modal.msgWarning("请选择要导出的数据")},handleImportSuccess:function(e){200===e.code?(this.$modal.msgSuccess("导入成功"),this.getList()):this.$modal.msgError(e.msg||"导入失败")},handleImportError:function(e){console.error("导入失败:",e),401===e.status?(this.$modal.msgError("认证失败，请重新登录"),this.$store.dispatch("LogOut").then((function(){location.href="/login"}))):this.$modal.msgError("导入失败，请检查文件格式")},beforeImportUpload:function(e){var t="application/vnd.openxmlformats-officedocument.wordprocessingml.document"===e.type;t||this.$modal.msgError("只能上传Word文件！");var a=e.size/1024/1024<2;return a||this.$modal.msgError("上传文件大小不能超过 2MB!"),t&&a},handleDownloadTemplate:function(){var e=this;this.$modal.loading("正在下载模板，请稍候..."),u().then((function(t){var a=new Blob([t],{type:"application/vnd.openxmlformats-officedocument.wordprocessingml.document"}),n=document.createElement("a");n.href=window.URL.createObjectURL(a),n.download="个人绩效计划模板.docx",n.click(),window.URL.revokeObjectURL(n.href),e.$modal.closeLoading()})).catch((function(){e.$modal.closeLoading(),e.$modal.msgError("下载模板失败")}))},handleViewPersonalIndicators:function(e){var t=this;p({planId:e.id,indicatorType:1}).then((function(e){t.personalIndicatorList=e.rows||[],t.personalIndicatorOpen=!0})).catch((function(){t.$modal.msgError("获取个性指标数据失败")}))},handleViewCommonIndicators:function(e){var t=this;p({planId:e.id,indicatorType:2}).then((function(e){t.commonIndicatorList=e.rows||[],t.commonIndicatorOpen=!0})).catch((function(){t.$modal.msgError("获取共性指标数据失败")}))}}},w=k,y=(a("2c50"),a("2877")),v=Object(y["a"])(w,n,o,!1,null,"f996b82e",null);t["default"]=v.exports}}]);