(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5a77da25"],{"39fb":function(e,t,o){"use strict";o("665d")},"665d":function(e,t,o){},dfe4:function(e,t,o){"use strict";o.r(t);var r=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"app-container"},[o("el-row",{staticClass:"mb8",attrs:{gutter:10}},[o("el-col",{attrs:{span:1.5}},[o("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),o("el-col",{attrs:{span:1.5}},[o("el-button",{attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini",disabled:e.multiple},on:{click:e.handleExportSelected}},[e._v("导出Word")])],1),o("el-col",{attrs:{span:1.5}},[o("el-upload",{staticClass:"upload-demo",staticStyle:{display:"inline-block"},attrs:{action:e.importUrl,headers:e.uploadHeaders,"on-success":e.handleImportSuccess,"on-error":e.handleImportError,"before-upload":e.beforeImportUpload,"show-file-list":!1}},[o("el-button",{attrs:{type:"info",plain:"",icon:"el-icon-upload2",size:"mini"}},[e._v("导入Word")])],1)],1),o("el-col",{attrs:{span:1.5}},[o("el-button",{attrs:{type:"info",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleDownloadTemplate}},[e._v("下载模板")])],1)],1),o("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.planList,"row-key":"id",border:""},on:{"selection-change":e.handleSelectionChange}},[o("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),o("el-table-column",{attrs:{label:"任务类型",align:"center",prop:"taskType",width:"120"}}),o("el-table-column",{attrs:{label:"任务来源",align:"center",prop:"taskSource",width:"120"}}),o("el-table-column",{attrs:{label:"绩效任务",align:"center",prop:"performanceTask","min-width":"200","show-overflow-tooltip":""}}),o("el-table-column",{attrs:{label:"目标及措施",align:"center",prop:"targetMeasures","min-width":"200","show-overflow-tooltip":""}}),o("el-table-column",{attrs:{label:"责任科室",align:"center",prop:"responsibleDept",width:"120"}}),o("el-table-column",{attrs:{label:"分值及权重",align:"center",prop:"valueWeight",width:"120"}}),o("el-table-column",{attrs:{label:"责任领导",align:"center",prop:"responsibleLeader",width:"120"}}),o("el-table-column",{attrs:{label:"完成时限",align:"center",prop:"deadline",width:"120"}}),o("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["performance:plan:org:edit"],expression:"['performance:plan:org:edit']"}],attrs:{size:"small",type:"text",icon:"Edit"},on:{click:function(o){return e.handleUpdate(t.row)}}},[e._v("修改")]),o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["performance:plan:org:remove"],expression:"['performance:plan:org:remove']"}],attrs:{size:"small",type:"text",icon:"Delete"},on:{click:function(o){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),o("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),o("el-dialog",{attrs:{title:e.title,visible:e.open,width:"780px","append-to-body":""},on:{"update:visible":function(t){e.open=t}},scopedSlots:e._u([{key:"footer",fn:function(){return[o("div",{staticClass:"dialog-footer"},[o("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),o("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)]},proxy:!0}])},[o("el-form",{ref:"planRef",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[o("el-form-item",{attrs:{label:"任务类型",prop:"taskType"}},[o("el-input",{attrs:{placeholder:"请输入任务类型"},model:{value:e.form.taskType,callback:function(t){e.$set(e.form,"taskType",t)},expression:"form.taskType"}})],1),o("el-form-item",{attrs:{label:"任务来源",prop:"taskSource"}},[o("el-input",{attrs:{placeholder:"请输入任务来源"},model:{value:e.form.taskSource,callback:function(t){e.$set(e.form,"taskSource",t)},expression:"form.taskSource"}})],1),o("el-form-item",{attrs:{label:"绩效任务",prop:"performanceTask"}},[o("el-input",{attrs:{type:"textarea",placeholder:"请输入绩效任务"},model:{value:e.form.performanceTask,callback:function(t){e.$set(e.form,"performanceTask",t)},expression:"form.performanceTask"}})],1),o("el-form-item",{attrs:{label:"目标及措施",prop:"targetMeasures"}},[o("el-input",{attrs:{type:"textarea",placeholder:"请输入目标及措施"},model:{value:e.form.targetMeasures,callback:function(t){e.$set(e.form,"targetMeasures",t)},expression:"form.targetMeasures"}})],1),o("el-form-item",{attrs:{label:"责任科室",prop:"responsibleDept"}},[o("el-input",{attrs:{placeholder:"请输入责任科室"},model:{value:e.form.responsibleDept,callback:function(t){e.$set(e.form,"responsibleDept",t)},expression:"form.responsibleDept"}})],1),o("el-form-item",{attrs:{label:"分值及权重",prop:"valueWeight"}},[o("el-input",{attrs:{placeholder:"请输入分值及权重"},model:{value:e.form.valueWeight,callback:function(t){e.$set(e.form,"valueWeight",t)},expression:"form.valueWeight"}})],1),o("el-form-item",{attrs:{label:"责任领导",prop:"responsibleLeader"}},[o("el-input",{attrs:{placeholder:"请输入责任领导"},model:{value:e.form.responsibleLeader,callback:function(t){e.$set(e.form,"responsibleLeader",t)},expression:"form.responsibleLeader"}})],1),o("el-form-item",{attrs:{label:"完成时限",prop:"deadline"}},[o("el-input",{attrs:{placeholder:"请输入完成时限"},model:{value:e.form.deadline,callback:function(t){e.$set(e.form,"deadline",t)},expression:"form.deadline"}})],1)],1)],1)],1)},a=[],n=(o("d81d"),o("d3b7"),o("3ca3"),o("0643"),o("a573"),o("ddb0"),o("2b3d"),o("bf19"),o("9861"),o("88a7"),o("271a"),o("5494"),o("b775"));function l(e){return Object(n["a"])({url:"/performance/plan/org/list",method:"get",params:e})}function i(e){return Object(n["a"])({url:"/performance/plan/org/"+e,method:"delete"})}function s(e){return Object(n["a"])({url:"/performance/plan/org/"+e,method:"get"})}function c(e){return Object(n["a"])({url:"/performance/plan/org",method:"post",data:e})}function d(e){return Object(n["a"])({url:"/performance/plan/org",method:"put",data:e})}function p(){return Object(n["a"])({url:"/performance/plan/org/export",method:"get",responseType:"blob"})}function m(e){return Object(n["a"])({url:"/performance/plan/org/batchExport",method:"post",data:e,responseType:"blob"})}function u(){return Object(n["a"])({url:"/performance/plan/org/downloadTemplate",method:"get",responseType:"blob"})}var f={name:"OrgPlan",data:function(){return{planList:[],total:0,queryParams:{pageNum:1,pageSize:10},formVisible:!1,formTitle:"",form:{id:void 0,seq:"",taskType:"",taskSource:"",performanceTask:"",targetMeasures:"",responsibleDept:"",valueWeight:"",responsibleLeader:"",deadline:""},loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,title:"",open:!1,importUrl:"/prod-api/performance/plan/org/import",uploadHeaders:{},rules:{taskType:[{required:!0,message:"任务类型不能为空",trigger:"blur"}],taskSource:[{required:!0,message:"任务来源不能为空",trigger:"blur"}],performanceTask:[{required:!0,message:"绩效任务不能为空",trigger:"blur"}],targetMeasures:[{required:!0,message:"目标及措施不能为空",trigger:"blur"}],responsibleDept:[{required:!0,message:"责任科室不能为空",trigger:"blur"}],valueWeight:[{required:!0,message:"分值及权重不能为空",trigger:"blur"}],responsibleLeader:[{required:!0,message:"责任领导不能为空",trigger:"blur"}],deadline:[{required:!0,message:"完成时限不能为空",trigger:"blur"}]}}},created:function(){this.getList(),this.uploadHeaders={Authorization:"Bearer "+this.$store.getters.token}},methods:{getList:function(){var e=this;this.loading=!0,l(this.queryParams).then((function(t){e.planList=t.rows||t,e.total=t.total||t.length||0,e.loading=!1}))},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.title="新增绩效计划",this.form={id:void 0,seq:"",taskType:"",taskSource:"",performanceTask:"",targetMeasures:"",responsibleDept:"",valueWeight:"",responsibleLeader:"",deadline:""},this.open=!0},handleEdit:function(e){this.title="编辑绩效计划",this.form=Object.assign({},e),this.open=!0},handleDelete:function(e){var t=this,o=e.id||this.ids;this.$modal.confirm('是否确认删除绩效计划编号为"'+o+'"的数据项？').then((function(){return i(o)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExportSelected:function(){var e=this;0!==this.ids.length?this.$modal.confirm("是否确认导出选中的"+this.ids.length+"条绩效计划数据项？").then((function(){e.$modal.loading("正在导出数据，请稍候..."),m(e.ids).then((function(t){var o=new Blob([t],{type:"application/vnd.openxmlformats-officedocument.wordprocessingml.document"}),r=document.createElement("a");r.href=window.URL.createObjectURL(o),r.download="组织绩效计划_"+(new Date).getTime()+".docx",r.click(),window.URL.revokeObjectURL(r.href),e.$modal.closeLoading(),e.$modal.msgSuccess("导出成功")})).catch((function(){e.$modal.closeLoading(),e.$modal.msgError("导出失败")}))})).catch((function(){})):this.$modal.msgWarning("请选择要导出的数据")},handleExport:function(){var e=this;this.$modal.confirm("是否确认导出所有绩效计划数据项？").then((function(){e.$modal.loading("正在导出数据，请稍候..."),p().then((function(t){var o=new Blob([t],{type:"application/vnd.openxmlformats-officedocument.wordprocessingml.document"}),r=document.createElement("a");r.href=window.URL.createObjectURL(o),r.download="组织绩效计划.docx",r.click(),window.URL.revokeObjectURL(r.href),e.$modal.closeLoading()})).catch((function(){e.$modal.closeLoading()}))})).catch((function(){}))},handleDownloadTemplate:function(){var e=this;this.$modal.loading("正在下载模板，请稍候..."),u().then((function(t){var o=new Blob([t],{type:"application/vnd.openxmlformats-officedocument.wordprocessingml.document"}),r=document.createElement("a");r.href=window.URL.createObjectURL(o),r.download="组织绩效计划模板.docx",r.click(),window.URL.revokeObjectURL(r.href),e.$modal.closeLoading()})).catch((function(){e.$modal.closeLoading()}))},handleUpdate:function(e){var t=this;this.reset();var o=e.id||this.ids;s(o).then((function(e){t.form=e.data,t.open=!0,t.title="修改绩效计划"}))},submitForm:function(){var e=this;this.$refs["planRef"].validate((function(t){t&&(e.form.id?d(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):c(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleImportSuccess:function(e,t,o){200===e.code||"导入成功"===e?(this.$modal.msgSuccess("导入成功"),this.getList()):this.$modal.msgError(e.msg||"导入失败")},handleImportError:function(e){console.error("导入失败:",e),401===e.status?(this.$modal.msgError("认证失败，请重新登录"),this.$store.dispatch("LogOut").then((function(){location.href="/login"}))):this.$modal.msgError("导入失败，请检查文件格式")},beforeImportUpload:function(e){var t="application/vnd.openxmlformats-officedocument.wordprocessingml.document"===e.type;return t||this.$message.error("只能上传Word文件!"),t},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:void 0,seq:"",taskType:"",taskSource:"",performanceTask:"",targetMeasures:"",responsibleDept:"",valueWeight:"",responsibleLeader:"",deadline:""},this.resetForm("planRef")},formatJson:function(e,t){return t.map((function(t){return e.map((function(e){return t[e]}))}))}}},h=f,g=(o("39fb"),o("2877")),b=Object(g["a"])(h,r,a,!1,null,"63125512",null);t["default"]=b.exports}}]);