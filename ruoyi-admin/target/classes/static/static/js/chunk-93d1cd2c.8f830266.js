(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-93d1cd2c"],{"1f34":function(e,t,s){"use strict";s.r(t);var i=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"app-container"},[s("el-row",{attrs:{gutter:20}},[s("splitpanes",{staticClass:"default-theme",attrs:{horizontal:"mobile"===this.$store.getters.device}},[s("pane",{attrs:{size:"16"}},[s("el-col",[s("div",{staticClass:"head-container"},[s("el-input",{staticStyle:{"margin-bottom":"20px"},attrs:{placeholder:"请输入部门名称",clearable:"",size:"small","prefix-icon":"el-icon-search"},model:{value:e.deptName,callback:function(t){e.deptName=t},expression:"deptName"}})],1),s("div",{staticClass:"head-container"},[s("el-tree",{ref:"tree",attrs:{data:e.deptOptions,props:e.defaultProps,"expand-on-click-node":!1,"filter-node-method":e.filterNode,"node-key":"id","default-expand-all":"","highlight-current":""},on:{"node-click":e.handleNodeClick}})],1)])],1),s("pane",{attrs:{size:"84"}},[s("el-col",[s("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[s("el-form-item",{attrs:{label:"用户名称",prop:"userName"}},[s("el-input",{staticStyle:{width:"240px"},attrs:{placeholder:"请输入用户名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.userName,callback:function(t){e.$set(e.queryParams,"userName",t)},expression:"queryParams.userName"}})],1),s("el-form-item",{attrs:{label:"手机号码",prop:"phonenumber"}},[s("el-input",{staticStyle:{width:"240px"},attrs:{placeholder:"请输入手机号码",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.phonenumber,callback:function(t){e.$set(e.queryParams,"phonenumber",t)},expression:"queryParams.phonenumber"}})],1),s("el-form-item",{attrs:{label:"状态",prop:"status"}},[s("el-select",{staticStyle:{width:"240px"},attrs:{placeholder:"用户状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.sys_normal_disable,(function(e){return s("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),s("el-form-item",{attrs:{label:"创建时间"}},[s("el-date-picker",{staticStyle:{width:"240px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.dateRange,callback:function(t){e.dateRange=t},expression:"dateRange"}})],1),s("el-form-item",[s("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),s("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),s("el-row",{staticClass:"mb8",attrs:{gutter:10}},[s("el-col",{attrs:{span:1.5}},[s("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:add"],expression:"['system:user:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),s("el-col",{attrs:{span:1.5}},[s("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:edit"],expression:"['system:user:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改")])],1),s("el-col",{attrs:{span:1.5}},[s("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:remove"],expression:"['system:user:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),s("el-col",{attrs:{span:1.5}},[s("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:import"],expression:"['system:user:import']"}],attrs:{type:"info",plain:"",icon:"el-icon-upload2",size:"mini"},on:{click:e.handleImport}},[e._v("导入")])],1),s("el-col",{attrs:{span:1.5}},[s("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:export"],expression:"['system:user:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),s("right-toolbar",{attrs:{showSearch:e.showSearch,columns:e.columns},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),s("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.userList},on:{"selection-change":e.handleSelectionChange}},[s("el-table-column",{attrs:{type:"selection",width:"50",align:"center"}}),e.columns[0].visible?s("el-table-column",{key:"userId",attrs:{label:"用户编号",align:"center",prop:"userId"}}):e._e(),e.columns[1].visible?s("el-table-column",{key:"userName",attrs:{label:"用户名称",align:"center",prop:"userName","show-overflow-tooltip":!0}}):e._e(),e.columns[2].visible?s("el-table-column",{key:"nickName",attrs:{label:"用户昵称",align:"center",prop:"nickName","show-overflow-tooltip":!0}}):e._e(),e.columns[3].visible?s("el-table-column",{key:"deptName",attrs:{label:"部门",align:"center",prop:"dept.deptName","show-overflow-tooltip":!0}}):e._e(),e.columns[4].visible?s("el-table-column",{key:"phonenumber",attrs:{label:"手机号码",align:"center",prop:"phonenumber",width:"120"}}):e._e(),e.columns[5].visible?s("el-table-column",{key:"status",attrs:{label:"状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("el-switch",{attrs:{"active-value":"0","inactive-value":"1"},on:{change:function(s){return e.handleStatusChange(t.row)}},model:{value:t.row.status,callback:function(s){e.$set(t.row,"status",s)},expression:"scope.row.status"}})]}}],null,!1,3955094654)}):e._e(),e.columns[6].visible?s("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createTime",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("span",[e._v(e._s(e.parseTime(t.row.createTime)))])]}}],null,!1,3078210614)}):e._e(),s("el-table-column",{attrs:{label:"操作",align:"center",width:"160","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return 1!==t.row.userId?[s("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:edit"],expression:"['system:user:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(s){return e.handleUpdate(t.row)}}},[e._v("修改")]),s("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:remove"],expression:"['system:user:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(s){return e.handleDelete(t.row)}}},[e._v("删除")]),s("el-dropdown",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:resetPwd","system:user:edit"],expression:"['system:user:resetPwd', 'system:user:edit']"}],attrs:{size:"mini"},on:{command:function(s){return e.handleCommand(s,t.row)}}},[s("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-d-arrow-right"}},[e._v("更多")]),s("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[s("el-dropdown-item",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:resetPwd"],expression:"['system:user:resetPwd']"}],attrs:{command:"handleResetPwd",icon:"el-icon-key"}},[e._v("重置密码")]),s("el-dropdown-item",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:edit"],expression:"['system:user:edit']"}],attrs:{command:"handleAuthRole",icon:"el-icon-circle-check"}},[e._v("分配角色")])],1)],1)]:void 0}}],null,!0)})],1),s("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1)],1)],1)],1),s("el-dialog",{attrs:{title:e.title,visible:e.open,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[s("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[s("el-row",[s("el-col",{attrs:{span:12}},[s("el-form-item",{attrs:{label:"用户昵称",prop:"nickName"}},[s("el-input",{attrs:{placeholder:"请输入用户昵称",maxlength:"30"},model:{value:e.form.nickName,callback:function(t){e.$set(e.form,"nickName",t)},expression:"form.nickName"}})],1)],1),s("el-col",{attrs:{span:12}},[s("el-form-item",{attrs:{label:"归属部门",prop:"deptId"}},[s("treeselect",{attrs:{options:e.enabledDeptOptions,"show-count":!0,placeholder:"请选择归属部门"},model:{value:e.form.deptId,callback:function(t){e.$set(e.form,"deptId",t)},expression:"form.deptId"}})],1)],1)],1),s("el-row",[s("el-col",{attrs:{span:12}},[s("el-form-item",{attrs:{label:"手机号码",prop:"phonenumber"}},[s("el-input",{attrs:{placeholder:"请输入手机号码",maxlength:"11"},model:{value:e.form.phonenumber,callback:function(t){e.$set(e.form,"phonenumber",t)},expression:"form.phonenumber"}})],1)],1),s("el-col",{attrs:{span:12}},[s("el-form-item",{attrs:{label:"邮箱",prop:"email"}},[s("el-input",{attrs:{placeholder:"请输入邮箱",maxlength:"50"},model:{value:e.form.email,callback:function(t){e.$set(e.form,"email",t)},expression:"form.email"}})],1)],1)],1),s("el-row",[s("el-col",{attrs:{span:12}},[void 0==e.form.userId?s("el-form-item",{attrs:{label:"用户名称",prop:"userName"}},[s("el-input",{attrs:{placeholder:"请输入用户名称",maxlength:"30"},model:{value:e.form.userName,callback:function(t){e.$set(e.form,"userName",t)},expression:"form.userName"}})],1):e._e()],1),s("el-col",{attrs:{span:12}},[void 0==e.form.userId?s("el-form-item",{attrs:{label:"用户密码",prop:"password"}},[s("el-input",{attrs:{placeholder:"请输入用户密码",type:"password",maxlength:"20","show-password":""},model:{value:e.form.password,callback:function(t){e.$set(e.form,"password",t)},expression:"form.password"}})],1):e._e()],1)],1),s("el-row",[s("el-col",{attrs:{span:12}},[s("el-form-item",{attrs:{label:"用户性别"}},[s("el-select",{attrs:{placeholder:"请选择性别"},model:{value:e.form.sex,callback:function(t){e.$set(e.form,"sex",t)},expression:"form.sex"}},e._l(e.dict.type.sys_user_sex,(function(e){return s("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),s("el-col",{attrs:{span:12}},[s("el-form-item",{attrs:{label:"状态"}},[s("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},e._l(e.dict.type.sys_normal_disable,(function(t){return s("el-radio",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label))])})),1)],1)],1)],1),s("el-row",[s("el-col",{attrs:{span:12}},[s("el-form-item",{attrs:{label:"岗位"}},[s("el-select",{attrs:{multiple:"",placeholder:"请选择岗位"},model:{value:e.form.postIds,callback:function(t){e.$set(e.form,"postIds",t)},expression:"form.postIds"}},e._l(e.postOptions,(function(e){return s("el-option",{key:e.postId,attrs:{label:e.postName,value:e.postId,disabled:1==e.status}})})),1)],1)],1),s("el-col",{attrs:{span:12}},[s("el-form-item",{attrs:{label:"角色"}},[s("el-select",{attrs:{multiple:"",placeholder:"请选择角色"},model:{value:e.form.roleIds,callback:function(t){e.$set(e.form,"roleIds",t)},expression:"form.roleIds"}},e._l(e.roleOptions,(function(e){return s("el-option",{key:e.roleId,attrs:{label:e.roleName,value:e.roleId,disabled:1==e.status}})})),1)],1)],1)],1),s("el-row",[s("el-col",{attrs:{span:24}},[s("el-form-item",{attrs:{label:"备注"}},[s("el-input",{attrs:{type:"textarea",placeholder:"请输入内容"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1)],1)],1),s("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[s("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),s("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1),s("el-dialog",{attrs:{title:e.upload.title,visible:e.upload.open,width:"400px","append-to-body":""},on:{"update:visible":function(t){return e.$set(e.upload,"open",t)}}},[s("el-upload",{ref:"upload",attrs:{limit:1,accept:".xlsx, .xls",headers:e.upload.headers,action:e.upload.url+"?updateSupport="+e.upload.updateSupport,disabled:e.upload.isUploading,"on-progress":e.handleFileUploadProgress,"on-success":e.handleFileSuccess,"auto-upload":!1,drag:""}},[s("i",{staticClass:"el-icon-upload"}),s("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),s("em",[e._v("点击上传")])]),s("div",{staticClass:"el-upload__tip text-center",attrs:{slot:"tip"},slot:"tip"},[s("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[s("el-checkbox",{model:{value:e.upload.updateSupport,callback:function(t){e.$set(e.upload,"updateSupport",t)},expression:"upload.updateSupport"}}),e._v("是否更新已经存在的用户数据 ")],1),s("span",[e._v("仅允许导入xls、xlsx格式文件。")]),s("el-link",{staticStyle:{"font-size":"12px","vertical-align":"baseline"},attrs:{type:"primary",underline:!1},on:{click:e.importTemplate}},[e._v("下载模板")])],1)]),s("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[s("el-button",{attrs:{type:"primary"},on:{click:e.submitFileForm}},[e._v("确 定")]),s("el-button",{on:{click:function(t){e.upload.open=!1}}},[e._v("取 消")])],1)],1)],1)},n=[],a=s("5530"),r=(s("4de4"),s("d81d"),s("14d9"),s("e9c4"),s("b64b"),s("d3b7"),s("ac1f"),s("00b4"),s("0643"),s("2382"),s("a573"),s("c0c7")),o=s("5f87"),l=s("ca17"),u=s.n(l),d=(s("542c"),Object.defineProperty),p=Object.defineProperties,c=Object.getOwnPropertyDescriptors,m=Object.getOwnPropertySymbols,h=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable,v=(e,t,s)=>t in e?d(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s,b=(e,t)=>{for(var s in t||(t={}))h.call(t,s)&&v(e,s,t[s]);if(m)for(var s of m(t))f.call(t,s)&&v(e,s,t[s]);return e},g=(e,t)=>p(e,c(t)),y=(e,t)=>{var s={};for(var i in e)h.call(e,i)&&t.indexOf(i)<0&&(s[i]=e[i]);if(null!=e&&m)for(var i of m(e))t.indexOf(i)<0&&f.call(e,i)&&(s[i]=e[i]);return s};function x(e,t,s,i,n,a,r,o){var l,u="function"===typeof e?e.options:e;if(t&&(u.render=t,u.staticRenderFns=s,u._compiled=!0),i&&(u.functional=!0),a&&(u._scopeId="data-v-"+a),r?(l=function(e){e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,e||"undefined"===typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),n&&n.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(r)},u._ssrRegister=l):n&&(l=o?function(){n.call(this,(u.functional?this.parent:this).$root.$options.shadowRoot)}:n),l)if(u.functional){u._injectStyles=l;var d=u.render;u.render=function(e,t){return l.call(t),d(e,t)}}else{var p=u.beforeCreate;u.beforeCreate=p?[].concat(p,l):[l]}return{exports:e,options:u}}const P={name:"splitpanes",props:{horizontal:{type:Boolean},pushOtherPanes:{type:Boolean,default:!0},dblClickSplitter:{type:Boolean,default:!0},rtl:{type:Boolean,default:!1},firstSplitter:{type:Boolean}},provide(){return{requestUpdate:this.requestUpdate,onPaneAdd:this.onPaneAdd,onPaneRemove:this.onPaneRemove,onPaneClick:this.onPaneClick}},data:()=>({container:null,ready:!1,panes:[],touch:{mouseDown:!1,dragging:!1,activeSplitter:null},splitterTaps:{splitter:null,timeoutId:null}}),computed:{panesCount(){return this.panes.length},indexedPanes(){return this.panes.reduce((e,t)=>(e[t.id]=t)&&e,{})}},methods:{updatePaneComponents(){this.panes.forEach(e=>{e.update&&e.update({[this.horizontal?"height":"width"]:this.indexedPanes[e.id].size+"%"})})},bindEvents(){document.addEventListener("mousemove",this.onMouseMove,{passive:!1}),document.addEventListener("mouseup",this.onMouseUp),"ontouchstart"in window&&(document.addEventListener("touchmove",this.onMouseMove,{passive:!1}),document.addEventListener("touchend",this.onMouseUp))},unbindEvents(){document.removeEventListener("mousemove",this.onMouseMove,{passive:!1}),document.removeEventListener("mouseup",this.onMouseUp),"ontouchstart"in window&&(document.removeEventListener("touchmove",this.onMouseMove,{passive:!1}),document.removeEventListener("touchend",this.onMouseUp))},onMouseDown(e,t){this.bindEvents(),this.touch.mouseDown=!0,this.touch.activeSplitter=t},onMouseMove(e){this.touch.mouseDown&&(e.preventDefault(),this.touch.dragging=!0,this.calculatePanesSize(this.getCurrentMouseDrag(e)),this.$emit("resize",this.panes.map(e=>({min:e.min,max:e.max,size:e.size}))))},onMouseUp(){this.touch.dragging&&this.$emit("resized",this.panes.map(e=>({min:e.min,max:e.max,size:e.size}))),this.touch.mouseDown=!1,setTimeout(()=>{this.touch.dragging=!1,this.unbindEvents()},100)},onSplitterClick(e,t){"ontouchstart"in window&&(e.preventDefault(),this.dblClickSplitter&&(this.splitterTaps.splitter===t?(clearTimeout(this.splitterTaps.timeoutId),this.splitterTaps.timeoutId=null,this.onSplitterDblClick(e,t),this.splitterTaps.splitter=null):(this.splitterTaps.splitter=t,this.splitterTaps.timeoutId=setTimeout(()=>{this.splitterTaps.splitter=null},500)))),this.touch.dragging||this.$emit("splitter-click",this.panes[t])},onSplitterDblClick(e,t){let s=0;this.panes=this.panes.map((e,i)=>(e.size=i===t?e.max:e.min,i!==t&&(s+=e.min),e)),this.panes[t].size-=s,this.$emit("pane-maximize",this.panes[t])},onPaneClick(e,t){this.$emit("pane-click",this.indexedPanes[t])},getCurrentMouseDrag(e){const t=this.container.getBoundingClientRect(),{clientX:s,clientY:i}="ontouchstart"in window&&e.touches?e.touches[0]:e;return{x:s-t.left,y:i-t.top}},getCurrentDragPercentage(e){e=e[this.horizontal?"y":"x"];const t=this.container[this.horizontal?"clientHeight":"clientWidth"];return this.rtl&&!this.horizontal&&(e=t-e),100*e/t},calculatePanesSize(e){const t=this.touch.activeSplitter;let s={prevPanesSize:this.sumPrevPanesSize(t),nextPanesSize:this.sumNextPanesSize(t),prevReachedMinPanes:0,nextReachedMinPanes:0};const i=0+(this.pushOtherPanes?0:s.prevPanesSize),n=100-(this.pushOtherPanes?0:s.nextPanesSize),a=Math.max(Math.min(this.getCurrentDragPercentage(e),n),i);let r=[t,t+1],o=this.panes[r[0]]||null,l=this.panes[r[1]]||null;const u=o.max<100&&a>=o.max+s.prevPanesSize,d=l.max<100&&a<=100-(l.max+this.sumNextPanesSize(t+1));if(u||d)u?(o.size=o.max,l.size=Math.max(100-o.max-s.prevPanesSize-s.nextPanesSize,0)):(o.size=Math.max(100-l.max-s.prevPanesSize-this.sumNextPanesSize(t+1),0),l.size=l.max);else{if(this.pushOtherPanes){const e=this.doPushOtherPanes(s,a);if(!e)return;({sums:s,panesToResize:r}=e),o=this.panes[r[0]]||null,l=this.panes[r[1]]||null}null!==o&&(o.size=Math.min(Math.max(a-s.prevPanesSize-s.prevReachedMinPanes,o.min),o.max)),null!==l&&(l.size=Math.min(Math.max(100-a-s.nextPanesSize-s.nextReachedMinPanes,l.min),l.max))}},doPushOtherPanes(e,t){const s=this.touch.activeSplitter,i=[s,s+1];return t<e.prevPanesSize+this.panes[i[0]].min&&(i[0]=this.findPrevExpandedPane(s).index,e.prevReachedMinPanes=0,i[0]<s&&this.panes.forEach((t,n)=>{n>i[0]&&n<=s&&(t.size=t.min,e.prevReachedMinPanes+=t.min)}),e.prevPanesSize=this.sumPrevPanesSize(i[0]),void 0===i[0])?(e.prevReachedMinPanes=0,this.panes[0].size=this.panes[0].min,this.panes.forEach((t,i)=>{i>0&&i<=s&&(t.size=t.min,e.prevReachedMinPanes+=t.min)}),this.panes[i[1]].size=100-e.prevReachedMinPanes-this.panes[0].min-e.prevPanesSize-e.nextPanesSize,null):t>100-e.nextPanesSize-this.panes[i[1]].min&&(i[1]=this.findNextExpandedPane(s).index,e.nextReachedMinPanes=0,i[1]>s+1&&this.panes.forEach((t,n)=>{n>s&&n<i[1]&&(t.size=t.min,e.nextReachedMinPanes+=t.min)}),e.nextPanesSize=this.sumNextPanesSize(i[1]-1),void 0===i[1])?(e.nextReachedMinPanes=0,this.panes[this.panesCount-1].size=this.panes[this.panesCount-1].min,this.panes.forEach((t,i)=>{i<this.panesCount-1&&i>=s+1&&(t.size=t.min,e.nextReachedMinPanes+=t.min)}),this.panes[i[0]].size=100-e.prevPanesSize-e.nextReachedMinPanes-this.panes[this.panesCount-1].min-e.nextPanesSize,null):{sums:e,panesToResize:i}},sumPrevPanesSize(e){return this.panes.reduce((t,s,i)=>t+(i<e?s.size:0),0)},sumNextPanesSize(e){return this.panes.reduce((t,s,i)=>t+(i>e+1?s.size:0),0)},findPrevExpandedPane(e){const t=[...this.panes].reverse().find(t=>t.index<e&&t.size>t.min);return t||{}},findNextExpandedPane(e){const t=this.panes.find(t=>t.index>e+1&&t.size>t.min);return t||{}},checkSplitpanesNodes(){const e=Array.from(this.container.children);e.forEach(e=>{const t=e.classList.contains("splitpanes__pane"),s=e.classList.contains("splitpanes__splitter");if(!t&&!s)return e.parentNode.removeChild(e),void console.warn("Splitpanes: Only <pane> elements are allowed at the root of <splitpanes>. One of your DOM nodes was removed.")})},addSplitter(e,t,s=!1){const i=e-1,n=document.createElement("div");n.classList.add("splitpanes__splitter"),s||(n.onmousedown=e=>this.onMouseDown(e,i),"undefined"!==typeof window&&"ontouchstart"in window&&(n.ontouchstart=e=>this.onMouseDown(e,i)),n.onclick=e=>this.onSplitterClick(e,i+1)),this.dblClickSplitter&&(n.ondblclick=e=>this.onSplitterDblClick(e,i+1)),t.parentNode.insertBefore(n,t)},removeSplitter(e){e.onmousedown=void 0,e.onclick=void 0,e.ondblclick=void 0,e.parentNode.removeChild(e)},redoSplitters(){const e=Array.from(this.container.children);e.forEach(e=>{e.className.includes("splitpanes__splitter")&&this.removeSplitter(e)});let t=0;e.forEach(e=>{e.className.includes("splitpanes__pane")&&(!t&&this.firstSplitter?this.addSplitter(t,e,!0):t&&this.addSplitter(t,e),t++)})},requestUpdate(e){var t=e,{target:s}=t,i=y(t,["target"]);const n=this.indexedPanes[s._uid];Object.entries(i).forEach(([e,t])=>n[e]=t)},onPaneAdd(e){let t=-1;Array.from(e.$el.parentNode.children).some(s=>(s.className.includes("splitpanes__pane")&&t++,s===e.$el));const s=parseFloat(e.minSize),i=parseFloat(e.maxSize);this.panes.splice(t,0,{id:e._uid,index:t,min:isNaN(s)?0:s,max:isNaN(i)?100:i,size:null===e.size?null:parseFloat(e.size),givenSize:e.size,update:e.update}),this.panes.forEach((e,t)=>e.index=t),this.ready&&this.$nextTick(()=>{this.redoSplitters(),this.resetPaneSizes({addedPane:this.panes[t]}),this.$emit("pane-add",{index:t,panes:this.panes.map(e=>({min:e.min,max:e.max,size:e.size}))})})},onPaneRemove(e){const t=this.panes.findIndex(t=>t.id===e._uid),s=this.panes.splice(t,1)[0];this.panes.forEach((e,t)=>e.index=t),this.$nextTick(()=>{this.redoSplitters(),this.resetPaneSizes({removedPane:g(b({},s),{index:t})}),this.$emit("pane-remove",{removed:s,panes:this.panes.map(e=>({min:e.min,max:e.max,size:e.size}))})})},resetPaneSizes(e={}){e.addedPane||e.removedPane?this.panes.some(e=>null!==e.givenSize||e.min||e.max<100)?this.equalizeAfterAddOrRemove(e):this.equalize():this.initialPanesSizing(),this.ready&&this.$emit("resized",this.panes.map(e=>({min:e.min,max:e.max,size:e.size})))},equalize(){const e=100/this.panesCount;let t=0,s=[],i=[];this.panes.forEach(n=>{n.size=Math.max(Math.min(e,n.max),n.min),t-=n.size,n.size>=n.max&&s.push(n.id),n.size<=n.min&&i.push(n.id)}),t>.1&&this.readjustSizes(t,s,i)},initialPanesSizing(){this.panesCount;let e=100,t=[],s=[],i=0;this.panes.forEach(n=>{e-=n.size,null!==n.size&&i++,n.size>=n.max&&t.push(n.id),n.size<=n.min&&s.push(n.id)});let n=100;e>.1&&(this.panes.forEach(t=>{null===t.size&&(t.size=Math.max(Math.min(e/(this.panesCount-i),t.max),t.min)),n-=t.size}),n>.1&&this.readjustSizes(e,t,s))},equalizeAfterAddOrRemove({addedPane:e,removedPane:t}={}){let s=100/this.panesCount,i=0,n=[],a=[];e&&null!==e.givenSize&&(s=(100-e.givenSize)/(this.panesCount-1)),this.panes.forEach(e=>{i-=e.size,e.size>=e.max&&n.push(e.id),e.size<=e.min&&a.push(e.id)}),Math.abs(i)<.1||(this.panes.forEach(t=>{e&&null!==e.givenSize&&e.id===t.id||(t.size=Math.max(Math.min(s,t.max),t.min)),i-=t.size,t.size>=t.max&&n.push(t.id),t.size<=t.min&&a.push(t.id)}),i>.1&&this.readjustSizes(i,n,a))},readjustSizes(e,t,s){let i;i=e>0?e/(this.panesCount-t.length):e/(this.panesCount-s.length),this.panes.forEach((n,a)=>{if(e>0&&!t.includes(n.id)){const t=Math.max(Math.min(n.size+i,n.max),n.min),s=t-n.size;e-=s,n.size=t}else if(!s.includes(n.id)){const t=Math.max(Math.min(n.size+i,n.max),n.min),s=t-n.size;e-=s,n.size=t}n.update({[this.horizontal?"height":"width"]:this.indexedPanes[n.id].size+"%"})}),Math.abs(e)>.1&&this.$nextTick(()=>{this.ready&&console.warn("Splitpanes: Could not resize panes correctly due to their constraints.")})}},watch:{panes:{deep:!0,immediate:!1,handler(){this.updatePaneComponents()}},horizontal(){this.updatePaneComponents()},firstSplitter(){this.redoSplitters()},dblClickSplitter(e){const t=[...this.container.querySelectorAll(".splitpanes__splitter")];t.forEach((t,s)=>{t.ondblclick=e?e=>this.onSplitterDblClick(e,s):void 0})}},beforeDestroy(){this.ready=!1},mounted(){this.container=this.$refs.container,this.checkSplitpanesNodes(),this.redoSplitters(),this.resetPaneSizes(),this.$emit("ready"),this.ready=!0},render(e){return e("div",{ref:"container",class:["splitpanes","splitpanes--"+(this.horizontal?"horizontal":"vertical"),{"splitpanes--dragging":this.touch.dragging}]},this.$slots.default)}};let z,w;const S={};var k=x(P,z,w,!1,_,null,null,null);function _(e){for(let t in S)this[t]=S[t]}var N=function(){return k.exports}(),O=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"splitpanes__pane",style:e.style,on:{click:function(t){return e.onPaneClick(t,e._uid)}}},[e._t("default")],2)},C=[];const $={name:"pane",inject:["requestUpdate","onPaneAdd","onPaneRemove","onPaneClick"],props:{size:{type:[Number,String],default:null},minSize:{type:[Number,String],default:0},maxSize:{type:[Number,String],default:100}},data:()=>({style:{}}),mounted(){this.onPaneAdd(this)},beforeDestroy(){this.onPaneRemove(this)},methods:{update(e){this.style=e}},computed:{sizeNumber(){return this.size||0===this.size?parseFloat(this.size):null},minSizeNumber(){return parseFloat(this.minSize)},maxSizeNumber(){return parseFloat(this.maxSize)}},watch:{sizeNumber(e){this.requestUpdate({target:this,size:e})},minSizeNumber(e){this.requestUpdate({target:this,min:e})},maxSizeNumber(e){this.requestUpdate({target:this,max:e})}}},M={};var I=x($,O,C,!1,E,null,null,null);function E(e){for(let t in M)this[t]=M[t]}var R=function(){return I.exports}(),j=(s("c1ea"),{name:"User",dicts:["sys_normal_disable","sys_user_sex"],components:{Treeselect:u.a,Splitpanes:N,Pane:R},data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,userList:null,title:"",deptOptions:void 0,enabledDeptOptions:void 0,open:!1,deptName:void 0,initPassword:void 0,dateRange:[],postOptions:[],roleOptions:[],form:{},defaultProps:{children:"children",label:"label"},upload:{open:!1,title:"",isUploading:!1,updateSupport:0,headers:{Authorization:"Bearer "+Object(o["a"])()},url:"/prod-api/system/user/importData"},queryParams:{pageNum:1,pageSize:10,userName:void 0,phonenumber:void 0,status:void 0,deptId:void 0},columns:[{key:0,label:"用户编号",visible:!0},{key:1,label:"用户名称",visible:!0},{key:2,label:"用户昵称",visible:!0},{key:3,label:"部门",visible:!0},{key:4,label:"手机号码",visible:!0},{key:5,label:"状态",visible:!0},{key:6,label:"创建时间",visible:!0}],rules:{userName:[{required:!0,message:"用户名称不能为空",trigger:"blur"},{min:2,max:20,message:"用户名称长度必须介于 2 和 20 之间",trigger:"blur"}],nickName:[{required:!0,message:"用户昵称不能为空",trigger:"blur"}],password:[{required:!0,message:"用户密码不能为空",trigger:"blur"},{min:5,max:20,message:"用户密码长度必须介于 5 和 20 之间",trigger:"blur"},{pattern:/^[^<>"'|\\]+$/,message:"不能包含非法字符：< > \" ' \\ |",trigger:"blur"}],email:[{type:"email",message:"请输入正确的邮箱地址",trigger:["blur","change"]}],phonenumber:[{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"请输入正确的手机号码",trigger:"blur"}]}}},watch:{deptName:function(e){this.$refs.tree.filter(e)}},created:function(){var e=this;this.getList(),this.getDeptTree(),this.getConfigKey("sys.user.initPassword").then((function(t){e.initPassword=t.msg}))},methods:{getList:function(){var e=this;this.loading=!0,Object(r["h"])(this.addDateRange(this.queryParams,this.dateRange)).then((function(t){e.userList=t.rows,e.total=t.total,e.loading=!1}))},getDeptTree:function(){var e=this;Object(r["d"])().then((function(t){e.deptOptions=t.data,e.enabledDeptOptions=e.filterDisabledDept(JSON.parse(JSON.stringify(t.data)))}))},filterDisabledDept:function(e){var t=this;return e.filter((function(e){return!e.disabled&&(e.children&&e.children.length&&(e.children=t.filterDisabledDept(e.children)),!0)}))},filterNode:function(e,t){return!e||-1!==t.label.indexOf(e)},handleNodeClick:function(e){this.queryParams.deptId=e.id,this.handleQuery()},handleStatusChange:function(e){var t=this,s="0"===e.status?"启用":"停用";this.$modal.confirm('确认要"'+s+'""'+e.userName+'"用户吗？').then((function(){return Object(r["b"])(e.userId,e.status)})).then((function(){t.$modal.msgSuccess(s+"成功")})).catch((function(){e.status="0"===e.status?"1":"0"}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={userId:void 0,deptId:void 0,userName:void 0,nickName:void 0,password:void 0,phonenumber:void 0,email:void 0,sex:void 0,status:"0",remark:void 0,postIds:[],roleIds:[]},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.dateRange=[],this.resetForm("queryForm"),this.queryParams.deptId=void 0,this.$refs.tree.setCurrentKey(null),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.userId})),this.single=1!=e.length,this.multiple=!e.length},handleCommand:function(e,t){switch(e){case"handleResetPwd":this.handleResetPwd(t);break;case"handleAuthRole":this.handleAuthRole(t);break;default:break}},handleAdd:function(){var e=this;this.reset(),Object(r["f"])().then((function(t){e.postOptions=t.posts,e.roleOptions=t.roles,e.open=!0,e.title="添加用户",e.form.password=e.initPassword}))},handleUpdate:function(e){var t=this;this.reset();var s=e.userId||this.ids;Object(r["f"])(s).then((function(e){t.form=e.data,t.postOptions=e.posts,t.roleOptions=e.roles,t.$set(t.form,"postIds",e.postIds),t.$set(t.form,"roleIds",e.roleIds),t.open=!0,t.title="修改用户",t.form.password=""}))},handleResetPwd:function(e){var t=this;this.$prompt('请输入"'+e.userName+'"的新密码',"提示",{confirmButtonText:"确定",cancelButtonText:"取消",closeOnClickModal:!1,inputPattern:/^.{5,20}$/,inputErrorMessage:"用户密码长度必须介于 5 和 20 之间",inputValidator:function(e){if(/<|>|"|'|\||\\/.test(e))return"不能包含非法字符：< > \" ' \\ |"}}).then((function(s){var i=s.value;Object(r["i"])(e.userId,i).then((function(e){t.$modal.msgSuccess("修改成功，新密码是："+i)}))})).catch((function(){}))},handleAuthRole:function(e){var t=e.userId;this.$router.push("/system/user-auth/role/"+t)},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(void 0!=e.form.userId?Object(r["k"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(r["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,s=e.userId||this.ids;this.$modal.confirm('是否确认删除用户编号为"'+s+'"的数据项？').then((function(){return Object(r["c"])(s)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/user/export",Object(a["a"])({},this.queryParams),"user_".concat((new Date).getTime(),".xlsx"))},handleImport:function(){this.upload.title="用户导入",this.upload.open=!0},importTemplate:function(){this.download("system/user/importTemplate",{},"user_template_".concat((new Date).getTime(),".xlsx"))},handleFileUploadProgress:function(e,t,s){this.upload.isUploading=!0},handleFileSuccess:function(e,t,s){this.upload.open=!1,this.upload.isUploading=!1,this.$refs.upload.clearFiles(),this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>"+e.msg+"</div>","导入结果",{dangerouslyUseHTMLString:!0}),this.getList()},submitFileForm:function(){this.$refs.upload.submit()}}}),D=j,q=s("2877"),T=Object(q["a"])(D,i,n,!1,null,null,null);t["default"]=T.exports},c0c7:function(e,t,s){"use strict";s.d(t,"h",(function(){return a})),s.d(t,"f",(function(){return r})),s.d(t,"a",(function(){return o})),s.d(t,"k",(function(){return l})),s.d(t,"c",(function(){return u})),s.d(t,"i",(function(){return d})),s.d(t,"b",(function(){return p})),s.d(t,"g",(function(){return c})),s.d(t,"l",(function(){return m})),s.d(t,"m",(function(){return h})),s.d(t,"n",(function(){return f})),s.d(t,"e",(function(){return v})),s.d(t,"j",(function(){return b})),s.d(t,"d",(function(){return g}));var i=s("b775"),n=s("c38a");function a(e){return Object(i["a"])({url:"/system/user/list",method:"get",params:e})}function r(e){return Object(i["a"])({url:"/system/user/"+Object(n["e"])(e),method:"get"})}function o(e){return Object(i["a"])({url:"/system/user",method:"post",data:e})}function l(e){return Object(i["a"])({url:"/system/user",method:"put",data:e})}function u(e){return Object(i["a"])({url:"/system/user/"+e,method:"delete"})}function d(e,t){var s={userId:e,password:t};return Object(i["a"])({url:"/system/user/resetPwd",method:"put",data:s})}function p(e,t){var s={userId:e,status:t};return Object(i["a"])({url:"/system/user/changeStatus",method:"put",data:s})}function c(){return Object(i["a"])({url:"/system/user/profile",method:"get"})}function m(e){return Object(i["a"])({url:"/system/user/profile",method:"put",data:e})}function h(e,t){var s={oldPassword:e,newPassword:t};return Object(i["a"])({url:"/system/user/profile/updatePwd",method:"put",data:s})}function f(e){return Object(i["a"])({url:"/system/user/profile/avatar",method:"post",headers:{"Content-Type":"application/x-www-form-urlencoded"},data:e})}function v(e){return Object(i["a"])({url:"/system/user/authRole/"+e,method:"get"})}function b(e){return Object(i["a"])({url:"/system/user/authRole",method:"put",params:e})}function g(){return Object(i["a"])({url:"/system/user/deptTree",method:"get"})}},c1ea:function(e,t,s){}}]);