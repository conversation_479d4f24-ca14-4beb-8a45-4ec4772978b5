<!DOCTYPE html><html><head><meta charset=utf-8><meta http-equiv=X-UA-Compatible content="IE=edge,chrome=1"><meta name=renderer content=webkit><meta name=viewport content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no"><link rel=icon href=/favicon.ico><title>绩效管理系统</title><!--[if lt IE 11]><script>window.location.href='/html/ie.html';</script><![endif]--><style>html,
    body,
    #app {
      height: 100%;
      margin: 0px;
      padding: 0px;
    }
    .chromeframe {
      margin: 0.2em 0;
      background: #ccc;
      color: #000;
      padding: 0.2em 0;
    }

    #loader-wrapper {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 999999;
    }

    #loader {
      display: block;
      position: relative;
      left: 50%;
      top: 50%;
      width: 150px;
      height: 150px;
      margin: -75px 0 0 -75px;
      border-radius: 50%;
      border: 3px solid transparent;
      border-top-color: #FFF;
      -webkit-animation: spin 2s linear infinite;
      -ms-animation: spin 2s linear infinite;
      -moz-animation: spin 2s linear infinite;
      -o-animation: spin 2s linear infinite;
      animation: spin 2s linear infinite;
      z-index: 1001;
    }

    #loader:before {
      content: "";
      position: absolute;
      top: 5px;
      left: 5px;
      right: 5px;
      bottom: 5px;
      border-radius: 50%;
      border: 3px solid transparent;
      border-top-color: #FFF;
      -webkit-animation: spin 3s linear infinite;
      -moz-animation: spin 3s linear infinite;
      -o-animation: spin 3s linear infinite;
      -ms-animation: spin 3s linear infinite;
      animation: spin 3s linear infinite;
    }

    #loader:after {
      content: "";
      position: absolute;
      top: 15px;
      left: 15px;
      right: 15px;
      bottom: 15px;
      border-radius: 50%;
      border: 3px solid transparent;
      border-top-color: #FFF;
      -moz-animation: spin 1.5s linear infinite;
      -o-animation: spin 1.5s linear infinite;
      -ms-animation: spin 1.5s linear infinite;
      -webkit-animation: spin 1.5s linear infinite;
      animation: spin 1.5s linear infinite;
    }


    @-webkit-keyframes spin {
      0% {
        -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
      }
      100% {
        -webkit-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
      }
    }

    @keyframes spin {
      0% {
        -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
      }
      100% {
        -webkit-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
      }
    }


    #loader-wrapper .loader-section {
      position: fixed;
      top: 0;
      width: 51%;
      height: 100%;
      background: #7171C6;
      z-index: 1000;
      -webkit-transform: translateX(0);
      -ms-transform: translateX(0);
      transform: translateX(0);
    }

    #loader-wrapper .loader-section.section-left {
      left: 0;
    }

    #loader-wrapper .loader-section.section-right {
      right: 0;
    }


    .loaded #loader-wrapper .loader-section.section-left {
      -webkit-transform: translateX(-100%);
      -ms-transform: translateX(-100%);
      transform: translateX(-100%);
      -webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
      transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
    }

    .loaded #loader-wrapper .loader-section.section-right {
      -webkit-transform: translateX(100%);
      -ms-transform: translateX(100%);
      transform: translateX(100%);
      -webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
      transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
    }

    .loaded #loader {
      opacity: 0;
      -webkit-transition: all 0.3s ease-out;
      transition: all 0.3s ease-out;
    }

    .loaded #loader-wrapper {
      visibility: hidden;
      -webkit-transform: translateY(-100%);
      -ms-transform: translateY(-100%);
      transform: translateY(-100%);
      -webkit-transition: all 0.3s 1s ease-out;
      transition: all 0.3s 1s ease-out;
    }

    .no-js #loader-wrapper {
      display: none;
    }

    .no-js h1 {
      color: #222222;
    }

    #loader-wrapper .load_title {
      font-family: 'Open Sans';
      color: #FFF;
      font-size: 19px;
      width: 100%;
      text-align: center;
      z-index: 9999999999999;
      position: absolute;
      top: 60%;
      opacity: 1;
      line-height: 30px;
    }

    #loader-wrapper .load_title span {
      font-weight: normal;
      font-style: italic;
      font-size: 13px;
      color: #FFF;
      opacity: 0.5;
    }</style><link href=/static/css/chunk-libs.20020923.css rel=stylesheet><link href=/static/css/app.bfe8aa7a.css rel=stylesheet></head><body><div id=app><div id=loader-wrapper><div id=loader></div><div class="loader-section section-left"></div><div class="loader-section section-right"></div><div class=load_title>正在加载系统资源，请耐心等待</div></div></div><script>(function(c){function e(e){for(var d,u,h=e[0],t=e[1],r=e[2],k=0,b=[];k<h.length;k++)u=h[k],Object.prototype.hasOwnProperty.call(a,u)&&a[u]&&b.push(a[u][0]),a[u]=0;for(d in t)Object.prototype.hasOwnProperty.call(t,d)&&(c[d]=t[d]);o&&o(e);while(b.length)b.shift()();return f.push.apply(f,r||[]),n()}function n(){for(var c,e=0;e<f.length;e++){for(var n=f[e],d=!0,u=1;u<n.length;u++){var h=n[u];0!==a[h]&&(d=!1)}d&&(f.splice(e--,1),c=t(t.s=n[0]))}return c}var d={},u={runtime:0},a={runtime:0},f=[];function h(c){return t.p+"static/js/"+({}[c]||c)+"."+{"chunk-005cb0c7":"8661c9b2","chunk-0238e9b0":"5ab89f43","chunk-04621586":"58cc6a56","chunk-062d5123":"781d8e17","chunk-5a77da25":"6d8c253a","chunk-639d91a7":"37fe0ea5","chunk-6e860020":"1ac1d244","chunk-7eb1d442":"84f6adbf","chunk-18ed6a5e":"e0a4269f","chunk-1d7d97ec":"553344c8","chunk-0e410955":"6e9ce4f7","chunk-6a189a5c":"dff08de0","chunk-2b02de32":"5cfde907","chunk-469ba5ae":"e847de43","chunk-67d2aed9":"3d733c0b","chunk-6a438376":"88eefe10","chunk-9f8a6494":"181a359b","chunk-210ca3e9":"bb44f915","chunk-210ce324":"3f71df92","chunk-2727631f":"17d0e1be","chunk-2d0b2b28":"6267aaf1","chunk-9a4aac0a":"ff051efd","chunk-2d0bce05":"9451a366","chunk-2d0c8e18":"f14d4d9d","chunk-2d0da2ea":"a2d97f00","chunk-2d0f012d":"ddeb06bb","chunk-2d20955d":"92f9682d","chunk-2d230898":"3f4babf0","chunk-39413ce8":"f0c961da","chunk-3a08d90c":"e362d1d5","chunk-3b69bc00":"464372bc","chunk-46f2cf5c":"a7c949a1","chunk-4f55a4ac":"6bf7aec0","chunk-5885ca9b":"6843540b","chunk-27d58c84":"7abebd4b","chunk-5bb73842":"dfe42524","chunk-25ccdf6b":"c764420c","chunk-2d0d38ff":"82f5a2b8","chunk-2d0de3b1":"6a7cf79c","chunk-55be2047":"31413e2a","chunk-93d1cd2c":"8f830266","chunk-6746b265":"23ced9c2","chunk-7aad1943":"18ebc352","chunk-372e775c":"89e225da","chunk-698a5ba1":"55b12ddb","chunk-7abff893":"a56056ad","chunk-31eae13f":"cf85bbf8","chunk-3339808c":"f95d27a9","chunk-0d5b0085":"7bf1a366","chunk-60006966":"9950570a","chunk-91b39d22":"1284ae8e","chunk-7e203972":"71bd2059","chunk-8579d4da":"7fa73ac8","chunk-8ee3fc10":"052672fc","chunk-e1a6d904":"acfe37cf","chunk-e648d5fe":"23db6bd1","chunk-e69ed224":"6ed71408","chunk-ecddd398":"7924ae89","chunk-f82139da":"46676bed"}[c]+".js"}function t(e){if(d[e])return d[e].exports;var n=d[e]={i:e,l:!1,exports:{}};return c[e].call(n.exports,n,n.exports,t),n.l=!0,n.exports}t.e=function(c){var e=[],n={"chunk-5a77da25":1,"chunk-639d91a7":1,"chunk-6e860020":1,"chunk-7eb1d442":1,"chunk-18ed6a5e":1,"chunk-6a189a5c":1,"chunk-9a4aac0a":1,"chunk-46f2cf5c":1,"chunk-4f55a4ac":1,"chunk-5885ca9b":1,"chunk-5bb73842":1,"chunk-25ccdf6b":1,"chunk-93d1cd2c":1,"chunk-6746b265":1,"chunk-372e775c":1,"chunk-698a5ba1":1,"chunk-3339808c":1,"chunk-91b39d22":1,"chunk-e648d5fe":1,"chunk-f82139da":1};u[c]?e.push(u[c]):0!==u[c]&&n[c]&&e.push(u[c]=new Promise((function(e,n){for(var d="static/css/"+({}[c]||c)+"."+{"chunk-005cb0c7":"31d6cfe0","chunk-0238e9b0":"31d6cfe0","chunk-04621586":"31d6cfe0","chunk-062d5123":"31d6cfe0","chunk-5a77da25":"fd67c2fc","chunk-639d91a7":"4318d9e0","chunk-6e860020":"4dfac68d","chunk-7eb1d442":"6c1a0361","chunk-18ed6a5e":"db631dbc","chunk-1d7d97ec":"31d6cfe0","chunk-0e410955":"31d6cfe0","chunk-6a189a5c":"74496202","chunk-2b02de32":"31d6cfe0","chunk-469ba5ae":"31d6cfe0","chunk-67d2aed9":"31d6cfe0","chunk-6a438376":"31d6cfe0","chunk-9f8a6494":"31d6cfe0","chunk-210ca3e9":"31d6cfe0","chunk-210ce324":"31d6cfe0","chunk-2727631f":"31d6cfe0","chunk-2d0b2b28":"31d6cfe0","chunk-9a4aac0a":"763df9f6","chunk-2d0bce05":"31d6cfe0","chunk-2d0c8e18":"31d6cfe0","chunk-2d0da2ea":"31d6cfe0","chunk-2d0f012d":"31d6cfe0","chunk-2d20955d":"31d6cfe0","chunk-2d230898":"31d6cfe0","chunk-39413ce8":"31d6cfe0","chunk-3a08d90c":"31d6cfe0","chunk-3b69bc00":"31d6cfe0","chunk-46f2cf5c":"17fbdb6b","chunk-4f55a4ac":"5a402cd2","chunk-5885ca9b":"ce2a2394","chunk-27d58c84":"31d6cfe0","chunk-5bb73842":"84f98409","chunk-25ccdf6b":"ecea2c5f","chunk-2d0d38ff":"31d6cfe0","chunk-2d0de3b1":"31d6cfe0","chunk-55be2047":"31d6cfe0","chunk-93d1cd2c":"3ee89ce5","chunk-6746b265":"3e10cd59","chunk-7aad1943":"31d6cfe0","chunk-372e775c":"f2078e28","chunk-698a5ba1":"f2078e28","chunk-7abff893":"31d6cfe0","chunk-31eae13f":"31d6cfe0","chunk-3339808c":"6dfe926d","chunk-0d5b0085":"31d6cfe0","chunk-60006966":"31d6cfe0","chunk-91b39d22":"c5292c00","chunk-7e203972":"31d6cfe0","chunk-8579d4da":"31d6cfe0","chunk-8ee3fc10":"31d6cfe0","chunk-e1a6d904":"31d6cfe0","chunk-e648d5fe":"bbc9fa95","chunk-e69ed224":"31d6cfe0","chunk-ecddd398":"31d6cfe0","chunk-f82139da":"28732a69"}[c]+".css",a=t.p+d,f=document.getElementsByTagName("link"),h=0;h<f.length;h++){var r=f[h],k=r.getAttribute("data-href")||r.getAttribute("href");if("stylesheet"===r.rel&&(k===d||k===a))return e()}var b=document.getElementsByTagName("style");for(h=0;h<b.length;h++){r=b[h],k=r.getAttribute("data-href");if(k===d||k===a)return e()}var o=document.createElement("link");o.rel="stylesheet",o.type="text/css",o.onload=e,o.onerror=function(e){var d=e&&e.target&&e.target.src||a,f=new Error("Loading CSS chunk "+c+" failed.\n("+d+")");f.code="CSS_CHUNK_LOAD_FAILED",f.request=d,delete u[c],o.parentNode.removeChild(o),n(f)},o.href=a;var i=document.getElementsByTagName("head")[0];i.appendChild(o)})).then((function(){u[c]=0})));var d=a[c];if(0!==d)if(d)e.push(d[2]);else{var f=new Promise((function(e,n){d=a[c]=[e,n]}));e.push(d[2]=f);var r,k=document.createElement("script");k.charset="utf-8",k.timeout=120,t.nc&&k.setAttribute("nonce",t.nc),k.src=h(c);var b=new Error;r=function(e){k.onerror=k.onload=null,clearTimeout(o);var n=a[c];if(0!==n){if(n){var d=e&&("load"===e.type?"missing":e.type),u=e&&e.target&&e.target.src;b.message="Loading chunk "+c+" failed.\n("+d+": "+u+")",b.name="ChunkLoadError",b.type=d,b.request=u,n[1](b)}a[c]=void 0}};var o=setTimeout((function(){r({type:"timeout",target:k})}),12e4);k.onerror=k.onload=r,document.head.appendChild(k)}return Promise.all(e)},t.m=c,t.c=d,t.d=function(c,e,n){t.o(c,e)||Object.defineProperty(c,e,{enumerable:!0,get:n})},t.r=function(c){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(c,"__esModule",{value:!0})},t.t=function(c,e){if(1&e&&(c=t(c)),8&e)return c;if(4&e&&"object"===typeof c&&c&&c.__esModule)return c;var n=Object.create(null);if(t.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:c}),2&e&&"string"!=typeof c)for(var d in c)t.d(n,d,function(e){return c[e]}.bind(null,d));return n},t.n=function(c){var e=c&&c.__esModule?function(){return c["default"]}:function(){return c};return t.d(e,"a",e),e},t.o=function(c,e){return Object.prototype.hasOwnProperty.call(c,e)},t.p="/",t.oe=function(c){throw console.error(c),c};var r=window["webpackJsonp"]=window["webpackJsonp"]||[],k=r.push.bind(r);r.push=e,r=r.slice();for(var b=0;b<r.length;b++)e(r[b]);var o=k;n()})([]);</script><script src=/static/js/chunk-elementUI.ef7743f1.js></script><script src=/static/js/chunk-libs.521b4d64.js></script><script src=/static/js/app.56fb3937.js></script></body></html>