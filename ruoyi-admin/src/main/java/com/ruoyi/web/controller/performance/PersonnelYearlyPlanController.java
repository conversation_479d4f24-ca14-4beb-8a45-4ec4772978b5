package com.ruoyi.web.controller.performance;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.PersonnelYearlyPlan;
import com.ruoyi.system.domain.PersonnelYearlyPersonalIndicator;
import com.ruoyi.system.domain.PersonnelYearlyCommonIndicator;
import com.ruoyi.system.service.IPersonnelYearlyPlanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 个人年度绩效计划Controller
 */
@RestController
@RequestMapping("/performance/personnel/yearly")
public class PersonnelYearlyPlanController extends BaseController {
    
    @Autowired
    private IPersonnelYearlyPlanService personnelYearlyPlanService;

    /**
     * 查询个人年度绩效计划列表
     */
    @PreAuthorize("@ss.hasPermi('performance:personnel:yearly:list')")
    @GetMapping("/list")
    public TableDataInfo list(PersonnelYearlyPlan personnelYearlyPlan) {
        startPage();
        List<PersonnelYearlyPlan> list = personnelYearlyPlanService.selectPersonnelYearlyPlanList(personnelYearlyPlan);
        return getDataTable(list);
    }

    /**
     * 获取个人年度绩效计划详细信息
     */
    @PreAuthorize("@ss.hasPermi('performance:personnel:yearly:query')")
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(personnelYearlyPlanService.selectPersonnelYearlyPlanById(id));
    }

    /**
     * 新增个人年度绩效计划
     */
    @PreAuthorize("@ss.hasPermi('performance:personnel:yearly:add')")
    @Log(title = "个人年度绩效计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PersonnelYearlyPlan personnelYearlyPlan) {
        return toAjax(personnelYearlyPlanService.insertPersonnelYearlyPlan(personnelYearlyPlan));
    }

    /**
     * 修改个人年度绩效计划
     */
    @PreAuthorize("@ss.hasPermi('performance:personnel:yearly:edit')")
    @Log(title = "个人年度绩效计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PersonnelYearlyPlan personnelYearlyPlan) {
        return toAjax(personnelYearlyPlanService.updatePersonnelYearlyPlan(personnelYearlyPlan));
    }

    /**
     * 删除个人年度绩效计划
     */
    @PreAuthorize("@ss.hasPermi('performance:personnel:yearly:remove')")
    @Log(title = "个人年度绩效计划", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(personnelYearlyPlanService.deletePersonnelYearlyPlanByIds(ids));
    }

    /**
     * 获取个性指标列表
     */
    @PreAuthorize("@ss.hasPermi('performance:personnel:yearly:query')")
    @GetMapping("/{planId}/personalIndicators")
    public AjaxResult getPersonalIndicators(@PathVariable("planId") Long planId) {
        List<PersonnelYearlyPersonalIndicator> indicators = personnelYearlyPlanService.selectPersonalIndicatorsByPlanId(planId);
        return AjaxResult.success(indicators);
    }

    /**
     * 获取共性指标列表
     */
    @PreAuthorize("@ss.hasPermi('performance:personnel:yearly:query')")
    @GetMapping("/{planId}/commonIndicators")
    public AjaxResult getCommonIndicators(@PathVariable("planId") Long planId) {
        List<PersonnelYearlyCommonIndicator> indicators = personnelYearlyPlanService.selectCommonIndicatorsByPlanId(planId);
        return AjaxResult.success(indicators);
    }

    /**
     * 导入Excel文档
     */
    @PreAuthorize("@ss.hasPermi('performance:personnel:yearly:import')")
    @Log(title = "个人年度绩效计划", businessType = BusinessType.IMPORT)
    @PostMapping("/importExcel")
    public AjaxResult importExcel(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return AjaxResult.error("请选择文件");
        }
        try {
            personnelYearlyPlanService.importExcelData(file);
            return AjaxResult.success("导入成功");
        } catch (Exception e) {
            return AjaxResult.error("导入失败: " + e.getMessage());
        }
    }

    /**
     * 下载Excel模板
     */
    @PreAuthorize("@ss.hasPermi('performance:personnel:yearly:list')")
    @GetMapping("/downloadExcelTemplate")
    public void downloadExcelTemplate(HttpServletResponse response) throws Exception {
        personnelYearlyPlanService.downloadExcelTemplate(response);
    }
}
