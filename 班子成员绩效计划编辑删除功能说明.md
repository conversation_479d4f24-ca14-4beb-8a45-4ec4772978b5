# 班子成员绩效计划编辑删除功能说明

## 功能概述

已为班子成员绩效计划模块实现了完整的前后端编辑和删除功能，包括：

### 前端功能
1. **表格操作列**：在数据表格中添加了操作列，包含"修改"和"删除"按钮
2. **批量删除**：在工具栏添加了批量删除按钮
3. **权限控制**：所有操作按钮都添加了相应的权限控制
4. **表单验证**：完善了表单验证规则，包括必填项和长度限制
5. **错误处理**：为所有异步操作添加了错误处理机制

### 后端功能
1. **编辑接口**：PUT `/performance/leader` - 修改班子成员绩效计划
2. **删除接口**：DELETE `/performance/leader/{ids}` - 删除班子成员绩效计划（支持批量）
3. **查询接口**：GET `/performance/leader/{id}` - 获取单条记录详情

## 使用说明

### 1. 单条记录编辑
- 点击表格中某行的"修改"按钮
- 系统会弹出编辑对话框，自动填充当前记录的数据
- 修改完成后点击"确定"保存

### 2. 单条记录删除
- 点击表格中某行的"删除"按钮
- 系统会弹出确认对话框，显示要删除的成员姓名
- 确认后删除该条记录

### 3. 批量删除
- 勾选表格中要删除的多条记录
- 点击工具栏的"删除"按钮
- 系统会弹出确认对话框，显示要删除的记录数量
- 确认后批量删除选中的记录

### 4. 权限要求
- 编辑功能需要：`performance:leader:edit` 权限
- 删除功能需要：`performance:leader:remove` 权限
- 新增功能需要：`performance:leader:add` 权限
- 导出功能需要：`performance:leader:export` 权限
- 导入功能需要：`performance:leader:import` 权限

## 表单字段说明

### 必填字段
- **姓名**：班子成员姓名（最大100字符）
- **任务类型**：绩效任务的类型分类（最大100字符）
- **绩效任务**：具体的绩效任务内容（最大1000字符）

### 可选字段
- **目标及措施**：实现目标的具体措施（最大1000字符）
- **评价标准**：绩效评价的标准和依据（最大1000字符）
- **责任**：具体责任描述（最大100字符）
- **责任分类**：责任的分类标识（最大100字符）
- **权重分值**：该项任务的权重或分值（最大50字符）
- **完成时限**：任务完成的时间要求（最大100字符）
- **所属组织**：班子成员所属的组织机构（最大100字符）
- **计划年份**：绩效计划的年份（最大10字符）

## 技术实现

### 前端实现
- 使用Element UI的表格组件和对话框组件
- 通过axios调用后端API接口
- 实现了完整的表单验证和错误处理
- 支持权限控制和批量操作

### 后端实现
- Spring Boot RESTful API
- MyBatis数据访问层
- 支持事务管理和异常处理
- 完整的CRUD操作

## 数据库表结构

```sql
CREATE TABLE performance_plan_leader (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) COMMENT '姓名',
    seq_no INT COMMENT '序号',
    task_type VARCHAR(100) COMMENT '任务类型',
    performance_task TEXT COMMENT '绩效任务',
    target_measures TEXT COMMENT '目标及措施',
    evaluation_criteria TEXT COMMENT '评价标准',
    responsibility VARCHAR(100) COMMENT '责任',
    task_category VARCHAR(100) COMMENT '责任分类',
    weight_score VARCHAR(50) COMMENT '权重分值',
    deadline VARCHAR(100) COMMENT '完成时限',
    org_name VARCHAR(100) COMMENT '所属组织',
    plan_year VARCHAR(10) COMMENT '计划年份',
    create_time DATETIME COMMENT '创建时间',
    update_time DATETIME COMMENT '更新时间'
);
```

## 注意事项

1. 删除操作不可恢复，请谨慎操作
2. 编辑时必填字段包括：姓名、任务类型、绩效任务
3. 批量操作时请确保选择了正确的记录
4. 所有操作都会记录操作日志
5. 需要相应的系统权限才能执行操作
6. 表单验证会在提交前进行，确保数据完整性

## 测试建议

1. 测试单条记录的编辑功能
2. 测试单条记录的删除功能
3. 测试批量删除功能
4. 测试权限控制是否生效
5. 测试表单验证是否正常
6. 测试错误处理机制
7. 测试数据完整性约束
